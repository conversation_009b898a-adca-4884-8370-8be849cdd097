'use client';

/**
 * <PERSON><PERSON> dụ về cách sử dụng trường attributes kiểu JSON
 *
 * Tr<PERSON>ờ<PERSON> attributes trong bảng products và product_variants là trường kiểu JSONB
 * cho phép lưu trữ dữ liệu có cấu trúc phức tạp mà không cần tạo bảng quan hệ.
 */

// Ví dụ về cấu trúc dữ liệu attributes trong sản phẩm
const productAttributesExample = {
  // Thuộc tính sản phẩm
  color: ['Đỏ', 'Xanh', 'Đen'],
  size: ['S', 'M', 'L', 'XL'],
  material: ['Cotton', 'Polyester'],
};

// Ví dụ về cấu trúc dữ liệu attributes trong biến thể
const variantAttributesExample = {
  // Giá trị thuộc tính của biến thể cụ thể
  color: 'Đỏ',
  size: 'M',
  material: 'Cotton',
};

/**
 * Hàm tạo biến thể từ thuộc tính sản phẩm
 * @param {Object} productAttributes - Thuộc tính sản phẩm
 * @returns {Array} - Danh sách biến thể
 */
function generateVariantsFromAttributes(productAttributes) {
  // Tạo tổ hợp từ các thuộc tính
  const attributeNames = Object.keys(productAttributes);

  // Hàm đệ quy để tạo tổ hợp
  function generateCombinations(index = 0, current = {}) {
    if (index === attributeNames.length) {
      return [current];
    }

    const attribute = attributeNames[index];
    const values = productAttributes[attribute];
    const combinations = [];

    values.forEach((value) => {
      const newCurrent = { ...current, [attribute]: value };
      combinations.push(...generateCombinations(index + 1, newCurrent));
    });

    return combinations;
  }

  // Tạo tất cả các tổ hợp
  return generateCombinations();
}

/**
 * Hàm lưu biến thể vào database
 * @param {string} productId - ID sản phẩm
 * @param {Array} variants - Danh sách biến thể
 */
async function saveVariantsToDatabase(productId, variants) {
  // Giả lập việc lưu vào database
  console.log(`Lưu ${variants.length} biến thể cho sản phẩm ${productId}`);

  // Cấu trúc dữ liệu để lưu vào database
  const variantsToSave = variants.map((variant, index) => ({
    product_id: productId,
    sku: `SKU-${productId}-${index}`,
    price: 100000,
    stock_quantity: 10,
    // Lưu thuộc tính dưới dạng JSON
    attributes: variant,
  }));

  console.log('Dữ liệu biến thể để lưu:', variantsToSave);

  // Trong thực tế, bạn sẽ gọi API để lưu vào database
  // await supabase.from('product_variants').insert(variantsToSave);

  return variantsToSave;
}

/**
 * Hàm tìm kiếm biến thể theo thuộc tính
 * @param {Array} variants - Danh sách biến thể
 * @param {Object} attributes - Thuộc tính cần tìm
 * @returns {Object|null} - Biến thể tìm thấy hoặc null
 */
function findVariantByAttributes(variants, attributes) {
  return variants.find((variant) =>
    // So sánh từng thuộc tính
    Object.entries(attributes).every(([key, value]) => variant.attributes[key] === value)
  );
}

// Ví dụ sử dụng
const productId = 'PROD-123';
const variants = generateVariantsFromAttributes(productAttributesExample);
console.log(`Đã tạo ${variants.length} biến thể`);

// Lưu biến thể vào database
const savedVariants = saveVariantsToDatabase(productId, variants);

// Tìm biến thể theo thuộc tính
const searchAttributes = {
  color: 'Đỏ',
  size: 'M',
  material: 'Cotton',
};

const foundVariant = findVariantByAttributes(savedVariants, searchAttributes);
console.log('Tìm thấy biến thể:', foundVariant);

// Ví dụ về cách truy vấn trong Supabase
// const supabaseQueryExample = `
// -- Tìm tất cả biến thể có màu đỏ
// SELECT * FROM product_variants 
// WHERE attributes->>'color' = 'Đỏ';

// -- Tìm tất cả biến thể có size M và màu đỏ
// SELECT * FROM product_variants 
// WHERE attributes->>'color' = 'Đỏ' 
// AND attributes->>'size' = 'M';

// -- Tìm tất cả sản phẩm có thuộc tính màu
// SELECT * FROM products 
// WHERE attributes ? 'color';
// `;

export {
  saveVariantsToDatabase,
  findVariantByAttributes,
  productAttributesExample,
  variantAttributesExample,
  generateVariantsFromAttributes,
};
