'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  LinearProgress,
  Alert
} from '@mui/material';
import { Iconify } from 'src/components/iconify';
import { useFacebookActivityLogs } from 'src/actions/mooly-chatbot/facebook-integration';

/**
 * Facebook Analytics Dashboard
 * Monitor auto reply activities and performance
 */

export default function FacebookAnalyticsDashboard({ pageId, pageName }) {
  const [loading, setLoading] = useState(true);
  const [period, setPeriod] = useState('7d');
  const [analytics, setAnalytics] = useState(null);
  const [activities, setActivities] = useState([]);

  useEffect(() => {
    loadAnalytics();
  }, [pageId, period]);

  const loadAnalytics = async () => {
    try {
      setLoading(true);

      const response = await fetch(
        `/api/facebook-integration/analytics?pageId=${pageId}&period=${period}&limit=20`
      );
      const data = await response.json();

      if (data.success) {
        setAnalytics(data.analytics);
        setActivities(data.activities || []);
      }
    } catch (error) {
      console.error('Error loading analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  const getActivityIcon = (activity) => {
    const icons = {
      'auto_reply_sent': 'mdi:check-circle',
      'auto_reply_failed': 'mdi:alert-circle',
      'new_comment': 'mdi:comment-outline',
      'private_message': 'mdi:message-outline',
      'webhook_subscription': 'mdi:webhook',
      'config_updated': 'mdi:cog',
      'auto_reply_test': 'mdi:test-tube'
    };
    return icons[activity] || 'mdi:information';
  };

  const getActivityColor = (activity) => {
    const colors = {
      'auto_reply_sent': 'success',
      'auto_reply_failed': 'error',
      'new_comment': 'info',
      'private_message': 'info',
      'webhook_subscription': 'primary',
      'config_updated': 'warning',
      'auto_reply_test': 'secondary'
    };
    return colors[activity] || 'default';
  };

  const getActivityLabel = (activity) => {
    const labels = {
      'auto_reply_sent': 'Auto Reply Sent',
      'auto_reply_failed': 'Auto Reply Failed',
      'new_comment': 'New Comment',
      'private_message': 'Private Message',
      'webhook_subscription': 'Webhook Setup',
      'config_updated': 'Config Updated',
      'auto_reply_test': 'Test Auto Reply'
    };
    return labels[activity] || activity;
  };

  if (loading) {
    return (
      <Card>
        <CardContent>
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <LinearProgress sx={{ mb: 2 }} />
            <Typography>Đang tải analytics...</Typography>
          </Box>
        </CardContent>
      </Card>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h6">
          Analytics - {pageName}
        </Typography>

        <FormControl size="small" sx={{ minWidth: 120 }}>
          <InputLabel>Thời gian</InputLabel>
          <Select
            value={period}
            label="Thời gian"
            onChange={(e) => setPeriod(e.target.value)}
          >
            <MenuItem value="1d">24 giờ</MenuItem>
            <MenuItem value="7d">7 ngày</MenuItem>
            <MenuItem value="30d">30 ngày</MenuItem>
          </Select>
        </FormControl>
      </Box>

      {analytics && (
        <Grid container spacing={3} sx={{ mb: 4 }}>
          {/* Overview Stats */}
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Iconify icon="mdi:chart-line" color="primary.main" />
                  <Typography variant="h6" color="primary">
                    {analytics.totalActivities}
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary">
                  Tổng hoạt động
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Iconify icon="mdi:robot" color="success.main" />
                  <Typography variant="h6" color="success.main">
                    {analytics.autoRepliesSent}
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary">
                  Auto Reply thành công
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Iconify icon="mdi:comment-multiple" color="info.main" />
                  <Typography variant="h6" color="info.main">
                    {analytics.commentsProcessed}
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary">
                  Comments xử lý
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Iconify icon="mdi:percent" color="warning.main" />
                  <Typography variant="h6" color="warning.main">
                    {analytics.successRate}%
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary">
                  Tỷ lệ thành công
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          {/* Platform Stats */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="subtitle1" gutterBottom>
                  Theo nền tảng
                </Typography>
                <Box sx={{ display: 'flex', gap: 2 }}>
                  <Box sx={{ flex: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                      <Iconify icon="mdi:facebook" color="primary.main" />
                      <Typography variant="body2">Facebook</Typography>
                    </Box>
                    <Typography variant="h6">{analytics.facebookActivities}</Typography>
                  </Box>
                  <Box sx={{ flex: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                      <Iconify icon="mdi:instagram" color="secondary.main" />
                      <Typography variant="body2">Instagram</Typography>
                    </Box>
                    <Typography variant="h6">{analytics.instagramActivities}</Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Activity Types */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="subtitle1" gutterBottom>
                  Loại hoạt động
                </Typography>
                <List dense>
                  {Object.entries(analytics.activityByType || {}).map(([type, count]) => (
                    <ListItem key={type} sx={{ px: 0 }}>
                      <ListItemIcon sx={{ minWidth: 32 }}>
                        <Iconify 
                          icon={getActivityIcon(type)} 
                          color={`${getActivityColor(type)}.main`}
                          width={20}
                        />
                      </ListItemIcon>
                      <ListItemText
                        primary={getActivityLabel(type)}
                        secondary={`${count} lần`}
                      />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Recent Activities */}
      <Card>
        <CardContent>
          <Typography variant="subtitle1" gutterBottom>
            Hoạt động gần đây
          </Typography>

          {activities.length === 0 ? (
            <Alert severity="info">
              Chưa có hoạt động nào trong khoảng thời gian này
            </Alert>
          ) : (
            <List>
              {activities.map((activity, index) => (
                <ListItem
                  key={activity.id}
                  sx={{
                    border: 1,
                    borderColor: 'divider',
                    borderRadius: 1,
                    mb: 1
                  }}
                >
                  <ListItemIcon>
                    <Iconify 
                      icon={getActivityIcon(activity.activity)} 
                      color={`${getActivityColor(activity.activity)}.main`}
                    />
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="body2">
                          {getActivityLabel(activity.activity)}
                        </Typography>
                        <Chip 
                          label={getActivityLabel(activity.activity)}
                          size="small"
                          color={getActivityColor(activity.activity)}
                          variant="outlined"
                        />
                      </Box>
                    }
                    secondary={
                      <Box>
                        {activity.metadata?.message && (
                          <Typography variant="caption" sx={{ display: 'block' }}>
                            Message: "{activity.metadata.message}"
                          </Typography>
                        )}
                        {activity.metadata?.response && (
                          <Typography variant="caption" sx={{ display: 'block', color: 'primary.main' }}>
                            Response: "{activity.metadata.response}"
                          </Typography>
                        )}
                        <Typography variant="caption" color="text.secondary">
                          {new Date(activity.createdAt).toLocaleString('vi-VN')}
                        </Typography>
                      </Box>
                    }
                  />
                </ListItem>
              ))}
            </List>
          )}
        </CardContent>
      </Card>
    </Box>
  );
}
