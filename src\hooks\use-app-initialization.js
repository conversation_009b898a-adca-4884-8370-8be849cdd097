'use client';

import { useMemo } from 'react';

import { useAuthContext } from 'src/auth/hooks';
import { useBusinessConfigContext } from 'src/actions/mooly-chatbot/business-config-service';

// ----------------------------------------------------------------------

/**
 * Hook để theo dõi trạng thái initialization của app
 * Kết hợp auth state và business config state
 */
export function useAppInitialization() {
  const { 
    loading: authLoading, 
    authenticated, 
    tenantId, 
    initialized: authInitialized,
    user 
  } = useAuthContext();
  
  const { 
    config, 
    loading: configLoading, 
    error: configError 
  } = useBusinessConfigContext();

  const status = useMemo(() => {
    // Nếu auth chưa initialized
    if (!authInitialized) {
      return {
        status: 'initializing',
        message: '<PERSON>ang khởi tạo xác thực...',
        ready: false,
        error: null
      };
    }

    // Nếu auth đang loading
    if (authLoading) {
      return {
        status: 'loading',
        message: 'Đang xác thực người dùng...',
        ready: false,
        error: null
      };
    }

    // Nếu user chưa authenticated
    if (!authenticated) {
      return {
        status: 'unauthenticated',
        message: 'Chưa đăng nhập',
        ready: true, // Ready để hiển thị login form
        error: null
      };
    }

    // Nếu user authenticated nhưng chưa có tenant
    if (!tenantId) {
      return {
        status: 'no_tenant',
        message: 'Tài khoản chưa được kích hoạt',
        ready: true, // Ready để hiển thị thông báo
        error: 'Tài khoản chưa được gán vào tenant nào'
      };
    }

    // Nếu business config đang loading
    if (configLoading) {
      return {
        status: 'loading_config',
        message: 'Đang tải cấu hình kinh doanh...',
        ready: false,
        error: null
      };
    }

    // Nếu có lỗi config nhưng chưa có config
    if (configError && !config) {
      return {
        status: 'config_error',
        message: 'Lỗi tải cấu hình kinh doanh',
        ready: false,
        error: configError
      };
    }

    // Nếu mọi thứ đã sẵn sàng
    return {
      status: 'ready',
      message: 'Ứng dụng đã sẵn sàng',
      ready: true,
      error: null
    };
  }, [
    authInitialized,
    authLoading,
    authenticated,
    tenantId,
    configLoading,
    config,
    configError
  ]);

  return {
    ...status,
    // Additional data
    user,
    tenantId,
    config,
    // Helper flags
    isInitializing: status.status === 'initializing',
    isLoading: ['loading', 'loading_config'].includes(status.status),
    isAuthenticated: authenticated,
    hasConfig: !!config,
    hasError: !!status.error,
    // Business config helpers
    businessType: config?.businessType,
    isFeatureEnabled: (feature) => config?.config?.features?.[feature]?.enabled || false,
    getEnabledFeatures: () => {
      if (!config?.config?.features) return [];
      return Object.keys(config.config.features).filter(
        feature => config.config.features[feature].enabled
      );
    }
  };
}

/**
 * Hook để kiểm tra xem app có sẵn sàng cho một feature cụ thể không
 */
export function useFeatureReady(feature) {
  const { ready, isFeatureEnabled } = useAppInitialization();
  
  return ready && isFeatureEnabled(feature);
}

/**
 * Hook để kiểm tra xem app có sẵn sàng cho một business type cụ thể không
 */
export function useBusinessTypeReady(allowedTypes = []) {
  const { ready, businessType } = useAppInitialization();
  
  if (!ready || !businessType) return false;
  
  return allowedTypes.length === 0 || allowedTypes.includes(businessType);
}

/**
 * Hook để lấy loading state phù hợp cho UI
 */
export function useLoadingState() {
  const { status, message, isLoading, isInitializing } = useAppInitialization();
  
  return {
    loading: isLoading || isInitializing,
    message,
    showSpinner: isLoading || isInitializing,
    showSkeleton: status === 'loading_config'
  };
}
