# 📋 Phân Tích Logic Kéo Thả Lead - Optimistic Updates Pattern

## 🎯 Tổng Quan

Logic kéo thả đã được tối ưu để cung cấp trải nghiệm người dùng mượt mà với **optimistic updates** và **proper rollback** khi có lỗi.

## 🔄 Luồng Hoạt Động (Workflow)

### 1. **onDragOver** - Real-time Preview
```javascript
// ✨ REAL-TIME FEEDBACK
const onDragOver = useCallback(({ active, over }) => {
  // 1. Validation cơ bản
  const activeContainer = findColumn(active.id);
  const overContainer = findColumn(over?.id);
  
  // 2. Kiểm tra target column hợp lệ
  const validStatuses = stages.map(stage => stage.id);
  if (!validStatuses.includes(overContainer)) return;
  
  // 3. OPTIMISTIC UI UPDATE ngay lập tức
  mutate((currentData) => {
    return {
      ...currentData,
      data: currentData.data.map(lead => 
        lead.id === activeTask.id 
          ? { ...lead, status: overContainer } // ⚡ Cập nhật tức thì
          : lead
      ),
    };
  }, false); // false = không revalidate API
}, [findColumn, tasks, stages, mutate]);
```

**✅ Benefits:**
- User thấy feedback tức thì khi kéo
- UI responsive và mượt mà
- Preview trạng thái mới trước khi commit

### 2. **onDragEnd** - Commit Changes & Handle Errors
```javascript
// 🎯 COMMIT & ERROR HANDLING
const onDragEnd = useCallback(async ({ active, over }) => {
  // 1. Early validation & cleanup
  setActiveId(null); // Reset drag state ngay
  
  // 2. Business logic validation
  if (oldStatus === newStatus) return; // Không thay đổi gì
  if (pendingUpdates.current.has(leadId)) return; // Tránh duplicate
  
  // 3. Add to pending set
  pendingUpdates.current.add(leadId);
  
  try {
    // 4. API Call (UI đã update trong onDragOver)
    const result = await updateLeadStatus(leadId, newStatus);
    
    if (result.success) {
      // ✅ SUCCESS: Confirm optimistic update
      setTimeout(() => mutate(), 300); // Sync với server
      toast.success(`Đã chuyển lead sang "${stageName}"`);
      
    } else {
      // ❌ API FAILED: Rollback optimistic update
      mutate((currentData) => ({
        ...currentData,
        data: currentData.data.map(lead =>
          lead.id === leadId ? { ...lead, status: oldStatus } : lead
        ),
      }), false);
      
      toast.error('Không thể cập nhật trạng thái lead. Đã hoàn tác thay đổi.');
    }
    
  } catch (updateError) {
    // 🔥 EXCEPTION: Rollback & show error
    mutate((currentData) => ({
      ...currentData,
      data: currentData.data.map(lead =>
        lead.id === leadId ? { ...lead, status: oldStatus } : lead
      ),
    }), false);
    
    toast.error(`Lỗi hệ thống: ${updateError.message}`);
    
  } finally {
    // 🧹 Cleanup: Always remove from pending
    pendingUpdates.current.delete(leadId);
  }
}, [findColumn, tasks, mutate, stages]);
```

## 🚀 So Sánh Với Kanban View Chuẩn

### **Standard Kanban** (`kanban-view.jsx`)
```javascript
// Chỉ di chuyển UI, không có API call
const onDragEnd = ({ active, over }) => {
  const updateTasks = {
    ...board.tasks,
    [overColumn]: arrayMove(board.tasks[overColumn], activeIndex, overIndex),
  };
  
  moveTask(updateTasks); // Chỉ update local state
};
```

### **Lead Kanban** (`leads-kanban-view.jsx`) 
```javascript
// UI update + API call + Error handling
const onDragEnd = async ({ active, over }) => {
  // 1. Preview đã update trong onDragOver ⚡
  // 2. Gọi API để persist changes 🌐
  // 3. Rollback nếu API failed 🔄
  // 4. Toast notifications 🔔
  // 5. Sync với server 🔄
};
```

## 💎 Optimizations Đã Áp Dụng

### 1. **Dual-Phase Updates**
- **Phase 1 (onDragOver)**: Real-time preview với optimistic update
- **Phase 2 (onDragEnd)**: Commit to server với proper error handling

### 2. **Graceful Error Handling**
```javascript
// ❌ Khi API failed
mutate((currentData) => ({
  ...currentData,
  data: currentData.data.map(lead =>
    lead.id === leadId 
      ? { ...lead, status: oldStatus } // 🔄 Rollback về trạng thái cũ
      : lead
  ),
}), false);
```

### 3. **Duplicate Prevention**
```javascript
const pendingUpdates = useRef(new Set()); // Track pending operations

// Check before API call
if (pendingUpdates.current.has(leadId)) {
  mutate(); // Revert preview
  return;
}
```

### 4. **Status Validation**
```javascript
// Validate workflow stages
const validStatuses = stages.map(stage => stage.id);
if (!validStatuses.includes(newStatus)) {
  toast.warning(`Trạng thái "${newStatus}" không tồn tại trong workflow`);
  mutate(); // Revert preview
  return;
}
```

## 🎨 UI/UX Benefits

### ✅ **Immediate Feedback**
- User thấy lead di chuyển ngay khi drag
- Không cần chờ API response để thấy thay đổi

### ✅ **Error Recovery**
- Tự động rollback khi API failed
- Clear error messages với toast notifications
- UI luôn consistent với server state

### ✅ **Performance Optimized**
- Optimistic updates giảm perceived latency
- Non-blocking UI operations
- Efficient state management với SWR

### ✅ **User Experience**
```
User Action: Drag lead → New column
├── ⚡ Immediate: UI updates (onDragOver)
├── 🌐 Background: API call (onDragEnd)  
├── ✅ Success: Confirm + Toast notification
└── ❌ Failed: Rollback + Error toast
```

## 🔧 Technical Implementation

### **Dependencies Array Optimization**
```javascript
// onDragOver dependencies
[findColumn, tasks, stages, mutate]

// onDragEnd dependencies  
[findColumn, tasks, mutate, stages]
```

### **Memory Management**
```javascript
const pendingUpdates = useRef(new Set()); // Efficient tracking
const recentlyMovedToNewContainer = useRef(false); // Drag state
```

### **State Management Pattern**
```javascript
// Optimistic update
mutate((currentData) => transformedData, false);

// Server sync (delayed)
setTimeout(() => mutate(), 300);

// Rollback on error
mutate((currentData) => revertedData, false);
```

## 🎯 Kết Luận

Logic kéo thả đã được tối ưu với pattern **optimistic updates** chuẩn, cung cấp:

1. **Real-time feedback** trong onDragOver
2. **Reliable persistence** trong onDragEnd  
3. **Graceful error handling** với rollback
4. **Performance optimization** với efficient state updates
5. **Consistent UX** với proper loading states và notifications

Pattern này đảm bảo user experience mượt mà trong khi vẫn maintain data integrity và error recovery capabilities. 