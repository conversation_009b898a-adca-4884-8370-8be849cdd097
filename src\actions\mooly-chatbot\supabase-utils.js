'use client';

import { supabase } from 'src/lib/supabase';
import {
  camelToSnake,
  snakeToCamelObject,
  camelToSnakeObject,
} from 'src/utils/format-data/case-converter';

// =====================================================
// OPTIMIZED SUPABASE UTILS WITH FULL RLS TRUST
// =====================================================
// Tenant filtering is handled at database level via:
// 1. RLS policies using get_user_tenant_id() function
// 2. Database triggers auto-setting tenant_id on INSERT
// 3. Cached tenant_id functions for optimal performance
//
// Client-side code trusts database-level security completely

/**
 * Lấy dữ liệu từ Supabase với các tùy chọn lọc
 * @param {string} table - Tên bảng
 * @param {Object} options - <PERSON><PERSON><PERSON> tùy chọn truy vấn
 * @param {Object} options.filters - <PERSON><PERSON><PERSON> điều kiện lọc
 * @param {Array} options.columns - Các cột cần lấy
 * @param {string} options.orderBy - Cột để sắp xếp
 * @param {boolean} options.ascending - Sắp xếp tăng dần hay giảm dần
 * @param {number} options.limit - Giới hạn số lượng bản ghi
 * @param {number} options.offset - Vị trí bắt đầu
 * @param {boolean} options.single - Chỉ lấy một bản ghi
 * @param {boolean} options.count - Đếm số lượng bản ghi
 * @returns {Promise<Object>} - Kết quả từ Supabase
 */
export async function fetchData(table, options = {}) {
  try {
    const {
      filters = {},
      columns = '*',
      orderBy,
      ascending = true,
      limit,
      offset,
      single = false,
      count,
    } = options;

    // ✅ OPTIMIZED: No client-side tenant filtering needed
    // RLS policies automatically filter by tenant_id at database level
    // Database functions are cached for optimal performance

    // Start query
    let query = supabase.from(table).select(columns, { count: count ? 'exact' : undefined });

    // Convert filters from camelCase to snake_case
    const snakeCaseFilters = camelToSnakeObject(filters);

    // Apply filters
    Object.entries(snakeCaseFilters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (typeof value === 'object' && value.operator) {
          // Handle complex operators
          const { operator, value: filterValue } = value;
          switch (operator) {
            case 'eq':
              query = query.eq(key, filterValue);
              break;
            case 'neq':
              query = query.neq(key, filterValue);
              break;
            case 'gt':
              query = query.gt(key, filterValue);
              break;
            case 'gte':
              query = query.gte(key, filterValue);
              break;
            case 'lt':
              query = query.lt(key, filterValue);
              break;
            case 'lte':
              query = query.lte(key, filterValue);
              break;
            case 'like':
              query = query.like(key, `%${filterValue}%`);
              break;
            case 'ilike':
              query = query.ilike(key, `%${filterValue}%`);
              break;
            case 'in':
              query = query.in(key, filterValue);
              break;
            case 'is':
              query = query.is(key, filterValue);
              break;
            default:
              query = query.eq(key, filterValue);
          }
        } else if (typeof value === 'object' && (value.gte !== undefined || value.lte !== undefined || value.gt !== undefined || value.lt !== undefined)) {
          // Handle range filters with gte and lte
          if (value.gte !== undefined) {
            query = query.gte(key, value.gte);
          }
          if (value.lte !== undefined) {
            query = query.lte(key, value.lte);
          }
          if (value.gt !== undefined) {
            query = query.gt(key, value.gt);
          }
          if (value.lt !== undefined) {
            query = query.lt(key, value.lt);
          }
        } else {
          // Default to equals operator
          query = query.eq(key, value);
        }
      }
    });

    // Sort (convert column name from camelCase to snake_case)
    if (orderBy) {
      if (Array.isArray(orderBy)) {
        // Handle array of order objects: [{ column: 'display_order', ascending: true }]
        orderBy.forEach(order => {
          const snakeCaseColumn = camelToSnake(order.column);
          query = query.order(snakeCaseColumn, { ascending: order.ascending !== false });
        });
      } else {
        // Handle single string column
        const snakeCaseOrderBy = camelToSnake(orderBy);
        query = query.order(snakeCaseOrderBy, { ascending });
      }
    }

    // Pagination
    if (limit) {
      query = query.limit(limit);
    }

    if (offset) {
      query = query.offset(offset);
    }

    // Execute query with improved timeout protection
    let result;
    try {
      // Add timeout protection to prevent hanging requests
      // Use longer timeout for background tabs
      const isVisible = document.visibilityState === 'visible';
      const timeoutDuration = isVisible ? 30000 : 120000; // 30s foreground, 120s background

      // Create an abortable fetch
      const abortController = new AbortController();

      // Register with global abort controller registry if available
      let unregisterController = () => {};
      if (typeof window !== 'undefined' && window.registerAbortController) {
        unregisterController = window.registerAbortController(abortController);
      }

      // Set timeout for the request
      const timeoutId = setTimeout(() => {
        abortController.abort('timeout');
      }, timeoutDuration);

      // Set abort signal on query
      query = query.abortSignal(abortController.signal);

      try {
        // Execute query
        result = single ? await query.single() : await query;
        clearTimeout(timeoutId);
      } catch (error) {
        clearTimeout(timeoutId);
        throw error;
      } finally {
        // Unregister from global registry
        unregisterController();
      }
    } catch (queryError) {
      // Check if this is an abort error
      if (
        queryError.name === 'AbortError' ||
        queryError.message?.includes('timeout') ||
        queryError.message?.includes('abort')
      ) {
        const reason = queryError.message?.includes('timeout')
          ? 'timeout'
          : queryError.message?.includes('visibility')
            ? 'visibility change'
            : 'abort';
        return {
          success: false,
          error: {
            message: `Query aborted - ${reason}`,
            code: 'QUERY_ABORTED',
            reason,
          },
          data: null,
          count: 0,
        };
      }

      return { success: false, error: queryError, data: null, count: 0 };
    }

    const { data, error, count: resultCount } = result;

    if (error) {
      console.error(`fetchData: Error in result for ${table}:`, error);

      // Handle specific Supabase errors
      if (error.code === 'PGRST116') {
        // "JSON object requested, multiple (or no) rows returned"
        if (single) {
          console.warn(`⚠️ Single query for ${table} returned no rows or multiple rows`);
          return {
            success: false,
            error: {
              ...error,
              message: 'Không tìm thấy dữ liệu hoặc có nhiều hơn 1 bản ghi',
              code: 'NO_ROWS_OR_MULTIPLE_ROWS'
            },
            data: null,
            count: 0
          };
        }
      }

      // Nếu lỗi liên quan đến RLS, cung cấp thông báo rõ ràng hơn
      if (error.message?.includes('row-level security') ||
          error.message?.includes('policy') ||
          error.message?.includes('permission denied')) {
        const enhancedError = {
          ...error,
          message: 'Bạn không có quyền truy cập dữ liệu này. Vui lòng kiểm tra lại quyền của bạn.',
          originalMessage: error.message
        };
        return { success: false, error: enhancedError, data: null, count: 0 };
      }

      return { success: false, error, data: null, count: 0 };
    }

    const transformedData = snakeToCamelObject(data);

    return {
      success: true,
      data: transformedData,
      error: null,
      count: count ? resultCount : undefined,
    };
  } catch (error) {
    return { success: false, error, data: null, count: 0 };
  }
}

/**
 * Tạo dữ liệu mới trong Supabase
 * @param {string} table - Tên bảng
 * @param {Object|Array} data - Dữ liệu cần tạo (đối tượng hoặc mảng các đối tượng)
 * @param {boolean} returnData - Có trả về dữ liệu sau khi tạo không
 * @returns {Promise<Object>} - Kết quả từ Supabase
 */
export async function createData(table, data, returnData = true) {
  try {
    // ✅ OPTIMIZED: Database triggers auto-set tenant_id for all tables
    // ✅ OPTIMIZED: RLS policies validate access based on tenant_id
    // ✅ OPTIMIZED: No client-side tenant_id manipulation needed

    const snakeCaseData = camelToSnakeObject(data);

    let query = supabase.from(table).insert(snakeCaseData);

    if (returnData) {
      query = query.select();
    }

    const { data: result, error } = await query;

    if (error) {
      // Enhanced error handling cho các lỗi phổ biến
      if (error.message?.includes('row-level security') ||
          error.message?.includes('policy') ||
          error.message?.includes('permission denied')) {
        const enhancedError = {
          ...error,
          message: 'Bạn không có quyền truy cập dữ liệu này. Vui lòng kiểm tra lại quyền của bạn.',
          originalMessage: error.message
        };
        return { success: false, error: enhancedError, data: null };
      }

      if (error.message?.includes('Invalid tenant_id') ||
          error.message?.includes('Access denied')) {
        const enhancedError = {
          ...error,
          message: 'Bạn không có quyền truy cập tenant này.',
          originalMessage: error.message
        };
        return { success: false, error: enhancedError, data: null };
      }

      return { success: false, error, data: null };
    }

    const transformedData = returnData ? snakeToCamelObject(result) : null;

    return { success: true, data: transformedData, error: null };
  } catch (error) {
    console.error(`Error in createData for ${table}:`, error);
    return { success: false, error, data: null };
  }
}

/**
 * Cập nhật dữ liệu trong Supabase
 * @param {string} table - Tên bảng
 * @param {Object} data - Dữ liệu cần cập nhật
 * @param {Object} filters - Các điều kiện lọc
 * @param {boolean} returnData - Có trả về dữ liệu sau khi cập nhật không
 * @returns {Promise<Object>} - Kết quả từ Supabase
 */
export async function updateData(table, data, filters, returnData = true) {
  try {
    // ✅ OPTIMIZED: RLS policies handle tenant filtering at database level

    const snakeCaseData = camelToSnakeObject(data);
    const snakeCaseFilters = camelToSnakeObject(filters);

    let query = supabase.from(table).update(snakeCaseData);

    // Áp dụng các bộ lọc
    Object.entries(snakeCaseFilters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        query = query.eq(key, value);
      }
    });

    if (returnData) {
      query = query.select();
    }

    const { data: result, error } = await query;

    if (error) {
      console.error(`updateData: Error updating ${table}:`, error);

      // Enhanced error handling cho các lỗi phổ biến
      if (error.message?.includes('row-level security') ||
          error.message?.includes('policy') ||
          error.message?.includes('permission denied')) {
        const enhancedError = {
          ...error,
          message: 'Bạn không có quyền cập nhật dữ liệu này. Vui lòng kiểm tra lại quyền của bạn.',
          originalMessage: error.message
        };
        return { success: false, error: enhancedError, data: null };
      }

      if (error.message?.includes('Invalid tenant_id') ||
          error.message?.includes('Access denied')) {
        const enhancedError = {
          ...error,
          message: 'Bạn không có quyền truy cập tenant này.',
          originalMessage: error.message
        };
        return { success: false, error: enhancedError, data: null };
      }

      return { success: false, error, data: null };
    }

    const transformedData = returnData ? snakeToCamelObject(result) : null;

    return { success: true, data: transformedData, error: null };
  } catch (error) {
    console.error(`Error in updateData for ${table}:`, error);
    return { success: false, error, data: null };
  }
}

/**
 * Xóa dữ liệu trong Supabase
 * @param {string} table - Tên bảng
 * @param {Object} filters - Các điều kiện lọc
 * @param {boolean} returnData - Có trả về dữ liệu đã xóa không
 * @returns {Promise<Object>} - Kết quả từ Supabase
 */
export async function deleteData(table, filters, returnData = false) {
  try {
    // ✅ OPTIMIZED: RLS policies handle tenant filtering at database level

    const snakeCaseFilters = camelToSnakeObject(filters);

    let query = supabase.from(table).delete();

    // Apply filters với support cho các operators phức tạp
    Object.entries(snakeCaseFilters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (typeof value === 'object' && value.operator) {
          // Handle complex operators
          const { operator, value: filterValue } = value;
          switch (operator) {
            case 'eq':
              query = query.eq(key, filterValue);
              break;
            case 'neq':
              query = query.neq(key, filterValue);
              break;
            case 'gt':
              query = query.gt(key, filterValue);
              break;
            case 'gte':
              query = query.gte(key, filterValue);
              break;
            case 'lt':
              query = query.lt(key, filterValue);
              break;
            case 'lte':
              query = query.lte(key, filterValue);
              break;
            case 'like':
              query = query.like(key, `%${filterValue}%`);
              break;
            case 'ilike':
              query = query.ilike(key, `%${filterValue}%`);
              break;
            case 'in':
              query = query.in(key, filterValue);
              break;
            case 'is':
              query = query.is(key, filterValue);
              break;
            default:
              query = query.eq(key, filterValue);
          }
        } else if (typeof value === 'object' && (value.gte !== undefined || value.lte !== undefined || value.gt !== undefined || value.lt !== undefined)) {
          // Handle range filters
          if (value.gte !== undefined) {
            query = query.gte(key, value.gte);
          }
          if (value.lte !== undefined) {
            query = query.lte(key, value.lte);
          }
          if (value.gt !== undefined) {
            query = query.gt(key, value.gt);
          }
          if (value.lt !== undefined) {
            query = query.lt(key, value.lt);
          }
        } else {
          // Default to equals operator
          query = query.eq(key, value);
        }
      }
    });

    if (returnData) {
      query = query.select();
    }

    const { data: result, error } = await query;

    if (error) {
      console.error(`deleteData: Error deleting from ${table}:`, error);
      return { success: false, error, data: null };
    }

    const transformedData = returnData ? snakeToCamelObject(result) : null;

    return { success: true, data: transformedData, error: null };
  } catch (error) {
    console.error(`Error in deleteData for ${table}:`, error);
    return { success: false, error, data: null };
  }
}

/**
 * Tạo hoặc cập nhật dữ liệu trong Supabase
 * @param {string} table - Tên bảng
 * @param {Object|Array} data - Dữ liệu cần tạo hoặc cập nhật
 * @param {Array} onConflict - Các cột xung đột
 * @param {boolean} returnData - Có trả về dữ liệu sau khi thực hiện không
 * @returns {Promise<Object>} - Kết quả từ Supabase
 */
export async function upsertData(table, data, onConflict = ['id'], returnData = true) {
  try {
    // ✅ OPTIMIZED: RLS policies and triggers handle tenant data at database level

    const snakeCaseData = camelToSnakeObject(data);
    // FIX: Convert onConflict column names from camelCase to snake_case to match database schema
    const snakeCaseOnConflict = onConflict.map(col => camelToSnake(col));

    let query = supabase.from(table).upsert(snakeCaseData, { onConflict: snakeCaseOnConflict, ignoreDuplicates: false });

    if (returnData) {
      query = query.select();
    }

    const { data: result, error } = await query;

    if (error) {
      return { success: false, error, data: null };
    }

    const transformedData = returnData ? snakeToCamelObject(result) : null;

    return { success: true, data: transformedData, error: null };
  } catch (error) {
    return { success: false, error, data: null };
  }
}

/**
 * Thực hiện RPC (Remote Procedure Call) trong Supabase
 * @param {string} functionName - Tên hàm RPC
 * @param {Object} params - Các tham số
 * @returns {Promise<Object>} - Kết quả từ Supabase
 */
export async function callRPC(functionName, params = {}) {
  try {

    const snakeCaseParams = camelToSnakeObject(params);

    const sanitizedParams = {};
    Object.entries(snakeCaseParams).forEach(([key, value]) => {
      if (typeof value === 'boolean') {
        sanitizedParams[key] = value === true;
      } else if (value === null || value === undefined) {
        // Xử lý giá trị null/undefined
        sanitizedParams[key] = null;
      } else {
        sanitizedParams[key] = value;
      }
    });

    const { data, error } = await supabase.rpc(functionName, sanitizedParams);

    if (error) {
      return { success: false, error, data: null };
    }

    const transformedData = snakeToCamelObject(data);

    return { success: true, data: transformedData, error: null };
  } catch (error) {
    return { success: false, error, data: null };
  }
}

/**
 * Thực hiện raw SQL query trong Supabase
 * @param {string} query - SQL query string
 * @returns {Promise<Object>} - Kết quả từ Supabase
 */
export async function executeQuery(query) {
  try {

    const { data, error } = await supabase.rpc('execute_sql', { query_text: query });

    if (error) {
      return { success: false, error, data: null };
    }

    return { success: true, data, error: null };
  } catch (error) {
    console.error('Error executing query:', error);
    return { success: false, error, data: null };
  }
}
