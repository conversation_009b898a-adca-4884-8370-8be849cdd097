Stack trace:
Frame         Function      Args
0007FFFFB740  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFA640) msys-2.0.dll+0x1FE8E
0007FFFFB740  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x67F9
0007FFFFB740  000210046832 (000210286019, 0007FFFFB5F8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFB740  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFB740  000210068E24 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBA20  00021006A225 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFDB0EB0000 ntdll.dll
7FFDAFAA0000 KERNEL32.DLL
7FFDAE170000 KERNELBASE.dll
7FFDB0B50000 USER32.dll
7FFDAE8A0000 win32u.dll
000210040000 msys-2.0.dll
7FFDAEC20000 GDI32.dll
7FFDAE8D0000 gdi32full.dll
7FFDAE550000 msvcp_win.dll
7FFDADFA0000 ucrtbase.dll
7FFDAEDD0000 advapi32.dll
7FFDAFD60000 msvcrt.dll
7FFDAF270000 sechost.dll
7FFDAE140000 bcrypt.dll
7FFDAFBE0000 RPCRT4.dll
7FFDAD830000 CRYPTBASE.DLL
7FFDAE760000 bcryptPrimitives.dll
7FFDAF230000 IMM32.DLL
