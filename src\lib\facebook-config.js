/**
 * Facebook Integration Configuration
 * Centralized configuration for Facebook API integration
 */

export const FACEBOOK_CONFIG = {
  // API Configuration - Sử dụng phiên bản mới nhất
  API_VERSION: 'v23.0',
  BASE_URL: 'https://graph.facebook.com',

  // Environment Variables - Hỗ trợ cả client và server
  APP_ID: process.env.NEXT_PUBLIC_FACEBOOK_APP_ID || process.env.FACEBOOK_APP_ID,
  APP_SECRET: process.env.FACEBOOK_APP_SECRET,
  WEBHOOK_VERIFY_TOKEN: process.env.FACEBOOK_WEBHOOK_VERIFY_TOKEN,

  // OAuth Configuration
  OAUTH_DIALOG_URL: 'https://www.facebook.com',
  REDIRECT_URI_PATH: '/api/facebook-integration/popup-callback',

  // Required Permissions - <PERSON>h sách chuẩn cho tất cả features
  PERMISSIONS: [
    'email',
    'pages_show_list',
    'pages_read_engagement',
    'pages_manage_posts',
    'pages_manage_engagement',
    'pages_messaging',
    'instagram_basic',
    'instagram_manage_comments',
    'instagram_manage_messages'
  ],

  // Auto Reply Settings
  DEFAULT_REPLY_TEXT: 'Đã Nhận',

  // Token Refresh Settings
  TOKEN_REFRESH_THRESHOLD_DAYS: 7, // Refresh token if expires within 7 days

  // API Endpoints
  ENDPOINTS: {
    OAUTH_ACCESS_TOKEN: '/oauth/access_token',
    OAUTH_DIALOG: '/dialog/oauth',
    COMMENT_REPLIES: (commentId) => `/${commentId}/comments`,
    PRIVATE_REPLIES: (commentId) => `/${commentId}/private_replies`,
    PAGE_MESSAGES: (pageId) => `/${pageId}/messages`,
    PAGE_INFO: (pageId) => `/${pageId}`,
  },

  // Popup Configuration
  POPUP: {
    WIDTH: 600,
    HEIGHT: 700,
    FEATURES: 'scrollbars=yes,resizable=yes'
  },

  // Validation
  isConfigured() {
    return !!(this.APP_ID && this.APP_SECRET && this.WEBHOOK_VERIFY_TOKEN);
  },

  // Get full API URL
  getApiUrl(endpoint) {
    return `${this.BASE_URL}/${this.API_VERSION}${endpoint}`;
  },

  // Get OAuth Dialog URL
  getOAuthDialogUrl() {
    return `${this.OAUTH_DIALOG_URL}/${this.API_VERSION}${this.ENDPOINTS.OAUTH_DIALOG}`;
  },

  // Get redirect URI
  getRedirectUri(baseUrl) {
    return `${baseUrl}${this.REDIRECT_URI_PATH}`;
  }
};

// Validate configuration on import
if (!FACEBOOK_CONFIG.isConfigured()) {
  console.warn('⚠️ Facebook configuration incomplete. Check environment variables.');
}
