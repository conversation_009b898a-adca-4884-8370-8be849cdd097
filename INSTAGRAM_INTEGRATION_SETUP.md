# Instagram Business API 2025 - Direct Integration Setup Guide

## 🚀 Tổng Quan Instagram API Mới 2025

**Instagram API with Instagram Login** đã được Meta ra mắt chính thức vào **July 23, 2024**, mang đến khả năng kết nối trực tiếp với Instagram **KHÔNG CẦN FACEBOOK PAGE**.

### ✅ Những Thay Đổi Cách Mạng

1. **Kết nối trực tiếp**: Không cần tạo hoặc liên kết Facebook Page
2. **OAuth riêng biệt**: Instagram login flow độc lập  
3. **Host URL mới**: `graph.instagram.com` thay vì `graph.facebook.com`
4. **Scope values mới**: `instagram_business_*` thay vì `business_*`
5. **Hỗ trợ đầy đủ**: Messaging, Comments, Content Publishing, Insights

## 🔧 Environment Variables Configuration

Thêm các environment variables sau vào file `.env.local`:

```bash
# Instagram Business API Configuration
INSTAGRAM_APP_ID=your_instagram_app_id_here
INSTAGRAM_APP_SECRET=your_instagram_app_secret_here
INSTAGRAM_REDIRECT_URI=https://your-domain.com/api/instagram-integration/callback
INSTAGRAM_WEBHOOK_VERIFY_TOKEN=your_webhook_verify_token_here

# Social Media Integration Features
ENABLE_INSTAGRAM_INTEGRATION=true
ENABLE_FACEBOOK_INTEGRATION=true

# Required: Base URL for webhooks
NEXT_PUBLIC_PUBLIC_SITE_URL=https://your-domain.com
```

## 📋 Instagram App Setup Steps

### 1. Tạo Facebook App với Instagram Basic Display

1. Truy cập [Facebook Developers](https://developers.facebook.com/)
2. Tạo App mới với type "Business"
3. Thêm Instagram Basic Display Product
4. Cấu hình Instagram Basic Display:
   - **Instagram App ID**: Copy vào `INSTAGRAM_APP_ID`
   - **Instagram App Secret**: Copy vào `INSTAGRAM_APP_SECRET`
   - **Deauthorize Callback URL**: `https://your-domain.com/api/instagram-integration/deauthorize`
   - **Data Deletion Request URL**: `https://your-domain.com/api/instagram-integration/data-deletion`

### 2. OAuth Redirect URIs

Thêm URI sau vào **Valid OAuth Redirect URIs**:
```
https://your-domain.com/api/instagram-integration/callback
```

### 3. App Review & Permissions

Cần approval cho các permissions sau:
- `instagram_basic` - Truy cập thông tin cơ bản
- `instagram_content_publish` - Đăng nội dung
- `instagram_manage_comments` - Quản lý comment
- `instagram_manage_messages` - Quản lý tin nhắn

### 4. Webhook Configuration

1. Thêm Webhook URL: `https://your-domain.com/api/instagram-webhook`
2. Verify token: Sử dụng giá trị từ `INSTAGRAM_WEBHOOK_VERIFY_TOKEN`
3. Subscribe to fields:
   - `comments` - Nhận notification khi có comment mới
   - `messages` - Nhận notification khi có tin nhắn mới
   - `mentions` - Nhận notification khi được mention

## ✅ Test Configuration

Sau khi setup xong, test bằng cách:

1. Start development server: `npm run dev`
2. Truy cập: `http://localhost:3000/dashboard/mooly-chatbot/social-media-integration`
3. Click nút "Kết nối Instagram"
4. Hoàn tất flow OAuth
5. Kiểm tra database table `facebook_accounts` với `platform = 'instagram'`

## 🔍 Debugging

### Check Environment Variables
```javascript
console.log('Instagram Config:', {
  appId: CONFIG.instagram.appId,
  hasSecret: !!CONFIG.instagram.appSecret,
  redirectUri: CONFIG.instagram.redirectUri
});
```

### Check Webhook Delivery
- Truy cập Facebook Developer Console
- Vào Webhooks section
- Kiểm tra delivery logs

### Database Verification
```sql
SELECT * FROM facebook_accounts WHERE platform = 'instagram';
```

## 📱 Instagram Business Account Requirements

⚠️ **Quan trọng**: Chỉ hoạt động với Instagram Business Account, không phải Personal Account.

### Convert Personal → Business Account:

1. Mở Instagram app trên mobile
2. Vào Settings → Account
3. Chọn "Switch to Professional Account"
4. Chọn "Business"
5. Kết nối với Facebook Page (bắt buộc)

### Verification Steps:

1. Instagram account phải được link với Facebook Page
2. Facebook Page phải có Business verification
3. Account phải có ít nhất 1 post
4. Account không được private

## 🚀 Features Enabled

Sau khi setup thành công:

✅ **Auto-reply Instagram Comments**: AI tự động trả lời comment  
✅ **Instagram Direct Messages**: Nhận và trả lời tin nhắn  
✅ **Content Moderation**: Filter spam/inappropriate content  
✅ **Analytics Dashboard**: Theo dõi engagement metrics  
✅ **Multi-account Management**: Quản lý nhiều Instagram account  

## 🔧 Troubleshooting

### Common Issues:

1. **"validateInstagramOAuthCallback doesn't exist"**
   - ✅ Fixed: Function đã được thêm vào service

2. **"Instagram Business Account not found"**
   - Đảm bảo Instagram account được link với Facebook Page
   - Account phải là Business type, không phải Personal

3. **OAuth redirect_uri_mismatch**
   - Kiểm tra `INSTAGRAM_REDIRECT_URI` trong `.env.local`
   - URI phải match chính xác với setting trong Facebook App

4. **Webhook verification failed**
   - Kiểm tra `INSTAGRAM_WEBHOOK_VERIFY_TOKEN`
   - Đảm bảo webhook endpoint accessible từ internet

5. **API permissions denied**
   - Submit app cho Instagram Basic Display review
   - Đảm bảo tất cả required permissions được approved

### Debug Commands:

```bash
# Check config
npm run debug:instagram-config

# Test webhook
curl -X GET "https://your-domain.com/api/instagram-webhook?hub.mode=subscribe&hub.challenge=test&hub.verify_token=your_token"

# Check database
npm run debug:instagram-accounts
```

## 🔧 Technical Requirements

### New Scope Values (CRITICAL - Deadline: 27/1/2025)
```javascript
// OLD (DEPRECATED) → NEW (REQUIRED)
'business_basic' → 'instagram_business_basic'
'business_content_publish' → 'instagram_business_content_publish'  
'business_manage_messages' → 'instagram_business_manage_messages'
'business_manage_comments' → 'instagram_business_manage_comments'
```

### API Endpoints Structure
```javascript
// New Direct Instagram API
Base URL: https://graph.instagram.com/
Endpoints: 
- /{ig-user-id}/messages
- /{ig-user-id}/media  
- /{ig-user-id}/conversations
- /me (represents Instagram professional account)
```

### Authentication Flow
```javascript
// 1. Authorization URL
https://www.instagram.com/oauth/authorize
  ?client_id=${INSTAGRAM_APP_ID}
  &redirect_uri=${REDIRECT_URI}
  &response_type=code
  &scope=instagram_business_basic,instagram_business_manage_messages,instagram_business_manage_comments,instagram_business_content_publish

// 2. Exchange code for token
POST https://api.instagram.com/oauth/access_token
{
  client_id: INSTAGRAM_APP_ID,
  client_secret: INSTAGRAM_APP_SECRET,
  grant_type: 'authorization_code',
  redirect_uri: REDIRECT_URI,
  code: AUTHORIZATION_CODE
}

// 3. Get long-lived token (60 days)
GET https://graph.instagram.com/access_token
  ?grant_type=ig_exchange_token
  &client_secret=${INSTAGRAM_APP_SECRET}
  &access_token=${SHORT_LIVED_TOKEN}
```

## 🛠️ Implementation Plan

### 1. Environment Variables Update
```bash
# .env.local
INSTAGRAM_APP_ID=your_instagram_app_id
INSTAGRAM_APP_SECRET=your_instagram_app_secret
INSTAGRAM_REDIRECT_URI=https://yourdomain.com/api/instagram-integration/callback
```

### 2. Service Layer Architecture
```
src/actions/mooly-chatbot/instagram-integration/
├── instagram-auth-service.js      # OAuth flow & token management
├── instagram-messaging-service.js  # Direct messaging API
├── instagram-webhook-service.js    # Webhook handling
├── instagram-constants.js         # Constants & config
└── instagram-types.js             # TypeScript definitions
```

### 3. API Routes Structure
```
src/app/api/instagram-integration/
├── authorize/route.js             # Initiate OAuth flow
├── callback/route.js              # Handle OAuth callback
├── webhook/route.js               # Instagram webhooks
├── messaging/route.js             # Send messages
└── accounts/route.js              # Manage connected accounts
```

### 4. Database Schema Updates
```sql
-- Instagram accounts table (separate from Facebook)
CREATE TABLE instagram_business_accounts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id),
    
    -- Instagram API Data
    instagram_user_id VARCHAR NOT NULL UNIQUE,
    username VARCHAR NOT NULL,
    account_type VARCHAR CHECK (account_type IN ('BUSINESS', 'CREATOR')),
    
    -- API Credentials
    access_token TEXT NOT NULL,
    token_expires_at TIMESTAMP,
    refresh_token TEXT,
    
    -- Account Info
    profile_picture_url TEXT,
    followers_count INTEGER,
    is_active BOOLEAN DEFAULT true,
    
    -- Permissions granted
    granted_scopes TEXT[], -- Array of scope strings
    
    -- Metadata
    connected_at TIMESTAMP DEFAULT NOW(),
    last_sync_at TIMESTAMP,
    
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Instagram messaging configuration
CREATE TABLE instagram_messaging_configs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id),
    instagram_account_id UUID NOT NULL REFERENCES instagram_business_accounts(id),
    
    -- Auto-reply settings
    auto_reply_enabled BOOLEAN DEFAULT true,
    business_hours_only BOOLEAN DEFAULT false,
    response_delay_seconds INTEGER DEFAULT 5,
    
    -- AI Configuration
    ai_personality TEXT,
    business_context TEXT,
    auto_response_templates JSONB,
    
    -- Webhook settings
    webhook_subscriptions TEXT[], -- ['messages', 'comments', 'mentions']
    
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Instagram activity logs
CREATE TABLE instagram_activity_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id),
    instagram_account_id UUID NOT NULL REFERENCES instagram_business_accounts(id),
    
    -- Activity data
    activity_type VARCHAR NOT NULL, -- 'message_received', 'message_sent', 'comment_reply', etc.
    external_id VARCHAR, -- Instagram message ID, comment ID, etc.
    
    -- Message/Content data
    sender_instagram_id VARCHAR,
    message_content TEXT,
    media_urls TEXT[],
    
    -- AI Response data
    ai_generated BOOLEAN DEFAULT false,
    ai_confidence_score DECIMAL,
    response_time_ms INTEGER,
    
    -- Metadata
    raw_webhook_data JSONB,
    occurred_at TIMESTAMP DEFAULT NOW(),
    processed_at TIMESTAMP DEFAULT NOW()
);
```

## 🔄 Migration Strategy

### Phase 1: Infrastructure Setup
1. ✅ Update environment variables
2. ✅ Create new service files
3. ✅ Add API routes
4. ✅ Database migration

### Phase 2: Core Integration  
1. ✅ Instagram OAuth flow
2. ✅ Token management & refresh
3. ✅ Basic account connection
4. ✅ Webhook setup & validation

### Phase 3: Messaging Features
1. ✅ Receive webhook notifications
2. ✅ Send automated responses
3. ✅ Message history & threads
4. ✅ Media handling (images, videos)

### Phase 4: Advanced Features
1. ✅ Comments management
2. ✅ Content publishing
3. ✅ Analytics & insights
4. ✅ Advanced AI responses

## 📋 Key Benefits vs Facebook Integration

| Feature | Facebook Integration | Instagram Direct API |
|---------|---------------------|---------------------|
| Setup Complexity | HIGH (requires FB Page) | LOW (direct connection) |
| Authentication | Multi-step via Facebook | Simple Instagram OAuth |
| Permissions | Complex FB permissions | Clear Instagram scopes |
| API Host | graph.facebook.com | graph.instagram.com |
| Rate Limits | Shared with FB usage | Instagram-specific |
| User Experience | Confusing multi-platform | Clean Instagram-only |
| Maintenance | Complex dependencies | Simplified architecture |

## 🔐 Security & Compliance

### Token Security
- ✅ Server-side token storage only
- ✅ Automatic token refresh (60-day expiry)
- ✅ Encrypted token storage in database
- ✅ Proper scope validation

### Webhook Security
- ✅ Signature verification with app secret
- ✅ HTTPS-only webhook URLs
- ✅ Request rate limiting
- ✅ Payload validation & sanitization

### Privacy Compliance
- ✅ Data minimization principles
- ✅ User consent flows
- ✅ Data retention policies
- ✅ Right to disconnect/delete

## 🚀 Next Steps

1. **Immediate Action Required**: Update scope values before 27/1/2025
2. **Create Instagram App**: Set up new Instagram app in Meta Developer Console
3. **Implement OAuth Flow**: Build Instagram-specific authentication
4. **Test Integration**: Verify messaging & webhook functionality
5. **User Migration**: Provide seamless upgrade path for existing users

## 📚 Resources

- [Instagram API with Instagram Login Documentation](https://developers.facebook.com/docs/instagram-platform/instagram-api-with-instagram-login/)
- [Business Login for Instagram](https://developers.facebook.com/docs/instagram-platform/instagram-api-with-instagram-login/business-login/)
- [Instagram Messaging API](https://developers.facebook.com/docs/instagram-platform/instagram-api-with-instagram-login/messaging-api/)
- [Instagram Webhooks](https://developers.facebook.com/docs/instagram-platform/webhooks/)

---

**Status**: 🔄 Ready for Implementation  
**Priority**: HIGH (Scope deprecation deadline: 27/1/2025)  
**Estimated Development Time**: 2-3 weeks
