'use client';

import { Box, Skeleton, Stack, Typography } from '@mui/material';

// ----------------------------------------------------------------------

/**
 * Business Configuration Loading Component
 * Hiển thị loading state khi đang tải business configuration
 */
export function BusinessConfigLoading({ message = 'Đang tải cấu hình kinh doanh...' }) {
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: 200,
        p: 3,
      }}
    >
      <Stack spacing={2} alignItems="center" sx={{ width: '100%', maxWidth: 400 }}>
        {/* Loading animation */}
        <Box sx={{ display: 'flex', gap: 1 }}>
          {[0, 1, 2].map((index) => (
            <Box
              key={index}
              sx={{
                width: 8,
                height: 8,
                borderRadius: '50%',
                bgcolor: 'primary.main',
                animation: 'pulse 1.5s ease-in-out infinite',
                animationDelay: `${index * 0.2}s`,
                '@keyframes pulse': {
                  '0%, 80%, 100%': {
                    transform: 'scale(0)',
                    opacity: 0.5,
                  },
                  '40%': {
                    transform: 'scale(1)',
                    opacity: 1,
                  },
                },
              }}
            />
          ))}
        </Box>

        {/* Loading message */}
        <Typography variant="body2" color="text.secondary" textAlign="center">
          {message}
        </Typography>

        {/* Skeleton placeholders */}
        <Stack spacing={1} sx={{ width: '100%' }}>
          <Skeleton variant="rectangular" height={40} />
          <Skeleton variant="rectangular" height={60} />
          <Skeleton variant="rectangular" height={30} />
        </Stack>
      </Stack>
    </Box>
  );
}

/**
 * Minimal Business Config Loading
 * Phiên bản nhỏ gọn cho navigation và các component nhỏ
 */
export function MinimalBusinessConfigLoading() {
  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        gap: 1,
        p: 1,
      }}
    >
      <Box
        sx={{
          width: 16,
          height: 16,
          border: 2,
          borderColor: 'primary.main',
          borderTopColor: 'transparent',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite',
          '@keyframes spin': {
            '0%': { transform: 'rotate(0deg)' },
            '100%': { transform: 'rotate(360deg)' },
          },
        }}
      />
      <Typography variant="caption" color="text.secondary">
        Đang tải...
      </Typography>
    </Box>
  );
}
