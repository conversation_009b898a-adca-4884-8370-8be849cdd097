'use client';

import { fetchData, createData, updateData, deleteData } from './supabase-utils';

/**
 * Advanced Automation Rules Service
 * Quản lý các quy tắc tự động hóa cho business processes
 */

// =====================================================
// 1. AUTOMATION RULE TYPES
// =====================================================

export const AUTOMATION_TYPES = {
  ORDER_STATUS: 'order_status',
  INVENTORY_ALERT: 'inventory_alert',
  CUSTOMER_NOTIFICATION: 'customer_notification',
  PRICING_RULE: 'pricing_rule',
  SHIPPING_RULE: 'shipping_rule',
  MARKETING_AUTOMATION: 'marketing_automation',
  BUSINESS_WORKFLOW: 'business_workflow',
  CUSTOMER_FOLLOW_UP: 'customer_follow_up'
};

export const TRIGGER_TYPES = {
  ORDER_CREATED: 'order_created',
  ORDER_PAID: 'order_paid',
  ORDER_SHIPPED: 'order_shipped',
  INVENTORY_LOW: 'inventory_low',
  INVENTORY_OUT: 'inventory_out',
  CUSTOMER_REGISTERED: 'customer_registered',
  PRODUCT_VIEWED: 'product_viewed',
  CART_ABANDONED: 'cart_abandoned',
  SCHEDULE_BASED: 'schedule_based',
  CUSTOMER_FOLLOW_UP: 'customer_follow_up',
  AI_ANALYSIS: 'ai_analysis'
};

export const FOLLOW_UP_TYPES = {
  AI_OFFER_SELECTION: 'ai_offer_selection',
  SCHEDULED_MESSAGE: 'scheduled_message'
};

export const ACTION_TYPES = {
  SEND_EMAIL: 'send_email',
  SEND_SMS: 'send_sms',
  UPDATE_STATUS: 'update_status',
  CREATE_TASK: 'create_task',
  APPLY_DISCOUNT: 'apply_discount',
  REORDER_STOCK: 'reorder_stock',
  NOTIFY_ADMIN: 'notify_admin',
  WEBHOOK_CALL: 'webhook_call'
};

// =====================================================
// 2. AUTOMATION RULE MANAGEMENT
// =====================================================

/**
 * Tạo automation rule mới
 */
export async function createAutomationRule(ruleData) {
  try {
    const rule = {
      name: ruleData.name,
      description: ruleData.description,
      automation_type: ruleData.automationType,
      trigger_type: ruleData.triggerType,
      trigger_conditions: ruleData.triggerConditions || {},
      actions: ruleData.actions || [],
      is_active: ruleData.isActive !== false,
      business_type_filter: ruleData.businessTypeFilter || null,
      priority: ruleData.priority || 1,
      // Customer follow-up specific fields
      follow_up_type: ruleData.followUpType || null,
      target_offers: ruleData.targetOffers || [],
      customer_intent: ruleData.customerIntent || null,
      follow_up_message: ruleData.followUpMessage || null,
      schedule_config: ruleData.scheduleConfig || {},
      customer_filters: ruleData.customerFilters || {},
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const result = await createData('automation_rules', rule);

    if (result.success) {
      // Log rule creation
      await logAutomationActivity({
        rule_id: result.data.id,
        action: 'rule_created',
        details: { rule_name: rule.name }
      });
    }

    return result;
  } catch (error) {
    console.error('Error creating automation rule:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Lấy danh sách automation rules
 */
export async function getAutomationRules(filters = {}) {
  try {
    let query = 'automation_rules';
    const conditions = [];

    if (filters.automationType) {
      conditions.push(`automation_type.eq.${filters.automationType}`);
    }

    if (filters.isActive !== undefined) {
      conditions.push(`is_active.eq.${filters.isActive}`);
    }

    if (filters.businessType) {
      conditions.push(`business_type_filter.eq.${filters.businessType}`);
    }

    if (conditions.length > 0) {
      query += `?${conditions.join('&')}`;
    }

    const result = await fetchData(query);
    return result;
  } catch (error) {
    console.error('Error fetching automation rules:', error);
    return {
      success: false,
      error: error.message,
      data: []
    };
  }
}

/**
 * Cập nhật automation rule
 */
export async function updateAutomationRule(ruleId, updates) {
  try {
    // Convert camelCase to snake_case for database fields
    const updateDataPayload = {
      ...updates,
      updated_at: new Date().toISOString()
    };

    // Handle specific field mappings
    if (updates.isActive !== undefined) {
      updateDataPayload.is_active = updates.isActive;
      delete updateDataPayload.isActive;
    }
    if (updates.customerIntent !== undefined) {
      updateDataPayload.customer_intent = updates.customerIntent;
      delete updateDataPayload.customerIntent;
    }
    if (updates.targetOffers !== undefined) {
      updateDataPayload.target_offers = updates.targetOffers;
      delete updateDataPayload.targetOffers;
    }
    if (updates.followUpMessage !== undefined) {
      updateDataPayload.follow_up_message = updates.followUpMessage;
      delete updateDataPayload.followUpMessage;
    }
    if (updates.scheduleConfig !== undefined) {
      updateDataPayload.schedule_config = updates.scheduleConfig;
      delete updateDataPayload.scheduleConfig;
    }
    if (updates.customerFilters !== undefined) {
      updateDataPayload.customer_filters = updates.customerFilters;
      delete updateDataPayload.customerFilters;
    }

    const result = await updateData('automation_rules', updateDataPayload, { id: ruleId });

    if (result.success) {
      await logAutomationActivity({
        rule_id: ruleId,
        action: 'rule_updated',
        details: updates
      });
    }

    return result;
  } catch (error) {
    console.error('Error updating automation rule:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Xóa automation rule
 */
export async function deleteAutomationRule(ruleId) {
  try {
    const result = await deleteData('automation_rules', { id: ruleId });

    if (result.success) {
      await logAutomationActivity({
        rule_id: ruleId,
        action: 'rule_deleted',
        details: {}
      });
    }

    return result;
  } catch (error) {
    console.error('Error deleting automation rule:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// =====================================================
// 3. RULE EXECUTION ENGINE
// =====================================================

/**
 * Thực thi automation rules dựa trên trigger
 */
export async function executeAutomationRules(triggerType, triggerData) {
  try {
    // Lấy các rules phù hợp
    const rulesResult = await getAutomationRules({
      isActive: true
    });

    if (!rulesResult.success) {
      throw new Error('Failed to fetch automation rules');
    }

    const applicableRules = rulesResult.data.filter(rule =>
      rule.trigger_type === triggerType &&
      checkTriggerConditions(rule.trigger_conditions, triggerData)
    );

    // Sắp xếp theo priority
    applicableRules.sort((a, b) => (b.priority || 1) - (a.priority || 1));

    const executionResults = [];

    // Thực thi từng rule
    for (const rule of applicableRules) {
      try {
        const result = await executeRule(rule, triggerData);
        executionResults.push({
          ruleId: rule.id,
          ruleName: rule.name,
          success: result.success,
          result: result.data,
          error: result.error
        });

        // Log execution
        await logAutomationActivity({
          rule_id: rule.id,
          action: 'rule_executed',
          details: {
            trigger_type: triggerType,
            trigger_data: triggerData,
            execution_result: result
          }
        });
      } catch (error) {
        executionResults.push({
          ruleId: rule.id,
          ruleName: rule.name,
          success: false,
          error: error.message
        });
      }
    }

    return {
      success: true,
      executedRules: executionResults.length,
      results: executionResults
    };
  } catch (error) {
    console.error('Error executing automation rules:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Kiểm tra trigger conditions
 */
function checkTriggerConditions(conditions, triggerData) {
  if (!conditions || Object.keys(conditions).length === 0) {
    return true; // No conditions = always match
  }

  for (const [key, condition] of Object.entries(conditions)) {
    const value = getNestedValue(triggerData, key);

    if (!evaluateCondition(value, condition)) {
      return false;
    }
  }

  return true;
}

/**
 * Đánh giá một condition
 */
function evaluateCondition(value, condition) {
  const { operator, operand } = condition;

  switch (operator) {
    case 'equals':
      return value === operand;
    case 'not_equals':
      return value !== operand;
    case 'greater_than':
      return parseFloat(value) > parseFloat(operand);
    case 'less_than':
      return parseFloat(value) < parseFloat(operand);
    case 'contains':
      return String(value).includes(String(operand));
    case 'in':
      return Array.isArray(operand) && operand.includes(value);
    case 'not_in':
      return Array.isArray(operand) && !operand.includes(value);
    default:
      return true;
  }
}

/**
 * Thực thi một rule cụ thể
 */
async function executeRule(rule, triggerData) {
  try {
    const actionResults = [];

    for (const action of rule.actions) {
      const result = await executeAction(action, triggerData, rule);
      actionResults.push(result);
    }

    return {
      success: true,
      data: actionResults
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Thực thi một action cụ thể
 */
async function executeAction(action, triggerData, rule) {
  const { type, config } = action;

  switch (type) {
    case ACTION_TYPES.SEND_EMAIL:
      return await sendEmailAction(config, triggerData);

    case ACTION_TYPES.SEND_SMS:
      return await sendSMSAction(config, triggerData);

    case ACTION_TYPES.UPDATE_STATUS:
      return await updateStatusAction(config, triggerData);

    case ACTION_TYPES.CREATE_TASK:
      return await createTaskAction(config, triggerData);

    case ACTION_TYPES.APPLY_DISCOUNT:
      return await applyDiscountAction(config, triggerData);

    case ACTION_TYPES.REORDER_STOCK:
      return await reorderStockAction(config, triggerData);

    case ACTION_TYPES.NOTIFY_ADMIN:
      return await notifyAdminAction(config, triggerData);

    case ACTION_TYPES.WEBHOOK_CALL:
      return await webhookCallAction(config, triggerData);

    default:
      throw new Error(`Unknown action type: ${type}`);
  }
}

// =====================================================
// 4. ACTION IMPLEMENTATIONS
// =====================================================

async function sendEmailAction(config, triggerData) {
  // Implementation for sending email
  // Sẽ tích hợp với email service
  return { success: true, message: 'Email sent' };
}

async function sendSMSAction(config, triggerData) {
  // Implementation for sending SMS
  return { success: true, message: 'SMS sent' };
}

async function updateStatusAction(config, triggerData) {
  // Implementation for updating status
  const { table, id_field, status_field, new_status } = config;
  const id = getNestedValue(triggerData, id_field);

  if (!id) {
    throw new Error('ID not found in trigger data');
  }

  const result = await updateData(table, {
    [status_field]: new_status,
    updated_at: new Date().toISOString()
  }, { id });

  return result;
}

async function createTaskAction(config, triggerData) {
  // Implementation for creating task
  return { success: true, message: 'Task created' };
}

async function applyDiscountAction(config, triggerData) {
  // Implementation for applying discount
  return { success: true, message: 'Discount applied' };
}

async function reorderStockAction(config, triggerData) {
  // Implementation for reordering stock
  return { success: true, message: 'Stock reorder initiated' };
}

async function notifyAdminAction(config, triggerData) {
  // Implementation for notifying admin
  return { success: true, message: 'Admin notified' };
}

async function webhookCallAction(config, triggerData) {
  // Implementation for webhook call
  const { url, method = 'POST', headers = {}, payload } = config;

  try {
    const response = await fetch(url, {
      method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      },
      body: JSON.stringify(payload || triggerData)
    });

    return {
      success: response.ok,
      status: response.status,
      message: response.ok ? 'Webhook called successfully' : 'Webhook call failed'
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

// =====================================================
// 5. UTILITY FUNCTIONS
// =====================================================

function getNestedValue(obj, path) {
  return path.split('.').reduce((current, key) => current?.[key], obj);
}

async function logAutomationActivity(activity) {
  try {
    await createData('automation_logs', {
      ...activity,
      created_at: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error logging automation activity:', error);
  }
}

// =====================================================
// 6. CUSTOMER FOLLOW-UP SPECIFIC FUNCTIONS
// =====================================================

/**
 * Lấy danh sách offers/promotions để chọn
 */
export async function getAvailableOffers() {
  try {
    const result = await fetchData('promotions?is_active.eq.true&order=created_at.desc');
    return result;
  } catch (error) {
    console.error('Error fetching offers:', error);
    return {
      success: false,
      error: error.message,
      data: []
    };
  }
}

/**
 * Tạo customer follow-up rule với AI offer selection
 */
export async function createAIOfferRule(ruleData) {
  const rule = {
    ...ruleData,
    automationType: AUTOMATION_TYPES.CUSTOMER_FOLLOW_UP,
    triggerType: TRIGGER_TYPES.AI_ANALYSIS,
    followUpType: FOLLOW_UP_TYPES.AI_OFFER_SELECTION,
    actions: [
      {
        type: 'ai_offer_selection',
        config: {
          offers: ruleData.targetOffers,
          intent: ruleData.customerIntent,
          filters: ruleData.customerFilters
        }
      }
    ]
  };

  return await createAutomationRule(rule);
}

/**
 * Tạo scheduled follow-up rule
 */
export async function createScheduledFollowUpRule(ruleData) {
  const rule = {
    ...ruleData,
    automationType: AUTOMATION_TYPES.CUSTOMER_FOLLOW_UP,
    triggerType: TRIGGER_TYPES.SCHEDULE_BASED,
    followUpType: FOLLOW_UP_TYPES.SCHEDULED_MESSAGE,
    actions: [
      {
        type: 'send_scheduled_message',
        config: {
          message: ruleData.followUpMessage,
          schedule: ruleData.scheduleConfig,
          filters: ruleData.customerFilters
        }
      }
    ]
  };

  return await createAutomationRule(rule);
}

// =====================================================
// 7. BUSINESS TYPE SPECIFIC RULES
// =====================================================

/**
 * Lấy default automation rules cho business type
 */
export function getDefaultAutomationRules(businessType) {
  const defaultRules = {
    retail: [
      {
        name: 'Low Stock Alert',
        automationType: AUTOMATION_TYPES.INVENTORY_ALERT,
        triggerType: TRIGGER_TYPES.INVENTORY_LOW,
        actions: [
          {
            type: ACTION_TYPES.NOTIFY_ADMIN,
            config: { message: 'Low stock alert for {{product_name}}' }
          }
        ]
      },
      {
        name: 'Order Confirmation Email',
        automationType: AUTOMATION_TYPES.CUSTOMER_NOTIFICATION,
        triggerType: TRIGGER_TYPES.ORDER_CREATED,
        actions: [
          {
            type: ACTION_TYPES.SEND_EMAIL,
            config: { template: 'order_confirmation' }
          }
        ]
      }
    ],
    digital: [
      {
        name: 'Digital Product Delivery',
        automationType: AUTOMATION_TYPES.ORDER_STATUS,
        triggerType: TRIGGER_TYPES.ORDER_PAID,
        actions: [
          {
            type: ACTION_TYPES.SEND_EMAIL,
            config: { template: 'digital_delivery' }
          }
        ]
      }
    ],
    services: [
      {
        name: 'Appointment Reminder',
        automationType: AUTOMATION_TYPES.CUSTOMER_NOTIFICATION,
        triggerType: TRIGGER_TYPES.SCHEDULE_BASED,
        actions: [
          {
            type: ACTION_TYPES.SEND_SMS,
            config: { template: 'appointment_reminder' }
          }
        ]
      }
    ]
  };

  return defaultRules[businessType] || [];
}
