'use client';

import { useState } from 'react';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import Typography from '@mui/material/Typography';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import IconButton from '@mui/material/IconButton';
import Divider from '@mui/material/Divider';
import CircularProgress from '@mui/material/CircularProgress';
import TextField from '@mui/material/TextField';

import { addFaqsToChatbot } from 'src/actions/mooly-chatbot/chatbot-faq-service';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';

import { useAuthContext } from 'src/auth/hooks';

// ----------------------------------------------------------------------

export default function ChatbotFaqAddDialog({ open, onClose, chatbot, onSuccess }) {
  const { user } = useAuthContext();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [faqs, setFaqs] = useState([
    {
      topic: '',
      content: '',
    },
  ]);

  // Reset form khi dialog đóng
  const handleClose = () => {
    setFaqs([
      {
        topic: '',
        content: '',
      },
    ]);
    onClose();
  };

  // Thêm FAQ mới
  const handleAddFaq = () => {
    setFaqs([...faqs, { topic: '', content: '' }]);
  };

  // Xóa FAQ
  const handleRemoveFaq = (index) => {
    if (faqs.length > 1) {
      setFaqs(faqs.filter((_, i) => i !== index));
    }
  };

  // Cập nhật FAQ
  const handleUpdateFaq = (index, field, value) => {
    const newFaqs = [...faqs];
    newFaqs[index][field] = value;
    setFaqs(newFaqs);
  };

  // Xử lý submit form
  const handleSubmit = async () => {
    try {
      setIsSubmitting(true);

      if (!chatbot?.id) {
        toast.error('Không tìm thấy thông tin chatbot');
        return;
      }

      if (!user?.app_metadata?.tenant_id) {
        toast.error('Không tìm thấy thông tin tenant');
        return;
      }

             // Validate và clean dữ liệu
       const cleanedFaqs = faqs
         .filter((faq) => faq.topic.trim() && faq.content.trim())
         .map((faq) => ({
           topic: faq.topic.trim(),
           content: faq.content.trim(),
         }));

       if (cleanedFaqs.length === 0) {
         toast.error('Vui lòng nhập ít nhất 1 FAQ hợp lệ');
         return;
       }

      // Thêm FAQs vào chatbot
      const result = await addFaqsToChatbot(
        chatbot.id,
        cleanedFaqs,
        user.app_metadata.tenant_id
      );

      if (result.success) {
        toast.success(`Đã thêm ${result.count} FAQ(s) vào chatbot thành công`);
        onSuccess?.();
        handleClose();
      } else {
        toast.error('Có lỗi xảy ra khi thêm FAQs');
      }
    } catch (error) {
      console.error('Error adding FAQs:', error);
      toast.error(error.message || 'Có lỗi xảy ra khi thêm FAQs');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Import từ template
  const handleImportTemplate = () => {
    const templateFaqs = [
      {
        topic: 'Thông tin giao hàng trong nước',
        content: 'Chúng tôi cung cấp dịch vụ giao hàng toàn quốc với thời gian từ 3-5 ngày làm việc tùy thuộc vào khu vực. Các khu vực nội thành Hà Nội và TP.HCM thường nhận hàng sau 1-2 ngày. Các tỉnh thành khác sẽ nhận trong 3-5 ngày.',
      },
      {
        topic: 'Chính sách đổi trả',
        content: 'Chúng tôi hỗ trợ đổi trả hàng trong vòng 7 ngày kể từ ngày nhận hàng. Sản phẩm cần còn nguyên seal, chưa sử dụng và có đầy đủ hóa đơn mua hàng.',
      },
      {
        topic: 'Phương thức thanh toán',
        content: 'Chúng tôi hỗ trợ các phương thức thanh toán: Tiền mặt khi nhận hàng (COD), chuyển khoản ngân hàng, ví điện tử (MoMo, ZaloPay), thẻ tín dụng/ghi nợ.',
      },
    ];

    setFaqs(templateFaqs);
    toast.info('Đã import template FAQs mẫu');
  };

  return (
    <Dialog 
      open={open} 
      onClose={handleClose} 
      maxWidth="md" 
      fullWidth
      PaperProps={{
        sx: { minHeight: 500 },
      }}
    >
      <DialogTitle>
        <Stack direction="row" alignItems="center" justifyContent="space-between">
          <Typography variant="h6">Thêm Chủ Đề vào Chatbot</Typography>
          <IconButton onClick={handleClose}>
            <Iconify icon="mingcute:close-line" />
          </IconButton>
        </Stack>
      </DialogTitle>

      <Divider />

      <DialogContent>
        <Stack spacing={3}>
          {/* Header actions */}
          <Stack direction="row" alignItems="center" justifyContent="space-between">
            <Typography variant="subtitle2" sx={{ color: 'text.secondary' }}>
              Thêm câu hỏi thường gặp để chatbot có thể trả lời khách hàng tốt hơn
            </Typography>
            
            <Button
              variant="outlined"
              size="small"
              startIcon={<Iconify icon="solar:download-bold" />}
              onClick={handleImportTemplate}
            >
              Import mẫu
            </Button>
          </Stack>

          {/* FAQs list */}
          <Stack spacing={2}>
            {faqs.map((faq, index) => (
              <Card key={index} sx={{ p: 3, position: 'relative' }}>
                <Stack spacing={2}>
                  <Stack direction="row" alignItems="center" justifyContent="space-between">
                    <Typography variant="subtitle2">
                      Chủ Đề #{index + 1}
                    </Typography>
                    
                    {faqs.length > 1 && (
                      <IconButton
                        size="small"
                        color="error"
                        onClick={() => handleRemoveFaq(index)}
                      >
                        <Iconify icon="mingcute:close-line" />
                      </IconButton>
                    )}
                  </Stack>

                  <TextField
                    label="Chủ đề / Câu hỏi"
                    placeholder="VD: Chính sách giao hàng, Phương thức thanh toán..."
                    value={faq.topic}
                    onChange={(e) => handleUpdateFaq(index, 'topic', e.target.value)}
                    fullWidth
                    required
                  />

                  <TextField
                    label="Nội dung trả lời"
                    placeholder="Nhập nội dung chi tiết cho câu hỏi này..."
                    value={faq.content}
                    onChange={(e) => handleUpdateFaq(index, 'content', e.target.value)}
                    multiline
                    rows={4}
                    fullWidth
                    required
                  />
                </Stack>
              </Card>
            ))}
          </Stack>

          {/* Add FAQ button */}
          <Box sx={{ textAlign: 'center' }}>
            <Button
              variant="outlined"
              startIcon={<Iconify icon="mingcute:add-line" />}
              onClick={handleAddFaq}
              sx={{ minWidth: 200 }}
            >
              Thêm Chủ Đề
            </Button>
          </Box>
        </Stack>
      </DialogContent>

      <Divider />

      <DialogActions sx={{ p: 2.5 }}>
        <Button variant="outlined" onClick={handleClose} disabled={isSubmitting}>
          Hủy
        </Button>
        
        <Button
          variant="contained"
          disabled={isSubmitting}
          onClick={handleSubmit}
          startIcon={isSubmitting ? <CircularProgress size={20} /> : <Iconify icon="mingcute:check-line" />}
        >
          {isSubmitting ? 'Đang thêm...' : `Thêm ${faqs.length} Chủ Đề(s)`}
        </Button>
      </DialogActions>
    </Dialog>
  );
} 