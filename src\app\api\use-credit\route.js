import { NextResponse } from 'next/server';
import { headers } from 'next/headers';

import { withTenantAuth } from 'src/utils/server-auth';
import { createClient } from 'src/utils/supabase/server';
import { checkRateLimit } from 'src/utils/rate-limiter';

/**
 * Secure API endpoint để sử dụng credit với database-level security
 * @param {Request} request - Request object
 * @returns {Promise<Response>} - Response object
 */
export const POST = withTenantAuth(async (request, { tenantId, userId }) => {
  try {
    // Rate limiting check - 5 requests per minute for credit usage
    const rateLimit = checkRateLimit(request, userId, 'credit_usage', 5, 60000);

    if (!rateLimit.allowed) {
      return NextResponse.json(
        {
          success: false,
          error: 'Too many credit usage requests. Please try again later.',
          rateLimit: {
            limit: rateLimit.limit,
            remaining: rateLimit.remaining,
            resetTime: rateLimit.resetTime,
            retryAfter: rateLimit.retryAfter,
          }
        },
        {
          status: 429,
          headers: {
            'X-RateLimit-Limit': rateLimit.limit.toString(),
            'X-RateLimit-Remaining': rateLimit.remaining.toString(),
            'X-RateLimit-Reset': rateLimit.resetTime,
            'Retry-After': rateLimit.retryAfter.toString(),
          }
        }
      );
    }

    const headersList = await headers();
    const userAgent = headersList.get('user-agent') || '';
    const forwardedFor = headersList.get('x-forwarded-for') || '';

    // Lấy dữ liệu từ request body
    const { amount, description, referenceId, referenceType } = await request.json();

    // Validation đầu vào nghiêm ngặt
    if (!amount || typeof amount !== 'number' || amount <= 0) {
      return NextResponse.json(
        { success: false, error: 'Amount must be a positive number' },
        { status: 400 }
      );
    }

    if (amount > 100) { // Giới hạn tối đa 100 credit per request
      return NextResponse.json(
        { success: false, error: 'Amount exceeds maximum limit (100 credits)' },
        { status: 400 }
      );
    }

    if (!description || typeof description !== 'string' || description.length > 500) {
      return NextResponse.json(
        { success: false, error: 'Description is required and must be less than 500 characters' },
        { status: 400 }
      );
    }

    // Kết nối với Supabase
    const supabase = await createClient();

    // Set request headers for audit logging
    await supabase.rpc('set_config', {
      setting_name: 'request.headers',
      setting_value: JSON.stringify({
        'user-agent': userAgent,
        'x-forwarded-for': forwardedFor
      }),
      is_local: true
    });

    // Sử dụng admin function với service role
    const { data: result, error } = await supabase.rpc('admin_deduct_tenant_credit', {
      p_tenant_id: tenantId,
      p_amount: amount,
      p_description: description,
      p_reference_type: referenceType || 'chatbot_usage',
      p_reference_id: referenceId,
      p_created_by: userId
    });

    if (error) {
      console.error('Database function error:', error);
      return NextResponse.json(
        { success: false, error: 'Database operation failed' },
        { status: 500 }
      );
    }

    // Check function result
    if (!result || !result.success) {
      return NextResponse.json(
        {
          success: false,
          error: result?.error || 'Credit usage failed',
          ...(result?.current_balance !== undefined && { currentBalance: result.current_balance }),
          ...(result?.required_amount !== undefined && { requiredAmount: result.required_amount })
        },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        transactionId: result.transaction_id,
        previousBalance: result.previous_balance,
        newBalance: result.new_balance,
        amountUsed: result.amount_used,
      },
    });

  } catch (error) {
    console.error('Error using credit:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
});
