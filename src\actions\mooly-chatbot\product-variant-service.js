'use client';

import { PRODUCT_VARIANTS_TABLE } from './product-constants';
import { fetchData, createData, deleteData, updateData } from './supabase-utils';

/**
 * <PERSON><PERSON><PERSON> danh sách biến thể của sản phẩm
 * @param {string} productId - ID sản phẩm
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getProductVariants(productId) {
  if (!productId) {
    return { success: false, error: new Error('Thiếu ID sản phẩm'), data: [] };
  }

  return fetchData(PRODUCT_VARIANTS_TABLE, {
    filters: { product_id: productId },
  });
}

/**
 * L<PERSON>y thông tin chi tiết biến thể
 * @param {string} variantId - ID biến thể
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getVariantById(variantId) {
  if (!variantId) {
    return { success: false, error: new Error('Thiếu ID biến thể'), data: null };
  }

  return fetchData(PRODUCT_VARIANTS_TABLE, {
    filters: { id: variantId },
    single: true,
  });
}

/**
 * Tạo biến thể sản phẩm - Đã tối ưu theo yêu cầu mới
 * @param {Object} variantData - Dữ liệu biến thể
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function createProductVariant(variantData) {
  const attributeKeys = Object.keys(variantData.attributes || {});
  attributeKeys.length ? attributeKeys.map((key) => delete variantData[key]) : null;
  delete variantData.id;

  // Đảm bảo có stock_quantity mặc định nếu không được cung cấp
  if (variantData.stock_quantity === undefined) {
    variantData.stock_quantity = 0;
  }

  // Gọi thẳng vào DB để tạo variant mà không cần xử lý phức tạp
  return createData(PRODUCT_VARIANTS_TABLE, variantData);
}

/**
 * Cập nhật biến thể sản phẩm - Đã tối ưu theo yêu cầu mới
 * @param {string} variantId - ID biến thể
 * @param {Object} variantData - Dữ liệu cập nhật
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function updateProductVariant(variantId, variantData) {
  if (!variantId) {
    return { success: false, error: new Error('Thiếu ID biến thể'), data: null };
  }

  try {
    // Cập nhật thông tin biến thể
    const result = await updateData(PRODUCT_VARIANTS_TABLE, variantData, { id: variantId });
    return result;
  } catch (error) {
    return { success: false, error, data: null };
  }
}

/**
 * Xóa biến thể sản phẩm
 * @param {string} variantId - ID biến thể
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function deleteProductVariant(variantId) {
  if (!variantId) {
    return { success: false, error: new Error('Thiếu ID biến thể') };
  }

  try {
    console.log(`Deleting variant ${variantId}`);

    // Xóa biến thể
    return deleteData(PRODUCT_VARIANTS_TABLE, { id: variantId });
  } catch (error) {
    console.error(`Error deleting variant ${variantId}:`, error);
    return { success: false, error };
  }
}

/**
 * Xóa tất cả biến thể của sản phẩm
 * @param {string} productId - ID sản phẩm
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function deleteAllProductVariants(productId) {
  if (!productId) {
    return { success: false, error: new Error('Thiếu ID sản phẩm') };
  }

  try {
    console.log(`Deleting all variants for product ${productId}`);

    // Xóa tất cả biến thể của sản phẩm
    return deleteData(PRODUCT_VARIANTS_TABLE, { product_id: productId });
  } catch (error) {
    console.error(`Error deleting all variants for product ${productId}:`, error);
    return { success: false, error };
  }
}
