import { NextResponse } from 'next/server';

import { withTenantAuth } from 'src/utils/server-auth';
import { createClient } from 'src/utils/supabase/server';

/**
 * L<PERSON>y thông tin tài khoản <PERSON>oly từ database
 * @param {string} accountId - ID tài khoản <PERSON>
 * @param {string} tenantId - ID của tenant
 * @returns {Promise<Object>} - Thông tin tài khoản
 */
async function getMoolyAccount(accountId, tenantId) {
  try {
    const supabase = await createClient();
    const { data, error } = await supabase
      .from('mooly_accounts')
      .select('*')
      .eq('account_id', accountId)
      .eq('tenant_id', tenantId)
      .single();

    if (error) {
      console.error('Error fetching Mooly account:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Exception when fetching Mooly account:', error);
    return null;
  }
}

/**
 * Xóa label từ API Mooly
 * @param {string} token - Token xác thực
 * @param {string} accountId - ID tài khoản
 * @param {string} labelId - ID của label cần xóa
 * @param {string} tenantId - ID của tenant
 * @param {string} userId - ID của người dùng thực hiện xóa
 * @returns {Promise<boolean>} - Kết quả xóa label
 */
async function deleteLabel(token, accountId, labelId, tenantId, userId) {
  const host = process.env.MOOLY_API_HOST || 'https://app.mooly.vn';
  const apiVersion = process.env.MOOLY_API_VERSION || 'api/v1';

  try {
    // Kiểm tra xem label có phải là system label không
    const account = await getMoolyAccount(accountId, tenantId);
    const labelsConfig = account?.labels_config || {};

    // Tìm label trong cấu hình
    const labelConfig = Object.values(labelsConfig).find(l => l.id === labelId);

    // Không cho phép xóa system label
    if (labelConfig && labelConfig.system_label) {
      return {
        success: false,
        error: 'Cannot delete system label',
        isSystemLabel: true
      };
    }

    // Gọi API để xóa label
    const response = await fetch(`${host}/${apiVersion}/accounts/${accountId}/labels/${labelId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'api_access_token': token
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to delete label: ${response.statusText}`);
    }

    // Cập nhật cấu hình label trong database
    if (labelConfig) {
      // Xóa label khỏi cấu hình
      const updatedLabelsConfig = { ...labelsConfig };
      Object.keys(updatedLabelsConfig).forEach(key => {
        if (updatedLabelsConfig[key].id === labelId) {
          delete updatedLabelsConfig[key];
        }
      });

      // Lưu cấu hình mới vào database
      const supabase = await createClient();
      await supabase
        .from('mooly_accounts')
        .update({
          labels_config: updatedLabelsConfig,
          updated_at: new Date().toISOString(),
          updated_by: userId
        })
        .eq('account_id', accountId)
        .eq('tenant_id', tenantId);
    }

    return {
      success: true,
      error: null
    };
  } catch (error) {
    console.error('Error deleting label:', error);
    return {
      success: false,
      error: error.message || 'Failed to delete label'
    };
  }
}

/**
 * API endpoint để xóa label của tài khoản Mooly
 * @param {Request} request - Yêu cầu HTTP
 * @returns {Promise<NextResponse>} - Phản hồi HTTP
 */
export const DELETE = withTenantAuth(async (request, { tenantId, userId }) => {
  try {
    // Lấy thông tin từ URL params
    const { searchParams } = new URL(request.url);
    const accountId = searchParams.get('account_id');
    const labelId = searchParams.get('label_id');

    // Kiểm tra dữ liệu đầu vào
    if (!accountId || !labelId) {
      return NextResponse.json(
        { success: false, error: 'Account ID and Label ID are required', data: null },
        { status: 400 }
      );
    }

    // Lấy thông tin tài khoản
    const account = await getMoolyAccount(accountId, tenantId);
    if (!account) {
      return NextResponse.json(
        { success: false, error: 'Account not found', data: null },
        { status: 404 }
      );
    }

    // Lấy token từ tài khoản
    const { token } = account;

    // Xóa label
    const result = await deleteLabel(token, accountId, labelId, tenantId, userId);

    if (!result.success) {
      // Nếu là system label, trả về lỗi 403 Forbidden
      if (result.isSystemLabel) {
        return NextResponse.json(
          { success: false, error: result.error, data: null },
          { status: 403 }
        );
      }

      return NextResponse.json(
        { success: false, error: result.error, data: null },
        { status: 500 }
      );
    }

    // Trả về kết quả thành công
    return NextResponse.json({
      success: true,
      error: null,
      data: {
        message: 'Label deleted successfully'
      }
    });
  } catch (error) {
    console.error('Error in delete label:', error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || 'Failed to delete label',
        data: null,
      },
      { status: 500 }
    );
  }
});
