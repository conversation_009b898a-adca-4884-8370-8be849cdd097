import { useCallback } from 'react';
import { useAuth0 } from '@auth0/auth0-react';

import Button from '@mui/material/Button';

import { useRouter } from 'src/routes/hooks';

import { CONFIG } from 'src/global-config';

import { toast } from 'src/components/snackbar';

import { useAuthContext } from 'src/auth/hooks';
import { signOut as jwtSignOut } from 'src/auth/context/jwt/action';
import { signOut as amplifySignOut } from 'src/auth/context/amplify/action';
import { signOut as supabaseSignOut } from 'src/auth/context/supabase/action';
import { signOut as firebaseSignOut } from 'src/auth/context/firebase/action';

// ----------------------------------------------------------------------

const getSignOutFunction = (authMethod) => {
  switch (authMethod) {
    case 'supabase':
      return supabaseSignOut;
    case 'firebase':
      return firebaseSignOut;
    case 'amplify':
      return amplifySignOut;
    default:
      return jwtSignOut;
  }
};

// ----------------------------------------------------------------------

export function SignOutButton({ onClose, sx, ...other }) {
  const router = useRouter();

  const { signOut: contextSignOut } = useAuthContext();

  const { logout: signOutAuth0 } = useAuth0();

  const handleLogout = useCallback(async () => {
    try {
      console.log('🔄 Starting logout process...');

      // Đóng popover trước để tránh UI lag
      onClose?.();

      // Sử dụng signOut từ AuthContext nếu có, nếu không thì dùng function tương ứng
      const signOutFunction = contextSignOut || getSignOutFunction(CONFIG.auth.method);

      // Gọi signOut function với timeout
      const signOutPromise = signOutFunction();
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Logout timeout')), 10000)
      );

      try {
        await Promise.race([signOutPromise, timeoutPromise]);
        console.log('✅ Logout successful');
      } catch (signOutError) {
        console.warn('⚠️ SignOut function failed, forcing logout:', signOutError);

        // Force clear localStorage và sessionStorage
        if (typeof window !== 'undefined') {
          localStorage.clear();
          sessionStorage.clear();
          console.log('🧹 Cleared local storage');
        }
      }

      // Đợi một chút để đảm bảo cleanup hoàn tất
      await new Promise(resolve => setTimeout(resolve, 500));

      // Force redirect về login page
      console.log('🔄 Redirecting to login...');
      window.location.href = '/auth/supabase/sign-in';

    } catch (error) {
      console.error('💥 Logout error:', error);

      // Fallback: force redirect dù có lỗi
      console.log('🚨 Force logout fallback...');
      if (typeof window !== 'undefined') {
        localStorage.clear();
        sessionStorage.clear();
        window.location.href = '/auth/supabase/sign-in';
      }

      toast.error('Đã đăng xuất (có lỗi nhỏ)');
    }
  }, [contextSignOut, onClose, router]);

  const handleLogoutAuth0 = useCallback(async () => {
    try {
      await signOutAuth0();

      onClose?.();
      router.refresh();
    } catch (error) {
      console.error(error);
      toast.error('Unable to logout!');
    }
  }, [onClose, router, signOutAuth0]);

  return (
    <Button
      fullWidth
      variant="soft"
      size="large"
      color="error"
      onClick={CONFIG.auth.method === 'auth0' ? handleLogoutAuth0 : handleLogout}
      sx={sx}
      {...other}
    >
      Logout
    </Button>
  );
}
