'use client';

/**
 * <PERSON><PERSON><PERSON> hằng số và cấu hình cho module phí vận chuyển
 */

// Tên bảng trong Supabase
export const TABLE_NAME = 'shipping_fees';

// Cấu hình mặc định
export const DEFAULT_SHIPPING_OPTIONS = {
  orderBy: 'sort_order',
  ascending: true,
};

// Loại phí vận chuyển
export const SHIPPING_FEE_TYPES = {
  FIXED: 'fixed',
  WEIGHT_BASED: 'weight_based',
  PRICE_BASED: 'price_based',
  CATEGORY_BASED: 'category_based',
};

// Các tùy chọn loại phí vận chuyển
export const SHIPPING_FEE_TYPE_OPTIONS = [
  { value: 'fixed', label: 'Phí cố định' },
  { value: 'weight_based', label: 'Dựa trên trọng lượng' },
  { value: 'price_based', label: 'Dựa trên giá trị đơn hàng' },
  { value: 'category_based', label: 'Dựa trên danh mục sản phẩm' },
];

// Tên trường trong database - đồng bộ chính xác với schema DB
export const DB_FIELDS = {
  ID: 'id',
  NAME: 'name',
  DESCRIPTION: 'description',
  TYPE: 'type',
  IS_ACTIVE: 'isActive',
  CONDITIONS: 'conditions',
  AMOUNT: 'amount',
  MIN_ORDER_AMOUNT: 'minOrderAmount',
  MAX_ORDER_AMOUNT: 'maxOrderAmount',
  CATEGORY_IDS: 'categoryIds',
  WEIGHT_RANGES: 'weightRanges',
  PRICE_RANGES: 'priceRanges',
  SORT_ORDER: 'sortOrder',
  TENANT_ID: 'tenantId',
  CREATED_AT: 'createdAt',
  UPDATED_AT: 'updatedAt',
};

// Tên trường trong UI form
export const FORM_FIELDS = {
  NAME: 'name',
  DESCRIPTION: 'description',
  TYPE: 'type',
  IS_ACTIVE: 'isActive',
  AMOUNT: 'amount',
  MIN_ORDER_AMOUNT: 'minOrderAmount',
  MAX_ORDER_AMOUNT: 'maxOrderAmount',
  CATEGORY_IDS: 'categoryIds',
  WEIGHT_RANGES: 'weightRanges',
  PRICE_RANGES: 'priceRanges',
  SORT_ORDER: 'sortOrder',
};
