import { NextResponse } from 'next/server';
import { createClient } from 'src/utils/supabase/server';
import { FacebookAutoReplyService } from 'src/lib/facebook-integration/auto-reply-service.js';

/**
 * Test Auto Reply API
 * Test auto reply functionality without sending actual messages
 */

const autoReplyService = new FacebookAutoReplyService();

// POST: Test auto reply for a page
export async function POST(request) {
  try {
    const {
      pageId,
      testMessage,
      eventType = 'facebook_comment',
      skipSend = true // Don't actually send the reply during testing
    } = await request.json();

    if (!pageId || !testMessage) {
      return NextResponse.json({
        error: 'Page ID and test message required'
      }, { status: 400 });
    }

    const supabase = await createClient();

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    // Verify page access
    const { data: page, error: pageError } = await supabase
      .from('facebook_accounts')
      .select('page_id, page_name, instagram_account_id')
      .eq('page_id', pageId)
      .single();

    if (pageError || !page) {
      return NextResponse.json({
        error: 'Page not found or access denied'
      }, { status: 404 });
    }

    // Get auto reply configuration
    const { data: config, error: configError } = await supabase
      .from('facebook_auto_reply_config')
      .select('*')
      .eq('page_id', pageId)
      .single();

    if (configError && configError.code !== 'PGRST116') {
      return NextResponse.json({
        error: 'Failed to fetch auto reply configuration'
      }, { status: 500 });
    }

    if (!config) {
      return NextResponse.json({
        success: false,
        message: 'No auto reply configuration found for this page',
        suggestion: 'Please configure auto reply settings first'
      });
    }

    // Check if auto reply is enabled for the event type
    const isEnabled = autoReplyService.isAutoReplyEnabled(config, eventType);
    if (!isEnabled) {
      return NextResponse.json({
        success: false,
        message: `Auto reply is not enabled for ${eventType}`,
        config: {
          enableCommentReply: config.enable_comment_reply,
          enableMessageReply: config.enable_message_reply,
          enableInstagramComments: config.enable_instagram_comments,
          enableInstagramMessages: config.enable_instagram_messages
        }
      });
    }

    // Create mock event data for testing
    const mockEventData = {
      message: testMessage,
      text: testMessage,
      comment_text: testMessage,
      comment_id: `test_comment_${Date.now()}`,
      sender: {
        id: `test_sender_${Date.now()}`,
        name: 'Test User'
      },
      post_id: `test_post_${Date.now()}`,
      test: true,
      skipSend: skipSend
    };

    // Test the auto reply generation
    console.log('🧪 Testing auto reply generation...');
    
    const aiResult = await autoReplyService.generateAIResponse(
      config,
      eventType,
      testMessage,
      mockEventData
    );

    if (!aiResult.success) {
      return NextResponse.json({
        success: false,
        message: 'Failed to generate AI response',
        error: aiResult.error,
        fallbackResponse: aiResult.response
      });
    }

    // Log test activity
    await supabase
      .from('facebook_activity_logs')
      .insert({
        page_id: pageId,
        activity: 'auto_reply_test',
        metadata: {
          testMessage,
          eventType,
          generatedResponse: aiResult.response,
          aiMetadata: aiResult.metadata,
          userId: user.id
        }
      });

    return NextResponse.json({
      success: true,
      message: 'Auto reply test completed successfully',
      test: {
        pageId,
        pageName: page.page_name,
        eventType,
        testMessage,
        generatedResponse: aiResult.response,
        metadata: aiResult.metadata,
        config: {
          replyTone: config.reply_tone,
          replyLanguage: config.reply_language,
          maxReplyLength: config.max_reply_length,
          businessInfo: config.business_info ? 'Configured' : 'Not configured',
          excludeKeywords: config.exclude_keywords?.length || 0
        }
      }
    });

  } catch (error) {
    console.error('💥 Error in test auto reply:', error);
    return NextResponse.json({
      error: 'Internal server error',
      details: error.message
    }, { status: 500 });
  }
}

// GET: Get test scenarios and examples
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const pageId = searchParams.get('pageId');

    const supabase = await createClient();

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    // Get test scenarios
    const testScenarios = [
      {
        name: 'Inquiry about products',
        message: 'Xin chào, tôi muốn hỏi về sản phẩm của bạn',
        eventType: 'facebook_comment',
        description: 'Test response to product inquiry'
      },
      {
        name: 'Price question',
        message: 'Giá bao nhiều vậy?',
        eventType: 'facebook_comment',
        description: 'Test response to pricing question'
      },
      {
        name: 'Contact request',
        message: 'Làm sao để liên hệ với bạn?',
        eventType: 'facebook_message',
        description: 'Test response to contact request'
      },
      {
        name: 'Instagram comment',
        message: 'Sản phẩm này còn không?',
        eventType: 'instagram_comment',
        description: 'Test Instagram comment response'
      },
      {
        name: 'Complaint',
        message: 'Tôi không hài lòng với dịch vụ',
        eventType: 'facebook_message',
        description: 'Test response to complaint'
      },
      {
        name: 'Compliment',
        message: 'Sản phẩm rất tốt, cảm ơn bạn!',
        eventType: 'facebook_comment',
        description: 'Test response to positive feedback'
      }
    ];

    // Get recent test results if pageId provided
    let recentTests = [];
    if (pageId) {
      const { data: tests } = await supabase
        .from('facebook_activity_logs')
        .select('*')
        .eq('page_id', pageId)
        .eq('activity', 'auto_reply_test')
        .order('created_at', { ascending: false })
        .limit(10);

      recentTests = tests?.map(test => ({
        id: test.id,
        testMessage: test.metadata?.testMessage,
        eventType: test.metadata?.eventType,
        generatedResponse: test.metadata?.generatedResponse,
        createdAt: test.created_at
      })) || [];
    }

    return NextResponse.json({
      success: true,
      testScenarios,
      recentTests,
      eventTypes: [
        { value: 'facebook_comment', label: 'Facebook Comment' },
        { value: 'facebook_message', label: 'Facebook Message' },
        { value: 'instagram_comment', label: 'Instagram Comment' },
        { value: 'instagram_message', label: 'Instagram Message' }
      ]
    });

  } catch (error) {
    console.error('💥 Error in GET test auto reply:', error);
    return NextResponse.json({
      error: 'Internal server error',
      details: error.message
    }, { status: 500 });
  }
}
