'use client';

/**
 * Workflow Configuration Service
 * Dịch vụ quản lý cấu hình workflow linh hoạt với:
 * - Template workflow có sẵn
 * - Customizable workflow stages
 * - Visual workflow builder support
 * - Business-specific templates
 */

import { useMemo } from 'react';
import useSWR from 'swr';

import { fetchData, createData, updateData, deleteData, callRPC } from './supabase-utils';

// Tên bảng trong Supabase
const WORKFLOW_TEMPLATES_TABLE = 'workflow_templates';
const WORKFLOW_INSTANCES_TABLE = 'workflow_instances';
const WORKFLOW_TRANSITIONS_TABLE = 'workflow_stage_transitions';

// =====================================================
// WORKFLOW TEMPLATE SERVICES
// =====================================================

/**
 * L<PERSON>y danh sách workflow templates
 * @param {Object} options - Tùy chọn truy vấn
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getWorkflowTemplates(options = {}) {
  const { category, includeSystem = true } = options;

  const fetchOptions = {
    filters: {},
    orderBy: [
      { column: 'is_system_template', ascending: false },
      { column: 'name', ascending: true }
    ],
  };

  if (category) {
    fetchOptions.filters.category = category;
  }

  if (!includeSystem) {
    fetchOptions.filters.is_system_template = false;
  }

  try {
    const result = await fetchData(WORKFLOW_TEMPLATES_TABLE, fetchOptions);
    return result;
  } catch (error) {
    return { success: false, error, data: null };
  }
}

/**
 * Tạo workflow template mới
 * @param {Object} templateData - Dữ liệu template
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function createWorkflowTemplate(templateData) {
  try {
    const data = {
      name: templateData.name,
      description: templateData.description || '',
      category: templateData.category || 'custom',
      stages: templateData.stages || [],
      transitions: templateData.transitions || [],
      notifications: templateData.notifications || {},
      automations: templateData.automations || [],
      colors: templateData.colors || {},
      icons: templateData.icons || {},
      isActive: templateData.isActive !== false,
    };

    return await createData(WORKFLOW_TEMPLATES_TABLE, data);
  } catch (error) {
    return { success: false, error, data: null };
  }
}

/**
 * Cập nhật workflow template
 * @param {string} templateId - ID của template
 * @param {Object} updateData - Dữ liệu cập nhật
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function updateWorkflowTemplate(templateId, templateUpdateData) {
  try {
    const data = {
      ...templateUpdateData,
      updatedAt: new Date().toISOString(),
    };

    return await updateData(WORKFLOW_TEMPLATES_TABLE, data, { id: templateId });
  } catch (error) {
    return { success: false, error, data: null };
  }
}

/**
 * Xóa workflow template
 * @param {string} templateId - ID của template
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function deleteWorkflowTemplate(templateId) {
  try {
    // Kiểm tra template có phải system template không
    const templateResult = await fetchData(WORKFLOW_TEMPLATES_TABLE, {
      filters: { id: templateId },
      single: true,
    });

    // Xử lý lỗi PGRST116 khi không có dữ liệu
    if (!templateResult.success && templateResult.error?.code === 'NO_ROWS_OR_MULTIPLE_ROWS') {
      return { success: false, error: 'Template không tồn tại', data: null };
    }

    if (!templateResult.success || !templateResult.data) {
      return { success: false, error: 'Template không tồn tại', data: null };
    }

    if (templateResult.data.isSystemTemplate) {
      return { success: false, error: 'Không thể xóa template hệ thống', data: null };
    }

    return await deleteData(WORKFLOW_TEMPLATES_TABLE, { id: templateId });
  } catch (error) {
    return { success: false, error, data: null };
  }
}

// =====================================================
// WORKFLOW INSTANCE SERVICES - UPDATED FOR GLOBAL WORKFLOW
// =====================================================

/**
 * Lấy global workflow cho tenant hiện tại
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getGlobalWorkflow() {
  try {
    const result = await fetchData(WORKFLOW_INSTANCES_TABLE, {
      filters: { 
        isGlobal: true,
        isActive: true
      },
      single: true,
    });

    // Xử lý lỗi PGRST116 khi không có dữ liệu - supabase-utils đã xử lý và trả về code này
    if (!result.success && (result.error?.code === 'NO_ROWS_OR_MULTIPLE_ROWS' || result.error?.code === 'PGRST116')) {
      return { success: true, data: null, error: null };
    }

    return result;
  } catch (error) {
    return { success: false, error, data: null };
  }
}

/**
 * Lấy workflow instance cho chatbot (DEPRECATED - chỉ để backward compatibility)
 * @param {string} chatbotId - ID của chatbot (không sử dụng nữa)
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getChatbotWorkflow(chatbotId) {
  // Chuyển hướng sang global workflow
  return await getGlobalWorkflow();
}

/**
 * Tạo hoặc cập nhật global workflow
 * @param {Object} workflowData - Dữ liệu workflow
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function upsertGlobalWorkflow(workflowData) {
  try {
    const data = {
      chatbotId: null, // Global workflow không có chatbot_id
      templateId: workflowData.templateId || null,
      name: workflowData.name,
      description: workflowData.description || '',
      stages: workflowData.stages || [],
      transitions: workflowData.transitions || [],
      notifications: workflowData.notifications || {},
      automations: workflowData.automations || [],
      colors: workflowData.colors || {},
      icons: workflowData.icons || {},
      isGlobal: true,
      isActive: workflowData.isActive !== false,
    };

    // Kiểm tra xem đã có global workflow chưa
    const existingResult = await getGlobalWorkflow();
    
    if (existingResult.success && existingResult.data) {
      // Cập nhật global workflow hiện tại
      return await updateData(WORKFLOW_INSTANCES_TABLE, {
        ...data,
        updatedAt: new Date().toISOString(),
      }, { id: existingResult.data.id });
    } else {
      // Tạo global workflow mới
      return await createData(WORKFLOW_INSTANCES_TABLE, data);
    }
  } catch (error) {
    return { success: false, error, data: null };
  }
}

/**
 * Tạo hoặc cập nhật workflow instance cho chatbot (DEPRECATED - chuyển sang global)
 * @param {string} chatbotId - ID của chatbot (không sử dụng nữa)
 * @param {Object} workflowData - Dữ liệu workflow
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function upsertChatbotWorkflow(chatbotId, workflowData) {
  // Chuyển hướng sang global workflow
  return await upsertGlobalWorkflow(workflowData);
}

/**
 * Áp dụng template cho global workflow
 * @param {string} templateId - ID của template
 * @param {Object} customizations - Tùy chỉnh bổ sung
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function applyGlobalWorkflowTemplate(templateId, customizations = {}) {
  try {
    // Lấy thông tin template
    const templateResult = await fetchData(WORKFLOW_TEMPLATES_TABLE, {
      filters: { id: templateId },
      single: true,
    });

    // Xử lý lỗi PGRST116 khi không có dữ liệu
    if (!templateResult.success && templateResult.error?.code === 'NO_ROWS_OR_MULTIPLE_ROWS') {
      return { success: false, error: 'Template không tồn tại', data: null };
    }

    if (!templateResult.success || !templateResult.data) {
      return { success: false, error: 'Template không tồn tại', data: null };
    }

    const template = templateResult.data;

    // Tạo global workflow từ template
    const workflowData = {
      templateId: template.id,
      name: customizations.name || template.name,
      description: customizations.description || template.description,
      stages: customizations.stages || template.stages,
      transitions: customizations.transitions || template.transitions,
      notifications: { ...template.notifications, ...customizations.notifications },
      automations: customizations.automations || template.automations,
      colors: { ...template.colors, ...customizations.colors },
      icons: { ...template.icons, ...customizations.icons },
      isActive: customizations.isActive !== false,
    };

    return await upsertGlobalWorkflow(workflowData);
  } catch (error) {
    return { success: false, error, data: null };
  }
}

/**
 * Áp dụng template cho chatbot (DEPRECATED - chuyển sang global)
 * @param {string} chatbotId - ID của chatbot (không sử dụng nữa)
 * @param {string} templateId - ID của template
 * @param {Object} customizations - Tùy chỉnh bổ sung
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function applyChatbotWorkflowTemplate(chatbotId, templateId, customizations = {}) {
  // Chuyển hướng sang global workflow
  return await applyGlobalWorkflowTemplate(templateId, customizations);
}

// =====================================================
// STAGE TRANSITION SERVICES
// =====================================================

/**
 * Chuyển đổi trạng thái lead
 * @param {string} leadId - ID của lead
 * @param {string} toStage - Trạng thái đích
 * @param {Object} options - Tùy chọn
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function transitionLeadStage(leadId, toStage, options = {}) {
  try {
    const { triggeredBy, notes } = options;

    const result = await callRPC('transition_lead_stage', {
      pLeadId: leadId,
      pToStage: toStage,
      pTriggeredBy: triggeredBy || null,
      pNotes: notes || null,
    });

    return result;
  } catch (error) {
    return { success: false, error, data: null };
  }
}

/**
 * Lấy lịch sử chuyển đổi trạng thái
 * @param {string} leadId - ID của lead
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getLeadTransitionHistory(leadId) {
  try {
    const result = await fetchData(WORKFLOW_TRANSITIONS_TABLE, {
      filters: { leadId: leadId },
      orderBy: [{ column: 'createdAt', ascending: false }],
      columns: `
        *,
        user:users(id, name, email)
      `,
    });

    return result;
  } catch (error) {
    return { success: false, error, data: null };
  }
}

// =====================================================
// WORKFLOW ANALYTICS SERVICES
// =====================================================

/**
 * Lấy analytics cho workflow
 * @param {string} workflowInstanceId - ID của workflow instance
 * @param {Object} dateRange - Khoảng thời gian
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getWorkflowAnalytics(workflowInstanceId, dateRange = {}) {
  try {
    const { startDate, endDate } = dateRange;
    
    // Tạo filter cho date range
    const filters = { workflowInstanceId: workflowInstanceId };
    
    if (startDate) {
      filters.createdAt = { gte: startDate };
    }
    if (endDate) {
      filters.createdAt = { ...filters.createdAt, lte: endDate };
    }

    const result = await fetchData(WORKFLOW_TRANSITIONS_TABLE, {
      filters,
      columns: `
        from_stage,
        to_stage,
        created_at,
        transition_type
      `,
    });

    if (result.success && result.data) {
      // Xử lý dữ liệu analytics
      const transitions = result.data;
      const analytics = processWorkflowAnalytics(transitions);
      
      return { success: true, data: analytics, error: null };
    }

    return result;
  } catch (error) {
    return { success: false, error, data: null };
  }
}

/**
 * Xử lý dữ liệu analytics workflow
 * @param {Array} transitions - Danh sách chuyển đổi
 * @returns {Object} - Dữ liệu analytics đã xử lý
 */
function processWorkflowAnalytics(transitions) {
  const analytics = {
    totalTransitions: transitions.length,
    stageDistribution: {},
    conversionRates: {},
    averageDuration: {},
    transitionPatterns: {},
  };

  // Đếm transitions theo stage
  transitions.forEach(transition => {
    const { from_stage, to_stage, created_at, transition_type } = transition;
    
    // Stage distribution
    if (!analytics.stageDistribution[to_stage]) {
      analytics.stageDistribution[to_stage] = 0;
    }
    analytics.stageDistribution[to_stage]++;

    // Transition patterns
    const pattern = `${from_stage}_to_${to_stage}`;
    if (!analytics.transitionPatterns[pattern]) {
      analytics.transitionPatterns[pattern] = 0;
    }
    analytics.transitionPatterns[pattern]++;
  });

  return analytics;
}

// =====================================================
// BUSINESS TEMPLATE GENERATORS
// =====================================================

/**
 * Tạo template workflow cho các ngành nghề cụ thể
 */
export const BUSINESS_WORKFLOW_TEMPLATES = {
  // Bán lẻ / E-commerce
  retail_ecommerce: {
    name: 'Quy trình Bán lẻ Online',
    description: 'Phù hợp cho shop online, bán lẻ, e-commerce',
    stages: [
      { id: 'browsing', name: 'Đang duyệt', color: 'info', icon: 'solar:eye-bold' },
      { id: 'interested', name: 'Quan tâm', color: 'primary', icon: 'solar:heart-bold' },
      { id: 'cart_added', name: 'Thêm giỏ hàng', color: 'warning', icon: 'solar:shopping-cart-bold' },
      { id: 'checkout', name: 'Thanh toán', color: 'secondary', icon: 'solar:card-bold' },
      { id: 'purchased', name: 'Đã mua', color: 'success', icon: 'solar:bag-check-bold' },
      { id: 'abandoned', name: 'Bỏ dở', color: 'error', icon: 'solar:logout-bold' },
    ]
  },

  // Dịch vụ
  services: {
    name: 'Quy trình Dịch vụ',
    description: 'Phù hợp cho công ty dịch vụ, tư vấn, spa, salon',
    stages: [
      { id: 'inquiry', name: 'Hỏi thông tin', color: 'info', icon: 'solar:question-circle-bold' },
      { id: 'consultation', name: 'Tư vấn', color: 'primary', icon: 'solar:chat-bold' },
      { id: 'quoted', name: 'Báo giá', color: 'warning', icon: 'solar:document-text-bold' },
      { id: 'booked', name: 'Đặt lịch', color: 'secondary', icon: 'solar:calendar-bold' },
      { id: 'completed', name: 'Hoàn thành', color: 'success', icon: 'solar:check-circle-bold' },
      { id: 'cancelled', name: 'Hủy bỏ', color: 'error', icon: 'solar:close-circle-bold' },
    ]
  },

  // Bất động sản
  real_estate: {
    name: 'Quy trình Bất động sản',
    description: 'Phù hợp cho môi giới BDS, chủ đầu tư, đại lý',
    stages: [
      { id: 'browsing', name: 'Duyệt tin', color: 'info', icon: 'solar:home-2-bold' },
      { id: 'interested', name: 'Quan tâm', color: 'primary', icon: 'solar:heart-bold' },
      { id: 'viewing', name: 'Xem nhà', color: 'warning', icon: 'solar:eye-bold' },
      { id: 'negotiating', name: 'Thương lượng', color: 'secondary', icon: 'solar:chat-bold' },
      { id: 'contracted', name: 'Ký hợp đồng', color: 'success', icon: 'solar:document-bold' },
      { id: 'lost', name: 'Không thành', color: 'error', icon: 'solar:close-circle-bold' },
    ]
  },

  // Giáo dục
  education: {
    name: 'Quy trình Giáo dục',
    description: 'Phù hợp cho trung tâm học, khóa học online, gia sư',
    stages: [
      { id: 'interested', name: 'Quan tâm khóa học', color: 'info', icon: 'solar:book-bold' },
      { id: 'trial', name: 'Học thử', color: 'primary', icon: 'solar:play-bold' },
      { id: 'counseled', name: 'Tư vấn', color: 'warning', icon: 'solar:chat-bold' },
      { id: 'enrolled', name: 'Đăng ký học', color: 'success', icon: 'solar:graduation-bold' },
      { id: 'completed', name: 'Hoàn thành', color: 'success', icon: 'solar:medal-bold' },
      { id: 'dropped', name: 'Bỏ học', color: 'error', icon: 'solar:logout-bold' },
    ]
  },

  // Sức khỏe - Y tế
  healthcare: {
    name: 'Quy trình Y tế - Sức khỏe',
    description: 'Phù hợp cho phòng khám, bệnh viện, dịch vụ y tế',
    stages: [
      { id: 'inquiry', name: 'Hỏi thông tin', color: 'info', icon: 'solar:question-circle-bold' },
      { id: 'appointment', name: 'Đặt lịch khám', color: 'primary', icon: 'solar:calendar-bold' },
      { id: 'checked_in', name: 'Check-in', color: 'warning', icon: 'solar:user-check-bold' },
      { id: 'examined', name: 'Khám xong', color: 'secondary', icon: 'solar:medical-kit-bold' },
      { id: 'follow_up', name: 'Tái khám', color: 'success', icon: 'solar:refresh-bold' },
      { id: 'completed', name: 'Hoàn thành', color: 'success', icon: 'solar:check-circle-bold' },
    ]
  },
};

// =====================================================
// REACT HOOKS
// =====================================================

/**
 * Hook để lấy danh sách workflow templates
 * @param {Object} options - Tùy chọn
 * @returns {Object} - SWR response
 */
export function useWorkflowTemplates(options = {}) {
  const { category, includeSystem = true } = options;
  
  const swrKey = useMemo(() => {
    const keyParts = ['workflow-templates'];
    if (category) keyParts.push(`category:${category}`);
    if (!includeSystem) keyParts.push('no-system');
    return keyParts.join('|');
  }, [category, includeSystem]);

  const { data, error, mutate } = useSWR(
    swrKey,
    () => getWorkflowTemplates(options)
  );

  return {
    templates: data?.success ? data.data : [],
    loading: !data && !error,
    error: error || (!data?.success ? data?.error : null),
    mutate,
  };
}

/**
 * Hook để lấy global workflow
 * @returns {Object} - SWR response
 */
export function useGlobalWorkflow() {
  const { data, error, mutate } = useSWR(
    'global-workflow',
    () => getGlobalWorkflow()
  );

  return useMemo(() => ({
    workflow: data?.success ? data.data : null,
    loading: !data && !error,
    error: error || (!data?.success ? data?.error : null),
    mutate,
  }), [data, error, mutate]);
}

/**
 * Hook để lấy workflow cho chatbot (DEPRECATED - chuyển sang global)
 * @param {string} chatbotId - ID của chatbot (không sử dụng nữa)
 * @returns {Object} - SWR response
 */
export function useChatbotWorkflow(chatbotId) {
  // Chuyển hướng sang global workflow
  return useGlobalWorkflow();
}

/**
 * Hook để lấy analytics workflow
 * @param {string} workflowInstanceId - ID của workflow instance
 * @param {Object} dateRange - Khoảng thời gian
 * @returns {Object} - SWR response
 */
export function useWorkflowAnalytics(workflowInstanceId, dateRange = {}) {
  const swrKey = useMemo(() => {
    if (!workflowInstanceId) return null;
    
    const keyParts = [`workflow-analytics-${workflowInstanceId}`];
    if (dateRange.startDate) keyParts.push(`start:${dateRange.startDate}`);
    if (dateRange.endDate) keyParts.push(`end:${dateRange.endDate}`);
    
    return keyParts.join('|');
  }, [workflowInstanceId, dateRange.startDate, dateRange.endDate]);

  const { data, error, mutate } = useSWR(
    swrKey,
    () => getWorkflowAnalytics(workflowInstanceId, dateRange)
  );

  return {
    analytics: data?.success ? data.data : null,
    loading: !data && !error,
    error: error || (!data?.success ? data?.error : null),
    mutate,
  };
}

// =====================================================
// HELPER FUNCTIONS
// =====================================================

/**
 * Lấy status options từ workflow instance
 * @param {Object} workflow - Workflow instance
 * @returns {Array} - Mảng status options
 */
export function getWorkflowStatusOptions(workflow) {
  if (!workflow || !workflow.stages) {
    // Fallback to default status options
    return [
      { value: 'new', label: 'Mới', color: 'info' },
      { value: 'contacted', label: 'Đã liên hệ', color: 'warning' },
      { value: 'qualified', label: 'Tiềm năng', color: 'primary' },
      { value: 'converted', label: 'Đã chuyển đổi', color: 'success' },
      { value: 'lost', label: 'Thất bại', color: 'error' },
    ];
  }

  return workflow.stages.map(stage => ({
    value: stage.id,
    label: stage.name,
    color: workflow.colors?.[stage.id] || 'default',
    icon: workflow.icons?.[stage.id] || null,
    description: stage.description || '',
    order: stage.order || 0,
  }));
}

/**
 * Validate workflow configuration
 * @param {Object} workflow - Workflow configuration
 * @returns {Object} - Validation result
 */
export function validateWorkflowConfig(workflow) {
  const errors = [];
  
  if (!workflow.name || !workflow.name.trim()) {
    errors.push('Tên workflow là bắt buộc');
  }
  
  if (!workflow.stages || !Array.isArray(workflow.stages) || workflow.stages.length === 0) {
    errors.push('Workflow phải có ít nhất một giai đoạn');
  }
  
  if (workflow.stages) {
    // Kiểm tra duplicate stage IDs
    const stageIds = workflow.stages.map(s => s.id);
    const duplicateIds = stageIds.filter((id, index) => stageIds.indexOf(id) !== index);
    
    if (duplicateIds.length > 0) {
      errors.push(`Có giai đoạn trùng ID: ${duplicateIds.join(', ')}`);
    }
    
    // Kiểm tra required fields cho từng stage
    workflow.stages.forEach((stage, index) => {
      if (!stage.id || !stage.id.trim()) {
        errors.push(`Giai đoạn ${index + 1}: ID là bắt buộc`);
      }
      
      if (!stage.name || !stage.name.trim()) {
        errors.push(`Giai đoạn ${index + 1}: Tên là bắt buộc`);
      }
    });
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
} 