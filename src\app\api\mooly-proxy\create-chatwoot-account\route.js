import axios from 'axios';
import { NextResponse } from 'next/server';

import { withTenantAuth } from 'src/utils/server-auth';
import { createClient } from 'src/utils/supabase/server';

// <PERSON>ác hằng số cho các label và đồng bộ với Mooly
export const LABEL_KEYS = {
  SPAM: 'SPAM',
  SUPPORT: 'SUPPORT',
  PURCHASE: 'PURCHASE',
  COMPLAINT: 'COMPLAINT',
  CONSULTATION: 'CONSULTATION'
};

// Mapping between label keys and their Vietnamese titles
const LABEL_TITLE_MAP = {
  [LABEL_KEYS.SPAM]: 'Spam',
  [LABEL_KEYS.SUPPORT]: 'Cần hỗ trợ',
  [LABEL_KEYS.PURCHASE]: 'Mua hàng',
  [LABEL_KEYS.COMPLAINT]: 'Khiếu nại',
  [LABEL_KEYS.CONSULTATION]: 'T<PERSON> vấn'
};

// <PERSON>h sách các label cần tạo
const PREDEFINED_LABELS = [
  {
    key: LABEL_KEYS.SPAM,
    title: LABEL_TITLE_MAP[LABEL_KEYS.SPAM],
    description: "Người dùng gửi tin nhắn spam (Label của hệ thống - KHÔNG ĐƯỢC XÓA)",
    color: "#FF5733",
    show_on_sidebar: true,
    system_label: true
  },
  {
    key: LABEL_KEYS.SUPPORT,
    title: LABEL_TITLE_MAP[LABEL_KEYS.SUPPORT],
    description: "Khách hàng cần hỗ trợ từ nhân viên (Label của hệ thống - KHÔNG ĐƯỢC XÓA)",
    color: "#33A1FF",
    show_on_sidebar: true,
    system_label: true
  },
  {
    key: LABEL_KEYS.PURCHASE,
    title: LABEL_TITLE_MAP[LABEL_KEYS.PURCHASE],
    description: "Khách hàng có ý định mua hàng (Label của hệ thống - KHÔNG ĐƯỢC XÓA)",
    color: "#33FF57",
    show_on_sidebar: true,
    system_label: true
  },
  {
    key: LABEL_KEYS.COMPLAINT,
    title: LABEL_TITLE_MAP[LABEL_KEYS.COMPLAINT],
    description: "Khách hàng có khiếu nại về sản phẩm/dịch vụ (Label của hệ thống - KHÔNG ĐƯỢC XÓA)",
    color: "#FF33A8",
    show_on_sidebar: true,
    system_label: true
  },
  {
    key: LABEL_KEYS.CONSULTATION,
    title: LABEL_TITLE_MAP[LABEL_KEYS.CONSULTATION],
    description: "Khách hàng cần tư vấn thêm thông tin (Label của hệ thống - KHÔNG ĐƯỢC XÓA)",
    color: "#FFD700",
    show_on_sidebar: true,
    system_label: true
  }
];

/**
 * Tạo các label cho tài khoản Chatwoot
 * @param {string} token - Token xác thực
 * @param {string} accountId - ID tài khoản
 * @returns {Promise<Object>} - Kết quả tạo label
 */
async function createLabels(token, accountId) {
  const host = process.env.MOOLY_API_HOST || 'https://app.mooly.vn';
  const apiVersion = process.env.MOOLY_API_VERSION || 'api/v1';
  const labelEndpoint = `${host}/${apiVersion}/accounts/${accountId}/labels`;

  const labelResults = {};

  // Tạo từng label và lưu kết quả
  for (const label of PREDEFINED_LABELS) {
    try {
      // Chuẩn bị dữ liệu label theo đúng định dạng API yêu cầu
      const labelData = {
        title: label.title,
        description: label.description,
        color: label.color,
        show_on_sidebar: label.show_on_sidebar
      };

      try {
        const response = await axios.post(labelEndpoint, { label: labelData }, {
          headers: {
            'Content-Type': 'application/json',
            'api_access_token': token
          }
        });

        // Axios trả về dữ liệu trong response.data
        const data = response.data;
        console.log(`Label created successfully:`, data);

        // Lưu ID của label với key là constant key thay vì title
        // Chỉ lưu các trường mà API trả về và thêm key để mapping
        labelResults[label.key] = {
          id: data.id,
          title: data.title || label.title,
          description: data.description || label.description,
          color: data.color || label.color,
          show_on_sidebar: data.show_on_sidebar || label.show_on_sidebar,
          key: label.key, // Thêm key để mapping với constant
          system_label: label.system_label || false
        };
      } catch (axiosError) {
        console.error(`Failed to create label ${label.title}:`, axiosError.response?.data || axiosError.message);
        // Tiếp tục với label tiếp theo
      }
    } catch (error) {
      console.error(`Error creating label ${label.title}:`, error);
    }
  }

  return labelResults;
}

/**
 * Lưu cấu hình label vào database
 * @param {string} accountId - ID tài khoản Chatwoot
 * @param {Object} labelConfig - Cấu hình label
 */
async function saveLabelsConfig(accountId, labelConfig) {
  try {
    // Chuẩn bị dữ liệu để lưu vào Supabase
    // Đảm bảo cấu trúc dữ liệu nhất quán và dễ truy cập
    const formattedLabelConfig = {};

    // Chuyển đổi từ object thành cấu trúc dễ truy cập hơn
    Object.keys(labelConfig).forEach(key => {
      const label = labelConfig[key];
      formattedLabelConfig[key] = {
        id: label.id,
        title: label.title,
        description: label.description,
        color: label.color,
        show_on_sidebar: label.show_on_sidebar,
        system_label: label.system_label || false
      };
    });

    // Thêm metadata để dễ dàng quản lý
    const labelsMetadata = {
      labels: formattedLabelConfig,
      updated_at: new Date().toISOString(),
      version: '1.0'
    };

    const supabase = await createClient();
    const { error } = await supabase
      .from('mooly_accounts')
      .update({ labels_config: labelsMetadata })
      .eq('account_id', accountId);

    if (error) {
      console.error('Error saving label config to database:', error);
    } else {
      console.log('Label config saved successfully for account:', accountId);
    }
  } catch (error) {
    console.error('Exception when saving label config:', error);
  }
}

/**
 * API proxy để tạo tài khoản Chatwoot trên Mooly
 * @param {Request} request - Yêu cầu HTTP
 * @returns {Promise<NextResponse>} - Phản hồi HTTP
 */
export const POST = withTenantAuth(async (request, { tenantId, userId }) => {
  try {
    // Lấy thông tin từ body
    const body = await request.json();
    const { first_name, last_name, email } = body;

    // Kiểm tra dữ liệu đầu vào
    if (!first_name || !last_name || !email) {
      return NextResponse.json(
        { success: false, error: 'First name, last name, and email are required' },
        { status: 400 }
      );
    }

    // Lấy thông tin cấu hình từ biến môi trường
    const n8nAccessToken = process.env.N8N_ACCESS_TOKEN;

    if (!n8nAccessToken) {
      return NextResponse.json(
        { success: false, error: 'Server configuration error' },
        { status: 500 }
      );
    }

    // Gọi API Mooly từ server để tạo tài khoản
    const response = await fetch('https://webhook.mooly.vn/webhook/create-chatwoot-account', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'access-token': n8nAccessToken,
      },
      body: JSON.stringify({
        first_name,
        last_name,
        email,
        tenant_id: tenantId
      }),
    });

    // Xử lý lỗi từ API
    if (!response.ok) {
      let errorData;
      try {
        errorData = await response.json();
         
      } catch (e) {
        errorData = { message: response.statusText };
      }

      return NextResponse.json(
        {
          success: false,
          error: errorData.message || 'Failed to create Chatwoot account',
          data: null,
        },
        { status: response.status }
      );
    }

    // Lấy dữ liệu tài khoản đã tạo
    const data = await response.json();
    const { token, account_id, user_id, default_password } = data;

    // Tạo các label cho tài khoản
    const labelConfig = await createLabels(token, account_id);

    // Lưu cấu hình label vào database và liên kết với tenant_id
    await saveLabelsConfig(account_id, labelConfig);

    // Chuẩn bị dữ liệu labels metadata giống như trong hàm saveLabelsConfig
    const formattedLabelConfig = {};
    Object.keys(labelConfig).forEach(key => {
      const label = labelConfig[key];
      formattedLabelConfig[key] = {
        id: label.id,
        title: label.title,
        description: label.description,
        color: label.color,
        show_on_sidebar: label.show_on_sidebar,
        system_label: label.system_label || false
      };
    });

    const labelsMetadata = {
      labels: formattedLabelConfig,
      updated_at: new Date().toISOString(),
      version: '1.0'
    };

    // Lưu thông tin tài khoản vào bảng mooly_accounts nếu chưa tồn tại
    const supabase = await createClient();
    const { error: accountError } = await supabase
      .from('mooly_accounts')
      .upsert({
        account_id,
        tenant_id: tenantId,
        labels_config: labelsMetadata,
        updated_by: userId,
        updated_at: new Date().toISOString()
      }, { onConflict: 'account_id' });

    if (accountError) {
      console.error('Error saving account to database:', accountError);
      // Tiếp tục xử lý vì đây không phải lỗi nghiêm trọng, chỉ ghi log
    }

    // Trả về dữ liệu thành công
    return NextResponse.json({
      success: true,
      error: null,
      data: {
        token,
        accountId: account_id,
        userId: user_id,
        defaultPassword: default_password,
        labelsConfig: labelsMetadata
      }
    });
  } catch (error) {
    console.error('Error in Mooly proxy create account:', error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || 'Failed to create Chatwoot account',
        data: null,
      },
      { status: 500 }
    );
  }
});
