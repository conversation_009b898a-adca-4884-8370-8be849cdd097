import axios from 'axios';
import { NextResponse } from 'next/server';

/**
 * API route để hủy link thanh toán PayOS
 * Chuyển logic từ client-side sang server-side để bảo mật thông tin xác thực
 * @param {Request} request - Request object
 * @returns {Promise<Response>} - Response object
 */
export async function POST(request) {
  try {
    // Lấy dữ liệu từ request
    const { orderId, cancellationReason = '' } = await request.json();

    if (!orderId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Order ID is required',
          data: null,
        },
        { status: 400 }
      );
    }

    // C<PERSON>u hình PayOS - sử dụng biến môi trường server-side
    const PAYOS_CONFIG = {
      clientId: process.env.PAYOS_CLIENT_ID || '',
      apiKey: process.env.PAYOS_API_KEY || '',
      checksumKey: process.env.PAYOS_CHECKSUM_KEY || '',
      baseUrl: 'https://api-merchant.payos.vn',
    };

    // Kiểm tra xem các khóa API có tồn tại không
    if (!PAYOS_CONFIG.clientId || !PAYOS_CONFIG.apiKey || !PAYOS_CONFIG.checksumKey) {
      return NextResponse.json(
        {
          success: false,
          error: 'PayOS configuration is missing',
          data: null,
        },
        { status: 500 }
      );
    }

    // Gọi API PayOS để hủy link thanh toán
    const response = await axios.post(
      `${PAYOS_CONFIG.baseUrl}/v2/payment-requests/${orderId}/cancel`,
      { cancellationReason },
      {
        headers: {
          'x-client-id': PAYOS_CONFIG.clientId,
          'x-api-key': PAYOS_CONFIG.apiKey,
        },
      }
    );

    // Kiểm tra kết quả từ PayOS
    if (response.data && response.data.code === '00') {
      return NextResponse.json({
        success: true,
        data: response.data.data,
        error: null,
      });
    }

    return NextResponse.json(
      {
        success: false,
        error: response.data.desc || 'Failed to cancel payment link',
        data: null,
      },
      { status: 400 }
    );
  } catch (error) {
    console.error('PayOS cancel payment link error:', error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || 'Failed to cancel payment link',
        data: null,
      },
      { status: 500 }
    );
  }
}
