'use client';

import { useRef, useState, useEffect, useCallback } from 'react';

import { fetchData, createData, updateData, deleteData, upsertData } from './supabase-utils';

// Tên bảng trong Supabase
const TABLE_NAME = 'promotions';

/**
 * L<PERSON>y danh sách khuyến mãi với các tùy chọn lọc
 * @param {Object} options - C<PERSON>c tùy chọn truy vấn
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getPromotions(options = {}) {
  return fetchData(TABLE_NAME, options);
}

/**
 * Lấy chi tiết một khuyến mãi theo ID
 * @param {string} promotionId - ID của khuyến mãi
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getPromotionById(promotionId) {
  if (!promotionId) return { success: false, error: 'Promotion ID is required', data: null };

  return fetchData(TABLE_NAME, {
    filters: { id: promotionId },
    single: true,
  });
}

/**
 * Tạo khuyến mãi mới
 * @param {Object} promotionData - Dữ liệu khuyến mãi
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function createPromotion(promotionData) {
  // Đảm bảo mã khuyến mãi luôn được chuyển đổi thành chữ hoa
  const updatedData = { ...promotionData };
  if (updatedData.code) {
    updatedData.code = updatedData.code.toUpperCase();
  }
  return createData(TABLE_NAME, updatedData);
}

/**
 * Cập nhật khuyến mãi
 * @param {string} promotionId - ID của khuyến mãi
 * @param {Object} promotionData - Dữ liệu cập nhật
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function updatePromotion(promotionId, promotionData) {
  if (!promotionId) return { success: false, error: 'Promotion ID is required', data: null };

  // Đảm bảo mã khuyến mãi luôn được chuyển đổi thành chữ hoa
  const updatedData = { ...promotionData };
  if (updatedData.code) {
    updatedData.code = updatedData.code.toUpperCase();
  }

  return updateData(TABLE_NAME, updatedData, { id: promotionId });
}

/**
 * Xóa khuyến mãi
 * @param {string} promotionId - ID của khuyến mãi
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function deletePromotion(promotionId) {
  if (!promotionId) return { success: false, error: 'Promotion ID is required', data: null };

  return deleteData(TABLE_NAME, { id: promotionId });
}

/**
 * Tạo hoặc cập nhật khuyến mãi
 * @param {Object} promotionData - Dữ liệu khuyến mãi
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function upsertPromotion(promotionData) {
  // Đảm bảo mã khuyến mãi luôn được chuyển đổi thành chữ hoa
  const updatedData = { ...promotionData };
  if (updatedData.code) {
    updatedData.code = updatedData.code.toUpperCase();
  }

  return upsertData(TABLE_NAME, updatedData);
}

/**
 * Hook để lấy danh sách khuyến mãi
 * @param {Object} options - Các tùy chọn truy vấn
 * @returns {Object} - Dữ liệu và các hàm liên quan
 */
export function usePromotions(options = {}) {
  const [data, setData] = useState({ success: false, data: [], error: null });
  const [isLoading, setIsLoading] = useState(true);
  const [isValidating, setIsValidating] = useState(false);
  const [error, setError] = useState(null);

  // Sử dụng ref để lưu trữ options mà không gây re-render
  const optionsRef = useRef(options);

  // Cập nhật ref khi options thay đổi
  useEffect(() => {
    optionsRef.current = options;
  }, [options]);

  const fetchPromotions = useCallback(async () => {
    setIsValidating(true);
    try {
      // Sử dụng giá trị ref để luôn lấy options mới nhất
      const currentOptions = optionsRef.current;

      const result = await getPromotions(currentOptions);
      setData(result);
      setError(result.error || null);
      return result;
    } catch (err) {
      console.error('Error fetching promotions:', err);
      setError(err);
      return { success: false, error: err, data: null };
    } finally {
      setIsValidating(false);
      setIsLoading(false);
    }
  }, []); // Không phụ thuộc vào options vì chúng ta đang sử dụng ref

  // Hàm để tải lại dữ liệu
  const mutate = useCallback(async () => {
    console.log('Manually refreshing promotions data...');
    return fetchPromotions();
  }, [fetchPromotions]);

  // Tải dữ liệu khi component mount
  useEffect(() => {
    fetchPromotions();
  }, [fetchPromotions]);

  // Trả về dữ liệu và các hàm liên quan
  return {
    promotions: data.data || [],
    isLoading,
    isValidating,
    error,
    mutate,
    data,
  };
}

/**
 * Hook để lấy chi tiết một khuyến mãi
 * @param {string} promotionId - ID của khuyến mãi
 * @returns {Object} - Dữ liệu và các hàm liên quan
 */
export function usePromotion(promotionId) {
  const [data, setData] = useState({ success: false, data: null, error: null });
  const [isLoading, setIsLoading] = useState(!!promotionId);
  const [isValidating, setIsValidating] = useState(false);
  const [error, setError] = useState(null);

  const fetchPromotion = useCallback(async () => {
    if (!promotionId) {
      setData(null);
      setError(null);
      setIsLoading(false);
      setIsValidating(false);
      return null;
    }

    setIsValidating(true);
    try {
      const result = await getPromotionById(promotionId);
      setData(result);
      setError(result.error || null);
      return result;
    } catch (err) {
      setError(err);
      return { success: false, error: err, data: null };
    } finally {
      setIsValidating(false);
      setIsLoading(false);
    }
  }, [promotionId]);

  // Hàm để tải lại dữ liệu
  const mutate = useCallback(async () => fetchPromotion(), [fetchPromotion]);

  // Tải dữ liệu khi component mount hoặc promotionId thay đổi
  useEffect(() => {
    fetchPromotion();
  }, [fetchPromotion]);

  // Trả về dữ liệu và các hàm liên quan
  return {
    promotion: data?.data || null,
    isLoading,
    isValidating,
    error,
    mutate,
    data,
  };
}

/**
 * Hook để tạo, cập nhật, xóa khuyến mãi
 * @returns {Object} - Các hàm mutation
 */
export function usePromotionMutations() {
  const [loadingStates, setLoadingStates] = useState({
    creating: false,
    updating: false,
    deleting: false,
    upserting: false,
  });

  // Helper function để xử lý trạng thái loading và xử lý lỗi
  const withLoadingState = async (stateKey, asyncFn) => {
    setLoadingStates((prev) => ({ ...prev, [stateKey]: true }));
    try {
      return await asyncFn();
    } finally {
      setLoadingStates((prev) => ({ ...prev, [stateKey]: false }));
    }
  };

  // Các hàm mutation sử dụng helper
  const createPromotionMutation = (promotionData) =>
    withLoadingState('creating', () => createPromotion(promotionData));

  const updatePromotionMutation = (id, data) =>
    withLoadingState('updating', () => updatePromotion(id, data));

  const deletePromotionMutation = (promotionId) =>
    withLoadingState('deleting', () => deletePromotion(promotionId));

  const upsertPromotionMutation = (promotionData) =>
    withLoadingState('upserting', () => upsertPromotion(promotionData));

  return {
    createPromotion: createPromotionMutation,
    updatePromotion: updatePromotionMutation,
    deletePromotion: deletePromotionMutation,
    upsertPromotion: upsertPromotionMutation,
    isCreating: loadingStates.creating,
    isUpdating: loadingStates.updating,
    isDeleting: loadingStates.deleting,
    isUpserting: loadingStates.upserting,
  };
}
