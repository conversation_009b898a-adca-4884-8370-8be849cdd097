'use client';

import { useMemo, useState } from 'react';
import { useBoolean } from 'minimal-shared/hooks';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Table from '@mui/material/Table';
import Button from '@mui/material/Button';
import { TextField } from '@mui/material';
import Tooltip from '@mui/material/Tooltip';
import TableRow from '@mui/material/TableRow';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import { alpha, useTheme } from '@mui/material/styles';
import InputAdornment from '@mui/material/InputAdornment';
import TableContainer from '@mui/material/TableContainer';
import TablePagination from '@mui/material/TablePagination';
import CircularProgress from '@mui/material/CircularProgress';
import Checkbox from '@mui/material/Checkbox';
import Toolbar from '@mui/material/Toolbar';

import { useChatbotFaqs, removeFaqsFromChatbot, invalidateChatbotFaqsCache } from 'src/actions/mooly-chatbot/chatbot-faq-service';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';
import { EmptyContent } from 'src/components/empty-content';
import { ConfirmDialog } from 'src/components/custom-dialog';

import { useAuthContext } from 'src/auth/hooks';

import ChatbotFaqAddDialog from './chatbot-faq-add-dialog';
import ChatbotFaqEditDialog from './chatbot-faq-edit-dialog';

// ----------------------------------------------------------------------

export default function ChatbotFaqsTab({ chatbot }) {
  const theme = useTheme();
  const { user } = useAuthContext();

  const [searchQuery, setSearchQuery] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [selectedFaqId, setSelectedFaqId] = useState(null);
  const [selectedFaqs, setSelectedFaqs] = useState([]);
  const [editingFaq, setEditingFaq] = useState(null);
  const [isUpdating, setIsUpdating] = useState(false);

  const addDialog = useBoolean();
  const editDialog = useBoolean();
  const removeDialog = useBoolean();
  const bulkRemoveDialog = useBoolean();

  // Lấy danh sách FAQs của chatbot
  const {
    faqs,
    isLoading,
    error,
    mutate: refreshFaqs,
  } = useChatbotFaqs(chatbot?.id);

  // Xử lý khi thay đổi trang
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
    setSelectedFaqs([]); // Clear selection khi chuyển trang
  };

  // Xử lý khi thay đổi số hàng mỗi trang
  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
    setSelectedFaqs([]); // Clear selection khi thay đổi rows per page
  };

  // Xử lý khi thay đổi truy vấn tìm kiếm
  const handleSearchChange = (event) => {
    setSearchQuery(event.target.value);
    setPage(0);
    setSelectedFaqs([]); // Clear selection khi tìm kiếm
  };

  // Xử lý khi thêm Chủ Đề vào chatbot
  const handleAddFaqSuccess = () => { 
    refreshFaqs();
    invalidateChatbotFaqsCache(chatbot?.id);
    addDialog.onFalse();
  };

  // Xử lý khi mở dialog sửa Chủ Đề
  const handleOpenEditDialog = (faq) => {
    setEditingFaq(faq);
    editDialog.onTrue();
  };

  // Xử lý khi sửa Chủ Đề thành công
  const handleEditFaqSuccess = () => {
    refreshFaqs();
    invalidateChatbotFaqsCache(chatbot?.id);
    editDialog.onFalse();
    setEditingFaq(null);
  };

  // Xử lý khi mở dialog xóa Chủ Đề đơn lẻ
  const handleOpenRemoveDialog = (faqId) => {
    setSelectedFaqId(faqId);
    removeDialog.onTrue();
  };

  // Xử lý khi xóa Chủ Đề đơn lẻ khỏi chatbot
  const handleRemoveFaq = async () => {
    if (!selectedFaqId || !chatbot?.id) return;

    try {
      setIsUpdating(true);

      // Sử dụng service để xóa Chủ Đề khỏi chatbot
      const result = await removeFaqsFromChatbot(
        chatbot.id,
        [selectedFaqId],
        user?.app_metadata?.tenant_id
      );

      refreshFaqs();
      invalidateChatbotFaqsCache(chatbot?.id);
      
      if (result.count > 0) {
        toast.success('Đã xóa Chủ Đề khỏi chatbot thành công');
      }
    } catch (err) {
      console.error('Error removing Chủ Đề from chatbot:', err);
      toast.error(err.message || 'Có lỗi xảy ra khi xóa Chủ Đề khỏi chatbot');
    } finally {
      setIsUpdating(false);
      removeDialog.onFalse();
      setSelectedFaqId(null);
    }
  };

  // Xử lý select/deselect một Chủ Đề
  const handleSelectFaq = (faqId) => {
    setSelectedFaqs((prev) => {
      if (prev.includes(faqId)) {
        return prev.filter((id) => id !== faqId);
      }
      return [...prev, faqId];
    });
  };

  // Xử lý select/deselect tất cả Chủ Đề trên trang hiện tại
  const handleSelectAllFaqs = (checked) => {
    if (checked) {
      const currentPageFaqIds = paginatedFaqs?.map((faq) => faq.id) || [];
      setSelectedFaqs((prev) => {
        const newSelected = [...prev];
        currentPageFaqIds.forEach((id) => {
          if (!newSelected.includes(id)) {
            newSelected.push(id);
          }
        });
        return newSelected;
      });
    } else {
      const currentPageFaqIds = paginatedFaqs?.map((faq) => faq.id) || [];
      setSelectedFaqs((prev) => prev.filter((id) => !currentPageFaqIds.includes(id)));
    }
  };

  // Xử lý xóa nhiều Chủ Đề cùng lúc
  const handleBulkRemoveFaqs = async () => {
    if (!selectedFaqs.length || !chatbot?.id) return;

    try {
      setIsUpdating(true);

      // Sử dụng service để xóa nhiều Chủ Đề cùng lúc
      const result = await removeFaqsFromChatbot(
        chatbot.id,
        selectedFaqs,
        user?.app_metadata?.tenant_id
      );

      refreshFaqs();
      invalidateChatbotFaqsCache(chatbot?.id);
      setSelectedFaqs([]); // Clear selection
      
      if (result.count > 0) {
        toast.success(`Đã xóa ${result.count} Chủ Đề khỏi chatbot thành công`);
      }
    } catch (err) {
      console.error('Error removing Chủ Đề from chatbot:', err);
      toast.error(err.message || 'Có lỗi xảy ra khi xóa Chủ Đề khỏi chatbot');
    } finally {
      setIsUpdating(false);
      bulkRemoveDialog.onFalse();
    }
  };

  // Lọc Chủ Đề theo từ khóa tìm kiếm
  const filteredFaqs = faqs?.filter((faq) => {
    const query = searchQuery.toLowerCase();
    return (
      faq.topic.toLowerCase().includes(query) ||
      (faq.content && faq.content.toLowerCase().includes(query))
    );
  });

  // Phân trang Chủ Đề
  const paginatedFaqs = useMemo(() => {
    const startIndex = page * rowsPerPage;
    return filteredFaqs?.slice(startIndex, startIndex + rowsPerPage) || [];
  }, [filteredFaqs, page, rowsPerPage]);

  // Kiểm tra tất cả Chủ Đề trên trang hiện tại có được chọn không
  const currentPageFaqIds = paginatedFaqs?.map((faq) => faq.id) || [];
  const isAllCurrentPageSelected = currentPageFaqIds.length > 0 && 
    currentPageFaqIds.every((id) => selectedFaqs.includes(id));
  const isIndeterminate = currentPageFaqIds.some((id) => selectedFaqs.includes(id)) && 
    !isAllCurrentPageSelected;

  // Hiển thị loading khi đang tải dữ liệu
  if (isLoading) {
    return (
      <Box sx={{ py: 10, textAlign: 'center' }}>
        <CircularProgress />
      </Box>
    );
  }

  // Hiển thị thông báo lỗi nếu có
  if (error) {
    return (
      <Box sx={{ py: 10, textAlign: 'center' }}>
        <Typography variant="h6" color="error" paragraph>
          Có lỗi xảy ra khi tải dữ liệu Chủ Đề
        </Typography>
        <Button variant="contained" onClick={refreshFaqs}>
          Thử lại
        </Button>
      </Box>
    );
  }

  return (
    <>
      <Stack spacing={3}>
        {/* Header với search và actions */}
        <Stack direction="row" alignItems="center" justifyContent="space-between">
          <TextField
            placeholder="Tìm kiếm Chủ Đề..."
            value={searchQuery}
            onChange={handleSearchChange}
            sx={{ minWidth: 320 }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Iconify icon="eva:search-fill" sx={{ color: 'text.disabled' }} />
                </InputAdornment>
              ),
            }}
          />

          <Button
            variant="contained"
            startIcon={<Iconify icon="mingcute:add-line" />}
            onClick={addDialog.onTrue}
          >
            Thêm Chủ Đề
          </Button>
        </Stack>

        {/* Bulk actions toolbar */}
        {selectedFaqs.length > 0 && (
          <Toolbar
            sx={{
              pl: 2,
              pr: 1,
              py: 1,
              color: 'primary.main',
              bgcolor: alpha(theme.palette.primary.main, 0.08),
              borderRadius: 1,
            }}
          >
            <Typography variant="subtitle1" component="div" sx={{ flex: '1 1 100%' }}>
              Đã chọn {selectedFaqs.length} Chủ Đề(s)
            </Typography>

            <Tooltip title="Xóa các Chủ Đề đã chọn">
              <IconButton color="primary" onClick={bulkRemoveDialog.onTrue}>
                <Iconify icon="solar:trash-bin-trash-bold" />
              </IconButton>
            </Tooltip>
          </Toolbar>
        )}

        {/* Bảng Chủ Đề */}
        <Card>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell padding="checkbox">
                    <Checkbox
                      indeterminate={isIndeterminate}
                      checked={isAllCurrentPageSelected}
                      onChange={(event) => handleSelectAllFaqs(event.target.checked)}
                    />
                  </TableCell>
                  <TableCell>Chủ đề</TableCell>
                  <TableCell>Nội dung</TableCell>
                  <TableCell align="center">Ngày tạo</TableCell>
                  <TableCell align="right">Hành động</TableCell>
                </TableRow>
              </TableHead>

              <TableBody>
                {paginatedFaqs.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={5}>
                      <EmptyContent
                        filled
                        title="Không có Chủ Đề nào"
                        description="Chưa có Chủ Đề nào được thêm vào chatbot này"
                        sx={{ py: 10 }}
                      />
                    </TableCell>
                  </TableRow>
                )}

                {paginatedFaqs.map((faq) => (
                  <TableRow key={faq.id} hover>
                    <TableCell padding="checkbox">
                      <Checkbox
                        checked={selectedFaqs.includes(faq.id)}
                        onChange={() => handleSelectFaq(faq.id)}
                      />
                    </TableCell>

                    <TableCell>
                      <Typography variant="subtitle2" noWrap>
                        {faq.topic}
                      </Typography>
                    </TableCell>

                    <TableCell>
                      <Typography 
                        variant="body2" 
                        sx={{ 
                          maxWidth: 300,
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap'
                        }}
                      >
                        {faq.content}
                      </Typography>
                    </TableCell>

                    <TableCell align="center">
                      <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                        {new Date(faq.createdAt).toLocaleDateString('vi-VN')}
                      </Typography>
                    </TableCell>

                    <TableCell align="right">
                      <Stack direction="row" justifyContent="flex-end" spacing={0.5}>
                        <Tooltip title="Chỉnh sửa Chủ Đề">
                          <IconButton
                            color="primary"
                            onClick={() => handleOpenEditDialog(faq)}
                            disabled={isUpdating}
                          >
                            <Iconify icon="solar:pen-bold" />
                          </IconButton>
                        </Tooltip>
                        
                        <Tooltip title="Xóa Chủ Đề">
                          <IconButton
                            color="error"
                            onClick={() => handleOpenRemoveDialog(faq.id)}
                            disabled={isUpdating}
                          >
                            <Iconify icon="solar:trash-bin-trash-bold" />
                          </IconButton>
                        </Tooltip>
                      </Stack>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          <TablePagination
            component="div"
            count={filteredFaqs?.length || 0}
            page={page}
            onPageChange={handleChangePage}
            rowsPerPage={rowsPerPage}
            onRowsPerPageChange={handleChangeRowsPerPage}
            rowsPerPageOptions={[10, 25, 50]}
            labelRowsPerPage="Số hàng mỗi trang:"
            labelDisplayedRows={({ from, to, count }) => 
              `${from}-${to} của ${count !== -1 ? count : `hơn ${to}`}`
            }
          />
        </Card>
      </Stack>

      {/* Dialog thêm Chủ Đề */}
      <ChatbotFaqAddDialog
        open={addDialog.value}
        onClose={addDialog.onFalse}
        chatbot={chatbot}
        onSuccess={handleAddFaqSuccess}
      />

      {/* Dialog sửa Chủ Đề */}
      <ChatbotFaqEditDialog
        open={editDialog.value}
        onClose={editDialog.onFalse}
        chatbot={chatbot}
        faq={editingFaq}
        onSuccess={handleEditFaqSuccess}
      />

      {/* Dialog xác nhận xóa Chủ Đề đơn lẻ */}
      <ConfirmDialog
        open={removeDialog.value}
        onClose={removeDialog.onFalse}
        title="Xóa Chủ Đề"
        content="Bạn có chắc chắn muốn xóa Chủ Đề này khỏi chatbot không?"
        action={
          <Button
            variant="contained"
            color="error"
            onClick={handleRemoveFaq}
            disabled={isUpdating}
          >
            {isUpdating ? <CircularProgress size={20} /> : 'Xóa'}
          </Button>
        }
      />

      {/* Dialog xác nhận xóa nhiều Chủ Đề */}
      <ConfirmDialog
        open={bulkRemoveDialog.value}
        onClose={bulkRemoveDialog.onFalse}
        title="Xóa Chủ Đề"
        content={
          <Stack spacing={2}>
            <Typography>
              Bạn có chắc chắn muốn xóa {selectedFaqs.length} Chủ Đề(s) đã chọn khỏi chatbot không?
            </Typography>
            <Typography variant="body2" sx={{ color: 'warning.main' }}>
              ⚠️ Hành động này không thể hoàn tác.
            </Typography>
          </Stack>
        }
        action={
          <Button
            variant="contained"
            color="error"
            onClick={handleBulkRemoveFaqs}
            disabled={isUpdating}
          >
            {isUpdating ? <CircularProgress size={20} /> : `Xóa ${selectedFaqs.length} Chủ Đề(s)`}
          </Button>
        }
      />
    </>
  );
} 