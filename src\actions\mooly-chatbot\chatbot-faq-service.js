'use client';

import useSWR from 'swr';
import { fetchData, createData, updateData, deleteData } from './supabase-utils';

// =====================================================
// CHATBOT FAQs SERVICE
// =====================================================

/**
 * Hook để lấy danh sách FAQs của chatbot
 * @param {string} chatbotId - ID của chatbot
 * @returns {Object} - Object chứa FAQs, loading state, error, và mutate function
 */
export function useChatbotFaqs(chatbotId) {
  const shouldFetch = !!chatbotId;

  const {
    data: faqs = [],
    error,
    isLoading,
    mutate,
  } = useSWR(
    shouldFetch ? ['chatbot-faqs', chatbotId] : null,
    async () => {
      if (!chatbotId) return [];

      const result = await fetchData('chatbot_faqs', {
        filters: {
          chatbotId,
        },
        orderBy: 'createdAt',
        ascending: false,
      });

      if (!result.success) {
        throw new Error(result.error?.message || 'Không thể tải danh sách FAQs');
      }

      return result.data || [];
    },
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: true,
      dedupingInterval: 30000, // 30 giây
    }
  );

  return {
    faqs,
    isLoading,
    error,
    mutate,
  };
}

/**
 * Thêm FAQs mới vào chatbot
 * @param {string} chatbotId - ID của chatbot
 * @param {Array} faqsData - Mảng các FAQ cần thêm
 * @param {string} tenantId - ID của tenant
 * @returns {Promise<Object>} - Kết quả thao tác
 */
export async function addFaqsToChatbot(chatbotId, faqsData, tenantId) {
  try {
    if (!chatbotId || !faqsData || !Array.isArray(faqsData) || faqsData.length === 0) {
      throw new Error('Dữ liệu FAQs không hợp lệ');
    }

    if (!tenantId) {
      throw new Error('Tenant ID là bắt buộc');
    }

    // 1. Lưu FAQs vào Supabase (loại bỏ isActive)
    const faqsToCreate = faqsData.map((faq) => ({
      chatbotId,
      topic: faq.topic,
      content: faq.content,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }));

    const createResult = await createData('chatbot_faqs', faqsToCreate);

    if (!createResult.success) {
      throw new Error(createResult.error?.message || 'Không thể lưu FAQs vào cơ sở dữ liệu');
    }

    const createdFaqs = createResult.data;

    // 2. Đồng bộ với Weaviate với supabase_id
    try {
      const weaviateFaqs = createdFaqs.map((faq) => ({
        topic: faq.topic,
        content: faq.content,
        bot_id: chatbotId,
        tenant_id: tenantId,
        supabase_id: faq.id, // Truyền supabase_id thay vì faq_id
      }));

      // Gọi API để đồng bộ với Weaviate
      const response = await fetch('/api/weaviate/faqs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ faqs: weaviateFaqs }),
      });

      if (!response.ok) {
        console.error('Lỗi khi đồng bộ FAQs với Weaviate:', await response.text());
        // Không throw error ở đây để không ảnh hưởng đến việc lưu vào Supabase
      }
    } catch (weaviateError) {
      console.error('Lỗi khi đồng bộ FAQs với Weaviate:', weaviateError);
      // Không throw error để việc lưu vào Supabase vẫn thành công
    }

    return {
      success: true,
      data: createdFaqs,
      count: createdFaqs.length,
    };
  } catch (error) {
    console.error('Error adding FAQs to chatbot:', error);
    throw error;
  }
}

/**
 * Cập nhật FAQ của chatbot
 * @param {string} faqId - ID của FAQ
 * @param {Object} faqUpdateData - Dữ liệu cần cập nhật
 * @param {string} tenantId - ID của tenant
 * @param {string} chatbotId - ID của chatbot
 * @returns {Promise<Object>} - Kết quả thao tác
 */
export async function updateChatbotFaq(faqId, faqUpdateData, tenantId, chatbotId) {
  try {
    if (!faqId || !faqUpdateData) {
      throw new Error('Dữ liệu cập nhật không hợp lệ');
    }

    // 1. Cập nhật trong Supabase
    const updatedData = {
      ...faqUpdateData,
      updatedAt: new Date().toISOString(),
    };

    const updateResult = await updateData('chatbot_faqs', updatedData, { id: faqId });

    if (!updateResult.success) {
      throw new Error(updateResult.error?.message || 'Không thể cập nhật FAQ');
    }

    const updatedFaq = updateResult.data?.[0];

    // 2. Đồng bộ với Weaviate sử dụng API mới
    if (updatedFaq && (faqUpdateData.topic || faqUpdateData.content)) {
      try {
        const response = await fetch('/api/weaviate/faqs/update', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            supabase_id: faqId,
            tenant_id: tenantId,
            updateData: {
              topic: updatedFaq.topic,
              content: updatedFaq.content,
              bot_id: chatbotId,
            },
          }),
        });

        if (!response.ok) {
          console.error('Lỗi khi cập nhật FAQ trong Weaviate:', await response.text());
        }
      } catch (weaviateError) {
        console.error('Lỗi khi đồng bộ cập nhật FAQ với Weaviate:', weaviateError);
      }
    }

    return {
      success: true,
      data: updatedFaq,
    };
  } catch (error) {
    console.error('Error updating chatbot FAQ:', error);
    throw error;
  }
}

/**
 * Xóa FAQs khỏi chatbot
 * @param {string} chatbotId - ID của chatbot
 * @param {Array} faqIds - Mảng ID của các FAQ cần xóa
 * @param {string} tenantId - ID của tenant
 * @returns {Promise<Object>} - Kết quả thao tác
 */
export async function removeFaqsFromChatbot(chatbotId, faqIds, tenantId) {
  try {
    if (!chatbotId || !faqIds || !Array.isArray(faqIds) || faqIds.length === 0) {
      throw new Error('Dữ liệu xóa FAQ không hợp lệ');
    }

    if (!tenantId) {
      throw new Error('Tenant ID là bắt buộc');
    }

    // 1. Xóa khỏi Supabase
    const deleteResult = await deleteData('chatbot_faqs', {
      id: { operator: 'in', value: faqIds },
      chatbotId,
    });

    if (!deleteResult.success) {
      throw new Error(deleteResult.error?.message || 'Không thể xóa FAQs khỏi cơ sở dữ liệu');
    }

    // 2. Đồng bộ xóa khỏi Weaviate sử dụng API mới
    try {
      const response = await fetch('/api/weaviate/faqs/delete-many', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          supabase_ids: faqIds,
          tenant_id: tenantId,
        }),
      });

      if (!response.ok) {
        // Không throw error ở đây để không ảh hưởng đến việc xóa khỏi Supabase
      }
    } catch (weaviateError) {
      // Không throw error để việc xóa khỏi Supabase vẫn thành công
    }

    return {
      success: true,
      count: faqIds.length,
    };
  } catch (error) {
    console.error('Error removing FAQs from chatbot:', error);
    throw error;
  }
}

/**
 * Invalidate cache cho FAQs của chatbot
 * @param {string} chatbotId - ID của chatbot
 */
export function invalidateChatbotFaqsCache(chatbotId) {
  if (typeof window !== 'undefined' && window.mutate) {
    window.mutate(['chatbot-faqs', chatbotId]);
  }
}

/**
 * Toggle trạng thái active của FAQ
 * @param {string} faqId - ID của FAQ
 * @param {boolean} isActive - Trạng thái mới
 * @param {string} tenantId - ID của tenant
 * @returns {Promise<Object>} - Kết quả thao tác
 */
export async function toggleFaqStatus(faqId, isActive, tenantId) {
  try {
    return await updateChatbotFaq(faqId, { isActive }, tenantId);
  } catch (error) {
    console.error('Error toggling FAQ status:', error);
    throw error;
  }
} 