import { NextResponse } from 'next/server';
import { createClient } from 'src/utils/supabase/server';

/**
 * Helper function to exchange Facebook code for access token directly
 * This avoids internal API call SSL issues with ngrok
 */
async function exchangeTokenDirectly(code, redirectUri, userId, supabase) {
    try {
        console.log('🔑 Exchanging code for access token directly...');
        console.log('📝 Token exchange parameters:', {
            redirectUri,
            codeLength: code.length,
            appId: process.env.NEXT_PUBLIC_FACEBOOK_APP_ID ? 'present' : 'missing',
            appSecret: process.env.FACEBOOK_APP_SECRET ? 'present' : 'missing'
        });

        // Validate environment variables
        const appId = process.env.NEXT_PUBLIC_FACEBOOK_APP_ID;
        const appSecret = process.env.FACEBOOK_APP_SECRET;

        console.log('🔐 Environment check:', {
            appId: appId ? `${appId.substring(0, 8)}...` : 'MISSING',
            appSecret: appSecret ? 'present' : 'MISSING',
            nodeEnv: process.env.NODE_ENV
        });

        if (!appId || !appSecret) {
            console.error('❌ Missing Facebook app credentials:', {
                appId: !!appId,
                appSecret: !!appSecret
            });
            return {
                success: false,
                error: { message: 'Facebook app not configured properly' }
            };
        }

        // Exchange code for access token
        const tokenParams = {
            client_id: appId,
            client_secret: appSecret,
            redirect_uri: redirectUri,
            code: code
        };
        
        console.log('🚀 Sending token exchange request with params:', {
            client_id: appId,
            redirect_uri: redirectUri,
            code: `${code.substring(0, 20)}...`
        });

        const tokenResponse = await fetch('https://graph.facebook.com/v19.0/oauth/access_token', {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: new URLSearchParams(tokenParams)
        });

        const tokenData = await tokenResponse.json();
        
        console.log('📋 Facebook token response status:', tokenResponse.status);
        console.log('📋 Facebook token response:', {
            hasAccessToken: !!tokenData.access_token,
            hasError: !!tokenData.error,
            errorType: tokenData.error?.type,
            errorCode: tokenData.error?.code,
            errorSubcode: tokenData.error?.error_subcode
        });

        if (tokenData.error) {
            console.error('❌ Facebook token exchange error:', tokenData.error);
            
            // Special handling for redirect_uri mismatch
            if (tokenData.error.code === 100 && tokenData.error.error_subcode === 36008) {
                console.error('🚨 REDIRECT_URI MISMATCH - Facebook app settings issue');
                console.error('Expected redirect_uri in Facebook app settings:', redirectUri);
                console.error('');
                console.error('🔧 TO FIX THIS:');
                console.error('1. Go to https://developers.facebook.com/apps/');
                console.error('2. Select your Facebook app');
                console.error('3. Go to Facebook Login > Settings');
                console.error('4. Add this URL to "Valid OAuth Redirect URIs":');
                console.error(`   ${redirectUri}`);
                console.error('5. Save changes and try again');
                console.error('');
                
                return {
                    success: false,
                    error: {
                        message: `Cần cấu hình redirect URI trong Facebook App. Vui lòng thêm URL này vào Facebook App settings: ${redirectUri}`,
                        code: tokenData.error.code,
                        subcode: tokenData.error.error_subcode,
                        type: tokenData.error.type,
                        helpUrl: 'https://developers.facebook.com/apps/',
                        redirectUriNeeded: redirectUri
                    }
                };
            }
            
            return {
                success: false,
                error: {
                    message: `Facebook API error: ${tokenData.error.message}`,
                    code: tokenData.error.code,
                    subcode: tokenData.error.error_subcode,
                    type: tokenData.error.type
                }
            };
        }

        if (!tokenData.access_token) {
            console.error('❌ No access token received from Facebook');
            return {
                success: false,
                error: { message: 'No access token received from Facebook' }
            };
        }

        console.log('✅ Access token received, fetching user pages...');

        // Get user's Facebook pages
        const pagesResponse = await fetch(
            `https://graph.facebook.com/v19.0/me/accounts?access_token=${tokenData.access_token}&fields=name,id,access_token,instagram_business_account{id,name,username}`
        );

        const pagesData = await pagesResponse.json();

        if (pagesData.error) {
            console.error('❌ Error fetching Facebook pages:', pagesData.error);
            return {
                success: false,
                error: {
                    message: `Failed to fetch Facebook pages: ${pagesData.error.message}`,
                    code: pagesData.error.code
                }
            };
        }

        if (!pagesData.data || pagesData.data.length === 0) {
            return {
                success: false,
                error: { 
                    message: 'Không tìm thấy Facebook Page nào. Vui lòng đảm bảo bạn là admin của ít nhất một Page.' 
                }
            };
        }

        // Process and save Facebook accounts
        const accountsToSave = [];

        for (const page of pagesData.data) {
            const accountData = {
                pageId: page.id,
                pageName: page.name,
                accessToken: page.access_token, // Keep for backward compatibility
                pageAccessToken: page.access_token,
                userAccessToken: tokenData.access_token,
                tokenExpiresAt: tokenData.expires_in
                    ? new Date(Date.now() + tokenData.expires_in * 1000).toISOString()
                    : null,
                instagramAccountId: page.instagram_business_account?.id || null,
                instagramUsername: page.instagram_business_account?.username || null,
                isActive: true,
                connectedAt: new Date().toISOString(),
                lastSyncAt: new Date().toISOString()
            };

            // Save to database
            const saveResult = await saveFacebookAccountToDatabase(accountData, userId, supabase);
            if (saveResult.success) {
                accountsToSave.push(saveResult.data);
            } else {
                console.error(`❌ Failed to save page ${page.name}:`, saveResult.error);
            }
        }

        console.log(`✅ Successfully saved ${accountsToSave.length} Facebook accounts`);

        return {
            success: true,
            data: {
                accountsConnected: accountsToSave.length,
                totalPages: pagesData.data.length,
                accounts: accountsToSave,
                message: `Kết nối thành công ${accountsToSave.length} Facebook Page${accountsToSave.length > 1 ? 's' : ''}`
            }
        };

    } catch (error) {
        console.error('💥 Direct token exchange error:', error);
        return {
            success: false,
            error: { 
                message: 'Internal server error during token exchange',
                details: error.message 
            }
        };
    }
}

/**
 * Helper function to save Facebook account to database
 */
async function saveFacebookAccountToDatabase(accountData, userId, supabase) {
    try {
        // Check if account already exists
        const { data: existingAccount } = await supabase
            .from('facebook_accounts')
            .select('id')
            .eq('page_id', accountData.pageId)
            .single();

        if (existingAccount) {
            // Update existing account
            const { data, error } = await supabase
                .from('facebook_accounts')
                .update({
                    page_name: accountData.pageName,
                    access_token: accountData.accessToken, // Keep for backward compatibility
                    page_access_token: accountData.pageAccessToken,
                    user_access_token: accountData.userAccessToken,
                    token_expires_at: accountData.tokenExpiresAt,
                    instagram_account_id: accountData.instagramAccountId,
                    instagram_username: accountData.instagramUsername,
                    is_active: accountData.isActive,
                    last_sync_at: accountData.lastSyncAt,
                    updated_at: new Date().toISOString()
                })
                .eq('id', existingAccount.id)
                .select()
                .single();

            if (error) throw error;
            return { success: true, data };
        } else {
            // Create new account
            const { data, error } = await supabase
                .from('facebook_accounts')
                .insert({
                    page_id: accountData.pageId,
                    page_name: accountData.pageName,
                    access_token: accountData.accessToken, // Keep for backward compatibility
                    page_access_token: accountData.pageAccessToken,
                    user_access_token: accountData.userAccessToken,
                    token_expires_at: accountData.tokenExpiresAt,
                    instagram_account_id: accountData.instagramAccountId,
                    instagram_username: accountData.instagramUsername,
                    is_active: accountData.isActive,
                    connected_at: accountData.connectedAt,
                    last_sync_at: accountData.lastSyncAt
                })
                .select()
                .single();

            if (error) throw error;
            return { success: true, data };
        }
    } catch (error) {
        console.error('💥 Database save error:', error);
        return {
            success: false,
            error: { message: error.message }
        };
    }
}

/**
 * Facebook Integration Popup Callback Route
 * Handles the OAuth callback from Facebook popup and returns result to parent window
 */
export async function GET(request) {
    console.log('🔗 Facebook Integration Popup Callback hit');
    
    try {
        const { searchParams } = new URL(request.url);
        const code = searchParams.get('code');
        const state = searchParams.get('state');
        const error = searchParams.get('error');
        const errorDescription = searchParams.get('error_description');

        // Handle Facebook OAuth errors
        if (error) {
            console.error('❌ Facebook OAuth Error:', { error, errorDescription });
            
            // Return HTML page that communicates error to parent window
            const errorHtml = `
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Facebook Connection Error</title>
                    <style>
                        body { 
                            font-family: Arial, sans-serif; 
                            text-align: center; 
                            padding: 50px;
                            background: #f5f5f5;
                        }
                        .error { 
                            color: #d32f2f; 
                            margin: 20px 0;
                        }
                        .close-btn {
                            background: #1976d2;
                            color: white;
                            border: none;
                            padding: 10px 20px;
                            border-radius: 4px;
                            cursor: pointer;
                            margin-top: 20px;
                        }
                    </style>
                </head>
                <body>
                    <h2>Lỗi kết nối Facebook</h2>
                    <div class="error">${errorDescription || error || 'Unknown error'}</div>
                    <button class="close-btn" onclick="closeWindow()">Đóng cửa sổ</button>
                    
                    <script>
                        function closeWindow() {
                            // Signal error to parent window
                            if (window.opener) {
                                try {
                                    // Try to call parent callback if available
                                    if (window.opener.facebookAuthCallback) {
                                        window.opener.facebookAuthCallback({
                                            success: false,
                                            message: '${encodeURIComponent(errorDescription || 'Facebook authentication failed')}'
                                        });
                                        window.close();
                                        return;
                                    }
                                } catch (e) {
                                    console.log('Parent callback not available, using URL method');
                                }
                                
                                // Fallback: Set URL parameters and reload
                                const url = new URL(window.location.href);
                                url.searchParams.set('error', '${error}');
                                url.searchParams.set('message', '${encodeURIComponent(errorDescription || 'Facebook authentication failed')}');
                                window.location.href = url.toString();
                            } else {
                                window.close();
                            }
                        }
                        
                        // Auto-close and signal error after 3 seconds
                        setTimeout(closeWindow, 3000);
                    </script>
                </body>
                </html>
            `;
            
            return new Response(errorHtml, {
                headers: { 'Content-Type': 'text/html' }
            });
        }

        // Validate required parameters
        if (!code) {
            console.error('❌ Missing authorization code from Facebook');
            
            const errorHtml = `
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Facebook Connection Error</title>
                    <style>
                        body { 
                            font-family: Arial, sans-serif; 
                            text-align: center; 
                            padding: 50px;
                            background: #f5f5f5;
                        }
                        .error { color: #d32f2f; margin: 20px 0; }
                    </style>
                </head>
                <body>
                    <h2>Lỗi kết nối Facebook</h2>
                    <div class="error">Không nhận được mã xác thực từ Facebook</div>
                    
                    <script>
                        // Signal error to parent window
                        if (window.opener) {
                            try {
                                if (window.opener.facebookAuthCallback) {
                                    window.opener.facebookAuthCallback({
                                        success: false,
                                        message: 'Authorization code not received from Facebook'
                                    });
                                    window.close();
                                } else {
                                    throw new Error('Callback not available');
                                }
                            } catch (e) {
                                const url = new URL(window.location.href);
                                url.searchParams.set('error', 'missing_code');
                                url.searchParams.set('message', '${encodeURIComponent('Authorization code not received from Facebook')}');
                                window.location.href = url.toString();
                            }
                        } else {
                            window.close();
                        }
                    </script>
                </body>
                </html>
            `;
            
            return new Response(errorHtml, {
                headers: { 'Content-Type': 'text/html' }
            });
        }

        console.log('✅ Facebook callback received:', { 
            code: code ? 'present' : 'missing',
            state: state || 'none'
        });

        // Get Supabase client for server-side operations
        const supabase = await createClient();
        
        // Get authenticated user (required for tenant isolation)
        const { data: { user }, error: userError } = await supabase.auth.getUser();
        
        if (userError || !user) {
            console.error('❌ User not authenticated:', userError);
            
            const errorHtml = `
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Authentication Required</title>
                    <style>
                        body { 
                            font-family: Arial, sans-serif; 
                            text-align: center; 
                            padding: 50px;
                            background: #f5f5f5;
                        }
                        .error { color: #d32f2f; margin: 20px 0; }
                    </style>
                </head>
                <body>
                    <h2>Yêu cầu đăng nhập</h2>
                    <div class="error">Vui lòng đăng nhập trước khi kết nối Facebook</div>
                    
                    <script>
                        // Signal error to parent window
                        const url = new URL(window.location.href);
                        url.searchParams.set('error', 'authentication_required');
                        url.searchParams.set('message', '${encodeURIComponent('Please login first to connect Facebook')}');
                        window.location.href = url.toString();
                    </script>
                </body>
                </html>
            `;
            
            return new Response(errorHtml, {
                headers: { 'Content-Type': 'text/html' }
            });
        }

        // Exchange authorization code for access token directly (avoid internal API call SSL issue)
        // IMPORTANT: Use exact same redirect_uri as in OAuth dialog
        const baseUrl = process.env.NEXTAUTH_URL || process.env.NEXT_PUBLIC_PUBLIC_SITE_URL || new URL(request.url).origin;
        const exactRedirectUri = `${baseUrl}/api/facebook-integration/popup-callback`;
        console.log('🔄 Using redirect_uri:', exactRedirectUri);
        console.log('🔄 Base URL from env:', baseUrl);
        
        const exchangeResult = await exchangeTokenDirectly(code, exactRedirectUri, user.id, supabase);

        if (!exchangeResult.success) {
            console.error('❌ Token exchange failed:', exchangeResult.error);
            
            const errorHtml = `
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Token Exchange Failed</title>
                    <style>
                        body { 
                            font-family: Arial, sans-serif; 
                            text-align: center; 
                            padding: 50px;
                            background: #f5f5f5;
                        }
                        .error { color: #d32f2f; margin: 20px 0; }
                    </style>
                </head>
                <body>
                    <h2>Lỗi xử lý token</h2>
                    <div class="error">${exchangeResult.error?.message || 'Không thể xử lý token từ Facebook'}</div>
                    
                    <script>
                        // Signal error to parent window
                        const url = new URL(window.location.href);
                        url.searchParams.set('error', 'token_exchange_failed');
                        url.searchParams.set('message', '${encodeURIComponent(exchangeResult.error?.message || 'Failed to exchange Facebook code for token')}');
                        window.location.href = url.toString();
                    </script>
                </body>
                </html>
            `;
            
            return new Response(errorHtml, {
                headers: { 'Content-Type': 'text/html' }
            });
        }

        console.log('✅ Facebook token exchange successful');

        // Return success HTML that signals to parent window
        const successHtml = `
            <!DOCTYPE html>
            <html>
            <head>
                <title>Facebook Connected Successfully</title>
                <style>
                    body { 
                        font-family: Arial, sans-serif; 
                        text-align: center; 
                        padding: 50px;
                        background: #e8f5e8;
                    }
                    .success { 
                        color: #2e7d32; 
                        margin: 20px 0;
                    }
                    .loading {
                        margin: 20px 0;
                    }
                    .spinner {
                        border: 4px solid #f3f3f3;
                        border-top: 4px solid #1976d2;
                        border-radius: 50%;
                        width: 40px;
                        height: 40px;
                        animation: spin 2s linear infinite;
                        margin: 0 auto;
                    }
                    @keyframes spin {
                        0% { transform: rotate(0deg); }
                        100% { transform: rotate(360deg); }
                    }
                </style>
            </head>
            <body>
                <h2>✅ Kết nối Facebook thành công!</h2>
                <div class="success">
                    Đã kết nối ${exchangeResult.data?.accountsConnected || 0} Facebook Page.
                </div>
                <div class="loading">
                    <div class="spinner"></div>
                    <p>Đang đóng cửa sổ...</p>
                </div>
                
                <script>
                    // Signal success to parent window and auto-close
                    if (window.opener) {
                        try {
                            // Try to call parent callback if available
                            if (window.opener.facebookAuthCallback) {
                                window.opener.facebookAuthCallback({
                                    success: true,
                                    message: 'Kết nối thành công ${exchangeResult.data?.accountsConnected || 0} Facebook Page',
                                    data: ${JSON.stringify(exchangeResult.data)}
                                });
                            }
                        } catch (e) {
                            console.log('Parent callback not available, using URL method');
                        }
                        
                        // Fallback: Set URL parameters and reload
                        const url = new URL(window.location.href);
                        url.searchParams.set('success', 'true');
                        url.searchParams.set('message', '${encodeURIComponent(`Kết nối thành công ${exchangeResult.data?.accountsConnected || 0} Facebook Page`)}');
                        window.location.href = url.toString();
                    } else {
                        // No parent, just close
                        window.close();
                    }
                </script>
            </body>
            </html>
        `;

        return new Response(successHtml, {
            headers: { 'Content-Type': 'text/html' }
        });

    } catch (error) {
        console.error('💥 Facebook Integration Popup Callback Error:', error);
        
        const errorHtml = `
            <!DOCTYPE html>
            <html>
            <head>
                <title>Unexpected Error</title>
                <style>
                    body { 
                        font-family: Arial, sans-serif; 
                        text-align: center; 
                        padding: 50px;
                        background: #f5f5f5;
                    }
                    .error { color: #d32f2f; margin: 20px 0; }
                </style>
            </head>
            <body>
                <h2>Lỗi không mong muốn</h2>
                <div class="error">Đã xảy ra lỗi trong quá trình xác thực Facebook</div>
                
                <script>
                    // Signal error to parent window
                    const url = new URL(window.location.href);
                    url.searchParams.set('error', 'callback_error');
                    url.searchParams.set('message', '${encodeURIComponent('An unexpected error occurred during Facebook authentication')}');
                    window.location.href = url.toString();
                </script>
            </body>
            </html>
        `;
        
        return new Response(errorHtml, {
            headers: { 'Content-Type': 'text/html' }
        });
    }
} 