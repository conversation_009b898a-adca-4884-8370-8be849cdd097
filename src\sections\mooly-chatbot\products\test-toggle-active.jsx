'use client';

import { useState } from 'react';
import { Button, Box, Typography, Alert } from '@mui/material';
import { updateData } from 'src/actions/mooly-chatbot/supabase-utils';

/**
 * Component test để kiểm tra logic cập nhật trạng thái sản phẩm
 */
export default function TestToggleActive() {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState(null);
  const [error, setError] = useState(null);

  // Test product ID (sử dụng một ID có sẵn từ database)
  const testProductId = '69691e7f-29dd-4a76-b983-0f90811677bc';

  const handleTestToggle = async (newActiveState) => {
    setIsLoading(true);
    setError(null);
    setResult(null);

    try {
      console.log('Testing toggle active with:', {
        productId: testProductId,
        newActiveState
      });

      const updateResult = await updateData(
        'products',
        { isActive: newActiveState },
        { id: testProductId },
        true
      );

      console.log('Update result:', updateResult);

      if (updateResult.success) {
        setResult(`Cập nhật thành công! Trạng thái mới: ${newActiveState}`);
      } else {
        setError(`Lỗi: ${updateResult.error?.message || 'Không thể cập nhật'}`);
      }
    } catch (err) {
      console.error('Test error:', err);
      setError(`Exception: ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Box sx={{ p: 3, maxWidth: 600 }}>
      <Typography variant="h6" gutterBottom>
        Test Toggle Active Product
      </Typography>
      
      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
        Product ID: {testProductId}
      </Typography>

      <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
        <Button
          variant="contained"
          color="success"
          onClick={() => handleTestToggle(true)}
          disabled={isLoading}
        >
          Kích hoạt (true)
        </Button>
        
        <Button
          variant="contained"
          color="error"
          onClick={() => handleTestToggle(false)}
          disabled={isLoading}
        >
          Vô hiệu hóa (false)
        </Button>
      </Box>

      {isLoading && (
        <Alert severity="info">
          Đang thực hiện cập nhật...
        </Alert>
      )}

      {result && (
        <Alert severity="success">
          {result}
        </Alert>
      )}

      {error && (
        <Alert severity="error">
          {error}
        </Alert>
      )}
    </Box>
  );
}
