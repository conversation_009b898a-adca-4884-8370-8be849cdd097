'use client';

import { useState, useEffect, useCallback } from 'react';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Switch from '@mui/material/Switch';
import Slider from '@mui/material/Slider';
import Button from '@mui/material/Button';
import Divider from '@mui/material/Divider';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import FormGroup from '@mui/material/FormGroup';
import InputLabel from '@mui/material/InputLabel';
import LoadingButton from '@mui/lab/LoadingButton';
import FormControlLabel from '@mui/material/FormControlLabel';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';

import { useChatbotMutations, getAvailableTools } from 'src/actions/mooly-chatbot/chatbot-service';

import ProductCardConfigDialog from './components/product-card-config-dialog';

// ----------------------------------------------------------------------

// Danh sách các tính năng mặc định (fallback khi không load được từ DB)
const DEFAULT_FEATURES = [
  {
    id: 'product_card_display',
    label: 'Hiển thị sản phẩm dạng card',
    description: 'Gửi thông tin sản phẩm dưới dạng card đẹp mắt với nút tương tác',
    hasDetailConfig: true
  }
];

// ----------------------------------------------------------------------

export default function ChatbotGeneralConfigTab({ chatbot, onDataChange }) {
  const { updateChatbot, isUpdating } = useChatbotMutations();

  // States cho available features từ database
  const [availableFeatures, setAvailableFeatures] = useState(DEFAULT_FEATURES);
  const [isLoadingFeatures, setIsLoadingFeatures] = useState(true);

  // States cho delay time (mặc định bật, từ 0-30s)
  const [savedDelayTime, setSavedDelayTime] = useState(chatbot?.delayTime ?? 10);
  const [tempDelayTime, setTempDelayTime] = useState(savedDelayTime);
  const [isUpdatingDelayTime, setIsUpdatingDelayTime] = useState(false);

  // States cho enabled features
  const [enabledFeatures, setEnabledFeatures] = useState(
    chatbot?.enabledFeatures || availableFeatures.map(f => f.id)
  );
  const [isUpdatingFeatures, setIsUpdatingFeatures] = useState(false);

  // State cho Product Card Config Dialog
  const [openProductCardDialog, setOpenProductCardDialog] = useState(false);

  // Load available features từ database
  useEffect(() => {
    const loadAvailableFeatures = async () => {
      setIsLoadingFeatures(true);
      try {
        const result = await getAvailableTools();
        if (result.success && result.data.length > 0) {
          // Thêm product card display vào danh sách từ DB
          const featuresFromDB = result.data;
          const allFeatures = [
            ...featuresFromDB,
            {
              id: 'product_card_display',
              label: 'Hiển thị sản phẩm dạng card',
              description: 'Gửi thông tin sản phẩm dưới dạng card đẹp mắt với nút tương tác',
              hasDetailConfig: true
            }
          ];
          setAvailableFeatures(allFeatures);
        } else {
          // Fallback to default features nếu không load được từ DB
          setAvailableFeatures(DEFAULT_FEATURES);
        }
      } catch (error) {
        console.error('Error loading available features:', error);
        setAvailableFeatures(DEFAULT_FEATURES);
      } finally {
        setIsLoadingFeatures(false);
      }
    };

    loadAvailableFeatures();
  }, []);

  // Cập nhật states khi chatbot thay đổi từ parent
  useEffect(() => {
    if (chatbot) {
      const newDelayTime = chatbot.delayTime ?? 10;
      const newEnabledFeatures = chatbot.enabledFeatures || availableFeatures.map(f => f.id);

      setSavedDelayTime(newDelayTime);
      setTempDelayTime(newDelayTime);
      setEnabledFeatures(newEnabledFeatures);
    }
  }, [chatbot?.delayTime, chatbot?.enabledFeatures, chatbot?.id, availableFeatures]); // Added availableFeatures dependency

  // Xử lý thay đổi delay time slider
  const handleDelayTimeChange = (event, newValue) => {
    setTempDelayTime(newValue);
  };

  // Xử lý thay đổi manual input
  const handleDelayTimeInputChange = (event) => {
    const value = Number(event.target.value);
    if (value >= 0 && value <= 30) {
      setTempDelayTime(value);
    }
  };

  // ✅ AUTO-SAVE: Xử lý toggle feature với auto-save
  const handleFeatureToggle = useCallback(async (featureId) => {
    const newEnabledFeatures = enabledFeatures.includes(featureId)
      ? enabledFeatures.filter(id => id !== featureId)
      : [...enabledFeatures, featureId];

    // Optimistic update
    setEnabledFeatures(newEnabledFeatures);
    setIsUpdatingFeatures(true);

    try {
      const updateData = { enabledFeatures: newEnabledFeatures };

      const result = await updateChatbot(chatbot.id, updateData);

      if (result.success) {
        // Call parent callback to refresh data
        if (onDataChange) {
          onDataChange();
        }

        const featureName = availableFeatures.find(f => f.id === featureId)?.label || featureId;
        const action = newEnabledFeatures.includes(featureId) ? 'kích hoạt' : 'tắt';
        toast.success(`Đã ${action} tính năng "${featureName}"`);
      } else {
        // Revert optimistic update on error
        setEnabledFeatures(enabledFeatures);
        toast.error(result.error?.message || 'Có lỗi xảy ra khi cập nhật tính năng');
      }
    } catch (error) {
      // Revert optimistic update on error
      setEnabledFeatures(enabledFeatures);
      console.error('Error updating feature:', error);
      toast.error('Có lỗi xảy ra khi cập nhật tính năng');
    } finally {
      setIsUpdatingFeatures(false);
    }
  }, [chatbot?.id, enabledFeatures, updateChatbot, onDataChange, availableFeatures]);

  // Xử lý toggle tất cả features
  const handleToggleAll = useCallback(async () => {
    const newEnabledFeatures = enabledFeatures.length === availableFeatures.length
      ? []
      : availableFeatures.map(f => f.id);

    setEnabledFeatures(newEnabledFeatures);
    setIsUpdatingFeatures(true);

    try {
      const updateData = { enabledFeatures: newEnabledFeatures };

      const result = await updateChatbot(chatbot.id, updateData);

      if (result.success) {
        if (onDataChange) {
          onDataChange();
        }

        const action = newEnabledFeatures.length === 0 ? 'tắt tất cả' : 'kích hoạt tất cả';
        toast.success(`Đã ${action} tính năng`);
      } else {
        setEnabledFeatures(enabledFeatures);
        toast.error(result.error?.message || 'Có lỗi xảy ra khi cập nhật tính năng');
      }
    } catch (error) {
      setEnabledFeatures(enabledFeatures);
      console.error('Error updating all features:', error);
      toast.error('Có lỗi xảy ra khi cập nhật tính năng');
    } finally {
      setIsUpdatingFeatures(false);
    }
  }, [chatbot?.id, enabledFeatures, updateChatbot, onDataChange, availableFeatures]);

  // ✅ SEPARATE SAVE: Xử lý lưu delay time riêng biệt
  const handleSaveDelayTime = useCallback(async () => {
    if (tempDelayTime === savedDelayTime) {
      toast.info('Không có thay đổi nào để lưu');
      return;
    }

    setIsUpdatingDelayTime(true);

    try {
      const result = await updateChatbot(chatbot.id, {
        delayTime: tempDelayTime
      });

      if (result.success) {
        setSavedDelayTime(tempDelayTime);
        if (onDataChange) {
          onDataChange();
        }
        toast.success(`Đã cập nhật thời gian delay thành ${tempDelayTime} giây`);
      } else {
        toast.error(result.error?.message || 'Có lỗi xảy ra khi cập nhật thời gian delay');
      }
    } catch (error) {
      console.error('Error updating delay time:', error);
      toast.error('Có lỗi xảy ra khi cập nhật thời gian delay');
    } finally {
      setIsUpdatingDelayTime(false);
    }
  }, [chatbot?.id, tempDelayTime, savedDelayTime, updateChatbot, onDataChange]);

  // Reset delay time về giá trị đã lưu
  const handleResetDelayTime = () => {
    setTempDelayTime(savedDelayTime);
  };

  // Kiểm tra có thay đổi delay time chưa lưu
  const hasDelayTimeChanges = tempDelayTime !== savedDelayTime;

  // Xử lý mở dialog cấu hình chi tiết
  const handleOpenDetailConfig = (featureId) => {
    if (featureId === 'product_card_display') {
      setOpenProductCardDialog(true);
    }
  };

  return (
    <Stack spacing={4}>
      {/* Delay Time Configuration */}
      <Card sx={{ p: 3 }}>
        <Stack spacing={3}>
          <Box>
            <Typography variant="h6" gutterBottom>
              ⚙️ Cấu hình thời gian phản hồi
            </Typography>
            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              Thiết lập thời gian delay để tạo trải nghiệm tự nhiên hơn. Đặt 0 giây để phản hồi ngay lập tức.
            </Typography>
          </Box>

          <Divider />

          <Box sx={{
            p: 3,
            bgcolor: 'background.default',
            borderRadius: 1.5,
            border: '1px solid',
            borderColor: hasDelayTimeChanges ? 'warning.main' : 'divider'
          }}>
            <Stack spacing={3}>
              <Stack direction="row" alignItems="center" justifyContent="space-between">
                <Typography variant="body2" sx={{ fontWeight: 600, color: 'primary.main' }}>
                  Thời gian delay hiện tại: {tempDelayTime} giây
                </Typography>
                {hasDelayTimeChanges && (
                  <Typography variant="caption" sx={{ color: 'warning.main', fontStyle: 'italic' }}>
                    * Có thay đổi chưa lưu
                  </Typography>
                )}
              </Stack>

              <Box>
                <InputLabel sx={{ mb: 1, fontSize: '0.875rem' }}>
                  Chọn thời gian delay (0 = phản hồi ngay lập tức)
                </InputLabel>
                <Slider
                  value={tempDelayTime}
                  onChange={handleDelayTimeChange}
                  min={0}
                  max={30}
                  step={1}
                  marks={[
                    { value: 0, label: '0s' },
                    { value: 10, label: '10s' },
                    { value: 20, label: '20s' },
                    { value: 30, label: '30s' }
                  ]}
                  size="small"
                  sx={{ mx: 1 }}
                  disabled={isUpdatingDelayTime}
                />
              </Box>

              <Stack direction="row" spacing={2} alignItems="center">
                <TextField
                  label="Thời gian (giây)"
                  type="number"
                  value={tempDelayTime}
                  onChange={handleDelayTimeInputChange}
                  slotProps={{
                    htmlInput: { min: 0, max: 30 }
                  }}
                  sx={{ width: 140 }}
                  size="small"
                  disabled={isUpdatingDelayTime}
                />
                <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                  Từ 0 đến 30 giây
                </Typography>
              </Stack>

              {/* ✅ DELAY TIME ACTIONS: Nút lưu riêng cho delay time */}
              {hasDelayTimeChanges && (
                <Stack direction="row" spacing={1.5} justifyContent="flex-end">
                  <Button
                    size="small"
                    variant="outlined"
                    onClick={handleResetDelayTime}
                    disabled={isUpdatingDelayTime}
                  >
                    Hủy
                  </Button>
                  <LoadingButton
                    size="small"
                    variant="contained"
                    onClick={handleSaveDelayTime}
                    loading={isUpdatingDelayTime}
                  >
                    Lưu thời gian delay
                  </LoadingButton>
                </Stack>
              )}
            </Stack>
          </Box>
        </Stack>
      </Card>

      {/* Feature Toggles */}
      {/* <Card sx={{ p: 3 }}>
        <Stack spacing={3}>
          <Box>
            <Stack direction="row" justifyContent="space-between" alignItems="center">
              <Box>
                <Typography variant="h6" gutterBottom>
                  Cấu hình tính năng chatbot
                </Typography>
                <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                  Chọn các tính năng mà chatbot có thể thực hiện. Thay đổi sẽ được lưu tự động.
                </Typography>
              </Box>

              <LoadingButton
                variant="outlined"
                size="small"
                onClick={handleToggleAll}
                loading={isUpdatingFeatures || isLoadingFeatures}
                disabled={isUpdating || isLoadingFeatures}
              >
                {enabledFeatures.length === availableFeatures.length ? 'Tắt tất cả' : 'Bật tất cả'}
              </LoadingButton>
            </Stack>
          </Box>

          <Divider />

          <FormGroup>
            <Stack spacing={2}>
              {isLoadingFeatures ? (
                <Typography variant="body2" sx={{ color: 'text.secondary', textAlign: 'center', py: 2 }}>
                  Đang tải danh sách tính năng...
                </Typography>
              ) : (
                availableFeatures.map((feature) => (
                <Card key={feature.id} variant="outlined" sx={{ p: 2 }}>
                  <Stack spacing={2}>
                    <Stack direction="row" alignItems="flex-start" spacing={2}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={enabledFeatures.includes(feature.id)}
                            onChange={() => handleFeatureToggle(feature.id)}
                            disabled={isUpdatingFeatures || isUpdating}
                            color="primary"
                          />
                        }
                        label=""
                        sx={{ m: 0 }}
                      />
                      
                      <Stack spacing={0.5} flexGrow={1}>
                        <Typography variant="subtitle2">
                          {feature.label}
                        </Typography>
                        <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                          {feature.description}
                        </Typography>
                      </Stack>
                    </Stack>

                    {feature.id === 'product_card_display' && enabledFeatures.includes('product_card_display') && (
                      <Box sx={{ 
                        ml: 6, 
                        p: 3, 
                        bgcolor: 'background.default', 
                        borderRadius: 1.5,
                        border: '1px solid',
                        borderColor: 'divider'
                      }}>
                        <Stack spacing={2}>
                          <Stack direction="row" alignItems="center" justifyContent="space-between">
                            <Typography variant="body2" sx={{ fontWeight: 600, color: 'primary.main' }}>
                              🎨 Cấu hình Product Card
                            </Typography>
                          </Stack>
                          
                          <Box>
                            <Typography variant="body2" sx={{ color: 'text.secondary', mb: 2 }}>
                              Tùy chỉnh cách hiển thị thông tin sản phẩm, nút tương tác và phương thức gửi.
                            </Typography>
                            
                            {chatbot?.productCardConfig && chatbot.productCardConfig.enabled && (
                              <Stack spacing={1} sx={{ mb: 2 }}>
                                <Typography variant="caption" sx={{ color: 'info.main' }}>
                                  📱 Phương thức: {chatbot.productCardConfig.send_method === 'direct_chat' ? 'Trực tiếp trong chat' : 'Mở link website'}
                                </Typography>
                                <Typography variant="caption" sx={{ color: 'info.main' }}>
                                  🎯 Kiểu hiển thị: {chatbot.productCardConfig.card_style === 'compact' ? 'Gọn nhẹ' : 'Chi tiết'}
                                </Typography>
                              </Stack>
                            )}
                          </Box>

                          <Stack direction="row" spacing={1.5} justifyContent="flex-end">
                            <Button
                              size="small"
                              variant="contained"
                              startIcon={<Iconify icon="eva:settings-2-fill" width={16} />}
                              onClick={() => handleOpenDetailConfig('product_card_display')}
                            >
                              Cấu hình chi tiết
                            </Button>
                          </Stack>
                        </Stack>
                      </Box>
                    )}


                  </Stack>
                </Card>
                ))
              )}
            </Stack>
          </FormGroup>

          <Box sx={{ mt: 2, p: 2, bgcolor: 'background.neutral', borderRadius: 1 }}>
            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              <strong>Đã kích hoạt:</strong> {enabledFeatures.length}/{availableFeatures.length} tính năng
              {isUpdatingFeatures && (
                <Typography component="span" sx={{ color: 'primary.main', ml: 1 }}>
                  • Đang cập nhật...
                </Typography>
              )}
            </Typography>
          </Box>
        </Stack>
      </Card> */}

      {/* ✅ PRODUCT CARD CONFIG DIALOG */}
      <ProductCardConfigDialog
        open={openProductCardDialog}
        onClose={() => setOpenProductCardDialog(false)}
        chatbot={chatbot}
        onDataChange={onDataChange}
      />
    </Stack>
  );
} 