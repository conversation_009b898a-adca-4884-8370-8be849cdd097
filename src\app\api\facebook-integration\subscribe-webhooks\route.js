import { NextResponse } from 'next/server';
import { createClient } from 'src/utils/supabase/server';

/**
 * Subscribe to Facebook/Instagram Webhooks
 * Sets up webhook subscriptions for pages and Instagram accounts
 */

export async function POST(request) {
  try {
    const { pageId, subscriptions = ['feed', 'comments', 'messaging'] } = await request.json();
    
    console.log('🔔 Setting up webhook subscriptions for page:', pageId);
    
    // Get Supabase client
    const supabase = await createClient();
    
    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }
    
    // Get page access token
    const { data: pageData, error: pageError } = await supabase
      .from('facebook_accounts')
      .select('access_token, page_name, instagram_account_id')
      .eq('page_id', pageId)
      .single();
    
    if (pageError || !pageData) {
      return NextResponse.json({ error: 'Page not found or not connected' }, { status: 404 });
    }
    
    const results = [];
    
    // Subscribe to Facebook Page webhooks
    for (const subscription of subscriptions) {
      try {
        const subscribeResult = await subscribeToWebhook(pageId, pageData.access_token, subscription);
        results.push({
          type: 'facebook',
          subscription,
          success: subscribeResult.success,
          message: subscribeResult.message
        });
      } catch (error) {
        console.error(`❌ Failed to subscribe to ${subscription}:`, error);
        results.push({
          type: 'facebook',
          subscription,
          success: false,
          message: error.message
        });
      }
    }
    
    // Subscribe to Instagram webhooks if Instagram account is connected
    if (pageData.instagram_account_id) {
      try {
        const instagramResult = await subscribeToInstagramWebhooks(
          pageData.instagram_account_id,
          pageData.access_token
        );
        results.push({
          type: 'instagram',
          subscription: 'comments,mentions',
          success: instagramResult.success,
          message: instagramResult.message
        });
      } catch (error) {
        console.error('❌ Failed to subscribe to Instagram webhooks:', error);
        results.push({
          type: 'instagram',
          subscription: 'comments,mentions',
          success: false,
          message: error.message
        });
      }
    }
    
    // Log the subscription activity
    await supabase
      .from('facebook_activity_logs')
      .insert({
        page_id: pageId,
        activity: 'webhook_subscription',
        metadata: { results, subscriptions }
      });
    
    const successCount = results.filter(r => r.success).length;
    const totalCount = results.length;
    
    return NextResponse.json({
      success: successCount > 0,
      message: `Successfully subscribed to ${successCount}/${totalCount} webhooks`,
      results
    });
    
  } catch (error) {
    console.error('💥 Webhook subscription error:', error);
    return NextResponse.json({ 
      error: 'Failed to subscribe to webhooks',
      details: error.message 
    }, { status: 500 });
  }
}

// Subscribe to specific Facebook webhook
async function subscribeToWebhook(pageId, accessToken, field) {
  try {
    const webhookUrl = `${process.env.NEXTAUTH_URL || 'https://your-domain.com'}/api/facebook-webhooks`;
    
    console.log(`🔔 Subscribing to ${field} webhook for page ${pageId}`);
    
    const response = await fetch(`https://graph.facebook.com/v19.0/${pageId}/subscribed_apps`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        subscribed_fields: field,
        access_token: accessToken,
        callback_url: webhookUrl,
        verify_token: process.env.FACEBOOK_WEBHOOK_VERIFY_TOKEN
      })
    });
    
    const data = await response.json();
    
    if (data.error) {
      console.error(`❌ Facebook webhook subscription error for ${field}:`, data.error);
      return {
        success: false,
        message: `Failed to subscribe to ${field}: ${data.error.message}`
      };
    }
    
    console.log(`✅ Successfully subscribed to ${field} webhook`);
    return {
      success: true,
      message: `Successfully subscribed to ${field} webhook`
    };
    
  } catch (error) {
    console.error(`💥 Error subscribing to ${field} webhook:`, error);
    return {
      success: false,
      message: `Error subscribing to ${field}: ${error.message}`
    };
  }
}

// Subscribe to Instagram webhooks
async function subscribeToInstagramWebhooks(instagramAccountId, accessToken) {
  try {
    console.log('🔔 Subscribing to Instagram webhooks for account:', instagramAccountId);
    
    const webhookUrl = `${process.env.NEXTAUTH_URL || 'https://your-domain.com'}/api/facebook-webhooks`;
    
    // Subscribe to Instagram comments and mentions
    const response = await fetch(`https://graph.facebook.com/v19.0/${instagramAccountId}/subscribed_apps`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        subscribed_fields: 'comments,mentions',
        access_token: accessToken,
        callback_url: webhookUrl,
        verify_token: process.env.FACEBOOK_WEBHOOK_VERIFY_TOKEN
      })
    });
    
    const data = await response.json();
    
    if (data.error) {
      console.error('❌ Instagram webhook subscription error:', data.error);
      return {
        success: false,
        message: `Failed to subscribe to Instagram webhooks: ${data.error.message}`
      };
    }
    
    console.log('✅ Successfully subscribed to Instagram webhooks');
    return {
      success: true,
      message: 'Successfully subscribed to Instagram webhooks'
    };
    
  } catch (error) {
    console.error('💥 Error subscribing to Instagram webhooks:', error);
    return {
      success: false,
      message: `Error subscribing to Instagram webhooks: ${error.message}`
    };
  }
}

// GET: Check current webhook subscriptions
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const pageId = searchParams.get('pageId');
    
    if (!pageId) {
      return NextResponse.json({ error: 'Page ID required' }, { status: 400 });
    }
    
    const supabase = await createClient();
    
    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }
    
    // Get page access token
    const { data: pageData, error: pageError } = await supabase
      .from('facebook_accounts')
      .select('access_token, instagram_account_id')
      .eq('page_id', pageId)
      .single();
    
    if (pageError || !pageData) {
      return NextResponse.json({ error: 'Page not found' }, { status: 404 });
    }
    
    // Check Facebook subscriptions
    const facebookResponse = await fetch(
      `https://graph.facebook.com/v19.0/${pageId}/subscribed_apps?access_token=${pageData.access_token}`
    );
    const facebookData = await facebookResponse.json();
    
    let instagramData = null;
    if (pageData.instagram_account_id) {
      // Check Instagram subscriptions
      const instagramResponse = await fetch(
        `https://graph.facebook.com/v19.0/${pageData.instagram_account_id}/subscribed_apps?access_token=${pageData.access_token}`
      );
      instagramData = await instagramResponse.json();
    }
    
    return NextResponse.json({
      success: true,
      facebook: facebookData,
      instagram: instagramData
    });
    
  } catch (error) {
    console.error('💥 Error checking webhook subscriptions:', error);
    return NextResponse.json({ 
      error: 'Failed to check webhook subscriptions',
      details: error.message 
    }, { status: 500 });
  }
}
