'use client';

import { <PERSON>, Card, Grid, <PERSON>ack, Button, Select, MenuItem, Container, TextField, Typography, FormControl, InputAdornment } from '@mui/material';

import { DashboardContent } from 'src/layouts/dashboard';

import { Iconify } from 'src/components/iconify';
import { CustomBreadcrumbs } from 'src/components/custom-breadcrumbs';

// ----------------------------------------------------------------------

export default function StaffPage() {
  const statsCards = [
    {
      title: 'Tổng nhân viên',
      value: '0',
      subtitle: 'Nhân viên',
      icon: 'eva:people-fill',
      color: 'primary',
    },
    {
      title: 'Đang làm việc',
      value: '0',
      subtitle: 'Nhân viên',
      icon: 'eva:checkmark-circle-fill',
      color: 'success',
    },
    {
      title: 'Nghỉ phép',
      value: '0',
      subtitle: 'Nhân viên',
      icon: 'eva:clock-fill',
      color: 'warning',
    },
    {
      title: 'Lịch hẹn hôm nay',
      value: '0',
      subtitle: 'Lịch hẹn',
      icon: 'eva:calendar-fill',
      color: 'info',
    },
  ];

  return (
    <DashboardContent>
      <Container maxWidth="xl">
        <CustomBreadcrumbs
          heading="Quản lý nhân viên"
          subheading="Quản lý thông tin nhân viên và phân công công việc"
          links={[
            { name: 'Dashboard', href: '/dashboard' },
            { name: 'Nhân viên' },
          ]}
          sx={{ mb: 3 }}
        />

        <Grid container spacing={3} sx={{ mb: 3 }}>
          {statsCards.map((card, index) => (
            <Grid item size={{xs: 12, sm: 6, md: 3}} key={index}>
              <Card sx={{ p: 3 }}>
                <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" sx={{ color: 'text.secondary' }}>
                    {card.title}
                  </Typography>
                  <Box
                    sx={{
                      width: 32,
                      height: 32,
                      borderRadius: 1.5,
                      bgcolor: `${card.color}.lighter`,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <Iconify icon={card.icon} width={16} sx={{ color: `${card.color}.main` }} />
                  </Box>
                </Stack>
                <Typography variant="h3" sx={{ mb: 0.5 }}>
                  {card.value}
                </Typography>
                <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                  {card.subtitle}
                </Typography>
              </Card>
            </Grid>
          ))}
        </Grid>

        <Card>
          <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ p: 3, borderBottom: 1, borderColor: 'divider' }}>
            <Typography variant="h6">Danh sách nhân viên</Typography>
            <Stack direction="row" spacing={2}>
              <TextField
                size="small"
                placeholder="Tìm kiếm nhân viên..."
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Iconify icon="eva:search-fill" sx={{ color: 'text.disabled' }} />
                    </InputAdornment>
                  ),
                }}
                sx={{ width: 240 }}
              />
              <FormControl size="small" sx={{ minWidth: 140 }}>
                <Select defaultValue="all" size="small">
                  <MenuItem value="all">Tất cả phòng ban</MenuItem>
                  <MenuItem value="sales">Kinh doanh</MenuItem>
                  <MenuItem value="tech">Kỹ thuật</MenuItem>
                  <MenuItem value="support">Hỗ trợ</MenuItem>
                </Select>
              </FormControl>
              <Button variant="contained" startIcon={<Iconify icon="eva:plus-fill" />} size="small">
                Thêm nhân viên
              </Button>
            </Stack>
          </Stack>

          <Box sx={{ p: 6 }}>
            <Stack alignItems="center" spacing={3}>
              <Box
                sx={{
                  width: 96,
                  height: 96,
                  borderRadius: '50%',
                  bgcolor: 'grey.100',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <Iconify icon="eva:people-fill" width={48} sx={{ color: 'text.disabled' }} />
              </Box>

              <Typography variant="h6">Chưa có nhân viên nào</Typography>

              <Typography variant="body1" sx={{ textAlign: 'center', maxWidth: 480, color: 'text.secondary' }}>
                Thêm nhân viên để quản lý lịch làm việc, phân công nhiệm vụ và theo dõi hiệu suất.
              </Typography>

              <Stack spacing={2} sx={{ maxWidth: 400, width: '100%' }}>
                {[
                  'Quản lý thông tin cá nhân',
                  'Lịch làm việc và ca trực',
                  'Kỹ năng và chuyên môn',
                  'Phân công lịch hẹn tự động',
                ].map((feature, index) => (
                  <Stack key={index} direction="row" alignItems="center" spacing={1}>
                    <Iconify icon="eva:checkmark-circle-2-fill" sx={{ color: 'success.main' }} />
                    <Typography variant="body2">{feature}</Typography>
                  </Stack>
                ))}
              </Stack>

              <Button variant="contained" size="large" sx={{ mt: 2 }}>
                Thêm nhân viên đầu tiên
              </Button>
            </Stack>
          </Box>
        </Card>
      </Container>
    </DashboardContent>
  );
}
