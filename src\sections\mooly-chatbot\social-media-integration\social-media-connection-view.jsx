/**
 * Unified Social Media Connection View
 * UI component để quản lý kết nối multi-platform social media
 */

'use client';

import React, { useState, useCallback, useEffect, useMemo } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Stack,
  Avatar,
  Chip,
  IconButton,
  Tooltip,
  Grid,
  Alert,
  Divider,
  Tab,
  Tabs,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  LinearProgress,
  Skeleton,
  Badge,
  CircularProgress
} from '@mui/material';
import { LoadingButton } from '@mui/lab';
import { toast } from 'src/components/snackbar';

import { useDebounce } from 'minimal-shared/hooks';
import { DashboardContent } from 'src/layouts/dashboard';
import { Iconify } from 'src/components/iconify';

import { 
  PLATFORMS, 
  PLATFORM_COLORS, 
  PLATFORM_ICONS,
  ACCOUNT_TYPES 
} from 'src/actions/mooly-chatbot/social-media-integration/social-media-constants';
import { useSocialMediaAccounts, useSocialMediaAccountMutations } from 'src/actions/mooly-chatbot/social-media-integration/unified-social-media-service';
import { useSocialMediaConfig } from 'src/actions/mooly-chatbot/social-media-integration/social-media-hooks';

import { SocialMediaConnectionDialog } from './social-media-connection-dialog';
import { SocialMediaConfigDialog } from './social-media-config-dialog';

// Component riêng biệt để hiển thị trạng thái auto-reply - FIX HOOKS ISSUE
const AutoReplyStatus = ({ account }) => {
  const { config, loading: configLoading } = useSocialMediaConfig(
    account?.pageId || account?.page_id || account?.id, 
    account?.platform
  );

  // Xác định trạng thái auto-reply dựa trên config
  const getAutoReplyStatus = () => {
    if (configLoading) {
      return { 
        status: 'loading', 
        label: 'Đang tải...', 
        icon: 'mdi:loading',
        color: 'default',
        activeFeatures: 0,
        description: 'Đang kiểm tra cấu hình',
        isConfigured: false
      };
    }

    if (!config) {
      return { 
        status: 'not_configured', 
        label: 'Chưa cấu hình', 
        icon: 'mdi:cog-outline',
        color: 'warning',
        activeFeatures: 0,
        description: 'Nhấp để thiết lập tự động trả lời',
        isConfigured: false
      };
    }

    // Đếm số tính năng đã bật
    const features = [];
    if (config.enableCommentReply) features.push('Comments');
    if (config.enableMessageReply) features.push('Messages');
    if (config.enableInstagramComments && account.platform === 'instagram') features.push('IG Comments');
    if (config.enableInstagramMessages && account.platform === 'instagram') features.push('IG Messages');
    if (config.enableInstagramStoryReplies && account.platform === 'instagram') features.push('Stories');

    if (features.length === 0) {
      return { 
        status: 'configured_disabled', 
        label: 'Đã tắt', 
        icon: 'mdi:pause',
        color: 'default',
        activeFeatures: 0,
        description: 'Tất cả tính năng đang tắt',
        isConfigured: true
      };
    }

    return { 
      status: 'configured_enabled', 
      label: `${features.length} tính năng`,
      icon: 'mdi:robot',
      color: 'success',
      activeFeatures: features.length,
      description: features.join(', '),
      isConfigured: true
    };
  };

  const autoReplyStatus = getAutoReplyStatus();

  if (configLoading) {
    return (
      <Box display="flex" alignItems="center" gap={1}>
        <CircularProgress size={16} />
        <Typography variant="caption" color="text.secondary">
          Đang tải cấu hình...
        </Typography>
      </Box>
    );
  }

  return (
    <Tooltip 
      title={
        <Box>
          <Typography variant="subtitle2" color="inherit" gutterBottom>
            Auto-Reply: {autoReplyStatus.label}
          </Typography>
          <Typography variant="body2" color="inherit">
            {autoReplyStatus.description}
          </Typography>
          {autoReplyStatus.status === 'not_configured' && (
            <Typography variant="body2" color="inherit" sx={{ mt: 1, fontStyle: 'italic' }}>
              💡 Nhấp vào nút cài đặt để bắt đầu
            </Typography>
          )}
        </Box>
      }
      arrow
      placement="top"
    >
      <Box display="flex" alignItems="center" gap={1}>
        <Badge 
          badgeContent={autoReplyStatus.activeFeatures || null}
          color={autoReplyStatus.activeFeatures > 0 ? "success" : "default"}
          variant={autoReplyStatus.activeFeatures > 0 ? "standard" : "dot"}
          sx={{
            '& .MuiBadge-badge': {
              fontSize: '0.6rem',
              height: '16px',
              minWidth: '16px'
            }
          }}
        >
          <Chip
            icon={<Iconify icon={autoReplyStatus.icon} />}
            label={autoReplyStatus.label}
            color={autoReplyStatus.color}
            size="small"
            variant={autoReplyStatus.status === 'not_configured' ? 'outlined' : 'filled'}
            sx={{
              fontWeight: autoReplyStatus.activeFeatures > 0 ? 600 : 400,
              cursor: 'help'
            }}
          />
        </Badge>
      </Box>
    </Tooltip>
  );
};

// Hook để lấy trạng thái config - FIX HOOKS ISSUE
const useAccountConfigStatus = (account) => {
  const { config, loading: configLoading } = useSocialMediaConfig(
    account?.pageId || account?.page_id || account?.id, 
    account?.platform
  );

  return useMemo(() => {
    if (configLoading) {
      return { isConfigured: false, loading: true };
    }

    if (!config) {
      return { isConfigured: false, loading: false };
    }

    // Đếm số tính năng đã bật
    const features = [];
    if (config.enableCommentReply) features.push('Comments');
    if (config.enableMessageReply) features.push('Messages');
    if (config.enableInstagramComments && account.platform === 'instagram') features.push('IG Comments');
    if (config.enableInstagramMessages && account.platform === 'instagram') features.push('IG Messages');
    if (config.enableInstagramStoryReplies && account.platform === 'instagram') features.push('Stories');

    return { 
      isConfigured: true, 
      loading: false,
      hasActiveFeatures: features.length > 0
    };
  }, [config, configLoading, account?.platform]);
};

// Error Boundary Component
class SocialMediaErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Social Media Integration Error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <DashboardContent>
          <Card>
            <CardContent sx={{ textAlign: 'center', py: 6 }}>
              <Avatar sx={{ 
                width: 80, 
                height: 80, 
                mx: 'auto', 
                mb: 2, 
                bgcolor: 'error.main',
                fontSize: 40
              }}>
                <Iconify icon="mdi:alert-circle" />
              </Avatar>
              <Typography variant="h6" color="error" gutterBottom>
                Đã xảy ra lỗi
              </Typography>
              <Typography variant="body2" color="text.secondary" mb={3}>
                Không thể tải trang kết nối Social Media. Vui lòng thử lại.
              </Typography>
              <Button
                variant="contained"
                onClick={() => window.location.reload()}
                startIcon={<Iconify icon="mdi:refresh" />}
                size="large"
              >
                Tải lại trang
              </Button>
            </CardContent>
          </Card>
        </DashboardContent>
      );
    }

    return this.props.children;
  }
}

function SocialMediaConnectionView() {
  const [selectedTab, setSelectedTab] = useState(0);
  const [showConnectionDialog, setShowConnectionDialog] = useState(false);
  const [selectedPlatform, setSelectedPlatform] = useState(null);
  const [selectedAccount, setSelectedAccount] = useState(null);
  const [showConfigDialog, setShowConfigDialog] = useState(false);
  const [accountToDelete, setAccountToDelete] = useState(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  // Hooks - Sử dụng unified service với field mapping chính xác
  const {
    accounts,
    accountsByPlatform,
    stats,
    loading: accountsLoading,
    error: accountsError,
    refetch: refetchAccounts
  } = useSocialMediaAccounts({
    platforms: ['facebook', 'instagram'],
    autoRefresh: false
  });

  const { 
    disconnectAccount, 
    loading: mutationLoading,
    error: mutationError 
  } = useSocialMediaAccountMutations();

  // Combined loading state
  const loading = accountsLoading || mutationLoading;

  // Error handling - CHỈ hiển thị error một lần
  useEffect(() => {
    if (accountsError) {
      console.error('Social media accounts error:', accountsError);
      toast.error('Lỗi khi tải danh sách tài khoản social media');
    }
  }, [accountsError]);

  useEffect(() => {
    if (mutationError) {
      console.error('Social media mutation error:', mutationError);
      toast.error('Lỗi khi thực hiện thao tác');
    }
  }, [mutationError]);

  // Stable refetch function - không có dependencies để tránh re-create
  const stableRefetch = useCallback(() => {
    if (refetchAccounts && typeof refetchAccounts === 'function') {
      refetchAccounts();
    }
  }, []); // Empty dependencies - sử dụng ref thay vì dependency

  // Handle URL parameters from OAuth callback - CHỈ chạy một lần khi mount
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const success = urlParams.get('success');
    const error = urlParams.get('error');
    const message = urlParams.get('message');
    const errorDescription = urlParams.get('error_description');
    const count = urlParams.get('count');

    if (success === 'facebook_connected' || success === 'instagram_connected' || success === 'true') {
      const platform = success === 'facebook_connected' ? 'Facebook' : 
                     success === 'instagram_connected' ? 'Instagram' : 'Social Media';
      const accountCount = count ? ` (${count} tài khoản)` : '';
      toast.success(decodeURIComponent(message || `${platform} kết nối thành công!${accountCount}`));
      // Clean URL
      window.history.replaceState({}, document.title, window.location.pathname);
      // Gọi refetch trực tiếp để tránh dependency
      if (refetchAccounts && typeof refetchAccounts === 'function') {
        refetchAccounts();
      }
    } else if (error) {
      const errorMsg = errorDescription || message || 'Lỗi khi kết nối social media';
      toast.error(decodeURIComponent(errorMsg));
      // Clean URL
      window.history.replaceState({}, document.title, window.location.pathname);
    }
  }, []); // CHỈ chạy một lần khi component mount

  // Tab options
  const tabOptions = [
    { label: 'Tất cả', value: 'all', icon: 'mdi:view-grid' },
    { label: 'Facebook', value: PLATFORMS.FACEBOOK, icon: PLATFORM_ICONS[PLATFORMS.FACEBOOK] },
    { label: 'Instagram', value: PLATFORMS.INSTAGRAM, icon: PLATFORM_ICONS[PLATFORMS.INSTAGRAM] }
  ];

  // Filter accounts based on selected tab - useMemo để tránh re-compute
  const filteredAccounts = useMemo(() => {
    if (!accounts || accounts.length === 0) return [];
    return selectedTab === 0 
      ? accounts 
      : accounts.filter(account => account.platform === tabOptions[selectedTab].value);
  }, [accounts, selectedTab]);

  // Handlers - gọi refetchAccounts trực tiếp để tránh dependencies phức tạp
  const handleConnectionSuccess = useCallback(async (result) => {
    try {
      if (result?.success) {
        toast.success(result.message || 'Kết nối social media thành công!');
        setShowConnectionDialog(false);
        setSelectedPlatform(null);
        // Gọi refetch trực tiếp
        if (refetchAccounts && typeof refetchAccounts === 'function') {
          refetchAccounts();
        }
      } else {
        throw new Error(result?.message || 'Không thể kết nối social media');
      }
    } catch (error) {
      console.error('Error handling connection success:', error);
      toast.error('Lỗi khi xử lý kết nối social media');
    }
  }, []); // Empty dependencies

  const handleConfigSuccess = useCallback(() => {
    setShowConfigDialog(false);
    setSelectedAccount(null);
    // Gọi refetch trực tiếp
    if (refetchAccounts && typeof refetchAccounts === 'function') {
      refetchAccounts();
    }
    toast.success('Cấu hình đã được lưu thành công');
  }, []); // Empty dependencies

  const handleDisconnectAccount = useCallback(async () => {
    if (!accountToDelete) return;

    try {
      console.log('🗑️ Disconnecting social media account:', accountToDelete.id);

      const result = await disconnectAccount(accountToDelete.id, accountToDelete.platform);
      
      if (result.success) {
        toast.success('Đã ngắt kết nối tài khoản thành công');
        setDeleteDialogOpen(false);
        setAccountToDelete(null);
        // Gọi refetch trực tiếp
        if (refetchAccounts && typeof refetchAccounts === 'function') {
          refetchAccounts();
        }
      } else {
        throw new Error(result.error?.message || 'Không thể ngắt kết nối tài khoản');
      }
    } catch (error) {
      console.error('Error disconnecting account:', error);
      toast.error('Lỗi khi ngắt kết nối tài khoản');
    }
  }, [accountToDelete, disconnectAccount]); // Chỉ dependency cần thiết

  const handleConnectPlatform = useCallback((platform) => {
    setSelectedPlatform(platform);
    setShowConnectionDialog(true);
  }, []);

  // Get connection status - sử dụng unified field names
  const getConnectionStatus = (account) => {
    const now = new Date();
    const expiresAt = account.tokenExpiresAt ? new Date(account.tokenExpiresAt) : null;
    
    // Kiểm tra token có hết hạn không
    const isTokenExpired = expiresAt && expiresAt < now;
    
    if (!account.isActive) {
      return { status: 'inactive', label: 'Tạm dừng', color: 'default', icon: 'mdi:pause-circle' };
    } else if (isTokenExpired) {
      return { status: 'expired', label: 'Cần cập nhật', color: 'error', icon: 'mdi:alert-circle' };
    } else {
      return { status: 'active', label: 'Đang hoạt động', color: 'success', icon: 'mdi:check-circle' };
    }
  };

  // Render platform stats với thông tin cấu hình
  const renderPlatformStats = () => {
    const configuredCount = accounts?.filter(account => 
      // Cần check config của từng account - simplified version
      account.isActive // Tạm thời dùng isActive, có thể cải thiện sau
    ).length || 0;

    return (
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Stack direction="row" alignItems="center" spacing={2}>
                <Avatar sx={{ bgcolor: 'primary.main' }}>
                  <Iconify icon="mdi:account-multiple" />
                </Avatar>
                <Box>
                  <Typography variant="h4">{stats.totalAccounts}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Tổng tài khoản
                  </Typography>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Stack direction="row" alignItems="center" spacing={2}>
                <Avatar sx={{ bgcolor: 'success.main' }}>
                  <Iconify icon="mdi:check-circle" />
                </Avatar>
                <Box>
                  <Typography variant="h4">{stats.activeAccounts}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Đang hoạt động
                  </Typography>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Stack direction="row" alignItems="center" spacing={2}>
                <Avatar sx={{ bgcolor: 'info.main' }}>
                  <Iconify icon="mdi:robot" />
                </Avatar>
                <Box>
                  <Typography variant="h4">{configuredCount}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Đã cấu hình AI
                  </Typography>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Stack direction="row" alignItems="center" spacing={2}>
                <Avatar sx={{ bgcolor: 'warning.main' }}>
                  <Iconify icon="mdi:pause-circle" />
                </Avatar>
                <Box>
                  <Typography variant="h4">{stats.inactiveAccounts}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Tạm dừng
                  </Typography>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    );
  };

  // Component riêng để render account card để tránh hooks issue
  const AccountCard = ({ account }) => {
    const connectionStatus = getConnectionStatus(account);
    const configStatus = useAccountConfigStatus(account);
    
    return (
      <Card 
        key={account.id} 
        elevation={1} 
        sx={{ 
          mb: 2, 
          position: 'relative',
          // Background mờ đi cho account chưa cấu hình
          opacity: configStatus.isConfigured ? 1 : 0.7,
          backgroundColor: configStatus.isConfigured ? 'background.paper' : 'action.hover',
          border: configStatus.isConfigured ? '2px solid transparent' : '2px dashed',
          borderColor: configStatus.isConfigured ? 'transparent' : 'warning.main',
          transition: 'all 0.3s ease',
          '&:hover': {
            opacity: 1,
            transform: 'translateY(-2px)',
            boxShadow: 3
          }
        }}
      >
        <CardContent sx={{ p: 3 }}>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Stack direction="row" alignItems="center" spacing={2}>
              {/* Platform Icon */}
              <Avatar 
                sx={{ 
                  bgcolor: PLATFORM_COLORS[account.platform],
                  width: 48,
                  height: 48
                }}
              >
                <Iconify icon={PLATFORM_ICONS[account.platform]} />
              </Avatar>

              {/* Account Avatar */}
              {account.avatarUrl && (
                <Avatar 
                  src={account.avatarUrl} 
                  sx={{ width: 40, height: 40 }}
                />
              )}

              {/* Account Info */}
              <Box sx={{ flex: 1 }}>
                <Stack direction="row" alignItems="center" spacing={1}>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    {account.displayName || account.pageName || account.username}
                  </Typography>
                  {account.isVerified && (
                    <Iconify 
                      icon="mdi:check-decagram" 
                      sx={{ color: 'primary.main', fontSize: 20 }}
                    />
                  )}
                  {/* Indicator cho chưa cấu hình */}
                  {!configStatus.isConfigured && !configStatus.loading && (
                    <Chip
                      icon={<Iconify icon="mdi:alert-outline" />}
                      label="Chưa cấu hình"
                      color="warning"
                      size="small"
                      variant="outlined"
                      sx={{ ml: 1 }}
                    />
                  )}
                </Stack>
                
                <Stack direction="row" alignItems="center" spacing={1} sx={{ mt: 0.5 }}>
                  <Chip
                    label={connectionStatus.label}
                    color={connectionStatus.color}
                    size="small"
                    icon={<Iconify icon={connectionStatus.icon} />}
                  />
                  
                  <Chip
                    label={account.platform.toUpperCase()}
                    size="small"
                    sx={{ 
                      bgcolor: PLATFORM_COLORS[account.platform],
                      color: 'white',
                      fontWeight: 'bold'
                    }}
                  />
                  
                  <Chip
                    label={account.accountType || 'page'}
                    size="small"
                    variant="outlined"
                  />
                </Stack>

                {/* Auto-Reply Status - Component riêng biệt */}
                <Box sx={{ mt: 1 }}>
                  <AutoReplyStatus account={account} />
                </Box>

                {account.username && (
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                    @{account.username}
                  </Typography>
                )}

                {account.followerCount > 0 && (
                  <Typography variant="body2" color="text.secondary">
                    {account.followerCount.toLocaleString()} người theo dõi
                  </Typography>
                )}

                <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 0.5 }}>
                  Kết nối từ {new Date(account.connectedAt || account.createdAt).toLocaleDateString('vi-VN')}
                </Typography>
              </Box>
            </Stack>

            {/* Actions - Đã tối ưu, bỏ icon cài đặt trùng */}
            <Stack direction="row" alignItems="center" spacing={1}>
              <Tooltip title={configStatus.isConfigured ? "Quản lý Auto-Reply" : "Thiết lập Auto-Reply"}>
                <LoadingButton
                  onClick={() => {
                    setSelectedAccount(account);
                    setShowConfigDialog(true);
                  }}
                  variant={configStatus.isConfigured ? "outlined" : "contained"}
                  color={configStatus.isConfigured ? "primary" : "warning"}
                  size="large"
                  startIcon={
                    <Iconify 
                      icon={configStatus.isConfigured ? "mdi:robot" : "mdi:robot-outline"} 
                    />
                  }
                  sx={{
                    minWidth: 140,
                    textTransform: 'none',
                    borderRadius: 2,
                    ...(configStatus.isConfigured && {
                      borderColor: 'primary.main',
                      bgcolor: 'primary.lighter',
                      '&:hover': {
                        bgcolor: 'primary.light',
                        borderColor: 'primary.dark'
                      }
                    }),
                    ...(!configStatus.isConfigured && {
                      bgcolor: 'warning.main',
                      '&:hover': {
                        bgcolor: 'warning.dark'
                      }
                    })
                  }}
                >
                  {configStatus.isConfigured ? 'Quản lý AI' : 'Thiết lập AI'}
                </LoadingButton>
              </Tooltip>

              <Tooltip title="Ngắt kết nối">
                <IconButton
                  onClick={() => {
                    setAccountToDelete(account);
                    setDeleteDialogOpen(true);
                  }}
                  color="error"
                  size="large"
                  sx={{
                    bgcolor: 'error.lighter',
                    '&:hover': {
                      bgcolor: 'error.light'
                    }
                  }}
                >
                  <Iconify icon="mdi:link-off" />
                </IconButton>
              </Tooltip>
            </Stack>
          </Box>
        </CardContent>
      </Card>
    );
  };

  return (
    <DashboardContent>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box>
          <Typography variant="h5" component="h1">
            Kết nối Social Media
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Quản lý các tài khoản Facebook và Instagram với AI tự động trả lời 24/7
          </Typography>
        </Box>
        <Stack direction="row" spacing={2}>
          <Tooltip title="Làm mới danh sách">
            <LoadingButton
              loading={accountsLoading}
              onClick={stableRefetch}
              startIcon={<Iconify icon="mdi:refresh" />}
              variant="outlined"
              size="small"
            >
              Làm mới
            </LoadingButton>
          </Tooltip>
          <Button
            variant="contained"
            startIcon={<Iconify icon="mdi:plus" />}
            onClick={() => setShowConnectionDialog(true)}
            color="primary"
          >
            Kết nối tài khoản
          </Button>
        </Stack>
      </Box>

      {/* Info Alert - Enhanced với thông tin visual indicators */}
      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="subtitle2" gutterBottom>
          🤖 AI trả lời tự động 24/7 - Tăng tương tác, chăm sóc khách hàng không giới hạn
        </Typography>
        <Typography variant="body2">
          • Tự động trả lời comments và tin nhắn riêng • AI hiểu ngữ cảnh kinh doanh và sản phẩm • Tùy chỉnh tone và ngôn ngữ theo thương hiệu 
          <br />
          <strong>💡 Visual Guide:</strong> Tài khoản <span style={{color: '#ff9800'}}>⚠️ chưa cấu hình</span> sẽ có background mờ và viền nét đứt để dễ nhận biết
        </Typography>
      </Alert>

      {/* Loading */}
      {loading && <LinearProgress sx={{ mb: 2 }} />}

      {/* Platform Stats */}
      {renderPlatformStats()}

      {/* Platform Tabs */}
      <Card sx={{ mb: 3 }}>
        <Tabs
          value={selectedTab}
          onChange={(e, newValue) => setSelectedTab(newValue)}
          variant="scrollable"
          scrollButtons="auto"
        >
          {tabOptions.map((tab, index) => (
            <Tab
              key={tab.value}
              label={tab.label}
              icon={<Iconify icon={tab.icon} />}
              iconPosition="start"
            />
          ))}
        </Tabs>
      </Card>

      {/* Accounts List */}
      {loading ? (
        // Loading skeleton
        <Stack spacing={2}>
          {[1, 2].map((index) => (
            <Card key={index}>
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="center">
                  <Box display="flex" alignItems="center" gap={2}>
                    <Skeleton variant="circular" width={48} height={48} />
                    <Skeleton variant="circular" width={40} height={40} />
                    <Box>
                      <Skeleton variant="text" width={200} height={24} />
                      <Skeleton variant="text" width={120} height={16} />
                      <Skeleton variant="text" width={150} height={12} />
                    </Box>
                  </Box>
                  <Stack direction="row" spacing={1}>
                    <Skeleton variant="rectangular" width={140} height={40} sx={{ borderRadius: 1 }} />
                    <Skeleton variant="circular" width={40} height={40} />
                  </Stack>
                </Box>
              </CardContent>
            </Card>
          ))}
        </Stack>
      ) : filteredAccounts.length === 0 ? (
        <Card>
          <CardContent sx={{ textAlign: 'center', py: 6 }}>
            <Avatar sx={{ 
              width: 80, 
              height: 80, 
              mx: 'auto', 
              mb: 2, 
              bgcolor: 'primary.main',
              fontSize: 40
            }}>
              <Iconify icon="mdi:robot-outline" />
            </Avatar>
            <Typography variant="h6" color="text.secondary" gutterBottom>
              Chưa có tài khoản nào được kết nối
            </Typography>
            <Typography variant="body2" color="text.secondary" mb={3}>
              Kết nối Facebook Page hoặc Instagram để bắt đầu sử dụng AI tự động trả lời khách hàng 24/7
            </Typography>
            
            <Stack direction="row" spacing={2} justifyContent="center">
              <Button
                variant="contained"
                startIcon={<Iconify icon={PLATFORM_ICONS[PLATFORMS.FACEBOOK]} />}
                onClick={() => handleConnectPlatform(PLATFORMS.FACEBOOK)}
                sx={{ bgcolor: PLATFORM_COLORS[PLATFORMS.FACEBOOK] }}
              >
                Kết nối Facebook
              </Button>
              <Button
                variant="contained"
                startIcon={<Iconify icon={PLATFORM_ICONS[PLATFORMS.INSTAGRAM]} />}
                onClick={() => handleConnectPlatform(PLATFORMS.INSTAGRAM)}
                sx={{ bgcolor: PLATFORM_COLORS[PLATFORMS.INSTAGRAM] }}
              >
                Kết nối Instagram
              </Button>
            </Stack>
          </CardContent>
        </Card>
      ) : (
        <Box>
          {filteredAccounts.map((account) => (
            <AccountCard key={account.id} account={account} />
          ))}
        </Box>
      )}

      {/* Connection Dialog */}
      <SocialMediaConnectionDialog
        open={showConnectionDialog}
        onClose={() => {
          setShowConnectionDialog(false);
          setSelectedPlatform(null);
        }}
        onSuccess={handleConnectionSuccess}
        selectedPlatform={selectedPlatform}
      />

      {/* Config Dialog */}
      {selectedAccount && (
        <SocialMediaConfigDialog
          open={showConfigDialog}
          onClose={() => {
            setShowConfigDialog(false);
            setSelectedAccount(null);
          }}
          onSuccess={handleConfigSuccess}
          account={selectedAccount}
        />
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          <Box display="flex" alignItems="center" gap={2}>
            <Iconify icon="mdi:link-off" color="error.main" />
            <Typography variant="h6">Ngắt kết nối tài khoản</Typography>
          </Box>
        </DialogTitle>
        <DialogContent>
          <Typography variant="body1" gutterBottom>
            Bạn có chắc chắn muốn ngắt kết nối tài khoản <strong>{accountToDelete?.page_name || accountToDelete?.username}</strong>?
          </Typography>
          <Alert severity="warning" sx={{ mt: 2 }}>
            <Typography variant="body2">
              ⚠️ Sau khi ngắt kết nối:
            </Typography>
            <Typography variant="body2" component="ul" sx={{ mt: 1, pl: 2 }}>
              <li>AI tự động trả lời sẽ dừng hoạt động</li>
              <li>Không thể nhận tin nhắn mới từ tài khoản này</li>
              <li>Có thể kết nối lại bất cứ lúc nào</li>
            </Typography>
          </Alert>
        </DialogContent>
        <DialogActions sx={{ p: 3 }}>
          <Button 
            onClick={() => setDeleteDialogOpen(false)}
            variant="outlined"
          >
            Hủy
          </Button>
          <LoadingButton
            onClick={handleDisconnectAccount}
            loading={mutationLoading}
            color="error"
            variant="contained"
            startIcon={<Iconify icon="mdi:link-off" />}
          >
            Ngắt kết nối
          </LoadingButton>
        </DialogActions>
      </Dialog>
    </DashboardContent>
  );
}

// Export component wrapped with Error Boundary
export default function SocialMediaConnectionViewWithErrorBoundary() {
  return (
    <SocialMediaErrorBoundary>
      <SocialMediaConnectionView />
    </SocialMediaErrorBoundary>
  );
}
