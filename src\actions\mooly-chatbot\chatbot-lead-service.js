'use client';

import { useMemo, useState, useEffect, useCallback } from 'react';
import useSWR from 'swr';

import { useAuthContext } from 'src/auth/hooks';

import { fetchData, createData, updateData, deleteData, upsertData } from './supabase-utils';

// Tên bảng trong Supabase
const LEAD_CONFIG_TABLE = 'chatbot_lead_configs';
const LEADS_TABLE = 'chatbot_leads';

// =====================================================
// LEAD CONFIGURATION SERVICES
// =====================================================

/**
 * Lấy cấu hình lead cho chatbot
 * @param {string} chatbotId - ID của chatbot
 * @returns {Promise<Object>} - <PERSON><PERSON><PERSON> quả từ API
 */
export async function getLeadConfig(chatbotId) {
  if (!chatbotId) {
    return { success: false, error: 'Chatbot ID is required', data: null };
  }

  try {
    const result = await fetchData(LEAD_CONFIG_TABLE, {
      filters: { chatbot_id: chatbotId },
      single: true,
    });
    
    // Xử lý trường hợp không có config (lần đầu tiên)
    if (!result.success && result.error?.code === 'NO_ROWS_OR_MULTIPLE_ROWS') {
      return { success: true, data: null, error: null };
    }
    
    return result;
  } catch (error) {
    return { success: false, error, data: null };
  }
}

/**
 * Cập nhật fields configuration cho chatbot
 * @param {string} chatbotId - ID của chatbot
 * @param {Array} fields - Mảng các field cấu hình
 * @param {boolean} isEnabled - Trạng thái kích hoạt
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function upsertLeadFields(chatbotId, fields = [], isEnabled = false) {
  if (!chatbotId) {
    return { success: false, error: 'Chatbot ID is required', data: null };
  }

  try {
    const dataToUpsert = {
      chatbot_id: chatbotId,
      fields_config: JSON.stringify(fields),
      is_enabled: isEnabled,
    };

    const result = await upsertData(LEAD_CONFIG_TABLE, dataToUpsert, ['chatbot_id']);
    return result;
  } catch (error) {
    return { success: false, error, data: null };
  }
}

/**
 * Xóa cấu hình lead
 * @param {string} chatbotId - ID của chatbot
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function deleteLeadConfig(chatbotId) {
  if (!chatbotId) {
    return { success: false, error: 'Chatbot ID is required', data: null };
  }

  return deleteData(LEAD_CONFIG_TABLE, { chatbot_id: chatbotId });
}

// =====================================================
// LEAD DATA SERVICES
// =====================================================

/**
 * Lấy danh sách leads cho chatbot hoặc tất cả leads
 * @param {string} chatbotId - ID của chatbot (null để lấy tất cả)
 * @param {Object} options - Các tùy chọn lọc
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getLeads(chatbotId, options = {}) {
  const { page = 1, limit = 10, status, search } = options;

  // Tính offset từ page và limit
  const offset = (page - 1) * limit;

  const fetchOptions = {
    filters: {},
    orderBy: [{ column: 'createdAt', ascending: false }],
    limit,
    offset,
    count: true, // Để có total count cho pagination
  };

  // Chỉ filter theo chatbot nếu có chatbotId
  if (chatbotId) {
    fetchOptions.filters.chatbotId = chatbotId;
  }

  // Thêm filter theo status
  if (status) {
    fetchOptions.filters.status = status;
  }

  // Thêm search filters - sử dụng ilike operator cho search
  if (search) {
    // Search trong multiple fields với OR logic
    // Note: supabase-utils chưa support OR logic phức tạp, tạm thời search trong fullName
    fetchOptions.filters.fullName = {
      operator: 'ilike',
      value: search
    };
  }

  const result = await fetchData(LEADS_TABLE, fetchOptions);
  
  return result;
}

/**
 * Xóa nhiều leads cùng lúc
 * @param {Array} leadIds - Mảng các ID leads cần xóa
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function deleteMultipleLeads(leadIds) {
  if (!leadIds || leadIds.length === 0) {
    return { success: false, error: 'Lead IDs are required', data: null };
  }

  return deleteData(LEADS_TABLE, { id: leadIds }, 'in');
}

/**
 * Lấy lead theo ID
 * @param {string} leadId - ID của lead
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getLeadById(leadId) {
  if (!leadId) {
    return { success: false, error: 'Lead ID is required', data: null };
  }

  return fetchData(LEADS_TABLE, {
    filters: { id: leadId },
    single: true,
  });
}

/**
 * Tạo lead mới
 * @param {Object} leadData - Dữ liệu lead
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function createLead(leadData) {
  return createData(LEADS_TABLE, leadData);
}

/**
 * Cập nhật lead
 * @param {string} leadId - ID của lead
 * @param {Object} leadData - Dữ liệu cập nhật
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function updateLead(leadId, leadData) {
  if (!leadId) {
    return { success: false, error: 'Lead ID is required', data: null };
  }

  const result = await updateData(LEADS_TABLE, leadData, { id: leadId });
  
  return result;
}

/**
 * Xóa lead
 * @param {string} leadId - ID của lead
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function deleteLead(leadId) {
  if (!leadId) {
    return { success: false, error: 'Lead ID is required', data: null };
  }

  return deleteData(LEADS_TABLE, { id: leadId });
}

/**
 * Cập nhật trạng thái lead
 * @param {string} leadId - ID của lead
 * @param {string} status - Trạng thái mới
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function updateLeadStatus(leadId, status) {
  if (!leadId) {
    return { success: false, error: { message: 'Lead ID is required' }, data: null };
  }

  if (!status) {
    return { success: false, error: { message: 'Status is required' }, data: null };
  }

  try {
    // ✅ FIX: Sử dụng updateData trực tiếp để tránh vấn đề field mapping
    // supabase-utils sẽ tự động convert camelCase -> snake_case
    const result = await updateData(LEADS_TABLE, {
      status,
      // Không cần set updatedAt vì database trigger sẽ tự động set updated_at
    }, { id: leadId });

    console.log('🔧 updateLeadStatus result:', { leadId, status, result });

    return result;
  } catch (error) {
    console.error('❌ updateLeadStatus error:', error);
    return { success: false, error: { message: error.message || 'Failed to update lead status' }, data: null };
  }
}

// =====================================================
// REACT HOOKS
// =====================================================

/**
 * Hook để lấy cấu hình lead
 * @param {string} chatbotId - ID của chatbot
 * @returns {Object} - SWR response với config và fields
 */
export function useLeadFields(chatbotId) {
  const { data, error, mutate } = useSWR(
    chatbotId ? `lead-fields-${chatbotId}` : null,
    () => getLeadConfig(chatbotId)
  );

  const result = useMemo(() => {
    if (!data?.success || !data?.data) {
      return {
        config: null,
        fields: [],
        isEnabled: false,
      };
    }
    
    // Parse fields từ JSON
    let fields = [];
    if (data.data.fieldsConfig) {
      try {
        fields = JSON.parse(data.data.fieldsConfig);
      } catch (err) {
        return;
      }
    }
    
    return {
      config: data.data,
      fields,
      isEnabled: data.data.isEnabled || false,
    };
  }, [data]);

  return {
    ...result,
    loading: !data && !error,
    error: error || (!data?.success ? data?.error : null),
    mutate,
  };
}

/**
 * Hook để lấy danh sách leads
 * @param {string} chatbotId - ID của chatbot (null để lấy tất cả)
 * @param {Object} options - Các tùy chọn
 * @returns {Object} - SWR response
 */
export function useLeads(chatbotId, options = {}) {
  // Memoize stable options để tránh thay đổi không cần thiết
  const stableOptions = useMemo(() => {
    // Nếu options rỗng hoặc undefined, trả về default
    if (!options || Object.keys(options).length === 0) {
      return { page: 1, limit: 100 };
    }
    
    // Tạo stable object với defaults
    const stable = {
      page: options.page || 1,
      limit: options.limit || 100,
    };
    
    // Chỉ thêm các field có giá trị
    if (options.status && options.status !== 'all') {
      stable.status = options.status;
    }
    if (options.search && typeof options.search === 'string' && options.search.trim()) {
      stable.search = options.search.trim();
    }
    
    return stable;
  }, [options.page, options.limit, options.status, options.search]);

  // Tạo stable key cho SWR để tránh re-fetch không cần thiết
  const swrKey = useMemo(() => {
    const keyParts = ['leads'];
    
    // Chatbot ID part - stable
    if (chatbotId) {
      keyParts.push(`chatbot:${chatbotId}`);
    } else {
      keyParts.push('all');
    }
    
    // Options part - chỉ serialize các key có giá trị với stable order
    const optionsPart = [];
    if (stableOptions.status) {
      optionsPart.push(`status:${stableOptions.status}`);
    }
    if (stableOptions.search) {
      optionsPart.push(`search:${stableOptions.search}`);
    }
    if (stableOptions.page !== 1) {
      optionsPart.push(`page:${stableOptions.page}`);
    }
    if (stableOptions.limit !== 100) {
      optionsPart.push(`limit:${stableOptions.limit}`);
    }
    
    if (optionsPart.length > 0) {
      keyParts.push(optionsPart.join('&'));
    }
    
    return keyParts.join('|');
  }, [chatbotId, stableOptions]);

  const { data, error, mutate } = useSWR(
    swrKey,
    () => getLeads(chatbotId, stableOptions),
    {
      // Tối ưu SWR options để giảm thiểu API calls
      revalidateOnFocus: false, // Không tự động revalidate khi focus
      revalidateOnReconnect: true, // Revalidate khi reconnect
      dedupingInterval: 10000, // Dedupe requests trong 10 giây (tăng từ 5s)
      errorRetryCount: 2, // Chỉ retry 2 lần khi lỗi
      errorRetryInterval: 2000, // Retry sau 2 giây (tăng từ 1s)
      refreshInterval: 0, // Không auto refresh
      shouldRetryOnError: (err) => 
        // Chỉ retry cho network errors, không retry cho business logic errors
        err?.code !== 'BUSINESS_ERROR' && err?.code !== 'VALIDATION_ERROR',
    }
  );

  return {
    leads: data?.success ? data.data : [],
    loading: !data && !error,
    error: error || (!data?.success ? data?.error : null),
    mutate,
  };
}

// =====================================================
// KANBAN DISPLAY CONFIGURATION SERVICES
// =====================================================

/**
 * Lấy cấu hình hiển thị Kanban cho user hiện tại
 * @returns {Promise<Object>} - Kết quả cấu hình hiển thị
 */
export async function getKanbanDisplayConfig() {
  try {
    // Thứ tự ưu tiên: user_preferences -> chatbot_lead_configs -> default
    
    // 1. Kiểm tra user preferences trước
    // NOTE: fetchData() tự động convert filters từ camelCase sang snake_case
    // preferenceType -> preference_type, module -> module (already snake_case)
    const userPrefResult = await fetchData('user_preferences', {
      filters: { 
        preferenceType: 'kanban_display_config',
        module: 'leads' 
      },
      columns: ['preferenceValue'],
      single: true,
    });
    
    // Nếu có user preferences, sử dụng nó
    if (userPrefResult.success && userPrefResult.data) {
      const rawConfig = userPrefResult.data.preferenceValue;
      if (rawConfig && typeof rawConfig === 'object') {
        // Normalize config data to ensure camelCase consistency
        const normalizedConfig = normalizeKanbanConfig(rawConfig);
        return { success: true, data: normalizedConfig, error: null };
      }
    }
    
    // 2. Fallback to chatbot_lead_configs (legacy support)
    const chatbotConfigResult = await fetchData('chatbot_lead_configs', {
      columns: ['kanbanDisplayConfig'],
      single: true,
    });
    
    if (chatbotConfigResult.success && chatbotConfigResult.data?.kanbanDisplayConfig) {
      const config = chatbotConfigResult.data.kanbanDisplayConfig;
      const normalizedConfig = normalizeKanbanConfig(config);
      return { success: true, data: normalizedConfig, error: null };
    }
    
    // 3. Default fallback
    const defaultConfig = getDefaultKanbanConfig();
    return { success: true, data: defaultConfig, error: null };
    
  } catch (error) {
    return { success: false, error, data: getDefaultKanbanConfig() };
  }
}

/**
 * Normalize config data từ database để đảm bảo camelCase consistency
 * @param {Object} rawConfig - Raw config từ database
 * @returns {Object} - Normalized config
 */
function normalizeKanbanConfig(rawConfig) {
  if (!rawConfig || typeof rawConfig !== 'object') {
    return getDefaultKanbanConfig();
  }

  // Convert database snake_case to JavaScript camelCase
  const normalized = {
    // Support both snake_case and camelCase field names
    visibleFields: rawConfig.visible_fields || rawConfig.visibleFields || [],
    fieldSettings: rawConfig.field_settings || rawConfig.fieldSettings || {},
  };

  // Validate and ensure all visible fields have corresponding settings
  const defaultSettings = getDefaultKanbanConfig().fieldSettings;
  
  // Merge với default settings để đảm bảo có đầy đủ field definitions
  normalized.fieldSettings = {
    ...defaultSettings,
    ...normalized.fieldSettings,
  };

  // Filter out invalid visible fields (không có trong field settings)
  normalized.visibleFields = normalized.visibleFields.filter(fieldName => 
    normalized.fieldSettings[fieldName]
  );

  return normalized;
}

/**
 * Cập nhật cấu hình hiển thị Kanban cho user hiện tại
 * @param {Object} displayConfig - Cấu hình hiển thị
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function updateKanbanDisplayConfig(displayConfig) {
  try {
    // Đơn giản: chỉ cần preference data, trigger sẽ auto-set tenant_id
    const dataToSave = {
      preferenceType: 'kanban_display_config',
      module: 'leads', 
      preferenceValue: displayConfig,
      // tenant_id sẽ được auto-set bởi trigger auto_set_tenant_id_simple()
    };

    // upsertData() automatically converts onConflict columns from camelCase to snake_case
    // Database columns: preference_type, module (snake_case)
    // JavaScript code: preferenceType, module (camelCase) - auto-converted
    const result = await upsertData('user_preferences', dataToSave, ['preferenceType', 'module']);
    
    return result;
  } catch (error) {
    return { success: false, error, data: null };
  }
}

/**
 * Lấy các tùy chọn trạng thái lead - DEPRECATED
 * Sử dụng getWorkflowStatusOptions() từ unified-workflow-service thay thế
 * @returns {Array} - Danh sách các tùy chọn trạng thái
 */
export function getLeadStatusOptions() {
  // DEPRECATED: Sử dụng để backward compatibility
  // Nên sử dụng stagesToStatusOptions() từ unified-workflow-service
  
  return [
    { value: 'new', label: 'Mới', color: 'info' },
    { value: 'contacted', label: 'Đã liên hệ', color: 'warning' },
    { value: 'qualified', label: 'Tiềm năng', color: 'secondary' },
    { value: 'converted', label: 'Đã chuyển đổi', color: 'success' },
    { value: 'lost', label: 'Thất bại', color: 'error' },
  ];
}

/**
 * Lấy status options từ workflow stages - RECOMMENDED
 * @param {Array} stages - Workflow stages từ unified-workflow-service
 * @returns {Array} - Danh sách status options đồng bộ với workflow
 */
export function getStatusOptionsFromWorkflow(stages) {
  if (!stages || !Array.isArray(stages)) {
    // Fallback to deprecated function
    return getLeadStatusOptions();
  }

  return stages.map(stage => ({
    value: stage.id,
    label: stage.name,
    color: stage.displayColor || stage.color || 'default',
    icon: stage.icon || null,
    description: stage.description || '',
    order: stage.order || 0,
    isFinal: stage.isFinal || false,
  })).sort((a, b) => (a.order || 0) - (b.order || 0));
}

/**
 * Lấy cấu hình mặc định cho Kanban display
 * @returns {Object} - Cấu hình mặc định
 */
export function getDefaultKanbanConfig() {
  return {
    visibleFields: ['phone', 'email', 'source', 'leadScore'],
    fieldSettings: {
      fullName: {
        show: true,
        label: 'Họ tên',
        icon: 'solar:user-bold',
        type: 'text',
        order: 1
      },
      phone: {
        show: true,
        label: 'Điện thoại',
        icon: 'solar:phone-bold',
        type: 'text',
        order: 2
      },
      email: {
        show: true,
        label: 'Email',
        icon: 'solar:letter-bold',
        type: 'email',
        order: 3
      },
      company: {
        show: false,
        label: 'Công ty',
        icon: 'solar:buildings-bold',
        type: 'text',
        order: 4
      },
      address: {
        show: false,
        label: 'Địa chỉ',
        icon: 'solar:map-point-bold',
        type: 'text',
        order: 5
      },
      source: {
        show: true,
        label: 'Nguồn',
        icon: 'solar:routing-bold',
        type: 'text',
        order: 6
      },
      status: {
        show: true,
        label: 'Trạng thái',
        icon: 'solar:flag-bold',
        type: 'status',
        order: 7
      },
      leadScore: {
        show: true,
        label: 'Điểm Lead',
        icon: 'solar:star-bold',
        type: 'number',
        order: 8
      },
      createdAt: {
        show: false,
        label: 'Ngày tạo',
        icon: 'solar:calendar-bold',
        type: 'date',
        order: 9
      },
      updatedAt: {
        show: false,
        label: 'Cập nhật',
        icon: 'solar:clock-circle-bold',
        type: 'date',
        order: 10
      },
      nextFollowUpAt: {
        show: false,
        label: 'Follow up',
        icon: 'solar:calendar-mark-bold',
        type: 'date',
        order: 11
      },
      notes: {
        show: false,
        label: 'Ghi chú',
        icon: 'solar:notes-bold',
        type: 'text',
        order: 12
      }
    }
  };
}

/**
 * Lấy danh sách các field options cho Kanban config
 * @returns {Array} - Danh sách field options
 */
export function getKanbanFieldOptions() {
  const defaultConfig = getDefaultKanbanConfig();
  
  return Object.entries(defaultConfig.fieldSettings).map(([key, config]) => ({
    value: key,
    label: config.label,
    icon: config.icon,
    type: config.type,
    order: config.order,
    show: config.show
  })).sort((a, b) => a.order - b.order);
}

/**
 * Hook để lấy cấu hình hiển thị Kanban
 * @returns {Object} - SWR response với config
 */
export function useKanbanDisplayConfig() {
  const { data, error, mutate } = useSWR(
    'kanban-display-config',
    () => getKanbanDisplayConfig()
  );

  return {
    config: data?.success ? data.data : null,
    loading: !data && !error,
    error: error || (!data?.success ? data?.error : null),
    mutate,
  };
}

// =====================================================
// UTILITY FUNCTIONS
// =====================================================

/**
 * Tạo fields mặc định
 * @returns {Array} - Mảng các field mặc định
 */
export function getDefaultFields() {
  return [
    {
      id: 'full_name',
      name: 'full_name',
      label: 'Họ và tên',
      type: 'text',
      required: true,
      description: 'Họ và tên đầy đủ của khách hàng, sử dụng để xưng hô và liên hệ',
      enabled: false, // Mặc định chưa kích hoạt
    },
    {
      id: 'phone',
      name: 'phone',
      label: 'Số điện thoại',
      type: 'text',
      required: true,
      description: 'Số điện thoại di động để liên hệ trực tiếp với khách hàng',
      enabled: false,
    },
    {
      id: 'email',
      name: 'email',
      label: 'Email',
      type: 'text',
      required: false,
      description: 'Địa chỉ email để gửi thông tin và theo dõi sau này',
      enabled: false,
    },
    {
      id: 'address',
      name: 'address',
      label: 'Địa chỉ',
      type: 'text',
      required: false,
      description: 'Địa chỉ liên hệ để giao hàng hoặc hỗ trợ',
      enabled: false,
    },
    {
      id: 'company',
      name: 'company',
      label: 'Công ty',
      type: 'text',
      required: false,
      description: 'Tên công ty nơi khách hàng làm việc',
      enabled: false,
    },
  ];
}

/**
 * Validate dữ liệu lead theo fields config
 * @param {Object} leadData - Dữ liệu lead
 * @param {Array} fields - Cấu hình fields
 * @returns {Object} - Kết quả validation
 */
export function validateLeadData(leadData, fields) {
  const errors = {};
  
  // Chỉ validate các field được kích hoạt
  const enabledFields = fields.filter(field => field.enabled);
  
  enabledFields.forEach(field => {
    const value = leadData[field.name];
    
    // Kiểm tra field bắt buộc
    if (field.required && (!value || value.toString().trim() === '')) {
      errors[field.name] = `${field.label} là bắt buộc`;
    }
    
    // Kiểm tra loại dữ liệu number
    if (field.type === 'number' && value && isNaN(Number(value))) {
      errors[field.name] = `${field.label} phải là số`;
    }
  });
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
}

/**
 * Lấy danh sách loại field
 * @returns {Array} - Mảng các tùy chọn loại field
 */
export function getFieldTypeOptions() {
  return [
    { value: 'text', label: 'Văn bản' },
    { value: 'number', label: 'Số' },
    { value: 'email', label: 'Email' },
    { value: 'phone', label: 'Số điện thoại' },
  ];
} 