'use client';

/**
 * Component để xử lý các vấn đề hydration
 */

import './styles.css';

import { useEffect } from 'react';

/**
 * Hook để xử lý các vấn đề hydration
 */
export function useHydrationFix() {
  useEffect(() => {
    // Chỉ chạy ở phía client
    if (typeof window !== 'undefined' && typeof document !== 'undefined') {
      // Xử lý các thuộc tính data-* trên thẻ html và body
      const elementsToCheck = [document.documentElement, document.body];
      
      // Danh sách các thuộc tính cần xóa
      const attributesToRemove = [
        'data-atm-ext-installed',
        'data-atm-ext-version',
        'data-extension-id',
        'data-extension-version',
      ];
      
      // Xóa các thuộc tính
      elementsToCheck.forEach((element) => {
        attributesToRemove.forEach((attr) => {
          if (element.hasAttribute(attr)) {
            element.removeAttribute(attr);
          }
        });
      });
      
      // <PERSON> dõ<PERSON> các thay đổi DOM trong tương lai
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'attributes' && 
              attributesToRemove.includes(mutation.attributeName)) {
            mutation.target.removeAttribute(mutation.attributeName);
          }
        });
      });
      
      // Bắt đầu theo dõi các thay đổi thuộc tính
      elementsToCheck.forEach((element) => {
        observer.observe(element, { attributes: true });
      });
      
      // Dọn dẹp khi component unmount
      return () => {
        observer.disconnect();
      };
    }
  }, []);
}

/**
 * Component để xử lý các vấn đề hydration
 */
export function HydrationFix({ children }) {
  useHydrationFix();
  return children;
}

export default HydrationFix;
