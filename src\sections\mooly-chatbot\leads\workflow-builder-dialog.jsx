'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';

import {
  Box,
  Tab,
  Tabs,
  Stack,
  Dialog,
  Button,
  TextField,
  Typography,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Card,
  Chip,
  Grid,
  IconButton,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Divider,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Tooltip,
  Paper,
  Avatar,
  Badge,
} from '@mui/material';
import { LoadingButton } from '@mui/lab';
import { alpha } from '@mui/material/styles';

import { Iconify } from 'src/components/iconify';
import { toast } from 'src/components/snackbar';
import { Label } from 'src/components/label';

import { 
  useWorkflowTemplates, 
  useGlobalWorkflow,
  applyGlobalWorkflowTemplate,
  upsertGlobalWorkflow,
  validateWorkflowConfig,
  BUSINESS_WORKFLOW_TEMPLATES
} from 'src/actions/mooly-chatbot/workflow-config-service';
import { DragDropContext, Draggable, Droppable } from '@hello-pangea/dnd';

// =====================================================
// CONSTANTS
// =====================================================

const STAGE_COLORS = {
  info: '#00B8D4',
  primary: '#1976D2', 
  warning: '#F57C00',
  secondary: '#7B1FA2',
  success: '#388E3C',
  error: '#D32F2F',
  default: '#757575',
};

const STAGE_ICONS = [
  'solar:user-plus-bold',
  'solar:phone-bold',
  'solar:star-bold',
  'solar:document-text-bold',
  'solar:check-circle-bold',
  'solar:close-circle-bold',
  'solar:heart-bold',
  'solar:shopping-cart-bold',
  'solar:bag-check-bold',
  'solar:calendar-bold',
  'solar:chat-bold',
  'solar:eye-bold',
  'solar:question-circle-bold',
  'solar:settings-bold',
];

const ACTION_OPTIONS = [
  { value: 'contact', label: 'Liên hệ', icon: 'solar:phone-bold' },
  { value: 'follow_up', label: 'Theo dõi', icon: 'solar:refresh-bold' },
  { value: 'qualify', label: 'Đánh giá', icon: 'solar:star-bold' },
  { value: 'quote', label: 'Báo giá', icon: 'solar:document-text-bold' },
  { value: 'schedule', label: 'Đặt lịch', icon: 'solar:calendar-bold' },
  { value: 'convert', label: 'Chuyển đổi', icon: 'solar:check-circle-bold' },
  { value: 'nurture', label: 'Chăm sóc', icon: 'solar:heart-bold' },
  { value: 'archive', label: 'Lưu trữ', icon: 'solar:archive-bold' },
];

// =====================================================
// STAGE EDITOR COMPONENT
// =====================================================

function StageEditor({ stage, onUpdate, onDelete, canDelete = true, index }) {
  const [isEditing, setIsEditing] = useState(false);
  const [editData, setEditData] = useState(stage);

  const handleSave = () => {
    if (!editData.name?.trim()) {
      toast.error('Tên giai đoạn là bắt buộc');
      return;
    }

    onUpdate(editData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditData(stage);
    setIsEditing(false);
  };

  const stageColor = STAGE_COLORS[stage.color] || STAGE_COLORS.default;

  if (isEditing) {
    return (
      <Card 
        sx={{ 
          p: 2, 
          border: '2px solid', 
          borderColor: 'primary.main',
          bgcolor: alpha(stageColor, 0.1)
        }}
      >
        <Stack spacing={2}>
          <TextField
            label="Tên giai đoạn"
            value={editData.name}
            onChange={(e) => setEditData({ ...editData, name: e.target.value })}
            size="small"
            fullWidth
          />

          <TextField
            label="Mô tả"
            value={editData.description || ''}
            onChange={(e) => setEditData({ ...editData, description: e.target.value })}
            size="small"
            multiline
            rows={2}
            fullWidth
          />

          <Grid container spacing={2}>
            <Grid item xs={6}>
              <FormControl fullWidth size="small">
                <InputLabel>Màu sắc</InputLabel>
                <Select
                  value={editData.color}
                  onChange={(e) => setEditData({ ...editData, color: e.target.value })}
                  label="Màu sắc"
                >
                  {Object.entries(STAGE_COLORS).map(([key, color]) => (
                    <MenuItem key={key} value={key}>
                      <Stack direction="row" alignItems="center" spacing={1}>
                        <Box
                          sx={{
                            width: 16,
                            height: 16,
                            bgcolor: color,
                            borderRadius: '50%',
                          }}
                        />
                        <Typography variant="body2" sx={{ textTransform: 'capitalize' }}>
                          {key}
                        </Typography>
                      </Stack>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={6}>
              <FormControl fullWidth size="small">
                <InputLabel>Icon</InputLabel>
                <Select
                  value={editData.icon}
                  onChange={(e) => setEditData({ ...editData, icon: e.target.value })}
                  label="Icon"
                >
                  {STAGE_ICONS.map((icon) => (
                    <MenuItem key={icon} value={icon}>
                      <Stack direction="row" alignItems="center" spacing={1}>
                        <Iconify icon={icon} width={16} />
                        <Typography variant="body2">
                          {icon.split(':')[1]?.replace('-bold', '') || icon}
                        </Typography>
                      </Stack>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          </Grid>

          <TextField
            label="Thời gian tối đa (giờ)"
            type="number"
            value={editData.maxDuration || ''}
            onChange={(e) => setEditData({ ...editData, maxDuration: parseInt(e.target.value) || null })}
            size="small"
            fullWidth
            helperText="Để trống nếu không giới hạn thời gian"
          />

          <Stack direction="row" spacing={1} justifyContent="flex-end">
            <Button size="small" onClick={handleCancel}>
              Hủy
            </Button>
            <Button size="small" variant="contained" onClick={handleSave}>
              Lưu
            </Button>
          </Stack>
        </Stack>
      </Card>
    );
  }

  return (
    <Card 
      sx={{ 
        p: 2, 
        cursor: 'move',
        bgcolor: alpha(stageColor, 0.1),
        border: '1px solid',
        borderColor: alpha(stageColor, 0.3),
        '&:hover': {
          borderColor: stageColor,
        }
      }}
    >
      <Stack spacing={1}>
        <Stack direction="row" alignItems="center" justifyContent="space-between">
          <Stack direction="row" alignItems="center" spacing={1}>
            <Badge badgeContent={index + 1} color="primary">
              <Avatar
                sx={{
                  width: 32,
                  height: 32,
                  bgcolor: stageColor,
                  color: 'white'
                }}
              >
                <Iconify icon={stage.icon || 'solar:user-bold'} width={16} />
              </Avatar>
            </Badge>

            <Box>
              <Typography variant="subtitle2" fontWeight="600">
                {stage.name}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {stage.description || 'Không có mô tả'}
              </Typography>
            </Box>
          </Stack>

          <Stack direction="row" spacing={0.5}>
            <Tooltip title="Chỉnh sửa">
              <IconButton size="small" onClick={() => setIsEditing(true)}>
                <Iconify icon="solar:pen-bold" width={16} />
              </IconButton>
            </Tooltip>

            {canDelete && (
              <Tooltip title="Xóa giai đoạn">
                <IconButton size="small" color="error" onClick={() => onDelete(stage.id)}>
                  <Iconify icon="solar:trash-bin-trash-bold" width={16} />
                </IconButton>
              </Tooltip>
            )}
          </Stack>
        </Stack>

        {stage.maxDuration && (
          <Label color="info" variant="soft" size="small">
            Tối đa: {stage.maxDuration} giờ
          </Label>
        )}

        <Label color={stage.color} variant="soft" size="small">
          {stage.color?.toUpperCase() || 'DEFAULT'}
        </Label>
      </Stack>
    </Card>
  );
}

// =====================================================
// MAIN COMPONENT
// =====================================================

export default function WorkflowBuilderDialog({
  open,
  onClose,
  onSuccess,
}) {
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(false);
  const [workflowConfig, setWorkflowConfig] = useState({
    name: '',
    description: '',
    stages: [],
    transitions: [],
    notifications: {},
    automations: [],
    colors: {},
    icons: {},
  });

  // Load templates and global workflow
  const { templates, loading: templatesLoading } = useWorkflowTemplates();
  const { workflow, mutate: mutateWorkflow } = useGlobalWorkflow();

  // Initialize with current workflow if exists
  useEffect(() => {
    if (open && workflow) {
      setWorkflowConfig({
        name: workflow.name || '',
        description: workflow.description || '',
        stages: workflow.stages || [],
        transitions: workflow.transitions || [],
        notifications: workflow.notifications || {},
        automations: workflow.automations || [],
        colors: workflow.colors || {},
        icons: workflow.icons || {},
      });
    } else if (open && !workflow) {
      // Reset to default
      setWorkflowConfig({
        name: 'Workflow Tùy chỉnh',
        description: '',
        stages: [],
        transitions: [],
        notifications: {},
        automations: [],
        colors: {},
        icons: {},
      });
    }
  }, [open, workflow]);

  // Handle template selection
  const handleApplyTemplate = useCallback(async (template) => {
    try {
      setLoading(true);

      if (template.isBusinessTemplate) {
        // Apply business template
        const businessTemplate = BUSINESS_WORKFLOW_TEMPLATES[template.key];
        const stages = businessTemplate.stages.map((stage, index) => ({
          id: stage.id,
          name: stage.name,
          description: stage.description || '',
          order: index + 1,
          color: stage.color,
          icon: stage.icon,
          maxDuration: null,
        }));

        const colors = {};
        const icons = {};
        stages.forEach(stage => {
          colors[stage.id] = stage.color;
          icons[stage.id] = stage.icon;
        });

        setWorkflowConfig(prev => ({
          ...prev,
          name: businessTemplate.name,
          description: businessTemplate.description,
          stages,
          colors,
          icons,
        }));
      } else {
        // Apply system template
        const result = await applyGlobalWorkflowTemplate(template.id);
        
        if (result.success) {
          await mutateWorkflow();
          toast.success('Áp dụng template thành công!');
          onClose();
        } else {
          toast.error(result.error || 'Không thể áp dụng template');
        }
      }
    } catch (error) {
      toast.error('Có lỗi xảy ra khi áp dụng template');
    } finally {
      setLoading(false);
    }
  }, [mutateWorkflow, onClose]);

  // Handle stage operations
  const handleAddStage = useCallback(() => {
    const newStage = {
      id: `stage_${Date.now()}`,
      name: `Giai đoạn ${workflowConfig.stages.length + 1}`,
      description: '',
      order: workflowConfig.stages.length + 1,
      color: 'info',
      icon: 'solar:star-bold',
      maxDuration: null,
    };

    setWorkflowConfig(prev => ({
      ...prev,
      stages: [...prev.stages, newStage],
      colors: { ...prev.colors, [newStage.id]: newStage.color },
      icons: { ...prev.icons, [newStage.id]: newStage.icon },
    }));
  }, [workflowConfig.stages.length]);

  const handleUpdateStage = useCallback((updatedStage) => {
    setWorkflowConfig(prev => ({
      ...prev,
      stages: prev.stages.map(stage => 
        stage.id === updatedStage.id ? updatedStage : stage
      ),
      colors: { ...prev.colors, [updatedStage.id]: updatedStage.color },
      icons: { ...prev.icons, [updatedStage.id]: updatedStage.icon },
    }));
  }, []);

  const handleDeleteStage = useCallback((stageId) => {
    setWorkflowConfig(prev => ({
      ...prev,
      stages: prev.stages.filter(stage => stage.id !== stageId),
      colors: { ...prev.colors, [stageId]: undefined },
      icons: { ...prev.icons, [stageId]: undefined },
    }));
  }, []);

  // Handle drag and drop
  const handleDragEnd = useCallback((result) => {
    if (!result.destination) return;

    const stages = Array.from(workflowConfig.stages);
    const [reorderedStage] = stages.splice(result.source.index, 1);
    stages.splice(result.destination.index, 0, reorderedStage);

    // Update order
    const reorderedStages = stages.map((stage, index) => ({
      ...stage,
      order: index + 1,
    }));

    setWorkflowConfig(prev => ({
      ...prev,
      stages: reorderedStages,
    }));
  }, [workflowConfig.stages]);

  // Handle save
  const handleSave = useCallback(async () => {
    try {
      setLoading(true);

      // Validate configuration
      const validation = validateWorkflowConfig(workflowConfig);
      if (!validation.isValid) {
        toast.error(`Cấu hình không hợp lệ: ${validation.errors.join(', ')}`);
        return;
      }

      // Save workflow
      const result = await upsertGlobalWorkflow(workflowConfig);

      if (result.success) {
        await mutateWorkflow();
        toast.success('Lưu workflow thành công!');
        onSuccess?.(result.data);
        onClose();
      } else {
        toast.error(result.error || 'Không thể lưu workflow');
      }
    } catch (error) {
      toast.error('Có lỗi xảy ra khi lưu workflow');
    } finally {
      setLoading(false);
    }
  }, [workflowConfig, mutateWorkflow, onSuccess, onClose]);

  // Business templates for selection
  const businessTemplates = useMemo(() => 
    Object.entries(BUSINESS_WORKFLOW_TEMPLATES).map(([key, template]) => ({
      ...template,
      key,
      isBusinessTemplate: true,
      id: key,
    }))
  , []);

  // All templates (system + business)
  const allTemplates = useMemo(() => [
    ...businessTemplates,
    ...(templates || []).map(t => ({ ...t, isBusinessTemplate: false }))
  ], [businessTemplates, templates]);

  const renderTemplateCard = (template) => (
    <Card
      key={template.id}
      sx={{
        p: 2,
        cursor: 'pointer',
        border: '2px solid',
        borderColor: workflowConfig.id === template.id ? 'primary.main' : 'transparent',
        '&:hover': {
          borderColor: workflowConfig.id === template.id ? 'primary.main' : 'primary.light',
        },
      }}
      onClick={() => handleApplyTemplate(template)}
    >
      <Stack spacing={1}>
        <Stack direction="row" alignItems="center" justifyContent="space-between">
          <Typography variant="subtitle1" fontWeight="600">
            {template.name}
          </Typography>
          {workflowConfig.id === template.id && (
            <Chip label="Đã chọn" color="primary" size="small" />
          )}
        </Stack>

        <Typography variant="body2" color="text.secondary">
          {template.description}
        </Typography>

        <Stack direction="row" spacing={1} flexWrap="wrap">
          {template.isBusinessTemplate && (
            <Label color="info" variant="soft" size="small">
              Nghiệp vụ
            </Label>
          )}
          {template.isSystemTemplate && (
            <Label color="warning" variant="soft" size="small">
              Hệ thống
            </Label>
          )}
          <Label color="default" variant="soft" size="small">
            {template.stages?.length || 0} giai đoạn
          </Label>
        </Stack>
      </Stack>
    </Card>
  );

  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      maxWidth="xl"
      fullWidth
      PaperProps={{
        sx: { height: '90vh' }
      }}
    >
      <DialogTitle>
        <Stack direction="row" alignItems="center" spacing={2}>
          <Iconify icon="solar:widget-5-bold" width={24} />
          <Typography variant="h6">Thiết kế Workflow Lead</Typography>
        </Stack>
      </DialogTitle>

      <DialogContent>
        <Box sx={{ width: '100%' }}>
          <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)}>
            <Tab label="Chọn Template" />
            <Tab label="Thiết kế Visual" />
            <Tab label="Cài đặt nâng cao" />
          </Tabs>

          <Box sx={{ mt: 3 }}>
            {/* Tab 1: Template Selection */}
            {activeTab === 0 && (
              <Stack spacing={3}>
                <Alert severity="info" icon={<Iconify icon="solar:lightbulb-bold" />}>
                  <Typography variant="body2">
                    Chọn template phù hợp với quy trình kinh doanh của bạn. Bạn có thể tùy chỉnh sau khi chọn.
                  </Typography>
                </Alert>

                <Typography variant="h6">Template Nghiệp vụ</Typography>
                <Grid container spacing={2}>
                  {businessTemplates.map(renderTemplateCard)}
                </Grid>

                {templates.length > 0 && (
                  <>
                    <Typography variant="h6">Template Hệ thống</Typography>
                    <Grid container spacing={2}>
                      {templates.map(renderTemplateCard)}
                    </Grid>
                  </>
                )}
              </Stack>
            )}

            {/* Tab 2: Visual Designer */}
            {activeTab === 1 && (
              <Stack spacing={3}>
                <Card sx={{ p: 2 }}>
                  <Stack spacing={2}>
                    <Typography variant="h6">Thông tin Workflow</Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={6}>
                        <TextField
                          label="Tên workflow"
                          value={workflowConfig.name}
                          onChange={(e) => setWorkflowConfig(prev => ({ 
                            ...prev, 
                            name: e.target.value 
                          }))}
                          fullWidth
                          required
                        />
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <TextField
                          label="Mô tả"
                          value={workflowConfig.description}
                          onChange={(e) => setWorkflowConfig(prev => ({ 
                            ...prev, 
                            description: e.target.value 
                          }))}
                          fullWidth
                        />
                      </Grid>
                    </Grid>
                  </Stack>
                </Card>

                <Card sx={{ p: 2 }}>
                  <Stack spacing={2}>
                    <Stack direction="row" alignItems="center" justifyContent="space-between">
                      <Typography variant="h6">Các giai đoạn Workflow</Typography>
                      <Button
                        variant="contained"
                        size="small"
                        startIcon={<Iconify icon="solar:add-circle-bold" />}
                        onClick={handleAddStage}
                      >
                        Thêm giai đoạn
                      </Button>
                    </Stack>

                    {workflowConfig.stages.length === 0 ? (
                      <Paper
                        sx={{
                          p: 4,
                          textAlign: 'center',
                          bgcolor: 'grey.50',
                          border: '2px dashed',
                          borderColor: 'grey.300',
                        }}
                      >
                        <Iconify icon="solar:widget-5-bold" width={48} sx={{ color: 'grey.400', mb: 2 }} />
                        <Typography variant="h6" color="text.secondary" gutterBottom>
                          Chưa có giai đoạn nào
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Thêm giai đoạn đầu tiên để bắt đầu thiết kế workflow
                        </Typography>
                      </Paper>
                    ) : (
                      <DragDropContext onDragEnd={handleDragEnd}>
                        <Droppable droppableId="stages">
                          {(provided) => (
                            <Stack 
                              spacing={2} 
                              ref={provided.innerRef}
                              {...provided.droppableProps}
                            >
                              {workflowConfig.stages.map((stage, index) => (
                                <Draggable key={stage.id} draggableId={stage.id} index={index}>
                                  {(providedDraggable, snapshotDraggable) => (
                                    <Box
                                      ref={providedDraggable.innerRef}
                                      {...providedDraggable.draggableProps}
                                      {...providedDraggable.dragHandleProps}
                                      sx={{
                                        transform: snapshotDraggable.isDragging ? 'rotate(5deg)' : 'none',
                                        transition: 'transform 0.2s',
                                      }}
                                    >
                                      <StageEditor
                                        stage={stage}
                                        index={index}
                                        onUpdate={handleUpdateStage}
                                        onDelete={handleDeleteStage}
                                        canDelete={workflowConfig.stages.length > 1}
                                      />
                                    </Box>
                                  )}
                                </Draggable>
                              ))}
                              {provided.placeholder}
                            </Stack>
                          )}
                        </Droppable>
                      </DragDropContext>
                    )}
                  </Stack>
                </Card>
              </Stack>
            )}

            {/* Tab 3: Advanced Settings */}
            {activeTab === 2 && (
              <Stack spacing={3}>
                <Card sx={{ p: 2 }}>
                  <Stack spacing={2}>
                    <Typography variant="h6">Cài đặt Thông báo</Typography>
                    
                    <FormControlLabel
                      control={
                        <Switch
                          checked={workflowConfig.notifications.email}
                          onChange={(e) => setWorkflowConfig(prev => ({
                            ...prev,
                            notifications: {
                              ...prev.notifications,
                              email: e.target.checked,
                            }
                          }))}
                        />
                      }
                      label="Thông báo Email"
                    />

                    <FormControlLabel
                      control={
                        <Switch
                          checked={workflowConfig.notifications.sms}
                          onChange={(e) => setWorkflowConfig(prev => ({
                            ...prev,
                            notifications: {
                              ...prev.notifications,
                              sms: e.target.checked,
                            }
                          }))}
                        />
                      }
                      label="Thông báo SMS"
                    />

                    <FormControlLabel
                      control={
                        <Switch
                          checked={workflowConfig.notifications.slack}
                          onChange={(e) => setWorkflowConfig(prev => ({
                            ...prev,
                            notifications: {
                              ...prev.notifications,
                              slack: e.target.checked,
                            }
                          }))}
                        />
                      }
                      label="Thông báo Slack"
                    />
                  </Stack>
                </Card>

                <Card sx={{ p: 2 }}>
                  <Stack spacing={2}>
                    <Typography variant="h6">Preview Workflow</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Xem trước workflow của bạn sẽ hiển thị như thế nào
                    </Typography>

                    <Box sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                      <Stack direction="row" spacing={2} flexWrap="wrap">
                        {workflowConfig.stages.map((stage, index) => (
                          <Box key={stage.id} sx={{ display: 'flex', alignItems: 'center' }}>
                            <Label 
                              color={stage.color} 
                              variant="filled"
                              sx={{ 
                                minWidth: 100,
                                '& .MuiSvgIcon-root': { mr: 1 }
                              }}
                              startIcon={<Iconify icon={stage.icon} />}
                            >
                              {stage.name}
                            </Label>
                            {index < workflowConfig.stages.length - 1 && (
                              <Iconify 
                                icon="solar:arrow-right-bold" 
                                sx={{ mx: 1, color: 'text.secondary' }} 
                              />
                            )}
                          </Box>
                        ))}
                      </Stack>
                    </Box>
                  </Stack>
                </Card>
              </Stack>
            )}
          </Box>
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} disabled={loading}>
          Hủy bỏ
        </Button>
        <LoadingButton
          variant="contained"
          loading={loading}
          onClick={handleSave}
          startIcon={<Iconify icon="solar:check-bold" />}
          disabled={workflowConfig.stages.length === 0}
        >
          Lưu Workflow
        </LoadingButton>
      </DialogActions>
    </Dialog>
  );
} 