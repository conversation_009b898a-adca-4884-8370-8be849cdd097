'use client';

import { useState, useEffect } from 'react';

import Alert from '@mui/material/Alert';
import Snackbar from '@mui/material/Snackbar';
import AlertTitle from '@mui/material/AlertTitle';

import { useAuthContext } from 'src/auth/hooks';

// ----------------------------------------------------------------------

export function WelcomeCreditsNotification() {
  const { user } = useAuthContext();
  const [showWelcome, setShowWelcome] = useState(false);

  useEffect(() => {
    if (user) {
      // Kiểm tra xem user có phải là user mới không
      const userCreatedAt = new Date(user.created_at);
      const now = new Date();
      const timeDiff = now.getTime() - userCreatedAt.getTime();
      const minutesDiff = timeDiff / (1000 * 60);

      // Nếu user được tạo trong vòng 5 phút qua, hiển thị thông báo chào mừng
      if (minutesDiff <= 5) {
        // Kiểm tra localStorage để tránh hiển thị lại
        const hasShownWelcome = localStorage.getItem(`welcome_shown_${user.id}`);
        if (!hasShownWelcome) {
          setShowWelcome(true);
          localStorage.setItem(`welcome_shown_${user.id}`, 'true');
        }
      }
    }
  }, [user]);

  const handleClose = () => {
    setShowWelcome(false);
  };

  if (!showWelcome) {
    return null;
  }

  return (
    <Snackbar
      open={showWelcome}
      autoHideDuration={8000}
      onClose={handleClose}
      anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      sx={{ mt: 8 }}
    >
      <Alert
        onClose={handleClose}
        severity="success"
        variant="filled"
        sx={{ width: '100%', maxWidth: 400 }}
      >
        <AlertTitle>Chào mừng bạn đến với Mooly AI!</AlertTitle>
        Tài khoản của bạn đã được tạo thành công và bạn đã nhận được{' '}
        <strong>200 credits miễn phí</strong> để bắt đầu sử dụng dịch vụ.
      </Alert>
    </Snackbar>
  );
}
