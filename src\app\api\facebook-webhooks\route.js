import { NextResponse } from 'next/server';
import crypto from 'crypto';
import { createAdminClient } from 'src/utils/supabase/server';
import { facebookTokenManager } from 'src/lib/facebook-token-manager';
import { FACEBOOK_CONFIG } from 'src/lib/facebook-config';

/**
 * Facebook Webhooks Handler
 * Handles webhooks from Facebook/Instagram for comments and messages
 */

// Verify webhook signature for security
function verifyWebhookSignature(payload, signature) {
  const expectedSignature = crypto
    .createHmac('sha256', FACEBOOK_CONFIG.APP_SECRET)
    .update(payload, 'utf8')
    .digest('hex');

  const signatureHash = signature.replace('sha256=', '');

  return crypto.timingSafeEqual(
    Buffer.from(expectedSignature, 'hex'),
    Buffer.from(signatureHash, 'hex')
  );
}

// GET: Webhook verification for Facebook
export async function GET(request) {
  const { searchParams } = new URL(request.url);

  const mode = searchParams.get('hub.mode');
  const token = searchParams.get('hub.verify_token');
  const challenge = searchParams.get('hub.challenge');

  // Verify the webhook
  if (mode === 'subscribe' && token === FACEBOOK_CONFIG.WEBHOOK_VERIFY_TOKEN) {
    return new Response(challenge, { status: 200 });
  }

  return new Response('Forbidden', { status: 403 });
}

// POST: Handle webhook events
export async function POST(request) {
  try {
    const body = await request.text();
    const signature = request.headers.get('x-hub-signature-256');

    // Verify webhook signature
    if (!signature || !verifyWebhookSignature(body, signature)) {
      return NextResponse.json({ error: 'Invalid signature' }, { status: 403 });
    }

    const data = JSON.parse(body);

    // Process webhook entries
    if (data.entry && Array.isArray(data.entry)) {
      for (const entry of data.entry) {
        await processWebhookEntry(entry);
      }
    }

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('💥 Webhook processing error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// Process individual webhook entry
async function processWebhookEntry(entry) {
  try {
    // console.log('🔄 Processing webhook entry:', entry.id); // Redundant log

    // Handle different types of changes
    if (entry.changes) {
      for (const change of entry.changes) {
        await processWebhookChange(entry.id, change);
      }
    }

    // Handle messaging events
    if (entry.messaging) {
      for (const message of entry.messaging) {
        await processMessagingEvent(entry.id, message);
      }
    }

  } catch (error) {
    console.error('💥 Error processing webhook entry:', error);
  }
}

// Process webhook changes (comments, posts, etc.)
async function processWebhookChange(pageId, change) {
  try {
    console.log(`📝 Processing change for page ${pageId}: ${change.field}`);

    switch (change.field) {
      case 'feed':
        await handleFeedChange(pageId, change.value);
        break;
      case 'comments':
        await handleCommentChange(pageId, change.value);
        break;
      case 'mention':
        await handleMentionChange(pageId, change.value);
        break;
      default:
        console.log('ℹ️ Unhandled change field:', change.field);
    }

  } catch (error) {
    console.error('💥 Error processing webhook change:', error);
  }
}

// Handle messaging events (private messages)
async function processMessagingEvent(pageId, message) {
  try {
    console.log('💬 Processing messaging event:', message);

    if (message.message) {
      await handlePrivateMessage(pageId, message);
    }

  } catch (error) {
    console.error('💥 Error processing messaging event:', error);
  }
}

// Handle feed changes (new posts, etc.)
async function handleFeedChange(pageId, value) {
  // console.log('📰 Feed change for page:', pageId, value); // This is redundant as handleNewComment will log details.

  // Check if this is a comment on a post
  if (value.item === 'comment' && value.verb === 'add') {
    await handleNewComment(pageId, value);
  }

  // Log activity
  await logActivity(pageId, 'feed_change', { value });
}

// Handle comment changes
async function handleCommentChange(pageId, value) {
  console.log('💬 Comment change for page:', pageId, value);

  if (value.verb === 'add') {
    // New comment added
    await handleNewComment(pageId, value);
  } else if (value.verb === 'edited') {
    // Comment edited
    console.log('✏️ Comment edited:', value.comment_id);
  } else if (value.verb === 'remove') {
    // Comment removed
    console.log('🗑️ Comment removed:', value.comment_id);
  }
}

// Handle mention changes
async function handleMentionChange(pageId, value) {
  console.log('🏷️ Mention change for page:', pageId, value);
  await logActivity(pageId, 'mention', { value });
}

// Handle private messages
async function handlePrivateMessage(pageId, message) {
  console.log('📨 Private message for page:', pageId, message);

  // Check if auto reply is enabled for messages
  const autoReplyEnabled = await checkAutoReplyConfig(pageId, 'message');

  if (autoReplyEnabled) {
    await generateAndSendReply(pageId, 'message', message);
  }

  await logActivity(pageId, 'private_message', { message });
}

// Handle new comments
async function handleNewComment(pageId, commentData) {
  // Check if comment is from the page itself to prevent auto-reply loop
  if (commentData.from?.id === pageId) {
    await logActivity(pageId, 'comment_skipped_self', { commentData });
    return;
  }

  // Check if auto reply is enabled for comments
  const autoReplyEnabled = await checkAutoReplyConfig(pageId, 'comment');

  if (autoReplyEnabled) {
    // Send both comment reply and private message
    await sendCommentReplyAndPrivateMessage(pageId, commentData);
  }

  await logActivity(pageId, 'new_comment', { commentData });
}

// Check auto reply configuration
// For testing purposes, always enable auto-reply.
// TODO: Revert this to use the database config.
async function checkAutoReplyConfig(pageId, type) {
  console.log(`[TEST MODE] Auto-reply check for page ${pageId} and type ${type} is always ON.`);
  return true;
}

// Send both comment reply and private message
async function sendCommentReplyAndPrivateMessage(pageId, commentData) {
  try {
    console.log(`🤖 Preparing to reply for comment: ${commentData.comment_id}`);

    const replyText = FACEBOOK_CONFIG.DEFAULT_REPLY_TEXT;
    const pageAccessToken = await getPageAccessToken(pageId);

    if (!pageAccessToken) {
      console.error(`❌ Missing Page Access Token for page: ${pageId}. Cannot send replies.`);
      return;
    }


    const results = [];

    // 1. Reply to the comment publicly
    if (commentData.comment_id) {
      try {
        const commentReplyEndpoint = FACEBOOK_CONFIG.getApiUrl(FACEBOOK_CONFIG.ENDPOINTS.COMMENT_REPLIES(commentData.comment_id));
        const commentReplyPayload = {
          message: replyText,
          access_token: pageAccessToken,
        };

        console.log('📝 Sending comment reply to:', commentData.comment_id);
        const commentResponse = await fetch(commentReplyEndpoint, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(commentReplyPayload),
        });

        const commentResult = await commentResponse.json();

        if (commentResult.error) {
          console.error('❌ Comment reply failed:', commentResult.error);
          results.push({ type: 'comment_reply', success: false, error: commentResult.error });
        } else {
          console.log('✅ Comment reply sent successfully:', commentResult.id);
          results.push({ type: 'comment_reply', success: true, id: commentResult.id });
        }
      } catch (error) {
        console.error('❌ Error sending comment reply:', error);
        results.push({ type: 'comment_reply', success: false, error: error.message });
      }
    }

    // 2. Send private message to commenter using Messages API
    if (commentData.comment_id) {
      try {
        const messageEndpoint = FACEBOOK_CONFIG.getApiUrl(FACEBOOK_CONFIG.ENDPOINTS.PAGE_MESSAGES(pageId));
        const messagePayload = {
          recipient: { comment_id: commentData.comment_id },
          message: { text: replyText },
          messaging_type: 'RESPONSE',
          access_token: pageAccessToken,
        };

        const messageResponse = await fetch(messageEndpoint, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(messagePayload),
        });

        const messageResult = await messageResponse.json();

        if (messageResult.error) {
          results.push({ type: 'private_message', success: false, error: messageResult.error });
        } else {
          results.push({ type: 'private_message', success: true, message_id: messageResult.message_id });
        }
      } catch (error) {
        results.push({ type: 'private_message', success: false, error: error.message });
      }
    }

    // Log the results
    await logActivity(pageId, 'auto_reply_sent', {
      comment_id: commentData.comment_id,
      commenter_id: commentData.from?.id,
      results: results
    });

    console.log('📊 Auto-reply results:', results);
    return results;

  } catch (error) {
    console.error('💥 Error in sendCommentReplyAndPrivateMessage:', error);
    await logActivity(pageId, 'auto_reply_failed', {
      comment_id: commentData.comment_id,
      error: error.message
    });
  }
}

// Helper to get Page Access Token with auto-refresh
async function getPageAccessToken(pageId) {
  try {
    return await facebookTokenManager.getValidPageToken(pageId);
  } catch (error) {
    console.error('❌ Error getting page access token:', error);
    return null;
  }
}

// Generate and send auto reply
async function generateAndSendReply(pageId, type, eventData) {
  try {
    console.log(`[TEST MODE] 🤖 Generating test reply for: ${pageId}, type: ${type}`);

    const replyText = FACEBOOK_CONFIG.DEFAULT_REPLY_TEXT;
    const pageAccessToken = await getPageAccessToken(pageId);

    if (!pageAccessToken) {
      console.error(`❌ Missing Page Access Token for page: ${pageId}. Cannot send reply.`);
      return;
    }

    let endpoint = '';
    let payload = {};

    if (type === 'message' && eventData.sender && eventData.sender.id) {
      // Reply to a private message
      endpoint = FACEBOOK_CONFIG.getApiUrl('/me/messages');
      payload = {
        recipient: { id: eventData.sender.id },
        message: { text: replyText },
        messaging_type: 'RESPONSE',
        access_token: pageAccessToken,
      };
    } else if (type === 'comment' && eventData.comment_id) {
      // Send a private reply to a comment
      endpoint = FACEBOOK_CONFIG.getApiUrl(FACEBOOK_CONFIG.ENDPOINTS.PRIVATE_REPLIES(eventData.comment_id));
      payload = {
        message: { text: replyText },
        access_token: pageAccessToken,
      };
    } else {
      console.log(`ℹ️ [TEST MODE] Unhandled event type for auto-reply: ${type}`, eventData);
      return;
    }

    console.log(`[TEST MODE] Sending reply to ${endpoint}`);
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload),
    });

    const responseData = await response.json();

    if (!response.ok) {
      console.error('💥 [TEST MODE] Failed to send Facebook reply:', JSON.stringify(responseData, null, 2));
    } else {
      console.log('✅ [TEST MODE] Successfully sent test reply:', JSON.stringify(responseData, null, 2));
    }

  } catch (error) {
    console.error('💥 [TEST MODE] Error generating auto reply:', error);
  }
}

// Log activity to database
async function logActivity(pageId, activity, metadata) {
  try {
    const supabase = createAdminClient();

    // Get tenant_id from facebook_accounts table
    const { data: accountData, error: accountError } = await supabase
      .from('facebook_accounts')
      .select('tenant_id')
      .eq('page_id', pageId)
      .single();

    if (accountError || !accountData) {
      return;
    }

    await supabase
      .from('facebook_activity_logs')
      .insert({
        tenant_id: accountData.tenant_id,
        page_id: pageId,
        activity,
        metadata,
        created_at: new Date().toISOString()
      });

  } catch (error) {
    // Silent fail for logging
  }
}
