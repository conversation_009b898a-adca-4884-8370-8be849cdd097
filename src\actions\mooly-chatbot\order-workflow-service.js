'use client';

/**
 * Order Workflow Service
 * Dịch vụ quản lý quy trình đơn hàng thông minh với:
 * - Smart order processing (xử lý đơn hàng thông minh)
 * - Status automation rules (quy tắc tự động hóa trạng thái)
 * - Notification system (hệ thống thông báo)
 * - Order analytics (phân tích đơn hàng)
 */

import { BUSINESS_TYPES } from './business-config-service';
import { callRPC, fetchData, createData, updateData } from './supabase-utils';

// Order related tables
const ORDERS_TABLE = 'orders';
const ORDER_ITEMS_TABLE = 'order_items';
const ORDER_STATUS_HISTORY_TABLE = 'order_history'; // Sửa tên bảng đúng
const WORKFLOW_RULES_TABLE = 'workflow_rules';
const NOTIFICATION_TEMPLATES_TABLE = 'notification_templates';

// Order status constants
export const ORDER_STATUS = {
  PENDING: 'pending',
  CONFIRMED: 'confirmed',
  PROCESSING: 'processing',
  SHIPPED: 'shipped',
  DELIVERED: 'delivered',
  CANCELLED: 'cancelled',
  REFUNDED: 'refunded'
};

// Workflow rule types
export const WORKFLOW_RULE_TYPES = {
  STATUS_CHANGE: 'status_change',
  PAYMENT_RECEIVED: 'payment_received',
  INVENTORY_UPDATE: 'inventory_update',
  NOTIFICATION: 'notification',
  EMAIL_SEND: 'email_send'
};

/**
 * 🔄 SMART ORDER PROCESSING FUNCTIONS
 */

/**
 * Xử lý đơn hàng thông minh
 * @param {Object} orderData - Dữ liệu đơn hàng
 * @param {Object} options - Tùy chọn xử lý
 * @returns {Promise<Object>} - Kết quả xử lý
 */
export async function processSmartOrder(orderData, options = {}) {
  try {
    const { businessType, autoConfirm = false, autoInventoryUpdate = true } = options;

    // 1. Validate order data based on business type
    const validationResult = validateOrderByBusinessType(orderData, businessType);
    if (!validationResult.isValid) {
      return {
        success: false,
        error: validationResult.errors.join(', ')
      };
    }

    // 2. Check inventory availability
    if (autoInventoryUpdate) {
      const inventoryCheck = await checkOrderInventory(orderData.items);
      if (!inventoryCheck.success) {
        return {
          success: false,
          error: inventoryCheck.error
        };
      }
    }

    // 3. Calculate order totals
    const orderTotals = await calculateOrderTotals(orderData);

    // 4. Create order with calculated data
    const finalOrderData = {
      ...orderData,
      ...orderTotals,
      status: autoConfirm ? ORDER_STATUS.CONFIRMED : ORDER_STATUS.PENDING,
      processedAt: new Date().toISOString()
    };

    const orderResult = await createData(ORDERS_TABLE, finalOrderData);

    if (!orderResult.success) {
      throw new Error('Không thể tạo đơn hàng');
    }

    const orderId = orderResult.data.id;

    // 5. Create order items
    if (orderData.items && orderData.items.length > 0) {
      const orderItems = orderData.items.map(item => ({
        orderId,
        productId: item.productId,
        variantId: item.variantId || null,
        quantity: item.quantity,
        price: item.price,
        totalPrice: item.price * item.quantity
      }));

      await createData(ORDER_ITEMS_TABLE, orderItems);
    }

    // 6. Update inventory if enabled
    if (autoInventoryUpdate) {
      await updateInventoryForOrder(orderData.items, 'decrease');
    }

    // 7. Create status history
    await createOrderStatusHistory(orderId, ORDER_STATUS.PENDING, 'Đơn hàng được tạo');

    // 8. Trigger workflow rules
    await triggerWorkflowRules('order_created', {
      orderId,
      orderData: finalOrderData,
      businessType
    });

    return {
      success: true,
      data: {
        order: orderResult.data,
        orderId
      }
    };
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Lỗi khi xử lý đơn hàng'
    };
  }
}

/**
 * Validate order theo business type
 * @param {Object} orderData - Dữ liệu đơn hàng
 * @param {string} businessType - Loại hình kinh doanh
 * @returns {Object} - Kết quả validation
 */
function validateOrderByBusinessType(orderData, businessType) {
  const errors = [];

  // Basic validation
  if (!orderData.customerId) {
    errors.push('Thiếu thông tin khách hàng');
  }

  if (!orderData.items || orderData.items.length === 0) {
    errors.push('Đơn hàng phải có ít nhất một sản phẩm');
  }

  // Business type specific validation
  if (businessType) {
    const businessConfig = BUSINESS_TYPES[businessType];

    if (businessConfig) {
      // Digital products validation
      if (businessConfig.features.digitalProducts) {
        const hasDigitalItems = orderData.items?.some(item => item.productType === 'digital');
        if (hasDigitalItems && !orderData.customerEmail) {
          errors.push('Đơn hàng sản phẩm số cần email khách hàng');
        }
      }

      // Services validation
      if (businessConfig.features.services) {
        const hasServiceItems = orderData.items?.some(item => item.productType === 'service');
        if (hasServiceItems && !orderData.appointmentDate) {
          errors.push('Đơn hàng dịch vụ cần thông tin lịch hẹn');
        }
      }

      // Shipping validation
      if (businessConfig.features.shipping) {
        const hasPhysicalItems = orderData.items?.some(item =>
          ['simple', 'variable'].includes(item.productType)
        );
        if (hasPhysicalItems && !orderData.shippingAddress) {
          errors.push('Đơn hàng sản phẩm vật lý cần địa chỉ giao hàng');
        }
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Kiểm tra tồn kho cho đơn hàng
 * @param {Array} orderItems - Danh sách sản phẩm trong đơn hàng
 * @returns {Promise<Object>} - Kết quả kiểm tra
 */
async function checkOrderInventory(orderItems) {
  try {
    for (const item of orderItems) {
      // Skip inventory check for digital products and services
      if (['digital', 'service'].includes(item.productType)) {
        continue;
      }

      // Check product or variant inventory
      const inventoryResult = await callRPC('check_product_inventory', {
        product_id: item.productId,
        variant_id: item.variantId,
        required_quantity: item.quantity
      });

      if (!inventoryResult.success || !inventoryResult.data.available) {
        return {
          success: false,
          error: `Sản phẩm ${item.productName} không đủ tồn kho`
        };
      }
    }

    return { success: true };
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Lỗi khi kiểm tra tồn kho'
    };
  }
}

/**
 * Tính toán tổng tiền đơn hàng
 * @param {Object} orderData - Dữ liệu đơn hàng
 * @returns {Promise<Object>} - Kết quả tính toán
 */
async function calculateOrderTotals(orderData) {
  try {
    let subtotal = 0;
    let totalDiscount = 0;
    let shippingFee = 0;
    let taxAmount = 0;

    // Calculate subtotal
    if (orderData.items) {
      subtotal = orderData.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    }

    // Calculate discount
    if (orderData.discountCode) {
      const discountResult = await callRPC('calculate_discount', {
        discount_code: orderData.discountCode,
        subtotal,
        items: orderData.items
      });

      if (discountResult.success) {
        totalDiscount = discountResult.data.discountAmount || 0;
      }
    }

    // Calculate shipping fee
    if (orderData.shippingMethod && orderData.shippingAddress) {
      const shippingResult = await callRPC('calculate_shipping_fee', {
        shipping_method: orderData.shippingMethod,
        shipping_address: orderData.shippingAddress,
        items: orderData.items
      });

      if (shippingResult.success) {
        shippingFee = shippingResult.data.shippingFee || 0;
      }
    }

    // Calculate tax
    if (orderData.taxIncluded !== false) {
      taxAmount = (subtotal - totalDiscount) * 0.1; // 10% VAT
    }

    const totalAmount = subtotal - totalDiscount + shippingFee + taxAmount;

    return {
      subtotal,
      totalDiscount,
      shippingFee,
      taxAmount,
      totalAmount
    };
  } catch (error) {
    // Return default values if calculation fails
    return {
      subtotal: 0,
      totalDiscount: 0,
      shippingFee: 0,
      taxAmount: 0,
      totalAmount: 0
    };
  }
}

/**
 * Cập nhật tồn kho cho đơn hàng
 * @param {Array} orderItems - Danh sách sản phẩm
 * @param {string} action - Hành động (increase/decrease)
 * @returns {Promise<Object>} - Kết quả cập nhật
 */
async function updateInventoryForOrder(orderItems, action = 'decrease') {
  try {
    for (const item of orderItems) {
      // Skip inventory update for digital products and services
      if (['digital', 'service'].includes(item.productType)) {
        continue;
      }

      await callRPC('update_product_inventory', {
        product_id: item.productId,
        variant_id: item.variantId,
        quantity_change: action === 'decrease' ? -item.quantity : item.quantity
      });
    }

    return { success: true };
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Lỗi khi cập nhật tồn kho'
    };
  }
}

/**
 * 📊 ORDER STATUS MANAGEMENT FUNCTIONS
 */

/**
 * Cập nhật trạng thái đơn hàng
 * @param {string} orderId - ID đơn hàng
 * @param {string} newStatus - Trạng thái mới
 * @param {string} note - Ghi chú
 * @param {Object} options - Tùy chọn
 * @returns {Promise<Object>} - Kết quả cập nhật
 */
export async function updateOrderStatus(orderId, newStatus, note = '', options = {}) {
  try {
    const { autoTriggerWorkflow = true, userId = null } = options;

    // 1. Update order status
    const updateResult = await updateData(ORDERS_TABLE, {
      status: newStatus,
      updatedAt: new Date().toISOString()
    }, { id: orderId });

    if (!updateResult.success) {
      throw new Error('Không thể cập nhật trạng thái đơn hàng');
    }

    // 2. Create status history
    await createOrderStatusHistory(orderId, newStatus, note, userId);

    // 3. Trigger workflow rules if enabled
    if (autoTriggerWorkflow) {
      await triggerWorkflowRules('status_changed', {
        orderId,
        newStatus,
        previousStatus: updateResult.data.status,
        note
      });
    }

    return {
      success: true,
      data: updateResult.data
    };
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Lỗi khi cập nhật trạng thái đơn hàng'
    };
  }
}

/**
 * Tạo lịch sử trạng thái đơn hàng
 * @param {string} orderId - ID đơn hàng
 * @param {string} status - Trạng thái
 * @param {string} note - Ghi chú
 * @param {string} userId - ID người dùng
 * @param {string} previousStatus - Trạng thái trước đó
 * @returns {Promise<Object>} - Kết quả tạo
 */
async function createOrderStatusHistory(orderId, status, note = '', userId = null, previousStatus = null) {
  try {
    // Sử dụng RPC function để tạo order history với tenant_id được xử lý đúng cách
    const result = await callRPC('create_order_history_entry', {
      p_order_id: orderId,
      p_status: status,
      p_comment: note,
      p_user_id: userId,
      p_previous_status: previousStatus
    });

    if (result.success && result.data) {
      return {
        success: true,
        data: { id: result.data },
        error: null
      };
    } else {
      return {
        success: false,
        error: result.error || 'Không thể tạo lịch sử đơn hàng',
        data: null
      };
    }
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Lỗi khi tạo lịch sử đơn hàng',
      data: null
    };
  }
}

/**
 * Lấy lịch sử trạng thái đơn hàng
 * @param {string} orderId - ID đơn hàng
 * @returns {Promise<Object>} - Kết quả truy vấn
 */
export async function getOrderStatusHistory(orderId) {
  return fetchData(ORDER_STATUS_HISTORY_TABLE, {
    filters: { orderId },
    orderBy: 'createdAt',
    ascending: false,
    columns: `
      *,
      user:users(id, name, email)
    `
  });
}

/**
 * 🔄 WORKFLOW AUTOMATION FUNCTIONS
 */

/**
 * Tạo quy tắc workflow
 * @param {Object} ruleData - Dữ liệu quy tắc
 * @returns {Promise<Object>} - Kết quả tạo
 */
export async function createWorkflowRule(ruleData) {
  try {
    const rule = {
      name: ruleData.name,
      description: ruleData.description,
      triggerEvent: ruleData.triggerEvent, // order_created, status_changed, payment_received
      conditions: ruleData.conditions || {}, // JSON conditions
      actions: ruleData.actions || [], // Array of actions
      isActive: ruleData.isActive !== false,
      businessType: ruleData.businessType || null,
      priority: ruleData.priority || 0
    };

    return createData(WORKFLOW_RULES_TABLE, rule);
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Lỗi khi tạo quy tắc workflow'
    };
  }
}

/**
 * Lấy danh sách quy tắc workflow
 * @param {Object} options - Tùy chọn truy vấn
 * @returns {Promise<Object>} - Kết quả truy vấn
 */
export async function getWorkflowRules(options = {}) {
  return fetchData(WORKFLOW_RULES_TABLE, {
    ...options,
    orderBy: 'priority',
    ascending: false
  });
}

/**
 * Trigger workflow rules
 * @param {string} event - Sự kiện trigger
 * @param {Object} context - Context data
 * @returns {Promise<Object>} - Kết quả thực thi
 */
async function triggerWorkflowRules(event, context) {
  try {
    // Get active workflow rules for this event
    const rulesResult = await fetchData(WORKFLOW_RULES_TABLE, {
      filters: {
        triggerEvent: event,
        isActive: true
      },
      orderBy: 'priority',
      ascending: false
    });

    if (!rulesResult.success || !rulesResult.data) {
      return { success: true, executedRules: 0 };
    }

    const rules = rulesResult.data;
    let executedRules = 0;

    // Execute each rule
    for (const rule of rules) {
      try {
        // Check if rule conditions are met
        const conditionsMet = evaluateRuleConditions(rule.conditions, context);

        if (conditionsMet) {
          // Execute rule actions
          await executeRuleActions(rule.actions, context);
          executedRules++;
        }
      } catch (ruleError) {
        console.error(`Error executing workflow rule ${rule.id}:`, ruleError);
        // Continue with other rules
      }
    }

    return {
      success: true,
      executedRules
    };
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Lỗi khi thực thi workflow rules'
    };
  }
}

/**
 * Đánh giá điều kiện của rule
 * @param {Object} conditions - Điều kiện
 * @param {Object} context - Context data
 * @returns {boolean} - Kết quả đánh giá
 */
function evaluateRuleConditions(conditions, context) {
  if (!conditions || Object.keys(conditions).length === 0) {
    return true; // No conditions means always execute
  }

  try {
    // Simple condition evaluation
    // This can be extended to support more complex conditions
    for (const [key, value] of Object.entries(conditions)) {
      const contextValue = getNestedValue(context, key);

      if (typeof value === 'object' && value !== null) {
        // Handle operators like { "gt": 100 }, { "eq": "confirmed" }
        for (const [operator, operandValue] of Object.entries(value)) {
          switch (operator) {
            case 'eq':
              if (contextValue !== operandValue) return false;
              break;
            case 'neq':
              if (contextValue === operandValue) return false;
              break;
            case 'gt':
              if (contextValue <= operandValue) return false;
              break;
            case 'gte':
              if (contextValue < operandValue) return false;
              break;
            case 'lt':
              if (contextValue >= operandValue) return false;
              break;
            case 'lte':
              if (contextValue > operandValue) return false;
              break;
            case 'in':
              if (!Array.isArray(operandValue) || !operandValue.includes(contextValue)) return false;
              break;
            default:
              return false;
          }
        }
      } else {
        // Simple equality check
        if (contextValue !== value) return false;
      }
    }

    return true;
  } catch (error) {
    console.error('Error evaluating rule conditions:', error);
    return false;
  }
}

/**
 * Lấy giá trị nested từ object
 * @param {Object} obj - Object
 * @param {string} path - Đường dẫn (vd: "orderData.totalAmount")
 * @returns {any} - Giá trị
 */
function getNestedValue(obj, path) {
  return path.split('.').reduce((current, key) => current && current[key] !== undefined ? current[key] : undefined, obj);
}

/**
 * Thực thi các action của rule
 * @param {Array} actions - Danh sách action
 * @param {Object} context - Context data
 * @returns {Promise<void>}
 */
async function executeRuleActions(actions, context) {
  for (const action of actions) {
    try {
      switch (action.type) {
        case 'update_status':
          if (context.orderId && action.status) {
            await updateOrderStatus(context.orderId, action.status, action.note || '', {
              autoTriggerWorkflow: false // Prevent infinite loops
            });
          }
          break;

        case 'send_notification':
          if (action.templateId) {
            await sendOrderNotification(context.orderId, action.templateId, action.recipients);
          }
          break;

        // case 'send_email':
        //   if (action.emailTemplate && action.recipient) {
        //     await sendOrderEmail(context.orderId, action.emailTemplate, action.recipient);
        //   }
        //   break;

        case 'update_inventory':
          if (context.orderData && context.orderData.items) {
            await updateInventoryForOrder(context.orderData.items, action.inventoryAction || 'decrease');
          }
          break;

        // case 'create_task':
        //   if (action.taskData) {
        //     await createOrderTask(context.orderId, action.taskData);
        //   }
        //   break;

        default:
          console.warn(`Unknown workflow action type: ${action.type}`);
      }
    } catch (actionError) {
      console.error(`Error executing action ${action.type}:`, actionError);
      // Continue with other actions
    }
  }
}

/**
 * 📧 NOTIFICATION SYSTEM FUNCTIONS
 */

/**
 * Gửi thông báo đơn hàng
 * @param {string} orderId - ID đơn hàng
 * @param {string} templateId - ID template thông báo
 * @param {Array} recipients - Danh sách người nhận
 * @returns {Promise<Object>} - Kết quả gửi
 */
export async function sendOrderNotification(orderId, templateId, recipients = []) {
  try {
    // Get notification template
    const templateResult = await fetchData(NOTIFICATION_TEMPLATES_TABLE, {
      filters: { id: templateId },
      single: true
    });

    if (!templateResult.success || !templateResult.data) {
      throw new Error('Không tìm thấy template thông báo');
    }

    const template = templateResult.data;

    // Get order data for template variables
    const orderResult = await fetchData(ORDERS_TABLE, {
      filters: { id: orderId },
      single: true,
      columns: `
        *,
        customer:customers(id, name, email, phone),
        items:order_items(
          *,
          product:products(id, name),
          variant:product_variants(id, name)
        )
      `
    });

    if (!orderResult.success || !orderResult.data) {
      throw new Error('Không tìm thấy đơn hàng');
    }

    const order = orderResult.data;

    // Render notification content
    const notificationContent = renderNotificationTemplate(template, order);

    // Send notification using RPC function
    const result = await callRPC('send_notification', {
      recipients,
      subject: notificationContent.subject,
      content: notificationContent.content,
      type: template.type || 'info',
      order_id: orderId
    });

    return result;
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Lỗi khi gửi thông báo'
    };
  }
}

/**
 * Render notification template
 * @param {Object} template - Template data
 * @param {Object} order - Order data
 * @returns {Object} - Rendered content
 */
function renderNotificationTemplate(template, order) {
  try {
    let subject = template.subject || '';
    let content = template.content || '';

    // Simple template variable replacement
    const variables = {
      '{{order.id}}': order.id,
      '{{order.status}}': order.status,
      '{{order.totalAmount}}': order.totalAmount?.toLocaleString('vi-VN') || '0',
      '{{customer.name}}': order.customer?.name || '',
      '{{customer.email}}': order.customer?.email || '',
      '{{customer.phone}}': order.customer?.phone || '',
      '{{order.createdAt}}': new Date(order.createdAt).toLocaleDateString('vi-VN'),
      '{{order.itemCount}}': order.items?.length || 0
    };

    // Replace variables in subject and content
    for (const [variable, value] of Object.entries(variables)) {
      subject = subject.replace(new RegExp(variable, 'g'), value);
      content = content.replace(new RegExp(variable, 'g'), value);
    }

    return {
      subject,
      content
    };
  } catch (error) {
    console.error('Error rendering notification template:', error);
    return {
      subject: template.subject || 'Thông báo đơn hàng',
      content: template.content || 'Có cập nhật mới về đơn hàng của bạn.'
    };
  }
}
