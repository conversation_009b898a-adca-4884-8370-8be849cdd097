'use client';

import { useState, useCallback } from 'react';

import {
  <PERSON>,
  <PERSON>,
  Stack,
  But<PERSON>,
  Container,
  Typo<PERSON>,
  Grid,
  <PERSON><PERSON>,
  Divider,
} from '@mui/material';

import { useBoolean } from 'src/hooks/use-boolean';

import { DashboardContent } from 'src/layouts/dashboard';

import { Iconify } from 'src/components/iconify';

import WorkflowSelector from '../leads/workflow-selector';
import WorkflowBuilderDialog from '../leads/workflow-builder-dialog';
import { useWorkflowTemplates } from 'src/actions/mooly-chatbot/workflow-config-service';

// =====================================================
// COMPONENT
// =====================================================

export default function WorkflowManagementView() {
  // Dialog states
  const workflowDialog = useBoolean();

  // Load templates for overview
  const { templates, loading: templatesLoading } = useWorkflowTemplates();

  // Handle workflow builder
  const handleWorkflowBuilder = useCallback(() => {
    workflowDialog.onTrue();
  }, [workflowDialog]);

  const renderTemplateCard = (template) => (
    <Card key={template.id} sx={{ p: 3, height: '100%' }}>
      <Stack spacing={2} height="100%">
        <Stack direction="row" alignItems="flex-start" justifyContent="space-between">
          <Box sx={{ flex: 1 }}>
            <Typography variant="h6" gutterBottom>
              {template.name}
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              {template.description}
            </Typography>
          </Box>
          <Iconify 
            icon={template.isSystemTemplate ? 'solar:shield-check-bold' : 'solar:user-bold'} 
            sx={{ color: template.isSystemTemplate ? 'warning.main' : 'primary.main' }}
          />
        </Stack>

        <Box sx={{ flex: 1 }}>
          <Typography variant="caption" color="text.secondary" gutterBottom>
            Giai đoạn workflow:
          </Typography>
          <Stack spacing={0.5} sx={{ mt: 1 }}>
            {template.stages?.slice(0, 3).map((stage, index) => (
              <Typography key={stage.id} variant="body2" fontSize="0.8rem">
                {index + 1}. {stage.name}
              </Typography>
            ))}
            {template.stages?.length > 3 && (
              <Typography variant="body2" fontSize="0.8rem" color="text.secondary">
                +{template.stages.length - 3} giai đoạn khác
              </Typography>
            )}
          </Stack>
        </Box>

        <Stack direction="row" spacing={1} alignItems="center">
          <Iconify icon="solar:layers-bold" width={16} />
          <Typography variant="caption">
            {template.stages?.length || 0} giai đoạn
          </Typography>
        </Stack>
      </Stack>
    </Card>
  );

  const renderQuickStartGuide = () => (
    <Card sx={{ p: 3, bgcolor: 'primary.lightest' }}>
      <Stack spacing={2}>
        <Stack direction="row" alignItems="center" spacing={2}>
          <Iconify icon="solar:lightbulb-bold" width={24} color="primary.main" />
          <Typography variant="h6">Hướng dẫn nhanh</Typography>
        </Stack>

        <Typography variant="body2" color="text.secondary">
          Thiết kế workflow giúp bạn quản lý leads hiệu quả hơn theo quy trình kinh doanh riêng của mình.
        </Typography>

        <Stack spacing={1}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Iconify icon="solar:check-circle-bold" width={16} color="success.main" />
            <Typography variant="body2">
              Chọn chatbot cần cấu hình workflow
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Iconify icon="solar:check-circle-bold" width={16} color="success.main" />
            <Typography variant="body2">
              Chọn template phù hợp với nghiệp vụ
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Iconify icon="solar:check-circle-bold" width={16} color="success.main" />
            <Typography variant="body2">
              Tùy chỉnh các giai đoạn theo nhu cầu
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Iconify icon="solar:check-circle-bold" width={16} color="success.main" />
            <Typography variant="body2">
              Lưu và áp dụng cho chatbot
            </Typography>
          </Box>
        </Stack>
      </Stack>
    </Card>
  );

  return (
    <DashboardContent>
      <Container maxWidth="xl">
        <Stack spacing={4}>
          {/* Header */}
          <Stack direction="row" alignItems="center" justifyContent="space-between">
            <Stack>
              <Typography variant="h4">Quản lý Workflow</Typography>
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                Thiết kế và quản lý quy trình leads cho từng chatbot theo logic kinh doanh của bạn
              </Typography>
            </Stack>

            <Button
              variant="contained"
              size="large"
              startIcon={<Iconify icon="solar:widget-5-bold" />}
              onClick={handleWorkflowBuilder}
            >
              Thiết kế Workflow
            </Button>
          </Stack>

          {/* Main Content */}
          <Grid container spacing={3}>
            {/* Left Column - Workflow Configuration */}
            <Grid item xs={12} lg={4}>
              <Stack spacing={3}>
                <WorkflowSelector
                  onWorkflowBuilderOpen={handleWorkflowBuilder}
                />

                {renderQuickStartGuide()}
              </Stack>
            </Grid>

            {/* Right Column - Templates & Resources */}
            <Grid item xs={12} lg={8}>
              <Stack spacing={3}>
                {/* Available Templates */}
                <Card sx={{ p: 3 }}>
                  <Stack spacing={3}>
                    <Stack direction="row" alignItems="center" spacing={2}>
                      <Iconify icon="solar:layers-bold" width={24} color="primary.main" />
                      <Typography variant="h6">Template Workflow có sẵn</Typography>
                    </Stack>

                    <Typography variant="body2" color="text.secondary">
                      Chọn template phù hợp với ngành nghề của bạn để bắt đầu nhanh chóng
                    </Typography>

                    <Grid container spacing={2}>
                      {templatesLoading ? (
                        // Loading skeleton
                        Array.from({ length: 3 }).map((_, index) => (
                          <Grid item xs={12} md={4} key={index}>
                            <Card sx={{ p: 3, height: 200 }}>
                              <Stack spacing={1}>
                                <Box sx={{ bgcolor: 'grey.200', height: 20, borderRadius: 1 }} />
                                <Box sx={{ bgcolor: 'grey.100', height: 40, borderRadius: 1 }} />
                                <Box sx={{ bgcolor: 'grey.100', height: 60, borderRadius: 1 }} />
                              </Stack>
                            </Card>
                          </Grid>
                        ))
                      ) : (
                        templates?.map((template) => (
                          <Grid item xs={12} md={4} key={template.id}>
                            {renderTemplateCard(template)}
                          </Grid>
                        ))
                      )}
                    </Grid>

                    {!templatesLoading && templates?.length === 0 && (
                      <Alert severity="info">
                        <Typography variant="body2">
                          Chưa có template nào. Bạn có thể tạo workflow tùy chỉnh riêng.
                        </Typography>
                      </Alert>
                    )}
                  </Stack>
                </Card>

                {/* Business Templates Section */}
                <Card sx={{ p: 3 }}>
                  <Stack spacing={3}>
                    <Stack direction="row" alignItems="center" spacing={2}>
                      <Iconify icon="solar:case-bold" width={24} color="primary.main" />
                      <Typography variant="h6">Template theo Ngành nghề</Typography>
                    </Stack>

                    <Typography variant="body2" color="text.secondary">
                      Template được thiết kế sẵn cho các ngành nghề phổ biến
                    </Typography>

                    <Grid container spacing={2}>
                      {[
                        { 
                          name: 'Bán lẻ / E-commerce',
                          description: 'Từ khách duyệt web đến mua hàng thành công',
                          icon: 'solar:bag-bold',
                          stages: ['Duyệt web', 'Quan tâm', 'Giỏ hàng', 'Thanh toán', 'Mua hàng']
                        },
                        { 
                          name: 'Dịch vụ / Tư vấn',
                          description: 'Từ hỏi thông tin đến hoàn thành dịch vụ',
                          icon: 'solar:user-speak-bold',
                          stages: ['Hỏi thông tin', 'Tư vấn', 'Báo giá', 'Đặt lịch', 'Hoàn thành']
                        },
                        { 
                          name: 'Bất động sản',
                          description: 'Từ duyệt tin đến ký hợp đồng',
                          icon: 'solar:home-2-bold',
                          stages: ['Duyệt tin', 'Quan tâm', 'Xem nhà', 'Thương lượng', 'Hợp đồng']
                        },
                        { 
                          name: 'Giáo dục',
                          description: 'Từ quan tâm khóa học đến hoàn thành',
                          icon: 'solar:book-bold',
                          stages: ['Quan tâm', 'Học thử', 'Tư vấn', 'Đăng ký', 'Hoàn thành']
                        },
                        { 
                          name: 'Y tế / Sức khỏe',
                          description: 'Từ hỏi thông tin đến hoàn thành điều trị',
                          icon: 'solar:medical-kit-bold',
                          stages: ['Hỏi thông tin', 'Đặt lịch', 'Check-in', 'Khám', 'Hoàn thành']
                        },
                        { 
                          name: 'Tùy chỉnh',
                          description: 'Tạo workflow riêng theo nhu cầu',
                          icon: 'solar:settings-bold',
                          stages: ['Tự thiết kế theo ý muốn']
                        },
                      ].map((business, index) => (
                        <Grid item xs={12} md={6} key={index}>
                          <Card sx={{ p: 2, height: '100%', cursor: 'pointer', '&:hover': { bgcolor: 'action.hover' } }}>
                            <Stack spacing={1.5} height="100%">
                              <Stack direction="row" alignItems="center" spacing={2}>
                                <Iconify icon={business.icon} width={24} color="primary.main" />
                                <Typography variant="subtitle2" fontWeight="600">
                                  {business.name}
                                </Typography>
                              </Stack>

                              <Typography variant="body2" color="text.secondary" fontSize="0.8rem">
                                {business.description}
                              </Typography>

                              <Box sx={{ flex: 1 }}>
                                <Typography variant="caption" color="text.secondary">
                                  Giai đoạn mẫu:
                                </Typography>
                                <Typography variant="body2" fontSize="0.75rem" sx={{ mt: 0.5 }}>
                                  {business.stages.join(' → ')}
                                </Typography>
                              </Box>
                            </Stack>
                          </Card>
                        </Grid>
                      ))}
                    </Grid>
                  </Stack>
                </Card>
              </Stack>
            </Grid>
          </Grid>
        </Stack>

        {/* Workflow Builder Dialog */}
        <WorkflowBuilderDialog
          open={workflowDialog.value}
          onClose={workflowDialog.onFalse}
          onSuccess={() => {
            // Workflow saved successfully
            // Can add success handling here
          }}
        />
      </Container>
    </DashboardContent>
  );
} 