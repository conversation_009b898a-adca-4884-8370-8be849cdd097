-- Migration: 001_enhance_products_table.sql
-- Description: Enhance products table for better business type support and optimization
-- Author: Development Team
-- Date: $(date)

-- =====================================================
-- 1. ADD BUSINESS TYPE TO TENANTS TABLE
-- =====================================================

-- Add business_type column to tenants table if not exists
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'tenants' AND column_name = 'business_type'
    ) THEN
        ALTER TABLE tenants ADD COLUMN business_type VARCHAR(50) DEFAULT 'retail';
        
        -- Add check constraint for business types
        ALTER TABLE tenants ADD CONSTRAINT check_business_type 
        CHECK (business_type IN ('retail', 'digital', 'services', 'hybrid'));
        
        COMMENT ON COLUMN tenants.business_type IS 'Type of business: retail, digital, services, or hybrid';
    END IF;
END $$;

-- =====================================================
-- 2. CREATE PRODUCT TYPE ENUM
-- =====================================================

-- Create product_type enum if not exists
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'product_type_enum') THEN
        CREATE TYPE product_type_enum AS ENUM (
            'simple',           -- Sản phẩm đơn giản
            'variable',         -- Sản phẩm có biến thể
            'digital',          -- Sản phẩm số
            'service',          -- Dịch vụ
            'bundle',           -- Gói sản phẩm
            'subscription'      -- Sản phẩm đăng ký
        );
    END IF;
END $$;

-- =====================================================
-- 3. ENHANCE PRODUCTS TABLE
-- =====================================================

-- Add new columns to products table
DO $$ 
BEGIN
    -- Add product_type_new column (temporary)
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'products' AND column_name = 'product_type_new'
    ) THEN
        ALTER TABLE products ADD COLUMN product_type_new product_type_enum DEFAULT 'simple';
    END IF;
    
    -- Add digital product fields
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'products' AND column_name = 'digital_product_info'
    ) THEN
        ALTER TABLE products ADD COLUMN digital_product_info JSONB DEFAULT '{}';
        COMMENT ON COLUMN products.digital_product_info IS 'Digital product specific information: download_url, file_size, license_type, etc.';
    END IF;
    
    -- Add service fields
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'products' AND column_name = 'service_info'
    ) THEN
        ALTER TABLE products ADD COLUMN service_info JSONB DEFAULT '{}';
        COMMENT ON COLUMN products.service_info IS 'Service specific information: duration, staff_required, booking_type, etc.';
    END IF;
    
    -- Add bundle fields
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'products' AND column_name = 'bundle_info'
    ) THEN
        ALTER TABLE products ADD COLUMN bundle_info JSONB DEFAULT '{}';
        COMMENT ON COLUMN products.bundle_info IS 'Bundle specific information: bundle_type, discount_type, etc.';
    END IF;
    
    -- Add enhanced dimensions
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'products' AND column_name = 'dimensions'
    ) THEN
        ALTER TABLE products ADD COLUMN dimensions JSONB DEFAULT '{}';
        COMMENT ON COLUMN products.dimensions IS 'Product dimensions: length, width, height, weight, volume';
    END IF;
    
    -- Add SEO and marketing fields
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'products' AND column_name = 'marketing_info'
    ) THEN
        ALTER TABLE products ADD COLUMN marketing_info JSONB DEFAULT '{}';
        COMMENT ON COLUMN products.marketing_info IS 'Marketing information: tags, featured_until, promotion_text, etc.';
    END IF;
    
    -- Add inventory settings
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'products' AND column_name = 'inventory_settings'
    ) THEN
        ALTER TABLE products ADD COLUMN inventory_settings JSONB DEFAULT '{}';
        COMMENT ON COLUMN products.inventory_settings IS 'Inventory settings: track_inventory, low_stock_threshold, allow_backorder, etc.';
    END IF;
    
    -- Add pricing settings
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'products' AND column_name = 'pricing_settings'
    ) THEN
        ALTER TABLE products ADD COLUMN pricing_settings JSONB DEFAULT '{}';
        COMMENT ON COLUMN products.pricing_settings IS 'Pricing settings: tax_class, pricing_rules, bulk_pricing, etc.';
    END IF;
END $$;

-- =====================================================
-- 4. MIGRATE EXISTING DATA
-- =====================================================

-- Migrate existing type field to new enum
UPDATE products 
SET product_type_new = CASE 
    WHEN type = 'simple' THEN 'simple'::product_type_enum
    WHEN type = 'variable' THEN 'variable'::product_type_enum
    WHEN type = 'digital' THEN 'digital'::product_type_enum
    WHEN type = 'service' THEN 'service'::product_type_enum
    WHEN type = 'bundle' THEN 'bundle'::product_type_enum
    ELSE 'simple'::product_type_enum
END;

-- Migrate dimensions data
UPDATE products 
SET dimensions = jsonb_build_object(
    'length', COALESCE(length, 0),
    'width', COALESCE(width, 0),
    'height', COALESCE(height, 0),
    'weight', COALESCE(weight, 0),
    'unit_length', 'cm',
    'unit_weight', 'kg'
)
WHERE length IS NOT NULL OR width IS NOT NULL OR height IS NOT NULL OR weight IS NOT NULL;

-- Migrate inventory settings
UPDATE products 
SET inventory_settings = jsonb_build_object(
    'track_inventory', COALESCE(track_inventory, true),
    'stock_quantity', COALESCE(stock_quantity, 0),
    'low_stock_threshold', 10,
    'allow_backorder', false,
    'stock_status', CASE 
        WHEN COALESCE(stock_quantity, 0) > 0 THEN 'in_stock'
        ELSE 'out_of_stock'
    END
);

-- =====================================================
-- 5. CREATE INDEXES FOR PERFORMANCE
-- =====================================================

-- Index for business type filtering
CREATE INDEX IF NOT EXISTS idx_tenants_business_type ON tenants(business_type);

-- Index for product type filtering
CREATE INDEX IF NOT EXISTS idx_products_product_type_new ON products(product_type_new);

-- Index for active products by tenant
CREATE INDEX IF NOT EXISTS idx_products_tenant_active ON products(tenant_id, is_active) WHERE is_active = true;

-- Index for featured products
CREATE INDEX IF NOT EXISTS idx_products_featured ON products(tenant_id, is_featured) WHERE is_featured = true;

-- GIN indexes for JSONB fields
CREATE INDEX IF NOT EXISTS idx_products_digital_info_gin ON products USING GIN(digital_product_info);
CREATE INDEX IF NOT EXISTS idx_products_service_info_gin ON products USING GIN(service_info);
CREATE INDEX IF NOT EXISTS idx_products_bundle_info_gin ON products USING GIN(bundle_info);
CREATE INDEX IF NOT EXISTS idx_products_dimensions_gin ON products USING GIN(dimensions);
CREATE INDEX IF NOT EXISTS idx_products_marketing_info_gin ON products USING GIN(marketing_info);
CREATE INDEX IF NOT EXISTS idx_products_inventory_settings_gin ON products USING GIN(inventory_settings);
CREATE INDEX IF NOT EXISTS idx_products_pricing_settings_gin ON products USING GIN(pricing_settings);

-- =====================================================
-- 6. ADD CONSTRAINTS AND VALIDATIONS
-- =====================================================

-- Add constraint for digital products
ALTER TABLE products ADD CONSTRAINT check_digital_product_info 
CHECK (
    product_type_new != 'digital' OR 
    (digital_product_info IS NOT NULL AND digital_product_info != '{}')
);

-- Add constraint for services
ALTER TABLE products ADD CONSTRAINT check_service_info 
CHECK (
    product_type_new != 'service' OR 
    (service_info IS NOT NULL AND service_info != '{}')
);

-- Add constraint for bundles
ALTER TABLE products ADD CONSTRAINT check_bundle_info 
CHECK (
    product_type_new != 'bundle' OR 
    (bundle_info IS NOT NULL AND bundle_info != '{}')
);

-- =====================================================
-- 7. UPDATE COMMENTS AND DOCUMENTATION
-- =====================================================

COMMENT ON TABLE products IS 'Enhanced products table supporting multiple business types and product types';
COMMENT ON COLUMN products.product_type_new IS 'Type of product: simple, variable, digital, service, bundle, subscription';

-- =====================================================
-- 8. CLEANUP (Run after verification)
-- =====================================================

-- Note: These commands should be run after verifying the migration
-- DROP COLUMN IF EXISTS type;
-- ALTER TABLE products RENAME COLUMN product_type_new TO product_type;
-- DROP COLUMN IF EXISTS length;
-- DROP COLUMN IF EXISTS width;
-- DROP COLUMN IF EXISTS height;
-- DROP COLUMN IF EXISTS weight;
-- DROP COLUMN IF EXISTS track_inventory;

-- =====================================================
-- ROLLBACK SCRIPT (if needed)
-- =====================================================

/*
-- To rollback this migration:

-- 1. Drop new columns
ALTER TABLE products DROP COLUMN IF EXISTS product_type_new;
ALTER TABLE products DROP COLUMN IF EXISTS digital_product_info;
ALTER TABLE products DROP COLUMN IF EXISTS service_info;
ALTER TABLE products DROP COLUMN IF EXISTS bundle_info;
ALTER TABLE products DROP COLUMN IF EXISTS dimensions;
ALTER TABLE products DROP COLUMN IF EXISTS marketing_info;
ALTER TABLE products DROP COLUMN IF EXISTS inventory_settings;
ALTER TABLE products DROP COLUMN IF EXISTS pricing_settings;

-- 2. Drop business_type from tenants
ALTER TABLE tenants DROP COLUMN IF EXISTS business_type;

-- 3. Drop enum type
DROP TYPE IF EXISTS product_type_enum;

-- 4. Drop indexes
DROP INDEX IF EXISTS idx_tenants_business_type;
DROP INDEX IF EXISTS idx_products_product_type_new;
DROP INDEX IF EXISTS idx_products_tenant_active;
DROP INDEX IF EXISTS idx_products_featured;
DROP INDEX IF EXISTS idx_products_digital_info_gin;
DROP INDEX IF EXISTS idx_products_service_info_gin;
DROP INDEX IF EXISTS idx_products_bundle_info_gin;
DROP INDEX IF EXISTS idx_products_dimensions_gin;
DROP INDEX IF EXISTS idx_products_marketing_info_gin;
DROP INDEX IF EXISTS idx_products_inventory_settings_gin;
DROP INDEX IF EXISTS idx_products_pricing_settings_gin;
*/
