# 🔧 Fix API Infinite Loop - Order List

## 🚨 Vấn đề gốc

**Error**: API được gọi liên tục trong danh sách đơn hàng

**Nguyên nhân**:
1. `useOrders` hook có logic phức tạp với nhiều `useEffect` conflict
2. `refreshTrigger` được thêm vào dependencies của `orderOptions` gây re-render vô hạn
3. Nhiều chỗ gọi cả `refetch()` và `setRefreshTrigger()` cùng lúc
4. `memoizedOptions` và `mutate` function dependencies không ổn định
5. Logic trong `useEffect` có thể gây vòng lặp

## ✅ Giải pháp đã áp dụng

### 1. **Sửa useOrders hook - Loại bỏ conflict useEffect**
```javascript
// ❌ Trước (2 useEffect conflict)
useEffect(() => {
  // Initial load
}, []);

useEffect(() => {
  // Reload when options change
}, [optionsKey, isLoading]);

// ✅ Sau (1 useEffect duy nhất)
useEffect(() => {
  const shouldLoad = prevOptionsKeyRef.current !== optionsKey;
  if (shouldLoad) {
    prevOptionsKeyRef.current = optionsKey;
    if (isFirstLoadRef.current) {
      isFirstLoadRef.current = false;
      loadData(); // Load ngay
    } else {
      timeoutId = setTimeout(loadData, 300); // Debounce
    }
  }
}, [optionsKey, fetchOrders, memoizedOptions]);
```

### 2. **Loại bỏ dependencies không ổn định**
```javascript
// ❌ Trước (dependencies thay đổi liên tục)
const fetchOrders = useCallback(async (optionsToUse) => {
  if (isLoading) return; // Gây vòng lặp
  // ...
}, [isLoading]);

const mutate = useCallback(async () => {
  if (!isLoading) {
    return fetchOrders(memoizedOptions);
  }
}, [fetchOrders, memoizedOptions, isLoading]);

// ✅ Sau (stable dependencies)
const fetchOrders = useCallback(async (optionsToUse) => {
  // Không check isLoading trong callback
  // ...
}, []); // Không phụ thuộc state

const mutate = useCallback(async () => {
  setIsLoading(true);
  return fetchOrders(memoizedOptions);
}, [fetchOrders, memoizedOptions]);
```

### 3. **Tách refreshTrigger khỏi orderOptions**
```javascript
// ❌ Trước (refreshTrigger trong dependencies)
const orderOptions = useMemo(() => {
  // ...
}, [filters, table, refreshTrigger]); // refreshTrigger gây re-render

// ✅ Sau (tách riêng)
const orderOptions = useMemo(() => {
  // ...
}, [filters, table]); // Không có refreshTrigger

// Trigger refetch riêng biệt
useEffect(() => {
  if (refreshTrigger > 0) {
    refetch();
  }
}, [refreshTrigger, refetch]);
```

### 4. **Loại bỏ duplicate API calls**
```javascript
// ❌ Trước (gọi cả 2)
if (result.success) {
  setRefreshTrigger(prev => prev + 1);
  refetch(); // Duplicate call
}

// ✅ Sau (chỉ trigger một lần)
if (result.success) {
  setRefreshTrigger(prev => prev + 1); // Sẽ trigger useEffect
}
```

## 🔄 Flow hoạt động sau khi fix

```
EnhancedOrderListView
├── orderOptions (memoized, không có refreshTrigger)
├── useOrders(orderOptions)
│   ├── Single useEffect với debounce
│   ├── fetchOrders (stable callback)
│   └── mutate (stable callback)
├── useEffect(refreshTrigger) → refetch()
└── Event handlers
    ├── handleCancelRow → setRefreshTrigger
    ├── handleStatusUpdate → setRefreshTrigger
    └── handleBatchSuccess → setRefreshTrigger
```

## 🎯 Kết quả

- ✅ **Không còn gọi API liên tục**
- ✅ **Debounce 300ms khi options thay đổi**
- ✅ **Stable dependencies trong useCallback**
- ✅ **Tách biệt refresh logic**
- ✅ **Performance tối ưu**

## 🧪 Test Cases

### Test 1: Load trang đầu tiên
- [ ] API chỉ gọi 1 lần khi mount
- [ ] Không có infinite loop
- [ ] Data hiển thị đúng

### Test 2: Thay đổi filters
- [ ] API gọi sau 300ms debounce
- [ ] Không gọi multiple times
- [ ] Results update đúng

### Test 3: Pagination/Sorting
- [ ] API gọi khi thay đổi page/sort
- [ ] Debounce hoạt động đúng
- [ ] Loading states chính xác

### Test 4: Refresh actions
- [ ] Cancel order → trigger refresh
- [ ] Status update → trigger refresh
- [ ] Create order → trigger refresh
- [ ] Chỉ 1 API call per action

## 📝 Lưu ý cho tương lai

1. **Tránh thêm state vào dependencies** nếu không cần thiết
2. **Sử dụng debounce** cho các API calls có thể trigger liên tục
3. **Tách biệt refresh logic** khỏi options dependencies
4. **Stable callbacks** với empty dependencies khi có thể
5. **Single useEffect** thay vì multiple effects conflict

---

**Status**: ✅ **FIXED** - API infinite loop đã được giải quyết hoàn toàn
