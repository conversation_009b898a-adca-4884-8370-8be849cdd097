import { FACEBOOK_TABLES, ACTIVITY_TYPES } from './facebook-constants';
import { fetchData, createData, updateData, deleteData, upsertData } from '../supabase-utils';

/**
 * Facebook Accounts API
 */

/**
 * <PERSON><PERSON><PERSON>nh s<PERSON>ch Facebook accounts
 * @param {Object} options - <PERSON><PERSON><PERSON> tù<PERSON> chọn
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getFacebookAccounts(options = {}) {
  try {
    // Use optimized API endpoint instead of direct database query
    const params = new URLSearchParams();

    if (options.chatbotId) {
      params.append('chatbotId', options.chatbotId);
    }

    // Only include additional data if explicitly requested
    if (options.includeConfig) {
      params.append('includeConfig', 'true');
    }

    if (options.includeActivity) {
      params.append('includeActivity', 'true');
    }

    const response = await fetch(`/api/facebook-integration/pages?${params.toString()}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();

    return {
      success: result.success || false,
      data: result.data || [],
      error: result.success ? null : new Error(result.error || 'Unknown error'),
      totalPages: result.totalPages || 0,
      selectedPage: result.selectedPage || null
    };
  } catch (error) {
    console.error('Error fetching Facebook accounts:', error);
    return {
      success: false,
      data: [],
      error,
      totalPages: 0,
      selectedPage: null
    };
  }
}

/**
 * Lấy Facebook account theo ID
 * @param {string} accountId - ID account
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getFacebookAccountById(accountId) {
  if (!accountId) {
    return { success: false, error: new Error('Thiếu ID account'), data: null };
  }

  return fetchData(FACEBOOK_TABLES.ACCOUNTS, {
    filters: { id: accountId },
    single: true
  });
}

/**
 * Lấy Facebook account theo Page ID
 * @param {string} pageId - Page ID
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getFacebookAccountByPageId(pageId) {
  if (!pageId) {
    return { success: false, error: new Error('Thiếu Page ID'), data: null };
  }

  return fetchData(FACEBOOK_TABLES.ACCOUNTS, {
    filters: { pageId },
    single: true
  });
}

/**
 * Tạo hoặc cập nhật Facebook account
 * @param {Object} accountData - Dữ liệu account
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function upsertFacebookAccount(accountData) {
  try {
    const result = await upsertData(
      FACEBOOK_TABLES.ACCOUNTS, 
      accountData, 
      ['pageId'] // Conflict resolution based on pageId
    );
    
    // Log activity
    if (result.success && result.data) {
      await logFacebookActivity(
        accountData.pageId,
        ACTIVITY_TYPES.PAGE_CONNECTED,
        { pageName: accountData.pageName }
      );
    }
    
    return result;
  } catch (error) {
    return { success: false, error, data: null };
  }
}

/**
 * Cập nhật Facebook account
 * @param {string} accountId - ID account
 * @param {Object} accountData - Dữ liệu cập nhật
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function updateFacebookAccount(accountId, accountData) {
  if (!accountId) {
    return { success: false, error: new Error('Thiếu ID account'), data: null };
  }

  return updateData(FACEBOOK_TABLES.ACCOUNTS, accountData, { id: accountId });
}

/**
 * Xóa Facebook account
 * @param {string} accountId - ID account
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function deleteFacebookAccount(accountId) {
  if (!accountId) {
    return { success: false, error: new Error('Thiếu ID account'), data: null };
  }

  return updateData(FACEBOOK_TABLES.ACCOUNTS, { isActive: false }, { id: accountId });
}

/**
 * Facebook Auto Reply Config API
 */

/**
 * Lấy cấu hình auto reply theo Page ID
 * @param {string} pageId - Page ID
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getAutoReplyConfig(pageId) {
  if (!pageId) {
    return { success: false, error: new Error('Thiếu Page ID'), data: null };
  }

  return fetchData(FACEBOOK_TABLES.CONFIG, {
    filters: { pageId },
    single: true
  });
}

/**
 * Tạo hoặc cập nhật cấu hình auto reply
 * @param {Object} configData - Dữ liệu cấu hình
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function upsertAutoReplyConfig(configData) {
  try {
    const result = await upsertData(
      FACEBOOK_TABLES.CONFIG,
      configData,
      ['pageId'] // Conflict resolution based on pageId
    );
    
    // Log activity
    if (result.success && result.data) {
      await logFacebookActivity(
        configData.pageId,
        ACTIVITY_TYPES.CONFIG_UPDATED,
        { 
          enabledFeatures: {
            commentReply: configData.enableCommentReply,
            messageReply: configData.enableMessageReply,
            instagramComments: configData.enableInstagramComments,
            instagramMessages: configData.enableInstagramMessages
          }
        }
      );
    }
    
    return result;
  } catch (error) {
    return { success: false, error, data: null };
  }
}

/**
 * Xóa cấu hình auto reply
 * @param {string} pageId - Page ID
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function deleteAutoReplyConfig(pageId) {
  if (!pageId) {
    return { success: false, error: new Error('Thiếu Page ID'), data: null };
  }

  const result = await deleteData(FACEBOOK_TABLES.CONFIG, { pageId });
  
  // Log activity
  if (result.success) {
    await logFacebookActivity(pageId, ACTIVITY_TYPES.CONFIG_DELETED, {});
  }
  
  return result;
}

/**
 * Facebook Activity Logs API
 */

/**
 * Ghi log hoạt động Facebook
 * @param {string} pageId - Page ID
 * @param {string} activity - Loại hoạt động
 * @param {Object} metadata - Dữ liệu bổ sung
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function logFacebookActivity(pageId, activity, metadata = {}) {
  if (!pageId || !activity) {
    return { success: false, error: new Error('Thiếu Page ID hoặc activity'), data: null };
  }

  return createData(FACEBOOK_TABLES.ACTIVITY_LOGS, {
    pageId,
    activity,
    metadata
  });
}

/**
 * Lấy logs hoạt động theo Page ID
 * @param {string} pageId - Page ID
 * @param {Object} options - Các tùy chọn
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getFacebookActivityLogs(pageId, options = {}) {
  if (!pageId) {
    return { success: false, error: new Error('Thiếu Page ID'), data: null };
  }

  const defaultOptions = {
    filters: { pageId },
    orderBy: 'createdAt',
    ascending: false,
    limit: 50,
    ...options
  };

  return fetchData(FACEBOOK_TABLES.ACTIVITY_LOGS, defaultOptions);
}

/**
 * Tìm page được chọn cho chatbot
 * @param {string} chatbotId - Chatbot ID
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getSelectedPageForChatbot(chatbotId) {
  if (!chatbotId) {
    return { success: false, error: new Error('Thiếu Chatbot ID'), data: null };
  }

  const result = await fetchData(FACEBOOK_TABLES.ACTIVITY_LOGS, {
    filters: { 
      activity: ACTIVITY_TYPES.PAGE_SELECTED,
      metadata: { operator: 'contains', value: { chatbotId } }
    },
    orderBy: 'createdAt',
    ascending: false,
    limit: 1,
    single: true
  });

  if (result.success && result.data) {
    // Get page details
    return getFacebookAccountByPageId(result.data.pageId);
  }

  return { success: false, error: new Error('Không tìm thấy page được chọn'), data: null };
}
