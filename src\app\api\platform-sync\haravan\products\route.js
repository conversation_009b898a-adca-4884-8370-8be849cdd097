import { NextResponse } from 'next/server';

import { withTenantAuth } from 'src/utils/server-auth';
import { callExternalApi, createErrorResponse, createSuccessResponse } from 'src/utils/api-response-handler';

/**
 * API route để đồng bộ sản phẩm từ Haravan
 * @param {Request} request - <PERSON><PERSON><PERSON> cầu HTTP
 * @returns {Promise<NextResponse>} - Phản hồi HTTP
 */
export const POST = withTenantAuth(async (request, { tenantId, userId }) => {
  try {
    // Lấy dữ liệu từ request body
    const { token, limit = 50, full_sync = true } = await request.json();

    // Kiểm tra dữ liệu đầu vào
    if (!token) {
      return NextResponse.json({ success: false, error: 'Token is required' }, { status: 400 });
    }

    // Lấy URL API từ biến môi trường
    const SYNC_API_URL = process.env.BACKEND_API_URL;

    // Nếu có BACKEND_API_URL, g<PERSON><PERSON> đến backend thực
    if (SYNC_API_URL && SYNC_API_URL !== process.env.NEXT_PUBLIC_SITE_URL) {
      try {
        const responseData = await callExternalApi(
          `${SYNC_API_URL}/api/sync/haravan/products`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${request.user?.accessToken || ''}`,
              'x-tenant-id': tenantId,
            },
            body: JSON.stringify({
              token,
              limit,
              full_sync,
              tenant_id: tenantId,
              user_id: userId
            }),
          },
          'Haravan sync'
        );

        if (!responseData.success) {
          throw new Error(responseData.message || 'Backend sync failed');
        }

        return NextResponse.json(createSuccessResponse(
          responseData.data,
          responseData.message || 'Đồng bộ sản phẩm từ Haravan thành công'
        ));
      } catch (backendError) {
        console.warn('Backend sync failed, falling back to mock:', backendError.message);
      }
    }

    // Mock implementation cho demo/testing
    console.log(`Mock: Syncing ${limit} products from Haravan for tenant ${tenantId}`);

    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Mock response
    const mockSyncedCount = Math.min(limit, Math.floor(Math.random() * 10) + 1);

    return NextResponse.json(createSuccessResponse(
      {
        synced_count: mockSyncedCount,
        total_available: limit,
        platform: 'haravan',
        sync_type: full_sync ? 'full' : 'incremental',
        tenant_id: tenantId
      },
      `Đã đồng bộ thành công ${mockSyncedCount} sản phẩm từ Haravan`
    ));
  } catch (error) {
    console.error('Error syncing products from Haravan:', error);
    const errorResponse = createErrorResponse(error, 'Internal server error', 500);
    return NextResponse.json(errorResponse, { status: errorResponse.status });
  }
});
