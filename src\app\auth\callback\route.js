import { NextResponse } from 'next/server';
import { createClient as createSupabaseClient } from '@supabase/supabase-js';

import { createClient } from 'src/utils/supabase/server';
import { CONFIG } from 'src/global-config';

export async function GET(request) {
  const { searchParams, origin } = new URL(request.url);
  const code = searchParams.get('code');
  const error = searchParams.get('error');
  const errorDescription = searchParams.get('error_description');

  // if "next" is in param, use it as the redirect URL
  let next = searchParams.get('next') ?? '/dashboard/mooly-chatbot/chatbots';
  if (!next.startsWith('/')) {
    // if "next" is not a relative URL, use the default
    next = '/dashboard/mooly-chatbot/chatbots';
  }

  // Handle OAuth errors
  if (error) {
    console.error('OAuth error:', { error, errorDescription });
    const errorUrl = new URL('/auth/auth-code-error', origin);
    errorUrl.searchParams.set('error', error);
    if (errorDescription) {
      errorUrl.searchParams.set('error_description', errorDescription);
    }
    return NextResponse.redirect(errorUrl.toString());
  }

  if (code) {
    try {
      const supabase = await createClient();
      const { data, error: exchangeError } = await supabase.auth.exchangeCodeForSession(code);

      if (exchangeError) {
        console.error('Code exchange error:', exchangeError);
        const errorUrl = new URL('/auth/auth-code-error', origin);
        errorUrl.searchParams.set('error', 'exchange_failed');
        errorUrl.searchParams.set('error_description', exchangeError.message);
        return NextResponse.redirect(errorUrl.toString());
      }

      if (data?.session) {
        console.log('✅ OAuth session created for user:', data.session.user.email);

        // Đơn giản hóa: chỉ redirect về dashboard và để client-side xử lý validation
        // Database triggers sẽ tự động tạo tenant và user record
        console.log('✅ Redirecting to dashboard, database triggers will handle user setup');

        const forwardedHost = request.headers.get('x-forwarded-host');
        const isLocalEnv = process.env.NODE_ENV === 'development';

        if (isLocalEnv) {
          return NextResponse.redirect(`${origin}${next}`);
        } else if (forwardedHost) {
          return NextResponse.redirect(`https://${forwardedHost}${next}`);
        } else {
          return NextResponse.redirect(`${origin}${next}`);
        }
      }
    } catch (err) {
      console.error('OAuth callback error:', err);
      const errorUrl = new URL('/auth/auth-code-error', origin);
      errorUrl.searchParams.set('error', 'callback_failed');
      errorUrl.searchParams.set('error_description', err.message);
      return NextResponse.redirect(errorUrl.toString());
    }
  }

  // return the user to an error page with instructions
  console.error('No code provided in OAuth callback');
  const errorUrl = new URL('/auth/auth-code-error', origin);
  errorUrl.searchParams.set('error', 'no_code');
  errorUrl.searchParams.set('error_description', 'Không nhận được mã xác thực từ Google.');
  return NextResponse.redirect(errorUrl.toString());
}
