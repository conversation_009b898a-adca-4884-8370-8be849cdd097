import { Controller, useFormContext } from 'react-hook-form';

import { CurrencyInput } from '../currency-input';

// ----------------------------------------------------------------------

export function RHFCurrencyInput({ name, helperText, currencySymbol = 'VND', ...other }) {
  const { control } = useFormContext();

  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState: { error } }) => (
        <CurrencyInput
          {...field}
          onChange={(event, value) => field.onChange(value)}
          currencySymbol={currencySymbol}
          error={!!error}
          helperText={error?.message ?? helperText}
          {...other}
        />
      )}
    />
  );
}
