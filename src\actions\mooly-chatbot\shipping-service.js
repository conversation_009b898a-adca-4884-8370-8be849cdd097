'use client';

import { useRef, useMemo, useState, useEffect, useCallback } from 'react';

import { TABLE_NAME, DEFAULT_SHIPPING_OPTIONS } from './shipping-constants';
import { fetchData, createData, updateData, deleteData, upsertData } from './supabase-utils';

/**
 * Lấy danh sách phí vận chuyển với các tùy chọn lọc
 * @param {Object} options - <PERSON><PERSON><PERSON> tùy chọn truy vấn
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getShippingFees(options = {}) {
  return fetchData(TABLE_NAME, options);
}

/**
 * L<PERSON>y thông tin chi tiết phí vận chuyển theo ID
 * @param {string} shippingFeeId - ID của phí vận chuyển
 * @returns {Promise<Object>} - Kế<PERSON> quả từ API
 */
export async function getShippingFeeById(shippingFeeId) {
  if (!shippingFeeId) return { success: false, error: 'Shipping Fee ID is required', data: null };

  return fetchData(TABLE_NAME, {
    filters: { id: shippingFeeId },
    single: true,
  });
}

/**
 * Tạo phí vận chuyển mới
 * @param {Object} shippingFeeData - Dữ liệu phí vận chuyển
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function createShippingFee(shippingFeeData) {
  if (!shippingFeeData)
    return { success: false, error: 'Shipping Fee data is required', data: null };

  return createData(TABLE_NAME, shippingFeeData);
}

/**
 * Cập nhật phí vận chuyển
 * @param {string} shippingFeeId - ID của phí vận chuyển
 * @param {Object} shippingFeeData - Dữ liệu cập nhật
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function updateShippingFee(shippingFeeId, shippingFeeData) {
  if (!shippingFeeId) return { success: false, error: 'Shipping Fee ID is required', data: null };
  if (!shippingFeeData)
    return { success: false, error: 'Shipping Fee data is required', data: null };

  return updateData(TABLE_NAME, shippingFeeData, { id: shippingFeeId });
}

/**
 * Xóa phí vận chuyển
 * @param {string} shippingFeeId - ID của phí vận chuyển
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function deleteShippingFee(shippingFeeId) {
  if (!shippingFeeId) return { success: false, error: 'Shipping Fee ID is required', data: null };

  return deleteData(TABLE_NAME, { id: shippingFeeId });
}

/**
 * Tạo hoặc cập nhật phí vận chuyển
 * @param {Object} shippingFeeData - Dữ liệu phí vận chuyển
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function upsertShippingFee(shippingFeeData) {
  return upsertData(TABLE_NAME, shippingFeeData);
}

/**
 * Hook để lấy danh sách phí vận chuyển
 * @param {Object} options - Các tùy chọn truy vấn
 * @returns {Object} - Dữ liệu và các hàm liên quan
 */
export function useShippingFees(options = {}) {
  const [data, setData] = useState({ success: false, data: [], error: null });
  const [isLoading, setIsLoading] = useState(true);
  const [isValidating, setIsValidating] = useState(false);
  const [error, setError] = useState(null);

  // Kết hợp options mặc định và options được truyền vào
  const mergedOptions = useMemo(
    () => ({
      ...DEFAULT_SHIPPING_OPTIONS,
      ...options,
    }),
    [options]
  );

  // Lưu options vào ref để tránh re-render không cần thiết
  const optionsRef = useRef(mergedOptions);

  // Cập nhật ref khi options thay đổi
  useEffect(() => {
    optionsRef.current = mergedOptions;
  }, [mergedOptions]);

  const fetchShippingFees = useCallback(async () => {
    setIsValidating(true);
    try {
      // Sử dụng giá trị ref để luôn lấy options mới nhất
      const currentOptions = optionsRef.current;

      const result = await getShippingFees(currentOptions);
      setData(result);
      setError(result.error || null);
      return result;
    } catch (err) {
      console.error('Error fetching shipping fees:', err);
      setError(err);
      return { success: false, error: err, data: null };
    } finally {
      setIsValidating(false);
      setIsLoading(false);
    }
  }, []); // Không phụ thuộc vào options vì chúng ta đang sử dụng ref

  // Hàm để tải lại dữ liệu
  const mutate = useCallback(async () => {
    console.log('Manually refreshing shipping fees data...');
    return fetchShippingFees();
  }, [fetchShippingFees]);

  // Tải dữ liệu khi component mount
  useEffect(() => {
    fetchShippingFees();
  }, [fetchShippingFees]);

  // Trả về dữ liệu và các hàm liên quan
  return {
    shippingFees: data.data || [],
    isLoading,
    isValidating,
    error,
    mutate,
    data,
  };
}

/**
 * Hook để lấy chi tiết một phí vận chuyển
 * @param {string} shippingFeeId - ID của phí vận chuyển
 * @returns {Object} - Dữ liệu và các hàm liên quan
 */
export function useShippingFee(shippingFeeId) {
  const [data, setData] = useState({ success: false, data: null, error: null });
  const [isLoading, setIsLoading] = useState(!!shippingFeeId);
  const [isValidating, setIsValidating] = useState(false);
  const [error, setError] = useState(null);

  const fetchShippingFee = useCallback(async () => {
    if (!shippingFeeId) {
      setData(null);
      setError(null);
      setIsLoading(false);
      setIsValidating(false);
      return null;
    }

    setIsValidating(true);
    try {
      const result = await getShippingFeeById(shippingFeeId);
      setData(result);
      setError(result.error || null);
      return result;
    } catch (err) {
      setError(err);
      return { success: false, error: err, data: null };
    } finally {
      setIsValidating(false);
      setIsLoading(false);
    }
  }, [shippingFeeId]);

  // Hàm để tải lại dữ liệu
  const mutate = useCallback(async () => fetchShippingFee(), [fetchShippingFee]);

  // Tải dữ liệu khi component mount hoặc shippingFeeId thay đổi
  useEffect(() => {
    fetchShippingFee();
  }, [fetchShippingFee]);

  // Trả về dữ liệu và các hàm liên quan
  return {
    shippingFee: data?.data,
    isLoading,
    isValidating,
    error,
    mutate,
  };
}

/**
 * Hook để tạo, cập nhật, xóa phí vận chuyển
 * @returns {Object} - Các hàm mutation
 */
export function useShippingFeeMutations() {
  const [loadingStates, setLoadingStates] = useState({
    creating: false,
    updating: false,
    deleting: false,
    upserting: false,
  });

  // Helper function để xử lý trạng thái loading và xử lý lỗi
  const withLoadingState = async (stateKey, asyncFn) => {
    setLoadingStates((prev) => ({ ...prev, [stateKey]: true }));
    try {
      return await asyncFn();
    } finally {
      setLoadingStates((prev) => ({ ...prev, [stateKey]: false }));
    }
  };

  // Các hàm mutation sử dụng helper
  const createShippingFeeMutation = (shippingFeeData) =>
    withLoadingState('creating', () => createShippingFee(shippingFeeData));

  const updateShippingFeeMutation = (shippingFeeId, shippingFeeData) =>
    withLoadingState('updating', () => updateShippingFee(shippingFeeId, shippingFeeData));

  const deleteShippingFeeMutation = (shippingFeeId) =>
    withLoadingState('deleting', () => deleteShippingFee(shippingFeeId));

  const upsertShippingFeeMutation = (shippingFeeData) =>
    withLoadingState('upserting', () => upsertShippingFee(shippingFeeData));

  return {
    createShippingFee: createShippingFeeMutation,
    updateShippingFee: updateShippingFeeMutation,
    deleteShippingFee: deleteShippingFeeMutation,
    upsertShippingFee: upsertShippingFeeMutation,
    isCreating: loadingStates.creating,
    isUpdating: loadingStates.updating,
    isDeleting: loadingStates.deleting,
    isUpserting: loadingStates.upserting,
  };
}
