'use client';

import { useSetState } from 'minimal-shared/hooks';
import { useMemo, useEffect, useCallback } from 'react';

import axios from 'src/lib/axios';
import { supabase } from 'src/lib/supabase';

import { AuthContext } from '../auth-context';

// ----------------------------------------------------------------------

// export function AuthProvider({ children }) {
//   const { state, setState } = useSetState({
//     user: null,
//     loading: true,
//     initialized: false
//   });

//   const checkUserSession = useCallback(async () => {
//     try {
//       const {
//         data: { session },
//         error,
//       } = await supabase.auth.getSession();

//       if (error) {
//         setState({ user: null, loading: false, initialized: true });
//         console.error('Auth error:', error.message);
//         return;
//       }

//       if (session) {
//         const accessToken = session?.access_token;

//         setState({
//           user: { ...session?.user, access_token: accessToken },
//           loading: false,
//           initialized: true
//         });

//         if (accessToken) {
//           axios.defaults.headers.common.Authorization = `Bearer ${accessToken}`;
//         }
//       } else {
//         setState({ user: null, loading: false, initialized: true });
//         delete axios.defaults.headers.common.Authorization;
//       }
//     } catch (error) {
//       console.error('Auth session check error:', error.message);
//       setState({ user: null, loading: false, initialized: true });
//       delete axios.defaults.headers.common.Authorization;
//     }
//   }, [setState]);

//   useEffect(() => {
//     checkUserSession();

//     // Listen for auth state changes
//     const { data: { subscription } } = supabase.auth.onAuthStateChange(
//       async (event, session) => {
//         if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
//           if (session) {
//             const accessToken = session?.access_token;
//             setState({
//               user: { ...session?.user, access_token: accessToken },
//               loading: false,
//               initialized: true
//             });
//             axios.defaults.headers.common.Authorization = `Bearer ${accessToken}`;
//           }
//         } else if (event === 'SIGNED_OUT') {
//           setState({ user: null, loading: false, initialized: true });
//           delete axios.defaults.headers.common.Authorization;
//         }
//       }
//     );

//     return () => {
//       subscription?.unsubscribe();
//     };
//   }, [checkUserSession, setState]);

//   // ----------------------------------------------------------------------

//   const checkAuthenticated = state.user ? 'authenticated' : 'unauthenticated';
//   const status = state.loading ? 'loading' : checkAuthenticated;

//   // Extract tenant_id from app_metadata
//   const tenantId = state.user?.app_metadata?.tenant_id || null;
//   const memoizedValue = useMemo(
//     () => ({
//       user: state.user
//         ? {
//           ...state.user,
//           id: state.user?.id,
//           accessToken: state.user?.access_token,
//           displayName: state.user?.user_metadata?.display_name || state.user?.user_metadata?.full_name || state.user?.email,
//           photoURL: state.user?.user_metadata?.avatar_url,
//           phoneNumber: state.user?.user_metadata?.phone_number || state.user?.user_metadata?.phone,
//           country: state.user?.user_metadata?.country,
//           address: state.user?.user_metadata?.address,
//           state: state.user?.user_metadata?.state,
//           city: state.user?.user_metadata?.city,
//           zipCode: state.user?.user_metadata?.zip_code,
//           about: state.user?.user_metadata?.about,
//           isPublic: state.user?.user_metadata?.is_public,
//           role: state.user?.app_metadata?.user_role ?? 'admin',
//           permissions: state.user?.app_metadata?.user_permissions || [],
//           tenantId,
//         }
//         : null,
//       checkUserSession,
//       loading: status === 'loading',
//       authenticated: status === 'authenticated',
//       unauthenticated: status === 'unauthenticated',
//       initialized: state.initialized,
//       tenantId,
//     }),
//     [checkUserSession, state.user, state.initialized, status, tenantId]
//   );

//   return <AuthContext.Provider value={memoizedValue}>{children}</AuthContext.Provider>;
// }

export function AuthProvider({ children }) {
  const { state, setState } = useSetState({ user: null, loading: true });

  const checkUserSession = useCallback(async () => {
    try {
      const {
        data: { session },
        error,
      } = await supabase.auth.getSession();

      if (error) {
        setState({ user: null, loading: false });
        console.error(error);
        throw error;
      }

      if (session) {
        const accessToken = session?.access_token;

        // Lấy thông tin user từ bảng users để có đầy đủ thông tin
        let userRecord = null;
        try {
          const { data: userData, error: userError } = await supabase
            .from('users')
            .select('*')
            .eq('id', session.user.id)
            .single();

          if (!userError && userData) {
            userRecord = userData;
          }
        } catch (userFetchError) {
          console.warn('Could not fetch user record:', userFetchError);
        }

        // Merge thông tin từ auth user và database user
        const mergedUser = {
          ...session,
          ...session?.user,
          // Thông tin từ database (ưu tiên cao hơn)
          ...(userRecord && {
            fullName: userRecord.full_name,
            phone: userRecord.phone,
            avatarUrl: userRecord.avatar_url,
            country: userRecord.country,
            address: userRecord.address,
            state: userRecord.state,
            city: userRecord.city,
            zipCode: userRecord.zip_code,
            about: userRecord.about,
            isPublic: userRecord.is_public,
          }),
        };

        setState({ user: mergedUser, loading: false });
        axios.defaults.headers.common.Authorization = `Bearer ${accessToken}`;
      } else {
        setState({ user: null, loading: false });
        delete axios.defaults.headers.common.Authorization;
      }
    } catch (error) {
      console.error(error);
      setState({ user: null, loading: false });
    }
  }, [setState]);

  useEffect(() => {
    checkUserSession();
  }, []);

  // Function để refresh user data sau khi cập nhật profile
  const refreshUserData = useCallback(async () => {
    if (state.user?.id) {
      await checkUserSession();
    }
  }, [checkUserSession, state.user?.id]);

  // ----------------------------------------------------------------------

  const checkAuthenticated = state.user ? 'authenticated' : 'unauthenticated';

  const status = state.loading ? 'loading' : checkAuthenticated;

  const memoizedValue = useMemo(
    () => ({
      user: state.user
        ? {
            ...state.user,
            id: state.user?.id,
            accessToken: state.user?.access_token,
            // Priority: database info, fallback to user_metadata, finally email
            displayName: state.user?.fullName ||
                        state.user?.user_metadata?.display_name ||
                        state.user?.user_metadata?.full_name ||
                        state.user?.email,
            photoURL: state.user?.avatarUrl || state.user?.user_metadata?.avatar_url,
            phoneNumber: state.user?.phone ||
                        state.user?.user_metadata?.phone_number ||
                        state.user?.user_metadata?.phone,
            // Additional fields from database
            country: state.user?.country || state.user?.user_metadata?.country || 'VN',
            address: state.user?.address || state.user?.user_metadata?.address || '',
            state: state.user?.state || state.user?.user_metadata?.state || '',
            city: state.user?.city || state.user?.user_metadata?.city || '',
            zipCode: state.user?.zipCode || state.user?.user_metadata?.zip_code || '',
            about: state.user?.about || state.user?.user_metadata?.about || '',
            isPublic: state.user?.isPublic ?? state.user?.user_metadata?.is_public ?? false,
            role: state.user?.role ?? 'admin',
            // Add tenant info
            tenantId: state.user?.app_metadata?.tenant_id || null,
          }
        : null,
      checkUserSession,
      refreshUserData,
      loading: status === 'loading',
      authenticated: status === 'authenticated',
      unauthenticated: status === 'unauthenticated',
    }),
    [checkUserSession, state.user, status]
  );

  return <AuthContext value={memoizedValue}>{children}</AuthContext>;
}