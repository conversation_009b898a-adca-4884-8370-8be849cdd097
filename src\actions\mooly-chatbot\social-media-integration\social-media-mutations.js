/**
 * Unified Social Media Integration Mutations
 * React hooks cho mutations của multi-platform social media integration
 */

import { useState, useCallback } from 'react';
import { 
  PLATFORMS, 
  TABLES, 
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  ACTIVITY_TYPES 
} from './social-media-constants.js';
import { isPlatformSupported, validateContent } from './social-media-utils.js';
import { fetchData, updateData, createData, deleteData } from '../supabase-utils.js';

/**
 * Hook cho mutations của social media accounts
 * @returns {Object} Mutation functions và state
 */
export function useSocialMediaAccountMutations() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const upsertAccount = useCallback(async (accountData) => {
    try {
      setLoading(true);
      setError(null);

      // Validate platform
      if (!isPlatformSupported(accountData.platform)) {
        throw new Error(ERROR_MESSAGES.PLATFORM_NOT_SUPPORTED);
      }

      // Check if account exists
      const existingAccountResult = await fetchData(TABLES.ACCOUNTS, {
        tenant_id: accountData.tenant_id,
        platform: accountData.platform,
        page_id: accountData.page_id
      });

      const accountPayload = {
        ...accountData,
        updated_at: new Date().toISOString()
      };

      let result;
      if (existingAccountResult.success && existingAccountResult.data?.[0]) {
        // Update existing account
        const updateResult = await updateData(
          TABLES.ACCOUNTS,
          { id: existingAccountResult.data[0].id },
          accountPayload
        );
        if (!updateResult.success) throw new Error(updateResult.error?.message || 'Update failed');
        result = updateResult.data;
      } else {
        // Create new account
        const createResult = await createData(TABLES.ACCOUNTS, {
          ...accountPayload,
          created_at: new Date().toISOString()
        });
        if (!createResult.success) throw new Error(createResult.error?.message || 'Create failed');
        result = createResult.data;
      }

      // Log activity
      await logActivity(
        accountData.pageId || accountData.page_id,
        ACTIVITY_TYPES.ACCOUNT_CONNECTED,
        { platform: accountData.platform }
      );

      return {
        success: true,
        data: result,
        message: SUCCESS_MESSAGES.ACCOUNT_CONNECTED
      };
    } catch (err) {
      console.error('❌ Error upserting social media account:', err);
      const errorMessage = err.message || ERROR_MESSAGES.NETWORK_ERROR;
      setError(errorMessage);
      return {
        success: false,
        error: errorMessage
      };
    } finally {
      setLoading(false);
    }
  }, []);

  const disconnectAccount = useCallback(async (accountId, platform) => {
    try {
      setLoading(true);
      setError(null);

      console.log('🔌 Disconnecting account:', accountId, platform);

      // Get account info for logging - sử dụng supabase-utils pattern
      const accountResult = await fetchData(TABLES.ACCOUNTS, {
        filters: { id: accountId }
      });
      
      if (!accountResult.success) {
        throw new Error(accountResult.error?.message || 'Không thể tìm thấy tài khoản');
      }

      const account = accountResult.data?.[0];
      if (!account) {
        throw new Error(ERROR_MESSAGES.ACCOUNT_NOT_FOUND);
      }

      console.log('📋 Account to disconnect:', account);

      // Soft delete - mark as inactive với supabase-utils pattern
      const updateResult = await updateData(
        TABLES.ACCOUNTS,
        {
          isActive: false,
          updatedAt: new Date().toISOString()
        },
        { id: accountId }
      );

      console.log('📝 Disconnect result:', updateResult);

      if (!updateResult.success) throw new Error(updateResult.error?.message || 'Không thể ngắt kết nối tài khoản');

      // Log activity
      await logActivity(
        account.pageId || account.page_id,
        ACTIVITY_TYPES.ACCOUNT_DISCONNECTED,
        { platform: account.platform }
      );

      return {
        success: true,
        data: updateResult.data,
        message: SUCCESS_MESSAGES.ACCOUNT_DISCONNECTED
      };
    } catch (err) {
      console.error('❌ Error disconnecting social media account:', err);
      const errorMessage = err.message || ERROR_MESSAGES.NETWORK_ERROR;
      setError(errorMessage);
      return {
        success: false,
        error: errorMessage
      };
    } finally {
      setLoading(false);
    }
  }, []);

  const updateAccountInfo = useCallback(async (accountId, updates) => {
    try {
      setLoading(true);
      setError(null);

      const updateResult = await updateData(
        TABLES.ACCOUNTS,
        { id: accountId },
        {
          ...updates,
          updated_at: new Date().toISOString()
        }
      );

      if (!updateResult.success) throw new Error(updateResult.error?.message || 'Update failed');

      return {
        success: true,
        data: updateResult.data,
        message: 'Cập nhật thông tin tài khoản thành công'
      };
    } catch (err) {
      console.error('❌ Error updating account info:', err);
      const errorMessage = err.message || ERROR_MESSAGES.NETWORK_ERROR;
      setError(errorMessage);
      return {
        success: false,
        error: errorMessage
      };
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    upsertAccount,
    disconnectAccount,
    updateAccountInfo,
    loading,
    error
  };
}

/**
 * Hook cho mutations của auto-reply config
 * @returns {Object} Mutation functions và state
 */
export function useSocialMediaConfigMutations() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const saveConfig = useCallback(async (configData) => {
    try {
      setLoading(true);
      setError(null);

      console.log('💾 Social media config mutation - received data:', configData);

      // Validate page_id (tenant_id sẽ được database tự động xử lý)
      if (!configData.pageId) {
        console.error('❌ Validation failed - missing pageId:', configData);
        throw new Error('Thiếu thông tin page ID');
      }

      console.log('✅ Validation passed - pageId:', configData.pageId);

      // Chuẩn bị data theo supabase-utils pattern (camelCase)
      const configPayload = {
        pageId: configData.pageId,
        enableCommentReply: configData.enableCommentReply,
        enableMessageReply: configData.enableMessageReply,
        enableInstagramComments: configData.enableInstagramComments,
        enableInstagramMessages: configData.enableInstagramMessages,
        enableInstagramStoryReplies: configData.enableInstagramStoryReplies,
        autoPrivateReply: configData.autoPrivateReply,
        enableAutoLikeComments: configData.enableAutoLikeComments,
        replyPrompt: configData.replyPrompt,
        replyTone: configData.replyTone,
        replyLanguage: configData.replyLanguage,
        maxReplyLength: configData.maxReplyLength,
        instagramReplyDelaySeconds: configData.instagramReplyDelaySeconds,
        businessInfo: configData.businessInfo,
        products: configData.products,
        policies: configData.policies,
        excludeKeywords: configData.excludeKeywords,
        updatedAt: new Date().toISOString()
      };

      console.log('📤 Prepared config payload:', configPayload);

      // Check if config exists (tìm theo pageId, database sẽ auto-filter theo tenant)
      const existingConfigResult = await fetchData(TABLES.CONFIG, {
        filters: { pageId: configData.pageId }
      });

      console.log('🔍 Existing config check:', existingConfigResult);

      let result;
      if (existingConfigResult.success && existingConfigResult.data?.[0]) {
        // Update existing config
        console.log('🔄 Updating existing config with ID:', existingConfigResult.data[0].id);
        
        const updateResult = await updateData(
          TABLES.CONFIG,
          configPayload,
          { id: existingConfigResult.data[0].id }
        );
        
        console.log('📝 Update result:', updateResult);
        
        if (!updateResult.success) throw new Error(updateResult.error?.message || 'Không thể cập nhật cấu hình');
        result = updateResult.data;
      } else {
        // Create new config (database trigger sẽ tự động set tenant_id)
        console.log('➕ Creating new config');
        
        const createPayload = {
          ...configPayload,
          createdAt: new Date().toISOString()
        };
        
        const createResult = await createData(TABLES.CONFIG, createPayload);
        
        console.log('📝 Create result:', createResult);
        
        if (!createResult.success) throw new Error(createResult.error?.message || 'Không thể tạo cấu hình');
        result = createResult.data;
      }

      // Log activity (chỉ cần pageId, database sẽ handle tenant)
      await logActivity(
        configData.pageId,
        'config_updated',
        { config_changes: Object.keys(configPayload) }
      );

      return {
        success: true,
        data: result,
        message: SUCCESS_MESSAGES.CONFIG_SAVED
      };
    } catch (err) {
      console.error('❌ Error saving social media config:', err);
      const errorMessage = err.message || ERROR_MESSAGES.NETWORK_ERROR;
      setError(errorMessage);
      return {
        success: false,
        error: errorMessage
      };
    } finally {
      setLoading(false);
    }
  }, []);

  const updateFeatureSettings = useCallback(async (pageId, features) => {
    try {
      setLoading(true);
      setError(null);

      console.log('🔧 Updating feature settings for pageId:', pageId, 'features:', features);

      // Chuẩn bị data theo supabase-utils pattern
      const updatePayload = {
        ...features,
        updatedAt: new Date().toISOString()
      };

      const updateResult = await updateData(
        TABLES.CONFIG,
        updatePayload,
        { pageId: pageId }
      );

      console.log('📝 Feature update result:', updateResult);

      if (!updateResult.success) throw new Error(updateResult.error?.message || 'Không thể cập nhật tính năng');

      return {
        success: true,
        data: updateResult.data,
        message: 'Cập nhật cài đặt tính năng thành công'
      };
    } catch (err) {
      console.error('❌ Error updating feature settings:', err);
      const errorMessage = err.message || ERROR_MESSAGES.NETWORK_ERROR;
      setError(errorMessage);
      return {
        success: false,
        error: errorMessage
      };
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    saveConfig,
    updateFeatureSettings,
    loading,
    error
  };
}

/**
 * Hook cho webhook mutations
 * @returns {Object} Mutation functions và state
 */
export function useSocialMediaWebhookMutations() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const subscribeWebhook = useCallback(async (webhookData) => {
    try {
      setLoading(true);
      setError(null);

      // Chuẩn bị data theo supabase-utils pattern
      const webhookPayload = {
        ...webhookData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      const createResult = await createData(TABLES.WEBHOOKS, webhookPayload);

      if (!createResult.success) throw new Error(createResult.error?.message || 'Không thể tạo webhook');

      // Log activity
      await logActivity(
        webhookData.accountId || webhookData.account_id,
        ACTIVITY_TYPES.WEBHOOK_SUBSCRIBED,
        { 
          platform: webhookData.platform,
          fields: webhookData.webhookFields || webhookData.webhook_fields 
        }
      );

      return {
        success: true,
        data: createResult.data,
        message: SUCCESS_MESSAGES.WEBHOOK_SUBSCRIBED
      };
    } catch (err) {
      console.error('❌ Error subscribing webhook:', err);
      const errorMessage = err.message || ERROR_MESSAGES.WEBHOOK_SUBSCRIPTION_FAILED;
      setError(errorMessage);
      return {
        success: false,
        error: errorMessage
      };
    } finally {
      setLoading(false);
    }
  }, []);

  const unsubscribeWebhook = useCallback(async (webhookId) => {
    try {
      setLoading(true);
      setError(null);

      const deleteResult = await deleteData(TABLES.WEBHOOKS, { id: webhookId });

      if (!deleteResult.success) throw new Error(deleteResult.error?.message || 'Delete failed');

      return {
        success: true,
        data: deleteResult.data,
        message: 'Hủy đăng ký webhook thành công'
      };
    } catch (err) {
      console.error('❌ Error unsubscribing webhook:', err);
      const errorMessage = err.message || ERROR_MESSAGES.NETWORK_ERROR;
      setError(errorMessage);
      return {
        success: false,
        error: errorMessage
      };
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    subscribeWebhook,
    unsubscribeWebhook,
    loading,
    error
  };
}

/**
 * Helper function để log activity
 * @param {string} pageId - Page/Account ID
 * @param {string} activity - Activity type
 * @param {Object} metadata - Activity metadata
 */
async function logActivity(pageId, activity, metadata = {}) {
  try {
    // Database sẽ tự động set tenant_id qua trigger
    const result = await createData(TABLES.ACTIVITY_LOGS, {
      pageId: pageId,
      activity,
      metadata: {
        ...metadata,
        timestamp: new Date().toISOString()
      },
      createdAt: new Date().toISOString()
    });

    if (!result.success) {
      console.error('❌ Error logging activity:', result.error);
    }
  } catch (error) {
    console.error('❌ Error logging activity:', error);
    // Don't throw error for logging failures
  }
}
