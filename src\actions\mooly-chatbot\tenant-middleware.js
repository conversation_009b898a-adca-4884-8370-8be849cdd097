'use client';

import { createClient } from 'src/utils/supabase/client';

/**
 * ⚠️ DEPRECATED: File này không còn cần thiết với RLS system mới
 *
 * RLS (Row Level Security) đã được cấu hình để tự động xử lý tenant_id từ JWT token
 * Sử dụng auth.get_tenant_id() function trong database thay vì các functions này
 *
 * File này được giữ lại để backward compatibility
 */

/**
 * @deprecated Sử dụng RLS system thay thế
 * Lấy tenant_id của người dùng hiện tại với validation tăng cường
 * Ưu tiên lấy từ app_metadata trước, fallback về bảng users
 * @returns {Promise<string|null>} - tenant_id hoặc null nếu không tìm thấy
 */
export async function getCurrentUserTenantId() {
  try {
    const supabase = createClient();

    // Lấy thông tin người dùng hiện tại
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError) {
      return null;
    }

    if (!user) {
      return null;
    }

    // Ưu tiên lấy tenant_id từ app_metadata (từ JWT token)
    const tenantIdFromMetadata = user.app_metadata?.tenant_id;
    if (tenantIdFromMetadata) {
      return tenantIdFromMetadata;
    }

    // Fallback: Lấy tenant_id từ bảng users với validation
    const { data, error } = await supabase
      .from('users')
      .select('tenant_id, email, full_name, is_active, is_tenant_owner')
      .eq('id', user.id)
      .single();

    if (error) {
      // Nếu user không tồn tại trong bảng users, có thể là lỗi sync
      if (error.code === 'PGRST116') {
        return null;
      }

      throw error;
    }

    // Validation bổ sung
    if (!data) {
      return null;
    }

    if (!data.is_active) {
      return null;
    }

    if (!data.tenant_id) {
      return null;
    }

    return data.tenant_id;
  } catch (error) {
    console.error('Error getting current user tenant ID:', error);
    return null;
  }
}

// No caching for tenant_id to ensure always fresh data
/**
 * @deprecated Sử dụng RLS system thay thế
 * Lấy tenant_id của người dùng hiện tại (không cache)
 * @returns {Promise<string|null>} - tenant_id hoặc null nếu không tìm thấy
 */
export async function getCachedTenantId() {
  // Always fetch fresh tenant_id to avoid data inconsistency
  return await getCurrentUserTenantId();
}

/**
 * @deprecated Không còn cần thiết với RLS system
 * Clear tenant cache - No longer needed since we don't cache
 */
export function clearTenantIdCache() {
  // No cache to clear - function kept for backward compatibility
}

/**
 * Thêm tenant_id vào dữ liệu
 * @param {Object|Array} data - Dữ liệu cần thêm tenant_id
 * @param {string} tenantId - tenant_id cần thêm
 * @returns {Object|Array} - Dữ liệu đã thêm tenant_id
 */
export function addTenantIdToData(data, tenantId) {
  if (!tenantId) return data;

  // Nếu data là mảng, thêm tenant_id vào từng phần tử
  if (Array.isArray(data)) {
    return data.map((item) => ({
      ...item,
      tenantId, // camelCase cho client-side
    }));
  }

  // Nếu data là object, thêm tenant_id vào object
  return {
    ...data,
    tenantId, // camelCase cho client-side
  };
}

/**
 * @deprecated Không còn cần thiết với RLS system
 * Thêm tenant_id vào bộ lọc
 * @param {Object} filters - Bộ lọc cần thêm tenant_id
 * @param {string} tenantId - tenant_id cần thêm
 * @returns {Object} - Bộ lọc đã thêm tenant_id
 */
export function addTenantIdToFilters(filters, tenantId) {
  if (!tenantId) return filters;

  return {
    ...filters,
    tenantId, // camelCase cho client-side
  };
}

// Danh sách các bảng không cần thêm tenant_id
const EXCLUDED_TABLES = [
  'tenants',
  'users',
  'roles',
  'permissions',
  'user_roles',
  'user_permissions',
  'role_permissions',
  'credit_packages', // Thêm bảng credit_packages vào danh sách loại trừ
];

/**
 * @deprecated Không còn cần thiết với RLS system
 * Kiểm tra xem bảng có cần thêm tenant_id không
 * @param {string} table - Tên bảng
 * @returns {boolean} - true nếu bảng cần thêm tenant_id
 */
export function shouldAddTenantId(table) {
  return !EXCLUDED_TABLES.includes(table);
}

/**
 * Validate tenant access cho user hiện tại
 * @param {string} targetTenantId - Tenant ID cần kiểm tra
 * @returns {Promise<boolean>} - true nếu user có quyền truy cập
 */
export async function validateTenantAccess(targetTenantId) {
  try {
    if (!targetTenantId) {
      console.warn('⚠️ No target tenant ID provided');
      return false;
    }

    const currentTenantId = await getCurrentUserTenantId();

    if (!currentTenantId) {
      console.warn('⚠️ Current user has no tenant ID');
      return false;
    }

    const hasAccess = currentTenantId === targetTenantId;

    if (!hasAccess) {
      console.warn('🚫 Tenant access denied:', {
        currentTenantId,
        targetTenantId,
      });
    }

    return hasAccess;
  } catch (error) {
    console.error('💥 Error validating tenant access:', error);
    return false;
  }
}

/**
 * Lấy thông tin tenant hiện tại với validation
 * @returns {Promise<Object|null>} - Thông tin tenant hoặc null
 */
export async function getCurrentTenantInfo() {
  try {
    const tenantId = await getCurrentUserTenantId();

    if (!tenantId) {
      return null;
    }

    const supabase = createClient();
    const { data, error } = await supabase
      .from('tenants')
      .select('id, name, slug, is_active, subscription_plan, subscription_status')
      .eq('id', tenantId)
      .single();

    if (error) {
      console.error('❌ Error fetching tenant info:', error);
      return null;
    }

    if (!data.is_active) {
      console.warn('⚠️ Current tenant is inactive');
      return null;
    }

    return data;
  } catch (error) {
    console.error('💥 Error getting current tenant info:', error);
    return null;
  }
}

/**
 * Kiểm tra quyền tenant owner
 * @returns {Promise<boolean>} - true nếu user là tenant owner
 */
export async function isCurrentUserTenantOwner() {
  try {
    const supabase = createClient();
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      return false;
    }

    const { data, error } = await supabase
      .from('users')
      .select('is_tenant_owner')
      .eq('id', user.id)
      .single();

    if (error) {
      console.error('❌ Error checking tenant owner status:', error);
      return false;
    }

    return data?.is_tenant_owner || false;
  } catch (error) {
    console.error('💥 Error checking tenant owner status:', error);
    return false;
  }
}
