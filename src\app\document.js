'use client';

/**
 * Document.js
 * 
 * File này chứa các hàm và component để xử lý các vấn đề hydration ở cấp độ document
 * và các vấn đề liên quan đến browser extensions.
 */

import { useEffect } from 'react';

/**
 * Hook để xử lý các vấn đề hydration ở cấp độ document
 */
export function useDocumentHydrationFix() {
  useEffect(() => {
    // Chỉ chạy ở phía client
    if (typeof window !== 'undefined' && typeof document !== 'undefined') {
      // Xử lý các thuộc tính data-* trên thẻ html và body
      const elementsToCheck = [document.documentElement, document.body];
      
      elementsToCheck.forEach((element) => {
        // Lấy tất cả các thuộc tính của element
        const attributes = element.attributes;
        const attributesToRemove = [];
        
        // Tìm các thuộc tính data-* liên quan đến extensions
        for (let i = 0; i < attributes.length; i++) {
          const attr = attributes[i];
          if (attr.name.startsWith('data-') && 
              (attr.name.includes('ext') || attr.name.includes('extension'))) {
            attributesToRemove.push(attr.name);
          }
        }
        
        // Xóa các thuộc tính đã tìm thấy
        attributesToRemove.forEach((attrName) => {
          element.removeAttribute(attrName);
        });
      });
      
      // Xử lý các script được thêm vào bởi extensions
      const scripts = document.querySelectorAll('script[src*="extension"]');
      scripts.forEach((script) => {
        // Thêm thuộc tính để đánh dấu script này là từ extension
        script.setAttribute('data-extension-script', 'true');
      });
    }
  }, []);
}

/**
 * Component để xử lý các vấn đề hydration ở cấp độ document
 */
export function DocumentHydrationFix({ children }) {
  useDocumentHydrationFix();
  return children;
}
