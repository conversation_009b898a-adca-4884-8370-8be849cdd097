'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Alert,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  CircularProgress
} from '@mui/material';
import { LoadingButton } from '@mui/lab';
import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';

/**
 * Facebook Auto Reply Test Component
 * Test auto reply functionality with different scenarios
 */

export default function FacebookAutoReplyTest({ pageId, pageName, onClose }) {
  const [loading, setLoading] = useState(false);
  const [testMessage, setTestMessage] = useState('');
  const [eventType, setEventType] = useState('facebook_comment');
  const [testResult, setTestResult] = useState(null);
  const [testScenarios, setTestScenarios] = useState([]);
  const [recentTests, setRecentTests] = useState([]);

  // Load test scenarios on mount
  useEffect(() => {
    loadTestScenarios();
  }, [pageId]);

  const loadTestScenarios = async () => {
    try {
      const response = await fetch(`/api/facebook-integration/test-auto-reply?pageId=${pageId}`);
      const data = await response.json();
      
      if (data.success) {
        setTestScenarios(data.testScenarios || []);
        setRecentTests(data.recentTests || []);
      }
    } catch (error) {
      console.error('Error loading test scenarios:', error);
    }
  };

  const handleTestAutoReply = async () => {
    if (!testMessage.trim()) {
      toast.error('Vui lòng nhập tin nhắn test');
      return;
    }

    try {
      setLoading(true);
      setTestResult(null);

      const response = await fetch('/api/facebook-integration/test-auto-reply', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          pageId,
          testMessage: testMessage.trim(),
          eventType,
          skipSend: true
        })
      });

      const data = await response.json();
      
      setTestResult(data);
      
      if (data.success) {
        toast.success('Test auto reply thành công!');
        // Reload recent tests
        await loadTestScenarios();
      } else {
        toast.error(data.message || 'Test thất bại');
      }

    } catch (error) {
      console.error('Test auto reply error:', error);
      toast.error('Lỗi khi test auto reply');
      setTestResult({
        success: false,
        message: 'Lỗi kết nối server'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleUseScenario = (scenario) => {
    setTestMessage(scenario.message);
    setEventType(scenario.eventType);
  };

  const getEventTypeLabel = (type) => {
    const labels = {
      'facebook_comment': 'Facebook Comment',
      'facebook_message': 'Facebook Message',
      'instagram_comment': 'Instagram Comment',
      'instagram_message': 'Instagram Message'
    };
    return labels[type] || type;
  };

  const getEventTypeIcon = (type) => {
    if (type.includes('instagram')) {
      return 'mdi:instagram';
    } else if (type.includes('comment')) {
      return 'mdi:comment-outline';
    } else {
      return 'mdi:message-outline';
    }
  };

  return (
    <Card>
      <CardContent sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
          <Typography variant="h6">
            Test Auto Reply - {pageName}
          </Typography>
          <Button
            variant="outlined"
            size="small"
            onClick={onClose}
            startIcon={<Iconify icon="mdi:close" />}
          >
            Đóng
          </Button>
        </Box>

        <Alert severity="info" sx={{ mb: 3 }}>
          <Typography variant="body2">
            Test tính năng auto reply mà không gửi tin nhắn thực tế. 
            Bạn có thể thử các kịch bản khác nhau để xem AI sẽ phản hồi như thế nào.
          </Typography>
        </Alert>

        {/* Test Form */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="subtitle1" gutterBottom>
            Thông tin test:
          </Typography>

          <Box sx={{ display: 'grid', gap: 2, mb: 3 }}>
            <FormControl fullWidth>
              <InputLabel>Loại sự kiện</InputLabel>
              <Select
                value={eventType}
                label="Loại sự kiện"
                onChange={(e) => setEventType(e.target.value)}
              >
                <MenuItem value="facebook_comment">Facebook Comment</MenuItem>
                <MenuItem value="facebook_message">Facebook Message</MenuItem>
                <MenuItem value="instagram_comment">Instagram Comment</MenuItem>
                <MenuItem value="instagram_message">Instagram Message</MenuItem>
              </Select>
            </FormControl>

            <TextField
              fullWidth
              multiline
              rows={3}
              label="Tin nhắn test"
              placeholder="Nhập tin nhắn để test auto reply..."
              value={testMessage}
              onChange={(e) => setTestMessage(e.target.value)}
            />
          </Box>

          <LoadingButton
            loading={loading}
            variant="contained"
            onClick={handleTestAutoReply}
            startIcon={<Iconify icon="mdi:play" />}
            disabled={!testMessage.trim()}
          >
            Chạy test
          </LoadingButton>
        </Box>

        {/* Test Result */}
        {testResult && (
          <Box sx={{ mb: 4 }}>
            <Typography variant="subtitle1" gutterBottom>
              Kết quả test:
            </Typography>

            <Alert 
              severity={testResult.success ? 'success' : 'error'} 
              sx={{ mb: 2 }}
            >
              <Typography variant="body2">
                {testResult.message}
              </Typography>
            </Alert>

            {testResult.success && testResult.test && (
              <Card variant="outlined">
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                    <Iconify icon={getEventTypeIcon(testResult.test.eventType)} />
                    <Chip 
                      label={getEventTypeLabel(testResult.test.eventType)} 
                      size="small" 
                      color="primary" 
                    />
                  </Box>

                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Tin nhắn test:
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 2, p: 1, bgcolor: 'grey.100', borderRadius: 1 }}>
                    "{testResult.test.testMessage}"
                  </Typography>

                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Phản hồi AI:
                  </Typography>
                  <Typography variant="body1" sx={{ p: 1, bgcolor: 'primary.lighter', borderRadius: 1 }}>
                    {testResult.test.generatedResponse}
                  </Typography>

                  {testResult.test.metadata && (
                    <Box sx={{ mt: 2, pt: 2, borderTop: 1, borderColor: 'divider' }}>
                      <Typography variant="caption" color="text.secondary">
                        Model: {testResult.test.metadata.model} | 
                        Tokens: {testResult.test.metadata.tokensUsed || 0}
                      </Typography>
                    </Box>
                  )}
                </CardContent>
              </Card>
            )}
          </Box>
        )}

        {/* Test Scenarios */}
        {testScenarios.length > 0 && (
          <Box sx={{ mb: 4 }}>
            <Typography variant="subtitle1" gutterBottom>
              Kịch bản test mẫu:
            </Typography>

            <List>
              {testScenarios.map((scenario, index) => (
                <ListItem
                  key={index}
                  sx={{
                    border: 1,
                    borderColor: 'divider',
                    borderRadius: 1,
                    mb: 1,
                    cursor: 'pointer',
                    '&:hover': { bgcolor: 'action.hover' }
                  }}
                  onClick={() => handleUseScenario(scenario)}
                >
                  <ListItemIcon>
                    <Iconify icon={getEventTypeIcon(scenario.eventType)} />
                  </ListItemIcon>
                  <ListItemText
                    primary={scenario.name}
                    secondary={
                      <Box>
                        <Typography variant="body2" color="text.secondary">
                          {scenario.description}
                        </Typography>
                        <Typography variant="caption" sx={{ fontStyle: 'italic' }}>
                          "{scenario.message}"
                        </Typography>
                      </Box>
                    }
                  />
                  <Chip 
                    label={getEventTypeLabel(scenario.eventType)} 
                    size="small" 
                    variant="outlined" 
                  />
                </ListItem>
              ))}
            </List>
          </Box>
        )}

        {/* Recent Tests */}
        {recentTests.length > 0 && (
          <Box>
            <Typography variant="subtitle1" gutterBottom>
              Test gần đây:
            </Typography>

            <List>
              {recentTests.slice(0, 5).map((test, index) => (
                <ListItem key={index} sx={{ px: 0 }}>
                  <ListItemIcon>
                    <Iconify icon={getEventTypeIcon(test.eventType)} />
                  </ListItemIcon>
                  <ListItemText
                    primary={test.testMessage}
                    secondary={
                      <Box>
                        <Typography variant="body2" color="text.secondary">
                          {test.generatedResponse}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {new Date(test.createdAt).toLocaleString('vi-VN')}
                        </Typography>
                      </Box>
                    }
                  />
                </ListItem>
              ))}
            </List>
          </Box>
        )}
      </CardContent>
    </Card>
  );
}
