# Facebook Integration - Auto Reply System

Hệ thống tích hợp Facebook/Instagram với tính năng auto reply sử dụng AI để tự động trả lời comments và messages.

## 🚀 Tính năng chính

### ✅ Đã hoàn thành

- **Facebook OAuth Integration**: Kế<PERSON> nối Facebook Pages với popup authentication
- **Instagram Integration**: Hỗ trợ Instagram Business accounts
- **AI Auto Reply**: Tự động trả lời comments và messages bằng AI
- **Webhook Handler**: Nhận real-time events từ Facebook/Instagram
- **Security**: Webhook signature verification và rate limiting
- **Analytics Dashboard**: Theo dõi hoạt động và hiệu suất
- **Test Interface**: Test auto reply với các kịch bản khác nhau
- **Multi-tenant**: Hỗ trợ nhiều tenant với RLS policies

### 🎯 Tính năng

1. **Auto Reply Comments**
   - Facebook public comments
   - Instagram public comments
   - AI-powered responses based on context

2. **Auto Reply Messages**
   - Facebook Messenger private messages
   - Instagram Direct messages
   - Personalized responses

3. **AI Configuration**
   - Custom prompts and business information
   - Multiple tone options (friendly, professional, etc.)
   - Language support (Vietnamese, English)
   - Exclude keywords functionality

4. **Analytics & Monitoring**
   - Real-time activity tracking
   - Success rate monitoring
   - Platform-specific analytics
   - Activity logs and history

## 🏗️ Kiến trúc hệ thống

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Facebook/     │    │   Next.js API    │    │   Supabase      │
│   Instagram     │───▶│   Routes         │───▶│   Database      │
│   Webhooks      │    │                  │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌──────────────────┐
                       │   OpenAI API     │
                       │   (Auto Reply)   │
                       └──────────────────┘
```

## 📁 Cấu trúc thư mục

```
src/
├── app/api/facebook-integration/
│   ├── auto-reply-config/route.js      # Quản lý cấu hình auto reply
│   ├── pages/route.js                  # Quản lý Facebook pages
│   ├── analytics/route.js              # Analytics và thống kê
│   ├── subscribe-webhooks/route.js     # Đăng ký webhooks
│   ├── test-auto-reply/route.js        # Test auto reply
│   └── popup-callback/route.js         # OAuth callback
├── app/api/facebook-webhooks/route.js  # Webhook handler chính
├── lib/facebook-integration/
│   ├── ai-service.js                   # AI response generation
│   ├── auto-reply-service.js           # Auto reply logic
│   └── security.js                     # Security utilities
└── sections/mooly-chatbot/facebook-integration/
    ├── facebook-setup-wizard.jsx       # Setup wizard UI
    ├── facebook-auto-reply-test.jsx    # Test interface
    └── facebook-analytics-dashboard.jsx # Analytics UI
```

## 🛠️ Cài đặt và cấu hình

### 1. Environment Variables

```bash
# Facebook App Configuration
NEXT_PUBLIC_FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret
FACEBOOK_WEBHOOK_VERIFY_TOKEN=your_custom_verify_token

# OpenAI for Auto Reply
OPENAI_API_KEY=your_openai_api_key

# Application URL
NEXTAUTH_URL=https://your-domain.com
```

### 2. Database Setup

Các bảng cần thiết đã được tạo:
- `facebook_accounts`: Lưu thông tin Facebook pages
- `facebook_auto_reply_config`: Cấu hình auto reply
- `facebook_activity_logs`: Logs hoạt động

### 3. Facebook App Setup

1. Tạo Facebook App tại [developers.facebook.com](https://developers.facebook.com)
2. Thêm các products: Facebook Login, Webhooks, Messenger
3. Cấu hình permissions và webhook URLs
4. Xem chi tiết trong `docs/facebook-integration-setup.md`

## 🧪 Testing

### Test tự động
```bash
node scripts/test-facebook-integration.js
```

### Test thủ công
1. Sử dụng Facebook Setup Wizard trong ứng dụng
2. Test auto reply với interface có sẵn
3. Kiểm tra analytics dashboard

## 📊 API Endpoints

### Webhook Handler
- `GET /api/facebook-webhooks` - Webhook verification
- `POST /api/facebook-webhooks` - Receive webhook events

### Configuration
- `GET /api/facebook-integration/auto-reply-config` - Get config
- `POST /api/facebook-integration/auto-reply-config` - Save config

### Management
- `GET /api/facebook-integration/pages` - Get connected pages
- `POST /api/facebook-integration/subscribe-webhooks` - Subscribe webhooks
- `GET /api/facebook-integration/analytics` - Get analytics

### Testing
- `POST /api/facebook-integration/test-auto-reply` - Test auto reply

## 🔒 Bảo mật

- **Webhook Signature Verification**: Xác thực tất cả webhooks
- **Rate Limiting**: Giới hạn số lượng requests
- **Token Encryption**: Mã hóa access tokens trong database
- **Tenant Isolation**: RLS policies cho multi-tenant
- **Input Sanitization**: Làm sạch dữ liệu đầu vào

## 📈 Monitoring

### Analytics Dashboard
- Tổng số hoạt động
- Tỷ lệ thành công auto reply
- Thống kê theo platform (Facebook/Instagram)
- Hoạt động gần đây

### Logs
- Activity logs trong database
- Server logs cho debugging
- Error tracking và monitoring

## 🚨 Troubleshooting

### Lỗi thường gặp

1. **Webhook verification failed**
   - Kiểm tra FACEBOOK_WEBHOOK_VERIFY_TOKEN
   - Đảm bảo URL webhook accessible

2. **Auto reply không hoạt động**
   - Kiểm tra cấu hình auto reply
   - Verify OpenAI API key
   - Xem activity logs

3. **Token expired**
   - Implement token refresh logic
   - Monitor token expiration

Xem chi tiết trong `docs/facebook-integration-setup.md`

## 🔄 Workflow

1. **Setup**: User kết nối Facebook Page qua OAuth
2. **Configuration**: Cấu hình AI auto reply settings
3. **Webhook**: Facebook gửi events khi có comments/messages
4. **Processing**: System xử lý và generate AI response
5. **Reply**: Gửi auto reply về Facebook/Instagram
6. **Logging**: Ghi lại activity và analytics

## 🎯 Roadmap

### Tính năng tiếp theo
- [ ] Bulk message sending
- [ ] Advanced AI training với custom data
- [ ] Integration với CRM systems
- [ ] Advanced analytics và reporting
- [ ] Multi-language support mở rộng
- [ ] Scheduled posts và campaigns

## 📞 Hỗ trợ

- Documentation: `docs/facebook-integration-setup.md`
- Test script: `scripts/test-facebook-integration.js`
- GitHub Issues: [Link to issues]
- Support team: [Contact info]

---

**Lưu ý**: Đây là hệ thống production-ready với đầy đủ tính năng bảo mật, monitoring và testing. Hãy đảm bảo cấu hình đúng environment variables và Facebook App trước khi deploy.
