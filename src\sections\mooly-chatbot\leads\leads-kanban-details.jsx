'use client';

import { useState, useCallback } from 'react';
import { varAlpha } from 'minimal-shared/utils';
import { useTabs, useBoolean } from 'minimal-shared/hooks';

import Box from '@mui/material/Box';
import Tab from '@mui/material/Tab';
import Chip from '@mui/material/Chip';
import Drawer from '@mui/material/Drawer';
import Button from '@mui/material/Button';
import Avatar from '@mui/material/Avatar';
import Tooltip from '@mui/material/Tooltip';
import { styled } from '@mui/material/styles';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import Stack from '@mui/material/Stack';
import Divider from '@mui/material/Divider';

import { Iconify } from 'src/components/iconify';
import { Scrollbar } from 'src/components/scrollbar';
import { CustomTabs } from 'src/components/custom-tabs';
import { Label } from 'src/components/label';

import { getLeadStatusOptions } from 'src/actions/mooly-chatbot/chatbot-lead-service';

// ----------------------------------------------------------------------

const BlockLabel = styled('span')(({ theme }) => ({
  ...theme.typography.caption,
  width: 100,
  flexShrink: 0,
  color: theme.vars.palette.text.secondary,
  fontWeight: theme.typography.fontWeightSemiBold,
}));

// ----------------------------------------------------------------------

export default function LeadsKanbanDetails({ task, open, onClose, onEdit, onDelete }) {
  const tabs = useTabs('overview');

  if (!task?.leadData) {
    return null;
  }

  const { leadData } = task;
  const statusOptions = getLeadStatusOptions();
  const statusOption = statusOptions.find((option) => option.value === leadData.status);

  // Extract lead data from JSON if needed
  let extractedLeadData = {};
  try {
    extractedLeadData = leadData.leadData ? 
      (typeof leadData.leadData === 'string' ? JSON.parse(leadData.leadData) : leadData.leadData) 
      : {};
  } catch (error) {
    console.error('Error parsing lead data:', error);
  }

  const renderToolbar = () => (
    <Box
      sx={{
        px: 2.5,
        py: 2,
        borderBottom: 1,
        borderColor: 'divider',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
      }}
    >
      <Typography variant="h6">Chi tiết Lead</Typography>

      <Stack direction="row" spacing={1}>
        <Tooltip title="Chỉnh sửa">
          <IconButton onClick={onEdit}>
            <Iconify icon="solar:pen-bold" />
          </IconButton>
        </Tooltip>

        <Tooltip title="Xóa">
          <IconButton onClick={onDelete} color="error">
            <Iconify icon="solar:trash-bin-trash-bold" />
          </IconButton>
        </Tooltip>

        <Tooltip title="Đóng">
          <IconButton onClick={onClose}>
            <Iconify icon="mingcute:close-line" />
          </IconButton>
        </Tooltip>
      </Stack>
    </Box>
  );

  const renderTabs = () => (
    <CustomTabs
      value={tabs.value}
      onChange={tabs.onChange}
      variant="fullWidth"
      slotProps={{ tab: { sx: { px: 0 } } }}
    >
      {[
        { value: 'overview', label: 'Tổng quan' },
        { value: 'contact', label: 'Liên hệ' },
        { value: 'notes', label: 'Ghi chú' },
      ].map((tab) => (
        <Tab key={tab.value} value={tab.value} label={tab.label} />
      ))}
    </CustomTabs>
  );

  const renderInfoItem = (label, value, icon) => (
    <Stack direction="row" spacing={2} alignItems="center" sx={{ py: 1 }}>
      {icon && (
        <Iconify 
          icon={icon} 
          width={20} 
          sx={{ color: 'text.secondary', flexShrink: 0 }} 
        />
      )}
      <Box sx={{ flex: 1 }}>
        <Typography variant="caption" sx={{ color: 'text.secondary' }}>
          {label}
        </Typography>
        <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
          {value || 'Chưa có thông tin'}
        </Typography>
      </Box>
    </Stack>
  );

  const renderTabOverview = () => (
    <Box sx={{ gap: 3, display: 'flex', flexDirection: 'column' }}>
      {/* Lead name and status */}
      <Stack spacing={2}>
        <Stack direction="row" alignItems="center" spacing={2}>
          <Avatar sx={{ bgcolor: 'primary.main', width: 48, height: 48 }}>
            {(leadData.fullName || leadData.phone || leadData.email || 'N').charAt(0).toUpperCase()}
          </Avatar>
          <Box sx={{ flex: 1 }}>
            <Typography variant="h6">
              {leadData.fullName || leadData.phone || leadData.email || 'Không có tên'}
            </Typography>
            <Label color={statusOption?.color || 'default'}>
              {statusOption?.label || leadData.status || 'Không xác định'}
            </Label>
          </Box>
        </Stack>
      </Stack>

      <Divider />

      {/* Basic info */}
      <Stack spacing={2}>
        <Typography variant="subtitle2">Thông tin cơ bản</Typography>
        
        {renderInfoItem(
          'Trạng thái',
          statusOption?.label || leadData.status,
          'solar:user-check-bold'
        )}
        
        {renderInfoItem(
          'Nguồn',
          leadData.source || 'Không xác định',
          'solar:global-bold'
        )}
        
        {renderInfoItem(
          'Điểm lead',
          leadData.leadScore || 0,
          'solar:star-bold'
        )}
        
        {renderInfoItem(
          'Phụ trách',
          leadData.assignedTo || 'Chưa phân công',
          'solar:user-bold'
        )}
        
        {renderInfoItem(
          'Ngày tạo',
          leadData.createdAt ? new Date(leadData.createdAt).toLocaleDateString('vi-VN') : '',
          'solar:calendar-bold'
        )}
      </Stack>
    </Box>
  );

  const renderTabContact = () => (
    <Box sx={{ gap: 3, display: 'flex', flexDirection: 'column' }}>
      <Stack spacing={2}>
        <Typography variant="subtitle2">Thông tin liên hệ</Typography>
        
        {renderInfoItem(
          'Số điện thoại',
          leadData.phone,
          'solar:phone-bold'
        )}
        
        {renderInfoItem(
          'Email',
          leadData.email,
          'solar:letter-bold'
        )}
        
        {renderInfoItem(
          'Địa chỉ',
          extractedLeadData.address || 'Chưa có thông tin',
          'solar:map-point-bold'
        )}
        
        {renderInfoItem(
          'Công ty',
          extractedLeadData.company || 'Chưa có thông tin',
          'solar:buildings-2-bold'
        )}
      </Stack>
    </Box>
  );

  const renderTabNotes = () => (
    <Box sx={{ gap: 3, display: 'flex', flexDirection: 'column' }}>
      <Stack spacing={2}>
        <Typography variant="subtitle2">Ghi chú</Typography>
        
        <TextField
          fullWidth
          multiline
          rows={4}
          value={leadData.notes || ''}
          placeholder="Chưa có ghi chú"
          slotProps={{ input: { readOnly: true } }}
          sx={{ 
            '& .MuiInputBase-input': { 
              cursor: 'default' 
            } 
          }}
        />
        
        {leadData.nextFollowUpAt && (
          <Box>
            <Typography variant="caption" sx={{ color: 'text.secondary' }}>
              Lịch theo dõi tiếp theo
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
              {new Date(leadData.nextFollowUpAt).toLocaleString('vi-VN')}
            </Typography>
          </Box>
        )}
      </Stack>
    </Box>
  );

  return (
    <Drawer
      open={open}
      onClose={onClose}
      anchor="right"
      slotProps={{
        backdrop: { invisible: true },
        paper: { sx: { width: { xs: 1, sm: 480 } } },
      }}
    >
      {renderToolbar()}
      {renderTabs()}

      <Scrollbar fillContent sx={{ py: 3, px: 2.5 }}>
        {tabs.value === 'overview' && renderTabOverview()}
        {tabs.value === 'contact' && renderTabContact()}
        {tabs.value === 'notes' && renderTabNotes()}
      </Scrollbar>
    </Drawer>
  );
} 