/**
 * UNIFIED WORKFLOW SERVICE
 * Service duy nhất để quản lý workflow với logic đồng bộ
 * Không có transform qua lại, chỉ có một nguồn truth
 */

import { fetchData, createData, updateData, deleteData, callRPC } from './supabase-utils';

// =====================================================
// CONSTANTS
// =====================================================

const WORKFLOW_INSTANCES_TABLE = 'workflow_instances';
const WORKFLOW_TEMPLATES_TABLE = 'workflow_templates';
const CHATBOT_LEADS_TABLE = 'chatbot_leads';

// Color mapping - linh hoạt cho cả hex và MUI colors
const COLOR_MAPPING = {
  // MUI Standard Colors
  'primary': 'primary',
  'secondary': 'secondary', 
  'success': 'success',
  'error': 'error',
  'warning': 'warning',
  'info': 'info',
  'default': 'default',
  
  // Custom hex colors mapped to closest MUI color
  '#e3f2fd': 'info',      // Light blue
  '#fff3e0': 'warning',   // Light orange
  '#f3e5f5': 'secondary', // Light purple
  '#e8f5e8': 'success',   // Light green
  '#ffebee': 'error',     // Light red
  '#fce4ec': 'secondary', // Light pink
  '#e0f2f1': 'success',   // Light teal
  '#fff8e1': 'warning',   // Light yellow
  '#f1f8e9': 'success',   // Light lime
  '#e8eaf6': 'primary',   // Light indigo
};

// Default workflow stages - SINGLE SOURCE OF TRUTH
const DEFAULT_WORKFLOW_STAGES = [
  {
    id: 'new',
    name: 'Lead Mới',
    color: '#e3f2fd', // Hex color - sẽ được map sang MUI color
    displayColor: 'info', // MUI color cho UI components
    icon: 'solar:user-plus-bold',
    description: 'Lead vừa được tạo từ các kênh',
    order: 1,
    duration: 24,
  },
  {
    id: 'contacted',
    name: 'Đã Liên Hệ',
    color: '#fff3e0',
    displayColor: 'warning',
    icon: 'solar:phone-bold',
    description: 'Đã có liên hệ ban đầu với lead',
    order: 2,
    duration: 72,
  },
  {
    id: 'qualified',
    name: 'Tiềm Năng',
    color: '#f3e5f5',
    displayColor: 'secondary',
    icon: 'solar:star-bold',
    description: 'Lead có tiềm năng chuyển đổi',
    order: 3,
    duration: 168,
  },
  {
    id: 'converted',
    name: 'Đã Chuyển Đổi',
    color: '#e8f5e8',
    displayColor: 'success',
    icon: 'solar:check-circle-bold',
    description: 'Lead đã trở thành khách hàng',
    order: 4,
    duration: null,
    isFinal: true,
  },
  {
    id: 'lost',
    name: 'Thất Bại',
    color: '#ffebee',
    displayColor: 'error',
    icon: 'solar:close-circle-bold',
    description: 'Lead không chuyển đổi',
    order: 5,
    duration: null,
    isFinal: true,
  },
];

// =====================================================
// COLOR UTILITY FUNCTIONS
// =====================================================

/**
 * Chuyển đổi color thành MUI color name an toàn
 * @param {string} color - Color value (hex, MUI name, etc.)
 * @returns {string} - MUI color name hợp lệ
 */
export function getDisplayColor(color) {
  if (!color) return 'default';
  
  // Nếu đã là MUI color name hợp lệ
  const muiColors = ['primary', 'secondary', 'success', 'error', 'warning', 'info', 'default'];
  if (muiColors.includes(color)) {
    return color;
  }
  
  // Map từ hex hoặc custom color
  return COLOR_MAPPING[color] || 'default';
}

/**
 * Lấy color cho background/styling (có thể là hex)
 * @param {string} color - Color value
 * @returns {string} - Color value cho styling
 */
export function getStyleColor(color) {
  if (!color) return '#757575'; // Default gray
  
  // Nếu là hex color, trả về nguyên bản
  if (color.startsWith('#')) {
    return color;
  }
  
  // Map MUI colors to hex values for consistent styling
  const muiToHex = {
    'primary': '#1976d2',
    'secondary': '#dc004e', 
    'success': '#2e7d32',
    'error': '#d32f2f',
    'warning': '#ed6c02',
    'info': '#0288d1',
    'default': '#757575',
  };
  
  // Trả về hex color tương ứng hoặc original value
  return muiToHex[color] || color;
}

/**
 * Validate color value
 * @param {string} color - Color value to validate
 * @returns {boolean} - True if valid
 */
export function isValidColor(color) {
  if (!color) return false;
  
  // Check MUI color names
  const muiColors = ['primary', 'secondary', 'success', 'error', 'warning', 'info', 'default'];
  if (muiColors.includes(color)) return true;
  
  // Check hex color format
  if (/^#[0-9A-Fa-f]{6}$/.test(color)) return true;
  
  // Check if it's in our mapping
  return Object.prototype.hasOwnProperty.call(COLOR_MAPPING, color);
}

/**
 * Tạo stage object với color properties đầy đủ
 * @param {Object} stageData - Raw stage data
 * @returns {Object} - Stage với color properties
 */
export function normalizeStageColors(stageData) {
  const color = stageData.color || '#757575';
  
  return {
    ...stageData,
    color, // Original color (có thể là hex)
    displayColor: getDisplayColor(color), // MUI color cho components
    styleColor: getStyleColor(color), // Color cho styling
  };
}

// =====================================================
// CORE WORKFLOW FUNCTIONS
// =====================================================

/**
 * Lấy các stages của workflow cho chatbot hoặc default workflow
 * @param {string} chatbotId - ID của chatbot (null để lấy default)
 * @returns {Promise<Object>} - Kết quả với các stages
 */
export async function getChatbotWorkflowStages(chatbotId = null) {
  try {
    // Nếu không có chatbotId, tìm global workflow
    if (!chatbotId) {
      // Tìm global workflow (chatbotId = null, isGlobal = true)
      const globalResult = await fetchData(WORKFLOW_INSTANCES_TABLE, {
        filters: { chatbotId: null, isGlobal: true, isActive: true },
        single: true,
      });

      if (globalResult.success && globalResult.data && globalResult.data.stages?.length > 0) {
        return {
          success: true,
          data: globalResult.data.stages,
          error: null
        };
      }

      // Không có global workflow, trả về default
      return {
        success: true,
        data: DEFAULT_WORKFLOW_STAGES,
        error: null
      };
    }

    // Lấy workflow instance cho chatbot cụ thể
    const result = await fetchData(WORKFLOW_INSTANCES_TABLE, {
      filters: { chatbotId },
      single: true,
    });

    if (!result.success && result.error?.code === 'NO_ROWS_OR_MULTIPLE_ROWS') {
      // Không có settings, trả về default
      return {
        success: true,
        data: DEFAULT_WORKFLOW_STAGES,
        error: null
      };
    }

    if (result.success && result.data) {
      const settings = result.data;
      
      if (settings.templateId) {
        // Lấy từ template
        const templateResult = await fetchData(WORKFLOW_TEMPLATES_TABLE, {
          filters: { id: settings.templateId },
          single: true,
        });
        
        if (templateResult.success && templateResult.data) {
          return {
            success: true,
            data: templateResult.data.stages || DEFAULT_WORKFLOW_STAGES,
            error: null
          };
        }
      } else if (settings.stages && settings.stages.length > 0) {
        // Sử dụng custom stages từ instance
        return {
          success: true,
          data: settings.stages,
          error: null
        };
      }
    }

    // Fallback to default
    return {
      success: true,
      data: DEFAULT_WORKFLOW_STAGES,
      error: null
    };
  } catch (error) {
    return {
      success: false,
      error,
      data: DEFAULT_WORKFLOW_STAGES // Fallback khi có lỗi
    };
  }
}

/**
 * Cập nhật workflow cho chatbot
 * @param {string} chatbotId - ID của chatbot
 * @param {Object} workflowData - Dữ liệu workflow
 * @returns {Promise<Object>} - Kết quả cập nhật
 */
export async function updateChatbotWorkflow(chatbotId, workflowData) {
  try {
    const { templateId, customStages, isUsingCustom = false } = workflowData;

    // Validate stages
    const stages = isUsingCustom ? customStages : null;
    if (stages && !Array.isArray(stages)) {
      return {
        success: false,
        error: { message: 'Custom stages phải là array' },
        data: null,
      };
    }

    // Validate stage IDs (linh hoạt hơn cho custom workflows)
    if (stages) {
      for (const stage of stages) {
        if (!stage.id || !/^[a-zA-Z][a-zA-Z0-9_]*$/.test(stage.id) || stage.id.length < 2) {
          return {
            success: false,
            error: { message: `Stage ID "${stage.id}" không hợp lệ. Chỉ cho phép chữ, số và underscore, bắt đầu bằng chữ, tối thiểu 2 ký tự.` },
            data: null,
          };
        }
      }
    }

    // Kiểm tra xem đã có workflow instance chưa
    const filters = chatbotId 
      ? { chatbotId } 
      : { chatbotId: null, isGlobal: true }; // Global workflow khi chatbotId = null

    const existingResult = await fetchData(WORKFLOW_INSTANCES_TABLE, {
      filters,
      single: true,
    });

    const instanceData = {
      chatbotId,
      templateId: templateId || null,
      name: `Workflow for ${chatbotId ? `chatbot ${chatbotId}` : 'global'}`,
      description: 'Auto-created workflow instance',
      stages: stages || [],
      isGlobal: !chatbotId, // Set isGlobal = true khi chatbotId = null
      isActive: true,
      updatedAt: new Date().toISOString(),
    };

    let result;
    if (existingResult.success && existingResult.data) {
      // Update existing - fix the parameter order: table, data, filters
      result = await updateData(WORKFLOW_INSTANCES_TABLE, instanceData, { id: existingResult.data.id });
    } else {
      // Create new - chatbotId có thể null cho global workflow
      result = await createData(WORKFLOW_INSTANCES_TABLE, instanceData);
    }

    return result;
  } catch (error) {
    console.error('Error updating chatbot workflow:', error);
    return {
      success: false,
      error,
      data: null,
    };
  }
}

/**
 * Lấy danh sách workflow templates
 * @returns {Promise<Object>} - Kết quả với templates
 */
export async function getWorkflowTemplates() {
  try {
    const result = await fetchData(WORKFLOW_TEMPLATES_TABLE, {
      filters: { isSystemTemplate: true },
      orderBy: [{ column: 'name', ascending: true }],
    });

    return result;
  } catch (error) {
    console.error('Error getting workflow templates:', error);
    return {
      success: false,
      error,
      data: [],
    };
  }
}

/**
 * Validate status với workflow stages
 * @param {string} status - Status cần validate
 * @param {Array} stages - Workflow stages
 * @returns {boolean} - True nếu hợp lệ
 */
export function validateLeadStatus(status, stages = DEFAULT_WORKFLOW_STAGES) {
  if (!status || !Array.isArray(stages)) {
    return false;
  }

  return stages.some(stage => stage.id === status);
}

// updateLeadStatus function đã được di chuyển sang chatbot-lead-service.js
// để tránh duplicate và đảm bảo consistency

// =====================================================
// REACT HOOKS
// =====================================================

import { useMemo } from 'react';
import useSWR from 'swr';

/**
 * Hook để lấy workflow stages cho chatbot
 * @param {string} chatbotId - ID của chatbot
 * @returns {Object} - { stages, loading, error, mutate }
 */
export function useChatbotWorkflowStages(chatbotId) {
  const swrKey = chatbotId ? `workflow-stages-${chatbotId}` : 'workflow-stages-default';

  const { data, error, mutate } = useSWR(
    swrKey,
    () => getChatbotWorkflowStages(chatbotId),
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: true,
      dedupingInterval: 30000, // 30 seconds
      errorRetryCount: 2,
      refreshInterval: 0,
    }
  );

  return {
    stages: data?.success ? data.data : DEFAULT_WORKFLOW_STAGES,
    loading: !data && !error,
    error: error || (!data?.success ? data?.error : null),
    mutate,
  };
}

/**
 * Hook để lấy workflow templates
 * @returns {Object} - { templates, loading, error, mutate }
 */
export function useWorkflowTemplates() {
  const { data, error, mutate } = useSWR(
    'workflow-templates',
    getWorkflowTemplates,
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: true,
      dedupingInterval: 60000, // 1 minute
      errorRetryCount: 2,
      refreshInterval: 0,
    }
  );

  return {
    templates: data?.success ? data.data : [],
    loading: !data && !error,
    error: error || (!data?.success ? data?.error : null),
    mutate,
  };
}

// =====================================================
// UTILITY FUNCTIONS
// =====================================================

/**
 * Transform stages thành options cho UI components
 * @param {Array} stages - Workflow stages
 * @returns {Array} - Options array
 */
export function stagesToStatusOptions(stages = DEFAULT_WORKFLOW_STAGES) {
  return stages.map(stage => {
    const normalizedStage = normalizeStageColors(stage);
    return {
      value: stage.id,
      label: stage.name,
      color: normalizedStage.displayColor, // MUI color cho components
      styleColor: normalizedStage.styleColor, // Hex color cho styling
      originalColor: normalizedStage.color, // Original color
      icon: stage.icon || null,
      description: stage.description || '',
      order: stage.order || 0,
      isFinal: stage.isFinal || false,
    };
  }).sort((a, b) => (a.order || 0) - (b.order || 0));
}

/**
 * Lấy stage info theo ID
 * @param {string} stageId - ID của stage
 * @param {Array} stages - Workflow stages
 * @returns {Object|null} - Stage info hoặc null
 */
export function getStageById(stageId, stages = DEFAULT_WORKFLOW_STAGES) {
  return stages.find(stage => stage.id === stageId) || null;
}

/**
 * Kiểm tra xem có thể chuyển từ stage này sang stage khác không
 * @param {string} fromStage - Stage hiện tại
 * @param {string} toStage - Stage đích
 * @param {Array} stages - Workflow stages
 * @returns {boolean} - True nếu có thể chuyển
 */
export function canTransitionStage(fromStage, toStage, stages = DEFAULT_WORKFLOW_STAGES) {
  const fromStageInfo = getStageById(fromStage, stages);
  const toStageInfo = getStageById(toStage, stages);

  if (!fromStageInfo || !toStageInfo) {
    return false;
  }

  // Không thể chuyển sang uncategorized
  if (toStage === 'uncategorized') {
    return false;
  }

  // LOGIC MỚI: Linh hoạt cho custom workflows
  // Kiểm tra xem có transition rules custom không
  if (fromStageInfo.allowedTransitions && Array.isArray(fromStageInfo.allowedTransitions)) {
    // Nếu có rules cụ thể, tuân theo rules đó
    return fromStageInfo.allowedTransitions.includes(toStage);
  }

  // FALLBACK: Logic mặc định linh hoạt
  // Cho phép chuyển tự do TRỪ KHI có restrictTransitions = true
  if (fromStageInfo.restrictTransitions === true) {
    // Nếu stage được đánh dấu restrict, chỉ cho phép chuyển sang final stages
    return toStageInfo.isFinal === true;
  }

  // Mặc định: Cho phép chuyển tự do (trừ uncategorized đã check ở trên)
  return true;
}

// =====================================================
// STAGE MANAGEMENT FUNCTIONS
// =====================================================

/**
 * Cập nhật thứ tự stages trong workflow (drag & drop)
 * @param {string} chatbotId - ID của chatbot (null cho global workflow)
 * @param {Array} reorderedStages - Stages đã được sắp xếp lại
 * @returns {Promise<Object>} - Kết quả cập nhật
 */
export async function updateStageOrder(chatbotId = null, reorderedStages) {
  try {
    // Cập nhật order cho từng stage
    const updatedStages = reorderedStages.map((stage, index) => ({
      ...stage,
      order: index + 1,
    }));

    // Sử dụng updateChatbotWorkflow để lưu
    const result = await updateChatbotWorkflow(chatbotId, {
      customStages: updatedStages,
      isUsingCustom: true,
    });

    return result;
  } catch (error) {
    console.error('Error updating stage order:', error);
    return {
      success: false,
      error: {
        message: error.message || 'Không thể cập nhật thứ tự giai đoạn',
        code: 'UPDATE_STAGE_ORDER_ERROR'
      },
      data: null,
    };
  }
}

/**
 * Thêm stage mới vào workflow
 * @param {string} chatbotId - ID của chatbot (null cho global workflow)
 * @param {Object} newStage - Stage mới
 * @param {number} insertIndex - Vị trí chèn stage (sau stage nào)
 * @returns {Promise<Object>} - Kết quả cập nhật
 */
export async function addNewStage(chatbotId = null, newStage, insertIndex = -1) {
  try {
    // Lấy stages hiện tại
    const currentResult = await getChatbotWorkflowStages(chatbotId);
    if (!currentResult.success) {
      return currentResult;
    }

    const currentStages = currentResult.data || DEFAULT_WORKFLOW_STAGES;

    // Tạo stage ID unique
    const stageId = `stage${Date.now()}${Math.floor(Math.random() * 1000)}`;
    
    // Chuẩn bị stage mới với defaults
    const stageToAdd = {
      id: stageId,
      name: newStage.name || 'Giai đoạn mới',
      description: newStage.description || '',
      color: newStage.color || '#e3f2fd',
      displayColor: getDisplayColor(newStage.color || '#e3f2fd'),
      icon: newStage.icon || 'solar:star-bold',
      duration: newStage.duration || null,
      isFinal: newStage.isFinal || false,
      order: 0, // Sẽ được set lại sau
    };

    // Chèn stage vào vị trí phù hợp
    let updatedStages;
    if (insertIndex >= 0 && insertIndex < currentStages.length) {
      // Chèn vào giữa
      updatedStages = [
        ...currentStages.slice(0, insertIndex + 1),
        stageToAdd,
        ...currentStages.slice(insertIndex + 1),
      ];
    } else {
      // Thêm vào cuối
      updatedStages = [...currentStages, stageToAdd];
    }

    // Cập nhật order cho tất cả stages
    updatedStages = updatedStages.map((stage, index) => ({
      ...stage,
      order: index + 1,
    }));

    // Lưu vào database
    const result = await updateChatbotWorkflow(chatbotId, {
      customStages: updatedStages,
      isUsingCustom: true,
    });

    if (result.success) {
      return {
        success: true,
        data: {
          ...result.data,
          newStageId: stageId,
          stages: updatedStages,
        },
        error: null,
      };
    }

    return result;
  } catch (error) {
    console.error('Error adding new stage:', error);
    return {
      success: false,
      error: {
        message: error.message || 'Không thể thêm giai đoạn mới',
        code: 'ADD_STAGE_ERROR'
      },
      data: null,
    };
  }
}

/**
 * Xóa stage khỏi workflow
 * @param {string} chatbotId - ID của chatbot (null cho global workflow)
 * @param {string} stageId - ID của stage cần xóa
 * @returns {Promise<Object>} - Kết quả cập nhật
 */
export async function removeStage(chatbotId = null, stageId) {
  try {
    // Lấy stages hiện tại
    const currentResult = await getChatbotWorkflowStages(chatbotId);
    if (!currentResult.success) {
      return currentResult;
    }

    const currentStages = currentResult.data || DEFAULT_WORKFLOW_STAGES;

    // Kiểm tra xem stage có tồn tại không
    const stageIndex = currentStages.findIndex(stage => stage.id === stageId);
    if (stageIndex === -1) {
      return {
        success: false,
        error: { message: 'Không tìm thấy giai đoạn cần xóa' },
        data: null,
      };
    }

    // Kiểm tra xem có lead nào đang ở stage này không
    const leadsInStageResult = await fetchData(CHATBOT_LEADS_TABLE, {
      filters: { status: stageId },
      limit: 1,
    });

    if (leadsInStageResult.success && leadsInStageResult.data?.length > 0) {
      return {
        success: false,
        error: { 
          message: 'Không thể xóa giai đoạn đang có leads. Vui lòng di chuyển leads sang giai đoạn khác trước.',
          code: 'STAGE_HAS_LEADS'
        },
        data: null,
      };
    }

    // Xóa stage và cập nhật order
    const updatedStages = currentStages
      .filter(stage => stage.id !== stageId)
      .map((stage, index) => ({
        ...stage,
        order: index + 1,
      }));

    // Lưu vào database
    const result = await updateChatbotWorkflow(chatbotId, {
      customStages: updatedStages,
      isUsingCustom: true,
    });

    return result;
  } catch (error) {
    console.error('Error removing stage:', error);
    return {
      success: false,
      error: {
        message: error.message || 'Không thể xóa giai đoạn',
        code: 'REMOVE_STAGE_ERROR'
      },
      data: null,
    };
  }
}

/**
 * Cập nhật thông tin stage
 * @param {string} chatbotId - ID của chatbot (null cho global workflow)
 * @param {string} stageId - ID của stage cần cập nhật
 * @param {Object} stageUpdates - Dữ liệu cập nhật
 * @returns {Promise<Object>} - Kết quả cập nhật
 */
export async function updateStage(chatbotId = null, stageId, stageUpdates) {
  try {
    // Lấy stages hiện tại
    const currentResult = await getChatbotWorkflowStages(chatbotId);
    if (!currentResult.success) {
      return currentResult;
    }

    const currentStages = currentResult.data || DEFAULT_WORKFLOW_STAGES;

    // Tìm và cập nhật stage
    const updatedStages = currentStages.map(stage => {
      if (stage.id === stageId) {
        const updated = { ...stage, ...stageUpdates };
        // Cập nhật displayColor nếu color thay đổi
        if (stageUpdates.color) {
          updated.displayColor = getDisplayColor(stageUpdates.color);
        }
        return updated;
      }
      return stage;
    });

    // Kiểm tra xem stage có tồn tại không
    if (updatedStages.length === currentStages.length && 
        updatedStages.every((stage, index) => stage.id === currentStages[index].id)) {
      return {
        success: false,
        error: { message: 'Không tìm thấy giai đoạn cần cập nhật' },
        data: null,
      };
    }

    // Lưu vào database
    const result = await updateChatbotWorkflow(chatbotId, {
      customStages: updatedStages,
      isUsingCustom: true,
    });

    return result;
  } catch (error) {
    console.error('Error updating stage:', error);
    return {
      success: false,
      error: {
        message: error.message || 'Không thể cập nhật giai đoạn',
        code: 'UPDATE_STAGE_ERROR'
      },
      data: null,
    };
  }
}

/**
 * Kiểm tra và tối ưu hóa workflow configuration
 * @param {Array} stages - Workflow stages
 * @returns {Object} - Validation result và optimized config
 */
export function validateAndOptimizeWorkflow(stages) {
  const issues = [];
  const optimizations = [];
  
  if (!stages || !Array.isArray(stages) || stages.length === 0) {
    return {
      isValid: false,
      issues: ['Workflow phải có ít nhất một stage'],
      optimizations: [],
      optimizedStages: DEFAULT_WORKFLOW_STAGES
    };
  }

  // Validate stage IDs
  const stageIds = new Set();
  const duplicates = [];
  
  stages.forEach((stage, index) => {
    // Check required fields
    if (!stage.id || typeof stage.id !== 'string') {
      issues.push(`Stage ${index + 1}: ID không hợp lệ`);
    } else if (stageIds.has(stage.id)) {
      duplicates.push(stage.id);
    } else {
      stageIds.add(stage.id);
    }
    
    if (!stage.name || typeof stage.name !== 'string') {
      issues.push(`Stage ${index + 1}: Tên không hợp lệ`);
    }
    
    // Check ID format
    if (stage.id && !/^[a-zA-Z][a-zA-Z0-9_]*$/.test(stage.id)) {
      issues.push(`Stage ${index + 1}: ID "${stage.id}" không đúng format (chỉ chữ, số, underscore)`);
    }
  });

  if (duplicates.length > 0) {
    issues.push(`Stage IDs trùng lặp: ${duplicates.join(', ')}`);
  }

  // Optimize stages
  const optimizedStages = stages.map((stage, index) => {
    const optimized = { ...stage };
    
    // Ensure order
    if (!optimized.order || typeof optimized.order !== 'number') {
      optimized.order = index + 1;
      optimizations.push(`Stage "${stage.name}": Tự động set order = ${index + 1}`);
    }
    
    // Normalize colors
    if (!isValidColor(optimized.color)) {
      optimized.color = '#757575'; // Default gray
      optimized.displayColor = 'default';
      optimizations.push(`Stage "${stage.name}": Sửa color không hợp lệ thành default`);
    } else {
      optimized.displayColor = getDisplayColor(optimized.color);
      optimized.styleColor = getStyleColor(optimized.color);
    }
    
    // Default icon
    if (!optimized.icon) {
      optimized.icon = 'solar:star-bold';
      optimizations.push(`Stage "${stage.name}": Thêm icon mặc định`);
    }
    
    // Validate duration
    if (optimized.duration && (typeof optimized.duration !== 'number' || optimized.duration < 0)) {
      optimized.duration = null;
      optimizations.push(`Stage "${stage.name}": Sửa duration không hợp lệ`);
    }
    
    return optimized;
  });

  return {
    isValid: issues.length === 0,
    issues,
    optimizations,
    optimizedStages,
    summary: {
      totalStages: optimizedStages.length,
      finalStages: optimizedStages.filter(s => s.isFinal).length,
      averageDuration: optimizedStages
        .filter(s => s.duration && !s.isFinal)
        .reduce((sum, s, _, arr) => sum + s.duration / arr.length, 0) || null
    }
  };
}

/**
 * Đồng bộ leads có status cũ với workflow mới
 * @param {string} chatbotId - ID của chatbot (null cho global)
 * @param {Array} newStages - Workflow stages mới
 * @param {Object} mappingRules - Rules để map status cũ sang mới
 * @returns {Promise<Object>} - Kết quả sync
 */
export async function syncLeadsWithNewWorkflow(chatbotId, newStages, mappingRules = {}) {
  try {
    // Lấy danh sách valid stage IDs mới
    const validStageIds = newStages.map(stage => stage.id);
    
    // Lấy leads có status không hợp lệ
    const leadsResult = await fetchData(CHATBOT_LEADS_TABLE, {
      filters: chatbotId ? { chatbotId } : {},
      columns: ['id', 'status', 'fullName']
    });
    
    if (!leadsResult.success) {
      return { success: false, error: leadsResult.error, data: null };
    }
    
    const leads = leadsResult.data || [];
    const invalidLeads = leads.filter(lead => !validStageIds.includes(lead.status));
    
    if (invalidLeads.length === 0) {
      return {
        success: true,
        data: { 
          message: 'Tất cả leads đã có status hợp lệ',
          synced: 0,
          total: leads.length 
        }
      };
    }
    
    // Tạo mapping rules mặc định
    const defaultMapping = {
      'proposal': 'qualified',
      'negotiation': 'qualified', 
      'won': 'converted',
      'nurturing': 'new'
    };
    
    const finalMapping = { ...defaultMapping, ...mappingRules };
    
    // Sync từng lead
    const syncResults = [];
    for (const lead of invalidLeads) {
      const newStatus = finalMapping[lead.status] || 'new'; // Default to 'new'
      
      const updateResult = await updateData(CHATBOT_LEADS_TABLE, 
        { status: newStatus },
        { id: lead.id }
      );
      
      syncResults.push({
        leadId: lead.id,
        leadName: lead.fullName,
        oldStatus: lead.status,
        newStatus,
        success: updateResult.success
      });
    }
    
    const successCount = syncResults.filter(r => r.success).length;
    
    return {
      success: true,
      data: {
        message: `Đã sync ${successCount}/${invalidLeads.length} leads`,
        synced: successCount,
        failed: invalidLeads.length - successCount,
        total: leads.length,
        details: syncResults
      }
    };
    
  } catch (error) {
    return { success: false, error, data: null };
  }
}

export default {
  getChatbotWorkflowStages,
  updateChatbotWorkflow,
  getWorkflowTemplates,
  validateLeadStatus,
  useChatbotWorkflowStages,
  useWorkflowTemplates,
  stagesToStatusOptions,
  getStageById,
  canTransitionStage,
  updateStageOrder,
  addNewStage,
  removeStage,
  updateStage,
  DEFAULT_WORKFLOW_STAGES,
  validateAndOptimizeWorkflow,
  syncLeadsWithNewWorkflow,
}; 