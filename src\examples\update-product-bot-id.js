'use client';

import { updateProductById } from 'src/app/actions/weaviate/products';
import weaviateService from 'src/actions/mooly-chatbot/weaviate-service';

/**
 * Ví dụ về cách sử dụng API mới để cập nhật bot_id cho sản phẩm
 * @param {string} productId - ID của sản phẩm
 * @param {string} tenantId - ID của tenant
 * @param {Array<string>} botIds - Danh sách ID của bot
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function updateProductBotIds(productId, tenantId, botIds) {
  try {
    // Cách 1: Sử dụng trực tiếp server action
    const result = await updateProductById({
      tenant_id: tenantId,
      product_id: productId,
      bot_id: botIds, // Mảng các ID của bot
    });

    if (!result.success) {
      console.warn('Cảnh báo: Cập nhật bot_id không thành công', result.error);
      return result;
    }

    return result;
  } catch (error) {
    console.error('Error updating product bot IDs:', error);
    return {
      success: false,
      data: null,
      error: error.message,
    };
  }
}

/**
 * Ví dụ về cách sử dụng service để cập nhật bot_id cho sản phẩm
 * @param {string} productId - ID của sản phẩm
 * @param {string} tenantId - ID của tenant
 * @param {Array<string>} botIds - Danh sách ID của bot
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function updateProductBotIdsViaService(productId, tenantId, botIds) {
  try {
    // Cách 2: Sử dụng weaviateService
    const result = await weaviateService.updateProductById({
      tenant_id: tenantId,
      product_id: productId,
      bot_id: botIds, // Mảng các ID của bot
    });

    if (!result.success) {
      console.warn('Cảnh báo: Cập nhật bot_id không thành công', result.error);
      return result;
    }

    return result;
  } catch (error) {
    console.error('Error updating product bot IDs:', error);
    return {
      success: false,
      data: null,
      error: error.message,
    };
  }
}

/**
 * Ví dụ về cách cập nhật cả trạng thái kích hoạt và bot_id cho sản phẩm
 * @param {string} productId - ID của sản phẩm
 * @param {string} tenantId - ID của tenant
 * @param {boolean} isActive - Trạng thái kích hoạt mới
 * @param {Array<string>} botIds - Danh sách ID của bot
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function updateProductStatusAndBotIds(productId, tenantId, isActive, botIds) {
  try {
    // Cập nhật cả trạng thái kích hoạt và bot_id
    const result = await updateProductById({
      tenant_id: tenantId,
      product_id: productId,
      is_active: isActive,
      bot_id: botIds,
    });

    if (!result.success) {
      console.warn('Cảnh báo: Cập nhật sản phẩm không thành công', result.error);
      return result;
    }

    return result;
  } catch (error) {
    console.error('Error updating product:', error);
    return {
      success: false,
      data: null,
      error: error.message,
    };
  }
}
