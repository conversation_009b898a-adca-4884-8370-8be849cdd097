'use client';

import { useState } from 'react';

import { useAuthContext } from 'src/auth/hooks';

/**
 * Hook để sử dụng API routes cho products với RLS protection
 */
export function useProductApiService() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const { user } = useAuthContext();

  /**
   * Helper function để gọi API
   */
  const callApi = async (url, options = {}) => {
    try {
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || `HTTP error! status: ${response.status}`);
      }

      return data;
    } catch (err) {
      console.error('API call error:', err);
      throw err;
    }
  };

  /**
   * <PERSON><PERSON><PERSON>nh s<PERSON>ch sản phẩm
   */
  const getProducts = async (params = {}) => {
    setIsLoading(true);
    setError(null);

    try {
      const searchParams = new URLSearchParams();
      
      if (params.page) searchParams.append('page', params.page);
      if (params.limit) searchParams.append('limit', params.limit);
      if (params.search) searchParams.append('search', params.search);
      if (params.status) searchParams.append('status', params.status);

      const url = `/api/products${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
      const result = await callApi(url, { method: 'GET' });

      return { success: true, data: result.data, pagination: result.pagination };
    } catch (err) {
      setError(err);
      return { success: false, error: err, data: null };
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Lấy thông tin chi tiết sản phẩm
   */
  const getProductById = async (productId) => {
    if (!productId) {
      return { success: false, error: new Error('Product ID is required'), data: null };
    }

    setIsLoading(true);
    setError(null);

    try {
      const result = await callApi(`/api/products/${productId}`, { method: 'GET' });
      return { success: true, data: result.data };
    } catch (err) {
      setError(err);
      return { success: false, error: err, data: null };
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Tạo sản phẩm mới
   */
  const createProduct = async (productData) => {
    if (!productData) {
      return { success: false, error: new Error('Product data is required'), data: null };
    }

    setIsLoading(true);
    setError(null);

    try {
      const result = await callApi('/api/products', {
        method: 'POST',
        body: JSON.stringify(productData),
      });

      return { success: true, data: result.data, message: result.message };
    } catch (err) {
      setError(err);
      return { success: false, error: err, data: null };
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Cập nhật sản phẩm
   */
  const updateProduct = async (productId, productData) => {
    if (!productId || !productData) {
      return { success: false, error: new Error('Product ID and data are required'), data: null };
    }

    setIsLoading(true);
    setError(null);

    try {
      const result = await callApi(`/api/products/${productId}`, {
        method: 'PUT',
        body: JSON.stringify(productData),
      });

      return { success: true, data: result.data, message: result.message };
    } catch (err) {
      setError(err);
      return { success: false, error: err, data: null };
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Xóa sản phẩm đơn lẻ
   */
  const deleteProduct = async (productId) => {
    if (!productId) {
      return { success: false, error: new Error('Product ID is required'), data: null };
    }

    setIsLoading(true);
    setError(null);

    try {
      const result = await callApi(`/api/products/${productId}`, {
        method: 'DELETE',
      });

      return { success: true, data: result.data, message: result.message };
    } catch (err) {
      setError(err);
      return { success: false, error: err, data: null };
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Xóa nhiều sản phẩm cùng lúc
   */
  const bulkDeleteProducts = async (productIds) => {
    if (!productIds || !Array.isArray(productIds) || productIds.length === 0) {
      return { 
        success: false, 
        error: new Error('Product IDs array is required'), 
        data: null 
      };
    }

    setIsLoading(true);
    setError(null);

    try {
      const result = await callApi('/api/products/bulk-delete', {
        method: 'DELETE',
        body: JSON.stringify({ product_ids: productIds }),
      });

      return { 
        success: true, 
        data: result.results, 
        message: result.message 
      };
    } catch (err) {
      setError(err);
      return { success: false, error: err, data: null };
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Xóa sản phẩm khỏi Weaviate theo ID
   */
  const deleteProductFromWeaviate = async (productId) => {
    if (!productId) {
      return { success: false, error: new Error('Product ID is required'), data: null };
    }

    setIsLoading(true);
    setError(null);

    try {
      const result = await callApi('/api/weaviate/products/delete-by-id', {
        method: 'DELETE',
        body: JSON.stringify({ product_id: productId }),
      });

      return { success: true, data: result };
    } catch (err) {
      setError(err);
      return { success: false, error: err, data: null };
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Xóa sản phẩm khỏi Weaviate theo URL hình ảnh
   */
  const deleteProductFromWeaviateByImage = async (imageUrl) => {
    if (!imageUrl) {
      return { success: false, error: new Error('Image URL is required'), data: null };
    }

    setIsLoading(true);
    setError(null);

    try {
      const result = await callApi('/api/weaviate/products/delete', {
        method: 'DELETE',
        body: JSON.stringify({ image_url: imageUrl }),
      });

      return { success: true, data: result };
    } catch (err) {
      setError(err);
      return { success: false, error: err, data: null };
    } finally {
      setIsLoading(false);
    }
  };

  return {
    // State
    isLoading,
    error,
    
    // Methods
    getProducts,
    getProductById,
    createProduct,
    updateProduct,
    deleteProduct,
    bulkDeleteProducts,
    deleteProductFromWeaviate,
    deleteProductFromWeaviateByImage,
  };
}
