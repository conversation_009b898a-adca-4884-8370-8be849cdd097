'use client';

import { useRef, useMemo, useState, useEffect, useCallback } from 'react';

import { createClient } from 'src/utils/supabase/client';

import storageService from './storage-service';
import { fetchData, createData, updateData, deleteData, upsertData } from './supabase-utils';

// Tên bảng trong Supabase
const TABLE_NAME = 'product_categories';

/**
 * Lấy danh sách danh mục với các tùy chọn lọc
 * @param {Object} options - <PERSON><PERSON><PERSON> tùy chọn truy vấn
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getCategories(options = {}) {
  return fetchData(TABLE_NAME, options);
}

/**
 * Lấy chi tiết một danh mục theo ID
 * @param {string} categoryId - ID của danh mục
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getCategoryById(categoryId) {
  if (!categoryId) return { success: false, error: 'Category ID is required', data: null };

  return fetchData(TABLE_NAME, {
    filters: { id: categoryId },
    single: true,
  });
}

/**
 * Xử lý tải lên hình ảnh danh mục
 * @param {File} imageFile - File hình ảnh cần tải lên
 * @param {string} tenantId - ID của tenant
 * @param {string|null} oldImageUrl - URL hình ảnh cũ (nếu có)
 * @returns {Promise<Object>} - Kết quả từ API với URL hình ảnh
 */
export async function uploadCategoryImage(imageFile, tenantId, oldImageUrl = null) {
  try {
    // Nếu không có file mới, trả về URL cũ hoặc null
    if (!imageFile) return { success: true, imageUrl: oldImageUrl };

    // Xóa hình ảnh cũ nếu có
    if (oldImageUrl) {
      try {
        console.log('Attempting to delete old image:', oldImageUrl);
        // Gọi trực tiếp hàm xóa hình ảnh để đảm bảo xử lý đường dẫn chính xác
        const deleteResult = await deleteCategoryImage(oldImageUrl);

        if (deleteResult.success) {
          console.log('Old image deleted successfully');
        } else {
          console.warn('Failed to delete old image, but will continue:', deleteResult.error);
        }
      } catch (deleteError) {
        // Bỏ qua lỗi khi xóa hình cũ, vẫn tiếp tục tải lên hình mới
        console.warn('Error deleting old image, but will continue:', deleteError);
      }
    }

    // Tạo tên file duy nhất
    const fileName = storageService.generateUniqueFileName(imageFile.name);

    // Đảm bảo có tenantId
    if (!tenantId) {
      // Lấy tenant_id của người dùng hiện tại nếu không được cung cấp
      const supabase = createClient();
      const { data: { user } } = await supabase.auth.getUser();

      if (user) {
        // Lấy tenant_id từ bảng users
        const { data } = await supabase
          .from('users')
          .select('tenant_id')
          .eq('id', user.id)
          .single();

        tenantId = data?.tenant_id;
      }
    }

    // Tạo đường dẫn lưu trữ - sử dụng tenantId
    const filePath = storageService.buildFilePath('categories', tenantId, fileName);

    console.log('Uploading new image to path:', filePath);
    // Tải lên hình ảnh mới
    const uploadResult = await storageService.uploadFile('public', filePath, imageFile, {
      upsert: true,
      cacheControl: '3600',
    });

    if (uploadResult.success) {
      console.log('Image uploaded successfully, public URL:', uploadResult.publicUrl);
      return { success: true, imageUrl: uploadResult.publicUrl };
    }

    console.error('Failed to upload image:', uploadResult.error);
    return {
      success: false,
      error: uploadResult.error,
      imageUrl: null,
    };
  } catch (error) {
    console.error('Error uploading category image:', error);
    return { success: false, error, imageUrl: null };
  }
}

/**
 * Tạo danh mục mới
 * @param {Object} categoryData - Dữ liệu danh mục
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function createCategory(categoryData) {
  return createData(TABLE_NAME, categoryData);
}

/**
 * Cập nhật danh mục
 * @param {string} categoryId - ID của danh mục
 * @param {Object} categoryData - Dữ liệu cập nhật
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function updateCategory(categoryId, categoryData) {
  if (!categoryId) return { success: false, error: 'Category ID is required', data: null };

  return updateData(TABLE_NAME, categoryData, { id: categoryId });
}

/**
 * Xóa danh mục
 * @param {string} categoryId - ID của danh mục
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function deleteCategory(categoryId) {
  if (!categoryId) return { success: false, error: 'Category ID is required', data: null };

  return deleteData(TABLE_NAME, { id: categoryId });
}

/**
 * Tạo hoặc cập nhật danh mục
 * @param {Object} categoryData - Dữ liệu danh mục
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function upsertCategory(categoryData) {
  return upsertData(TABLE_NAME, categoryData);
}

/**
 * Hook để lấy danh sách danh mục
 * @param {Object} options - Các tùy chọn truy vấn
 * @returns {Object} - Kết quả và các hàm điều khiển
 */
export function useCategories(options = {}) {
  const [data, setData] = useState(null);
  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isValidating, setIsValidating] = useState(false);

  // Use useRef to avoid re-renders when only storing values
  const optionsRef = useRef(options);

  // Update ref when options change
  useEffect(() => {
    optionsRef.current = options;
  }, [options]);

  const fetchCategories = useCallback(async () => {
    setIsValidating(true);
    try {
      // Use ref value to always get the latest options
      const currentOptions = optionsRef.current;

      const result = await getCategories(currentOptions);
      setData(result);
      setError(result.error || null);
      return result;
    } catch (err) {
      console.error('Error fetching categories:', err);
      setError(err);
      return { success: false, error: err, data: null };
    } finally {
      setIsValidating(false);
      setIsLoading(false);
    }
  }, []); // No dependency on options because we're using ref

  // Hàm để tải lại dữ liệu
  const mutate = useCallback(async () => {
    console.log('Manually refreshing categories data...');
    return fetchCategories();
  }, [fetchCategories]);

  // Tải dữ liệu khi component mount
  useEffect(() => {
    console.log('Initial categories data load');
    setIsLoading(true);
    fetchCategories();
  }, [fetchCategories]);

  // Track options changes and reload data when needed
  const prevOptionsRef = useRef('');

  useEffect(() => {
    const optionsString = JSON.stringify(options);
    // Only call API when options actually change (deep comparison)
    if (prevOptionsRef.current !== optionsString) {
      console.log('Categories options changed, reloading data');
      prevOptionsRef.current = optionsString;
      setIsLoading(true);
      fetchCategories();
    }
  }, [options, fetchCategories]);

  const memoizedValue = useMemo(() => {
    const result = {
      categories: data?.data || [],
      totalCount: data?.count || 0,
      isLoading,
      isError: !!error,
      error,
      isValidating,
      mutate,
      isEmpty: !isLoading && !isValidating && (!data?.data || data.data.length === 0),
    };

    return result;
  }, [data, error, isLoading, isValidating, mutate]);

  return memoizedValue;
}

/**
 * Hook để lấy chi tiết danh mục
 * @param {string} categoryId - ID của danh mục
 * @returns {Object} - Kết quả và các hàm điều khiển
 */
export function useCategory(categoryId) {
  const [data, setData] = useState(null);
  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isValidating, setIsValidating] = useState(false);

  const fetchCategory = useCallback(async () => {
    if (!categoryId) {
      setData(null);
      setError(null);
      setIsLoading(false);
      setIsValidating(false);
      return null;
    }

    setIsValidating(true);
    try {
      const result = await getCategoryById(categoryId);
      setData(result);
      setError(result.error || null);
      return result;
    } catch (err) {
      setError(err);
      return { success: false, error: err, data: null };
    } finally {
      setIsValidating(false);
      setIsLoading(false);
    }
  }, [categoryId]);

  // Hàm để tải lại dữ liệu
  const mutate = useCallback(async () => fetchCategory(), [fetchCategory]);

  // Tải dữ liệu khi component mount hoặc categoryId thay đổi
  useEffect(() => {
    if (categoryId) {
      setIsLoading(true);
      fetchCategory();
    }
  }, [fetchCategory, categoryId]);

  const memoizedValue = useMemo(
    () => ({
      category: data?.data || null,
      isLoading,
      isError: !!error,
      error,
      isValidating,
      mutate,
    }),
    [data, error, isLoading, isValidating, mutate]
  );

  return memoizedValue;
}

/**
 * Xóa hình ảnh danh mục
 * @param {string} imageUrl - URL hình ảnh cần xóa
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function deleteCategoryImage(imageUrl) {
  try {
    if (!imageUrl) return { success: true };

    console.log('Deleting image with URL:', imageUrl);

    // Sử dụng hàm mới để trích xuất đường dẫn từ URL
    const imagePath = storageService.extractPathFromUrl(imageUrl);
    if (!imagePath) {
      console.warn('Could not extract path from image URL:', imageUrl);
      return { success: false, error: 'Invalid image URL format' };
    }

    // Kiểm tra xem đường dẫn có chứa 'public/' ở đầu không
    const cleanPath = imagePath.startsWith('public/')
      ? imagePath.substring(7) // Cắt bỏ 'public/' ở đầu nếu có
      : imagePath;

    console.log('Clean path for deletion:', cleanPath);

    // Kiểm tra xem file có tồn tại trước khi xóa
    const fileExists = await storageService.checkFileExists('public', cleanPath);
    if (!fileExists.exists) {
      console.warn('Image file not found in storage:', cleanPath);
      // Vẫn trả về success vì mục tiêu là đảm bảo hình ảnh không còn tồn tại
      return { success: true, data: null, message: 'File not found in storage' };
    }

    // Xóa hình ảnh từ storage
    const deleteResult = await storageService.deleteFiles('public', cleanPath);
    if (deleteResult.success) {
      console.log('Image deleted successfully:', cleanPath);
    } else {
      console.error('Failed to delete image:', deleteResult.error);
    }
    return deleteResult;
  } catch (error) {
    console.error('Error deleting category image:', error);
    return { success: false, error };
  }
}

/**
 * Hook để tạo, cập nhật, xóa danh mục
 * @returns {Object} - Các hàm mutation
 */
export function useCategoryMutations() {
  const [loadingStates, setLoadingStates] = useState({
    creating: false,
    updating: false,
    deleting: false,
    upserting: false,
    uploadingImage: false,
  });

  // Helper function to handle loading state and error handling
  const withLoadingState = async (stateKey, asyncFn) => {
    setLoadingStates((prev) => ({ ...prev, [stateKey]: true }));
    try {
      return await asyncFn();
    } finally {
      setLoadingStates((prev) => ({ ...prev, [stateKey]: false }));
    }
  };

  // Mutation functions using the helper
  const createCategoryMutation = (categoryData) =>
    withLoadingState('creating', () => createCategory(categoryData));

  const updateCategoryMutation = (id, data) =>
    withLoadingState('updating', () => updateCategory(id, data));

  const deleteCategoryMutation = (categoryId) =>
    withLoadingState('deleting', () => deleteCategory(categoryId));

  const upsertCategoryMutation = (categoryData) =>
    withLoadingState('upserting', () => upsertCategory(categoryData));

  // Hàm xử lý tải lên hình ảnh
  const uploadImageMutation = (imageFile, tenantId, oldImageUrl) =>
    withLoadingState('uploadingImage', () => uploadCategoryImage(imageFile, tenantId, oldImageUrl));

  // Hàm xóa hình ảnh
  const deleteImageMutation = (imageUrl) =>
    withLoadingState('uploadingImage', () => deleteCategoryImage(imageUrl));

  // Calculate if any mutation is in progress
  const isMutating = Object.values(loadingStates).some(Boolean);

  return {
    createCategory: createCategoryMutation,
    updateCategory: updateCategoryMutation,
    deleteCategory: deleteCategoryMutation,
    upsertCategory: upsertCategoryMutation,
    uploadCategoryImage: uploadImageMutation,
    deleteCategoryImage: deleteImageMutation,
    isCreating: loadingStates.creating,
    isUpdating: loadingStates.updating,
    isDeleting: loadingStates.deleting,
    isUpserting: loadingStates.upserting,
    isUploadingImage: loadingStates.uploadingImage,
    isMutating,
  };
}
