'use client';

import { useState } from 'react';

import { fetchData, createData, updateData, deleteData, upsertData } from './supabase-utils';

// Tên bảng trong Supabase
const TABLE_NAME = 'permissions';

/**
 * <PERSON><PERSON><PERSON> danh sách quyền với các tùy chọn lọc
 * @param {Object} options - <PERSON><PERSON><PERSON> tùy chọn truy vấn
 * @returns {Promise<Object>} - <PERSON><PERSON><PERSON> quả từ API
 */
export async function getPermissions(options = {}) {
  return fetchData(TABLE_NAME, options);
}

// Helper function để xử lý lỗi tham số đầu vào
const validateParam = (param, paramName) => {
  if (!param) {
    return { success: false, error: `${paramName} is required`, data: null };
  }
  return null;
};

/**
 * Lấy chi tiết một quyền theo ID
 * @param {string} permissionId - ID của quyền
 * @returns {Promise<Object>} - <PERSON><PERSON><PERSON> qu<PERSON> từ API
 */
export async function getPermissionById(permissionId) {
  const validationError = validateParam(permissionId, 'Permission ID');
  if (validationError) return validationError;

  return fetchData(TABLE_NAME, {
    filters: { id: permissionId },
    single: true,
  });
}

/**
 * Lấy chi tiết một quyền theo mã
 * @param {string} permissionCode - Mã của quyền
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getPermissionByCode(permissionCode) {
  const validationError = validateParam(permissionCode, 'Permission code');
  if (validationError) return validationError;

  return fetchData(TABLE_NAME, {
    filters: { code: permissionCode },
    single: true,
  });
}

/**
 * Tạo quyền mới
 * @param {Object} permissionData - Dữ liệu quyền
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function createPermission(permissionData) {
  return createData(TABLE_NAME, permissionData);
}

/**
 * Cập nhật quyền
 * @param {string} permissionId - ID của quyền
 * @param {Object} permissionData - Dữ liệu cập nhật
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function updatePermission(permissionId, permissionData) {
  const validationError = validateParam(permissionId, 'Permission ID');
  if (validationError) return validationError;

  return updateData(TABLE_NAME, permissionData, { id: permissionId });
}

/**
 * Xóa quyền
 * @param {string} permissionId - ID của quyền
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function deletePermission(permissionId) {
  const validationError = validateParam(permissionId, 'Permission ID');
  if (validationError) return validationError;

  return deleteData(TABLE_NAME, { id: permissionId });
}

/**
 * Tạo hoặc cập nhật quyền
 * @param {Object} permissionData - Dữ liệu quyền
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function upsertPermission(permissionData) {
  return upsertData(TABLE_NAME, permissionData);
}

/**
 * Hook để tạo, cập nhật, xóa quyền
 * @returns {Object} - Các hàm mutation
 */
export function usePermissionMutations() {
  const [loadingStates, setLoadingStates] = useState({
    creating: false,
    updating: false,
    deleting: false,
    upserting: false,
  });

  // Helper function to handle loading state and error handling
  const withLoadingState = async (stateKey, asyncFn) => {
    setLoadingStates((prev) => ({ ...prev, [stateKey]: true }));
    try {
      return await asyncFn();
    } finally {
      setLoadingStates((prev) => ({ ...prev, [stateKey]: false }));
    }
  };

  // Mutation functions using the helper
  const createPermissionMutation = (permissionData) =>
    withLoadingState('creating', () => createPermission(permissionData));

  const updatePermissionMutation = (id, data) =>
    withLoadingState('updating', () => updatePermission(id, data));

  const deletePermissionMutation = (permissionId) =>
    withLoadingState('deleting', () => deletePermission(permissionId));

  const upsertPermissionMutation = (permissionData) =>
    withLoadingState('upserting', () => upsertPermission(permissionData));

  // Calculate if any mutation is in progress
  const isMutating = Object.values(loadingStates).some(Boolean);

  return {
    createPermission: createPermissionMutation,
    updatePermission: updatePermissionMutation,
    deletePermission: deletePermissionMutation,
    upsertPermission: upsertPermissionMutation,
    isCreating: loadingStates.creating,
    isUpdating: loadingStates.updating,
    isDeleting: loadingStates.deleting,
    isUpserting: loadingStates.upserting,
    isMutating,
  };
}
