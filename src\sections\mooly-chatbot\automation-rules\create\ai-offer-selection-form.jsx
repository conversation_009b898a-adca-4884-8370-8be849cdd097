'use client';

import { useState } from 'react';
import { Controller } from 'react-hook-form';

import {
  Box,
  Card,
  Chip,
  Stack,
  Button,
  Checkbox,
  TextField,
  Typography,
  FormControl
} from '@mui/material';

import { Iconify } from 'src/components/iconify';

import { QuickOfferCreateDialog } from './quick-offer-create-dialog';

// ----------------------------------------------------------------------

export function AIOfferSelectionForm({ control, errors, offers, onOffersUpdate }) {
  const [quickOfferDialogOpen, setQuickOfferDialogOpen] = useState(false);

  const handleOfferCreated = (newOffer) => {
    // Callback to parent to refresh offers list
    if (onOffersUpdate) {
      onOffersUpdate();
    }
  };

  return (
    <Stack spacing={3}>
      {/* Customer Intent */}
      <Card sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          Ý Định <PERSON>hách Hàng
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          Mô tả ý định hoặc hành vi khách hàng để AI có thể phân tích và chọn offer phù hợp
        </Typography>

        <Controller
          name="customerIntent"
          control={control}
          rules={{ required: 'Ý định khách hàng là bắt buộc' }}
          render={({ field }) => (
            <TextField
              {...field}
              label="Ý Định Khách Hàng"
              placeholder="VD: Khách hàng quan tâm đến sản phẩm điện tử, đã xem nhiều lần nhưng chưa mua..."
              multiline
              rows={4}
              error={!!errors.customerIntent}
              helperText={errors.customerIntent?.message}
              fullWidth
            />
          )}
        />
      </Card>

      {/* Target Offers */}
      <Card sx={{ p: 3 }}>
        <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 2 }}>
          <Box>
            <Typography variant="h6" gutterBottom>
              Danh Sách Offers
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Chọn các offers mà AI có thể sử dụng để bám đuổi khách hàng
            </Typography>
          </Box>
          <Button
            variant="outlined"
            startIcon={<Iconify icon="solar:add-circle-bold" />}
            onClick={() => setQuickOfferDialogOpen(true)}
            size="small"
          >
            Tạo Offer Nhanh
          </Button>
        </Stack>

        {offers.length === 0 ? (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <Iconify icon="solar:gift-bold" width={48} sx={{ mb: 2, color: 'text.disabled' }} />
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Chưa có offers nào. Hãy tạo offers trước khi cấu hình automation rule.
            </Typography>
            <Button
              variant="contained"
              startIcon={<Iconify icon="solar:add-circle-bold" />}
              onClick={() => setQuickOfferDialogOpen(true)}
              size="small"
            >
              Tạo Offer Đầu Tiên
            </Button>
          </Box>
        ) : (
          <Controller
            name="targetOffers"
            control={control}
            rules={{
              required: 'Vui lòng chọn ít nhất một offer',
              validate: (value) => value?.length > 0 || 'Vui lòng chọn ít nhất một offer'
            }}
            render={({ field: { value = [], onChange } }) => (
              <FormControl error={!!errors.targetOffers} fullWidth>
                <Stack spacing={2}>
                  {offers.map((offer) => (
                    <Box
                      key={offer.id}
                      sx={{
                        p: 2,
                        border: 1,
                        borderColor: value.includes(offer.id) ? 'primary.main' : 'divider',
                        borderRadius: 1,
                        bgcolor: value.includes(offer.id) ? 'primary.lighter' : 'transparent',
                        cursor: 'pointer',
                        transition: 'all 0.2s'
                      }}
                      onClick={() => {
                        const newValue = value.includes(offer.id)
                          ? value.filter(id => id !== offer.id)
                          : [...value, offer.id];
                        onChange(newValue);
                      }}
                    >
                      <Stack direction="row" alignItems="center" spacing={2}>
                        <Checkbox
                          checked={value.includes(offer.id)}
                          onChange={(e) => {
                            const newValue = e.target.checked
                              ? [...value, offer.id]
                              : value.filter(id => id !== offer.id);
                            onChange(newValue);
                          }}
                        />

                        <Box sx={{ flex: 1 }}>
                          <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 1 }}>
                            <Typography variant="subtitle2">
                              {offer.name}
                            </Typography>
                            <Chip
                              label={offer.code}
                              size="small"
                              color="primary"
                              variant="soft"
                            />
                          </Stack>

                          <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                            {offer.description || 'Không có mô tả'}
                          </Typography>

                          <Stack direction="row" spacing={2}>
                            <Chip
                              label={`${offer.value_type === 'percentage' ? `${offer.value}%` : `${Number(offer.value).toLocaleString()}đ`} giảm`}
                              size="small"
                              color="success"
                              variant="soft"
                            />
                            {offer.min_purchase_amount && (
                              <Chip
                                label={`Tối thiểu ${Number(offer.min_purchase_amount).toLocaleString()}đ`}
                                size="small"
                                variant="outlined"
                              />
                            )}
                          </Stack>
                        </Box>
                      </Stack>
                    </Box>
                  ))}
                </Stack>

                {errors.targetOffers && (
                  <Typography variant="caption" color="error" sx={{ mt: 1 }}>
                    {errors.targetOffers.message}
                  </Typography>
                )}
              </FormControl>
            )}
          />
        )}
      </Card>

      {/* Customer Filters */}
      <Card sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          Bộ Lọc Khách Hàng (Tùy chọn)
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          Cấu hình điều kiện để áp dụng rule cho nhóm khách hàng cụ thể
        </Typography>

        <Stack spacing={2}>
          <Controller
            name="customerFilters.minOrderValue"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                label="Giá trị đơn hàng tối thiểu"
                type="number"
                placeholder="0"
                InputProps={{
                  endAdornment: 'đ'
                }}
                fullWidth
              />
            )}
          />

          <Controller
            name="customerFilters.orderCount"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                label="Số đơn hàng tối thiểu"
                type="number"
                placeholder="0"
                fullWidth
              />
            )}
          />

          <Controller
            name="customerFilters.lastOrderDays"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                label="Số ngày từ đơn hàng cuối"
                type="number"
                placeholder="30"
                helperText="Khách hàng có đơn hàng cuối cách đây X ngày"
                fullWidth
              />
            )}
          />
        </Stack>
      </Card>

      {/* Quick Offer Create Dialog */}
      <QuickOfferCreateDialog
        open={quickOfferDialogOpen}
        onClose={() => setQuickOfferDialogOpen(false)}
        onSuccess={handleOfferCreated}
      />
    </Stack>
  );
}
