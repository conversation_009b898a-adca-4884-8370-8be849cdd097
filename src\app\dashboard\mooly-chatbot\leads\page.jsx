'use client';

import { Container } from '@mui/material';

import { useSettingsContext } from 'src/components/settings';
import { EmptyContent } from 'src/components/empty-content';

import LeadsListView from 'src/sections/mooly-chatbot/leads/leads-list-view';

// ----------------------------------------------------------------------

export default function LeadsPage() {
  const settings = useSettingsContext();

  return (
    <Container maxWidth={settings.themeStretch ? false : 'xl'}>
      <LeadsListView />
      {/* <EmptyContent
        filled
        title="Tính năng đang phát triển"
        description="Chúng tôi đang nỗ lực hoàn thiện tính năng này. Vui lòng quay lại sau!"
        imgUrl="/assets/icons/empty/ic_content.svg"
        sx={{ py: 10, height: '60vh', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
      /> */}
    </Container>
  );
} 