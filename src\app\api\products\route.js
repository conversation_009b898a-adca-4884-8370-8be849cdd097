import { NextResponse } from 'next/server';

import { withTenantAuth, validateTenantId } from 'src/utils/server-auth';

/**
 * API route để lấy danh sách sản phẩm
 * @param {Request} request - <PERSON><PERSON><PERSON> cầu HTTP
 * @returns {Promise<NextResponse>} - <PERSON><PERSON>n hồi HTTP
 */
export const GET = withTenantAuth(async (request, { tenantId, userId }) => {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '10', 10);
    const search = searchParams.get('search') || '';
    const status = searchParams.get('status'); // active, inactive, all

    // Tạo query với RLS protection
    let query = request.supabase
      .from('products')
      .select('*', { count: 'exact' })
      .eq('tenant_id', tenantId);

    // Thêm filter theo search
    if (search) {
      query = query.or(`name.ilike.%${search}%,sku.ilike.%${search}%`);
    }

    // Thêm filter theo status
    if (status && status !== 'all') {
      const isActive = status === 'active';
      query = query.eq('is_active', isActive);
    }

    // Thêm pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    query = query.range(from, to);

    // Sắp xếp theo created_at mới nhất
    query = query.order('created_at', { ascending: false });

    const { data, error, count } = await query;

    if (error) {
      console.error('Error fetching products:', error);
      return NextResponse.json(
        { error: 'Failed to fetch products', details: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data,
      pagination: {
        page,
        limit,
        total: count,
        totalPages: Math.ceil(count / limit),
      },
    });
  } catch (error) {
    console.error('Error in products GET route:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
});

/**
 * API route để tạo sản phẩm mới
 * @param {Request} request - Yêu cầu HTTP
 * @returns {Promise<NextResponse>} - Phản hồi HTTP
 */
export const POST = withTenantAuth(async (request, { tenantId, userId }) => {
  try {
    // Validate request body và kiểm tra tenant_id
    const validation = await validateTenantId(request, tenantId);
    if (!validation.valid) {
      return NextResponse.json({ error: validation.error }, { status: validation.status });
    }

    const productData = validation.body;

    // Thêm tenant_id và user_id
    productData.tenant_id = tenantId;
    productData.created_by = userId;

    // Kiểm tra SKU unique trong tenant
    if (productData.sku) {
      const { data: existingProduct } = await request.supabase
        .from('products')
        .select('id')
        .eq('tenant_id', tenantId)
        .eq('sku', productData.sku)
        .single();

      if (existingProduct) {
        return NextResponse.json(
          { error: 'SKU đã tồn tại trong hệ thống' },
          { status: 400 }
        );
      }
    }

    // Tạo sản phẩm mới
    const { data, error } = await request.supabase
      .from('products')
      .insert(productData)
      .select()
      .single();

    if (error) {
      console.error('Error creating product:', error);
      return NextResponse.json(
        { error: 'Failed to create product', details: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data,
      message: 'Tạo sản phẩm thành công',
    });
  } catch (error) {
    console.error('Error in products POST route:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
});
