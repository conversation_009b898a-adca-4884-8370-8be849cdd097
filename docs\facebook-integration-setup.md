# Facebook Integration Setup Guide

## Environment Variables Required

Add these environment variables to your `.env.local` file:

```bash
# Facebook App Configuration
NEXT_PUBLIC_FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret
FACEBOOK_WEBHOOK_VERIFY_TOKEN=your_custom_verify_token

# Your application URL (for webhooks)
NEXTAUTH_URL=https://your-domain.com
```

## Facebook App Setup Steps

### 1. Create Facebook App

1. Go to [Facebook Developers](https://developers.facebook.com/apps/)
2. Click "Create App"
3. Choose "Business" type
4. Fill in app details:
   - App Name: "Your App Name"
   - Contact Email: your email
   - Business Account: select your business account

### 2. Add Required Products

Add these products to your Facebook App:

#### Facebook Login
- Go to "Facebook Login" > "Settings"
- Add Valid OAuth Redirect URIs:
  ```
  https://your-domain.com/api/facebook-integration/popup-callback
  ```

#### Webhooks
- Go to "Webhooks" > "Settings"
- Add webhook URL:
  ```
  https://your-domain.com/api/facebook-webhooks
  ```
- Set Verify Token: `your_custom_verify_token` (same as in .env)

#### Messenger
- Add "Messenger" product for private messaging features

### 3. Configure Permissions

Request these permissions for your app:

**Basic Permissions:**
- `public_profile`
- `email`

**Pages Permissions:**
- `pages_show_list` - View list of pages
- `pages_read_engagement` - Read comments and reactions
- `pages_manage_posts` - Manage posts
- `pages_messaging` - Send messages
- `pages_manage_metadata` - Manage page metadata

**Instagram Permissions:**
- `instagram_basic` - Basic Instagram access
- `instagram_manage_comments` - Manage Instagram comments
- `instagram_manage_messages` - Manage Instagram messages

### 4. Webhook Subscriptions

For each connected page, subscribe to these webhook fields:

**Facebook Page Webhooks:**
- `feed` - New posts
- `comments` - Comments on posts
- `messaging` - Private messages

**Instagram Webhooks:**
- `comments` - Comments on Instagram posts
- `mentions` - Mentions in Instagram stories/posts

## Testing Webhooks Locally

### Using ngrok for local development:

1. Install ngrok: `npm install -g ngrok`
2. Start your Next.js app: `npm run dev`
3. In another terminal: `ngrok http 3000`
4. Use the ngrok URL in Facebook App settings:
   ```
   https://abc123.ngrok.io/api/facebook-webhooks
   ```

### Webhook Verification

Facebook will send a GET request to verify your webhook:
- Mode: `subscribe`
- Verify Token: your `FACEBOOK_WEBHOOK_VERIFY_TOKEN`
- Challenge: random string to echo back

## Security Best Practices

1. **Webhook Signature Verification**: Always verify webhook signatures using your app secret
2. **Token Storage**: Store access tokens securely in database with encryption
3. **Rate Limiting**: Implement rate limiting for API calls
4. **Error Handling**: Comprehensive error handling and logging
5. **Tenant Isolation**: Use RLS policies for multi-tenant data security

## API Endpoints

### Webhook Handler
- `GET /api/facebook-webhooks` - Webhook verification
- `POST /api/facebook-webhooks` - Receive webhook events

### Configuration
- `GET /api/facebook-integration/auto-reply-config?pageId=xxx` - Get config
- `POST /api/facebook-integration/auto-reply-config` - Save config
- `DELETE /api/facebook-integration/auto-reply-config?pageId=xxx` - Delete config

### Webhook Management
- `POST /api/facebook-integration/subscribe-webhooks` - Subscribe to webhooks
- `GET /api/facebook-integration/subscribe-webhooks?pageId=xxx` - Check subscriptions

## Testing

### Manual Testing

1. **Test Webhook Verification**:
   ```bash
   curl "https://your-domain.com/api/facebook-webhooks?hub.mode=subscribe&hub.verify_token=your_verify_token&hub.challenge=test"
   ```

2. **Test Auto Reply**:
   Use the built-in test interface in the Facebook Setup Wizard or call the API directly:
   ```bash
   curl -X POST https://your-domain.com/api/facebook-integration/test-auto-reply \
     -H "Content-Type: application/json" \
     -d '{"pageId":"your_page_id","testMessage":"Hello","eventType":"facebook_comment"}'
   ```

### Automated Testing

Run the comprehensive test suite:
```bash
node scripts/test-facebook-integration.js
```

Set environment variables for testing:
```bash
export TEST_PAGE_ID=your_test_page_id
export FACEBOOK_WEBHOOK_VERIFY_TOKEN=your_verify_token
```

## Troubleshooting

### Common Issues

1. **Webhook Verification Failed**
   - Check `FACEBOOK_WEBHOOK_VERIFY_TOKEN` matches in app settings
   - Ensure webhook URL is accessible and returns correct challenge
   - Verify SSL certificate is valid

2. **Invalid Signature**
   - Verify `FACEBOOK_APP_SECRET` is correct
   - Check webhook signature verification logic
   - Ensure request body is read correctly

3. **Permission Denied**
   - Ensure all required permissions are granted
   - Check if user is admin of the Facebook page
   - Verify app is approved for production use

4. **Token Expired**
   - Implement token refresh logic
   - Handle token expiration gracefully
   - Monitor token expiration dates

5. **Auto Reply Not Working**
   - Check if auto reply is enabled in configuration
   - Verify AI service (OpenAI) API key is valid
   - Check exclude keywords configuration
   - Review activity logs for errors

6. **Rate Limiting**
   - Implement exponential backoff
   - Monitor API usage in Facebook Developer Console
   - Use batch requests when possible

### Debug Mode

Enable debug logging by setting:
```bash
DEBUG=facebook:*
NODE_ENV=development
```

### Monitoring

Monitor your integration using:
- Facebook Developer Console
- Built-in analytics dashboard
- Activity logs in database
- Server logs and error tracking

## Rate Limits

Facebook API has rate limits:
- **Page-level**: 200 calls per hour per page
- **App-level**: 200 calls per hour per user per app
- **Platform-level**: Various limits based on app usage

Implement proper rate limiting and retry logic in your application.

## Production Deployment

### Pre-deployment Checklist

- [ ] All environment variables configured
- [ ] Facebook App approved for production
- [ ] Webhook URLs use HTTPS
- [ ] SSL certificates valid
- [ ] Database migrations applied
- [ ] Rate limiting implemented
- [ ] Error monitoring setup
- [ ] Backup and recovery plan

### Monitoring and Maintenance

1. **Monitor webhook delivery**
2. **Track auto reply success rates**
3. **Monitor API rate limits**
4. **Regular token refresh**
5. **Database cleanup for old logs**
6. **Performance optimization**

## Support

For issues and questions:
1. Check this documentation
2. Review Facebook Developer Documentation
3. Check GitHub issues
4. Contact support team
