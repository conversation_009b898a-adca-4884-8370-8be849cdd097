'use client';

import PropTypes from 'prop-types';
import React, { useState } from 'react';
import { useFieldArray } from 'react-hook-form';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Avatar from '@mui/material/Avatar';
import Tooltip from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';

import { fCurrency } from 'src/utils/format-number';

import { Iconify } from 'src/components/iconify';
import { Field, RHFTextField } from 'src/components/hook-form';

import { ProductSelectionDialog } from './ProductSelectionDialog';
import {
  getStockInfo,
  getProductImage,
  getProductTypeInfo,
  formatVariantAttributes
} from './orderFieldConfig';

/**
 * Component hiển thị danh sách sản phẩm trong đơn hàng
 */
export function OrderItemsList({
  control,
  watch,
  isLoading,
  handleQuantityChange,
  handleUnitPriceChange
}) {
  const [dialogOpen, setDialogOpen] = useState(false);

  // Debug log để kiểm tra props
  React.useEffect(() => {
    console.log('OrderItemsList props:', {
      handleQuantityChange: typeof handleQuantityChange,
      handleUnitPriceChange: typeof handleUnitPriceChange,
      hasHandleQuantityChange: !!handleQuantityChange,
      hasHandleUnitPriceChange: !!handleUnitPriceChange
    });
  }, [handleQuantityChange, handleUnitPriceChange]);

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'orderItems',
  });

  // Kiểm tra an toàn cho watch function
  const watchedOrderItems = React.useMemo(() => {
    if (!watch || typeof watch !== 'function') {
      return [];
    }
    try {
      return watch('orderItems') || [];
    } catch (error) {
      console.warn('Error watching orderItems:', error);
      return [];
    }
  }, [watch]);

  // Xử lý thêm sản phẩm từ dialog
  const handleProductSelect = (orderItem) => {
    // orderItem đã được chuẩn hóa từ createOrderItem trong ProductSelectionDialog
    append(orderItem);
    setDialogOpen(false);
  };

  const handleRemoveItem = (index) => {
    remove(index);
  };

  // Kiểm tra an toàn cho các handler functions
  const safeHandleQuantityChange = React.useCallback((index, quantity) => {
    if (handleQuantityChange && typeof handleQuantityChange === 'function') {
      handleQuantityChange(index, quantity);
    } else {
      console.warn('handleQuantityChange is not a function');
    }
  }, [handleQuantityChange]);

  const safeHandleUnitPriceChange = React.useCallback((index, unitPrice) => {
    if (handleUnitPriceChange && typeof handleUnitPriceChange === 'function') {
      handleUnitPriceChange(index, unitPrice);
    } else {
      console.warn('handleUnitPriceChange is not a function');
    }
  }, [handleUnitPriceChange]);

  const isFormLoading = isLoading;

  return (
    <>
      <Card sx={{ p: 3 }}>
        <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 3 }}>
          <Typography variant="h6">Sản phẩm</Typography>
          <Button
            size="small"
            color="primary"
            startIcon={<Iconify icon="mingcute:add-line" />}
            onClick={() => setDialogOpen(true)}
            disabled={isFormLoading}
          >
            Thêm sản phẩm
          </Button>
        </Stack>

        <Stack spacing={3}>
          {fields.map((item, index) => {
            const currentItem = watchedOrderItems[index] || {};

            // Debug log để kiểm tra dữ liệu (có thể xóa sau khi debug xong)
            console.log('OrderItem data:', { index, currentItem, item });

            // Merge dữ liệu từ currentItem và item để đảm bảo có đầy đủ thông tin
            const mergedItem = {
              ...item,
              ...currentItem,
              // Đảm bảo các field quan trọng không bị undefined
              name: currentItem.name || item.name || '',
              sku: currentItem.sku || item.sku || '',
              unitPrice: currentItem.unitPrice || item.unitPrice || 0,
              quantity: currentItem.quantity || item.quantity || 1,
              totalPrice: currentItem.totalPrice || item.totalPrice || 0,
              productId: currentItem.productId || item.productId || '',
              variantId: currentItem.variantId || item.variantId || '',
              imageUrl: currentItem.imageUrl || item.imageUrl || null,
              variantInfo: currentItem.variantInfo || item.variantInfo || null
            };

            // Sử dụng utility functions để lấy thông tin
            const productTypeInfo = getProductTypeInfo(mergedItem);
            const productImage = getProductImage(mergedItem);
            const stockInfo = getStockInfo(mergedItem);
            const variantAttributes = formatVariantAttributes(mergedItem.variantInfo || mergedItem.variantAttributes);

            // Kiểm tra xem có dữ liệu sản phẩm không
            const hasProductData = mergedItem.name || mergedItem.productId;

            return (
              <Card key={item.id} variant="outlined">
                <Box sx={{ p: 3 }}>
                  {/* Header với nút xóa */}
                  <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 2 }}>
                    <Typography variant="subtitle2">
                      Sản phẩm #{index + 1}
                    </Typography>
                    <Tooltip title="Xóa sản phẩm">
                      <Button
                        size="small"
                        color="error"
                        onClick={() => handleRemoveItem(index)}
                        disabled={isFormLoading}
                        sx={{ minWidth: 'auto', p: 0.5 }}
                      >
                        <Iconify icon="mingcute:delete-2-line" width={16} />
                      </Button>
                    </Tooltip>
                  </Stack>

                  {/* Hiển thị thông tin sản phẩm đã chọn */}
                  {hasProductData && (
                    <Card variant="outlined" sx={{ p: 2, bgcolor: 'background.neutral', mb: 2 }}>
                      <Stack direction="row" alignItems="flex-start" spacing={2}>
                        <Avatar
                          src={productImage}
                          sx={{
                            width: 56,
                            height: 56,
                            borderRadius: 1,
                            bgcolor: 'grey.100'
                          }}
                          variant="rounded"
                        >
                          {mergedItem.name?.charAt(0)}
                        </Avatar>
                        <Box sx={{ flex: 1, minWidth: 0 }}>
                          <Typography variant="body1" sx={{
                            fontWeight: 'bold',
                            mb: 0.5,
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap'
                          }}>
                            {mergedItem.name || 'Sản phẩm chưa có tên'}
                          </Typography>

                          {/* Thông tin chi tiết sản phẩm */}
                          <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 1, flexWrap: 'wrap', gap: 0.5 }}>
                            {mergedItem.sku && (
                              <Typography variant="caption" sx={{
                                color: 'text.secondary',
                                bgcolor: 'background.paper',
                                px: 1,
                                py: 0.25,
                                borderRadius: 0.5,
                                fontFamily: 'monospace'
                              }}>
                                SKU: {mergedItem.sku}
                              </Typography>
                            )}

                            <Typography variant="caption" sx={{
                              color: 'success.main',
                              bgcolor: 'success.lighter',
                              px: 1,
                              py: 0.25,
                              borderRadius: 0.5,
                              fontWeight: 'bold'
                            }}>
                              {fCurrency(mergedItem.unitPrice)}
                            </Typography>

                            <Typography variant="caption" sx={{
                              color: `${productTypeInfo.color}.main`,
                              bgcolor: `${productTypeInfo.color}.lighter`,
                              px: 1,
                              py: 0.25,
                              borderRadius: 0.5
                            }}>
                              {productTypeInfo.displayType}
                            </Typography>
                          </Stack>

                          {/* Hiển thị thông tin biến thể nếu có */}
                          {variantAttributes.length > 0 && (
                            <Stack direction="row" spacing={1} sx={{ flexWrap: 'wrap', gap: 0.5 }}>
                              {variantAttributes.map((attr) => (
                                <Typography key={attr.key} variant="caption" sx={{
                                  color: 'text.secondary',
                                  bgcolor: 'grey.100',
                                  px: 1,
                                  py: 0.25,
                                  borderRadius: 0.5,
                                  fontSize: '0.7rem'
                                }}>
                                  {attr.display}
                                </Typography>
                              ))}
                            </Stack>
                          )}

                          {/* Hiển thị thông tin tồn kho */}
                          {(stockInfo.stockQuantity > 0 || !stockInfo.isInStock) && (
                            <Stack direction="row" alignItems="center" spacing={1} sx={{ mt: 0.5 }}>
                              <Typography variant="caption" sx={{
                                color: `${stockInfo.color}.main`,
                                bgcolor: `${stockInfo.color}.lighter`,
                                px: 1,
                                py: 0.25,
                                borderRadius: 0.5,
                                fontSize: '0.7rem'
                              }}>
                                {stockInfo.displayText}
                              </Typography>
                            </Stack>
                          )}
                        </Box>
                      </Stack>
                    </Card>
                  )}

                  {/* Form chỉnh sửa thông tin đơn hàng */}
                  <Stack spacing={2.5}>
                    {/* Số lượng với controls tối ưu */}
                    <Box>
                      <Typography variant="caption" sx={{ color: 'text.secondary', mb: 1, display: 'block' }}>
                        Số lượng
                      </Typography>
                      <Stack direction="row" alignItems="center" spacing={1}>
                        <Button
                          size="small"
                          variant="outlined"
                          onClick={() => {
                            const newQuantity = Math.max(1, (mergedItem.quantity || 1) - 1);
                            safeHandleQuantityChange(index, newQuantity);
                          }}
                          disabled={isFormLoading || (mergedItem.quantity || 1) <= 1}
                          sx={{ minWidth: 32, width: 32, height: 32, p: 0 }}
                        >
                          -
                        </Button>
                        <RHFTextField
                          name={`orderItems.${index}.quantity`}
                          type="number"
                          disabled={isFormLoading}
                          size="small"
                          inputProps={{ min: 1, style: { textAlign: 'center' } }}
                          sx={{
                            width: 80,
                            '& .MuiInputBase-input': {
                              fontWeight: 'bold',
                              fontSize: '0.9rem'
                            }
                          }}
                          onChange={(e) => {
                            const quantity = parseInt(e.target.value, 10) || 1;
                            safeHandleQuantityChange(index, quantity);
                          }}
                        />
                        <Button
                          size="small"
                          variant="outlined"
                          onClick={() => {
                            const newQuantity = (mergedItem.quantity || 1) + 1;
                            safeHandleQuantityChange(index, newQuantity);
                          }}
                          disabled={isFormLoading}
                          sx={{ minWidth: 32, width: 32, height: 32, p: 0 }}
                        >
                          +
                        </Button>
                        <Typography variant="body2" sx={{ color: 'text.secondary', ml: 1 }}>
                          x {fCurrency(mergedItem.unitPrice)}
                        </Typography>
                      </Stack>
                    </Box>

                    {/* Đơn giá và thành tiền */}
                    <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>
                      <Box sx={{ flex: 1 }}>
                        <Typography variant="caption" sx={{ color: 'text.secondary', mb: 1, display: 'block' }}>
                          Đơn giá
                        </Typography>
                        <Field.CurrencyInput
                          name={`orderItems.${index}.unitPrice`}
                          disabled={isFormLoading}
                          size="small"
                          sx={{
                            '& .MuiInputBase-input': {
                              fontWeight: 'bold'
                            }
                          }}
                          onChange={(e) => {
                            const unitPrice = e.target.value;
                            safeHandleUnitPriceChange(index, unitPrice);
                          }}
                        />
                      </Box>

                      <Box sx={{ flex: 1 }}>
                        <Typography variant="caption" sx={{ color: 'text.secondary', mb: 1, display: 'block' }}>
                          Thành tiền
                        </Typography>
                        <Field.CurrencyInput
                          name={`orderItems.${index}.totalPrice`}
                          disabled
                          size="small"
                          sx={{
                            '& .MuiInputBase-input': {
                              fontWeight: 'bold',
                              color: 'success.main',
                              fontSize: '1rem'
                            },
                            '& .MuiOutlinedInput-root': {
                              bgcolor: 'success.lighter'
                            }
                          }}
                        />
                      </Box>
                    </Stack>

                    {/* Thông tin bổ sung */}
                    {mergedItem.sku && (
                      <Box>
                        <Typography variant="caption" sx={{ color: 'text.secondary', mb: 1, display: 'block' }}>
                          Mã sản phẩm (SKU)
                        </Typography>
                        <RHFTextField
                          name={`orderItems.${index}.sku`}
                          disabled
                          size="small"
                          sx={{
                            '& .MuiInputBase-input': {
                              fontFamily: 'monospace',
                              fontSize: '0.85rem'
                            }
                          }}
                        />
                      </Box>
                    )}
                  </Stack>
                </Box>
              </Card>
            );
          })}

          {fields.length === 0 && (
            <Box
              sx={{
                p: 6,
                textAlign: 'center',
                border: '2px dashed',
                borderColor: 'divider',
                borderRadius: 2,
                bgcolor: 'background.neutral'
              }}
            >
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                Chưa có sản phẩm nào. Nhấn &quot;Thêm sản phẩm&quot; để bắt đầu.
              </Typography>
            </Box>
          )}
        </Stack>
      </Card>

      {/* Dialog chọn sản phẩm */}
      <ProductSelectionDialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        onSelectProduct={handleProductSelect}
      />
    </>
  );
}

OrderItemsList.propTypes = {
  control: PropTypes.object.isRequired,
  watch: PropTypes.func.isRequired,
  isLoading: PropTypes.bool,
  handleQuantityChange: PropTypes.func,
  handleUnitPriceChange: PropTypes.func,
};
