'use client';

import { useState } from 'react';

import Button from '@mui/material/Button';
import { useTheme } from '@mui/material/styles';

import { Iconify } from 'src/components/iconify';

import { signInWithGoogle } from '../context/supabase';

// ----------------------------------------------------------------------

export function GoogleSignInButton({ onError, children, ...other }) {
  const theme = useTheme();
  const [loading, setLoading] = useState(false);

  const handleGoogleSignIn = async () => {
    try {
      setLoading(true);
      console.log('🔄 Starting Google OAuth flow...');

      await signInWithGoogle();

      // Redirect sẽ được xử lý bởi Supabase OAuth flow
      console.log('✅ Google OAuth initiated successfully');
    } catch (error) {
      console.error('💥 Google sign in error:', error);

      // Tạo error message thân thiện hơn
      let errorMessage = 'Đ<PERSON>ng nhập Google thất bại. Vui lòng thử lại.';

      if (error.message?.includes('popup')) {
        errorMessage = 'Popup bị chặn. Vui lòng cho phép popup và thử lại.';
      } else if (error.message?.includes('network')) {
        errorMessage = 'Lỗi kết nối mạng. Vui lòng kiểm tra internet và thử lại.';
      } else if (error.message?.includes('cancelled')) {
        errorMessage = 'Đăng nhập đã bị hủy.';
      }

      onError?.(new Error(errorMessage));
    } finally {
      setLoading(false);
    }
  };

  return (
    <Button
      fullWidth
      size="large"
      variant="outlined"
      loading={loading}
      loadingIndicator="Đang xử lý..."
      onClick={handleGoogleSignIn}
      startIcon={
        <Iconify
          icon="logos:google-icon"
          width={20}
          height={20}
        />
      }
      sx={{
        color: theme.palette.text.primary,
        borderColor: theme.palette.divider,
        '&:hover': {
          borderColor: theme.palette.primary.main,
          backgroundColor: theme.palette.action.hover,
        },
      }}
      {...other}
    >
      {children || 'Đăng nhập với Google'}
    </Button>
  );
}
