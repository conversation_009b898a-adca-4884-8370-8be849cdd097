'use client';

import { fetchData, createData, deleteData } from './supabase-utils';
import {
  PRODUCT_ATTRIBUTES_TABLE,
  PRODUCT_ATTRIBUTE_VALUES_TABLE,
  PRODUCT_VARIANT_ATTRIBUTES_TABLE,
} from './product-constants';

/**
 * <PERSON><PERSON><PERSON> danh sách thuộc tính của sản phẩm
 * @param {string} productId - ID sản phẩm
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getProductAttributes(productId) {
  if (!productId) {
    return { success: false, error: new Error('Thiếu ID sản phẩm'), data: [] };
  }

  // L<PERSON>y danh sách thuộc tính từ bảng product_variant_attributes
  // vì bảng product_attributes không có cột product_id
  return fetchData(PRODUCT_VARIANT_ATTRIBUTES_TABLE, {
    filters: { product_id: productId },
  });
}

/**
 * L<PERSON>y gi<PERSON> trị thuộc tính của sản phẩm
 * @param {string} attributeId - ID thuộc tính
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getAttributeValues(attributeId) {
  if (!attributeId) {
    return { success: false, error: new Error('Thiếu ID thuộc tính'), data: [] };
  }

  return fetchData(PRODUCT_ATTRIBUTE_VALUES_TABLE, {
    filters: { attribute_id: attributeId },
  });
}

/**
 * Lấy thuộc tính của biến thể sản phẩm
 * @param {string} variantId - ID biến thể
 * @param {string} productId - ID sản phẩm (bắt buộc vì bảng không có cột variant_id)
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getVariantAttributes(variantId, productId) {
  if (!variantId) {
    return { success: false, error: new Error('Thiếu ID biến thể'), data: [] };
  }

  if (!productId) {
    return { success: false, error: new Error('Thiếu ID sản phẩm'), data: [] };
  }

  // Bảng product_variant_attributes không có cột variant_id, chỉ có product_id và attribute_id
  // Nên chúng ta chỉ có thể lọc theo product_id
  return fetchData(PRODUCT_VARIANT_ATTRIBUTES_TABLE, {
    filters: { product_id: productId },
  });
}

/**
 * Lấy thuộc tính của nhiều biến thể sản phẩm
 * @param {string} productId - ID sản phẩm (bắt buộc vì bảng không có cột variant_id)
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getMultipleVariantAttributes(productId) {
  if (!productId) {
    return { success: false, error: new Error('Thiếu ID sản phẩm'), data: [] };
  }

  // Bảng product_variant_attributes không có cột variant_id, chỉ có product_id và attribute_id
  // Nên chúng ta chỉ có thể lọc theo product_id
  return fetchData(PRODUCT_VARIANT_ATTRIBUTES_TABLE, {
    filters: { product_id: productId },
  });
}

/**
 * Tạo thuộc tính sản phẩm
 * @param {Object} attributeData - Dữ liệu thuộc tính
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function createProductAttribute(attributeData) {
  return createData(PRODUCT_ATTRIBUTES_TABLE, attributeData);
}

/**
 * Tạo giá trị thuộc tính
 * @param {Object} valueData - Dữ liệu giá trị thuộc tính
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function createAttributeValue(valueData) {
  return createData(PRODUCT_ATTRIBUTE_VALUES_TABLE, valueData);
}

/**
 * Tạo thuộc tính biến thể
 * @param {Object} variantAttributeData - Dữ liệu thuộc tính biến thể
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function createVariantAttribute(variantAttributeData) {
  return createData(PRODUCT_VARIANT_ATTRIBUTES_TABLE, variantAttributeData);
}

/**
 * Xóa tất cả thuộc tính của sản phẩm
 * @param {string} productId - ID sản phẩm
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function deleteAllProductAttributes(productId) {
  if (!productId) {
    return { success: false, error: new Error('Thiếu ID sản phẩm') };
  }

  // Lấy danh sách thuộc tính
  const attributesResult = await getProductAttributes(productId);

  if (!attributesResult.success) {
    return attributesResult;
  }

  const attributes = attributesResult.data || [];
  const attributeIds = attributes.map((attr) => attr.id);

  // Xóa giá trị thuộc tính
  if (attributeIds.length > 0) {
    await deleteData(PRODUCT_ATTRIBUTE_VALUES_TABLE, {
      attribute_id: { in: attributeIds },
    });
  }

  // Không thể xóa thuộc tính bằng product_id vì không có cột này trong bảng
  // Thay vào đó, chỉ xóa các thuộc tính biến thể
  return { success: true, message: 'Đã xóa thuộc tính biến thể' };
}

/**
 * Xóa tất cả thuộc tính của biến thể
 * @param {string} productId - ID sản phẩm (bắt buộc vì bảng không có cột variant_id)
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function deleteVariantAttributes(productId) {
  if (!productId) {
    return { success: false, error: new Error('Thiếu ID sản phẩm') };
  }

  // Bảng product_variant_attributes không có cột variant_id, chỉ có product_id và attribute_id
  // Nên chúng ta chỉ có thể xóa theo product_id
  return deleteData(PRODUCT_VARIANT_ATTRIBUTES_TABLE, { product_id: productId });
}

/**
 * Tạo thuộc tính sản phẩm với các giá trị
 * @param {Object} attribute - Dữ liệu thuộc tính
 * @param {string} attribute.name - Tên thuộc tính
 * @param {string} attribute.productId - ID sản phẩm
 * @param {string} attribute.storeId - ID cửa hàng
 * @param {Array<string>} attribute.values - Danh sách giá trị
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function createAttributeWithValues(attribute) {
  try {
    const { name, productId, storeId, values = [] } = attribute;

    if (!name || !productId || !storeId) {
      return {
        success: false,
        error: new Error('Thiếu thông tin bắt buộc cho thuộc tính'),
      };
    }

    // Kiểm tra xem thuộc tính đã tồn tại chưa để tránh lỗi trùng khóa
    // Vì bảng product_attributes có ràng buộc duy nhất trên cặp (store_id, name)
    const existingAttributesResult = await fetchData(PRODUCT_ATTRIBUTES_TABLE, {
      filters: {
        store_id: storeId,
        name,
      },
    });

    let attributeResult;

    if (
      existingAttributesResult.success &&
      existingAttributesResult.data &&
      existingAttributesResult.data.length > 0
    ) {
      // Nếu thuộc tính đã tồn tại, sử dụng thuộc tính đó
      console.log(`Attribute "${name}" already exists, reusing it`);
      attributeResult = {
        success: true,
        data: [existingAttributesResult.data[0]],
      };
    } else {
      // Nếu thuộc tính chưa tồn tại, tạo mới
      attributeResult = await createProductAttribute({
        name,
        display_name: name, // Thêm display_name vì là trường bắt buộc
        store_id: storeId,
        is_variant: true,
      });
    }

    if (!attributeResult.success) {
      return attributeResult;
    }

    const attributeId = attributeResult.data[0].id;

    // Tạo các giá trị thuộc tính
    if (values.length > 0) {
      const valuePromises = values.map((value) =>
        createAttributeValue({
          attribute_id: attributeId,
          value,
          // Loại bỏ store_id vì bảng product_attribute_values không có cột này
        })
      );

      await Promise.all(valuePromises);
    }

    return {
      success: true,
      data: {
        ...attributeResult.data[0],
        values,
      },
      error: null,
    };
  } catch (error) {
    console.error('Error creating attribute with values:', error);
    return { success: false, error };
  }
}
