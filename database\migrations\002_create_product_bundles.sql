-- Migration: 002_create_product_bundles.sql
-- Description: Create product bundles system for combo products
-- Author: Development Team
-- Date: $(date)

-- =====================================================
-- 1. CREATE BUNDLE DISCOUNT TYPE ENUM
-- =====================================================

DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'bundle_discount_type_enum') THEN
        CREATE TYPE bundle_discount_type_enum AS ENUM (
            'fixed',            -- Giảm giá cố định
            'percentage',       -- Giả<PERSON> giá theo phần trăm
            'bundle_price'      -- Gi<PERSON> gói cố định
        );
    END IF;
END $$;

-- =====================================================
-- 2. CREATE PRODUCT_BUNDLES TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS product_bundles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    
    -- Bundle Information
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL,
    description TEXT,
    short_description TEXT,
    
    -- Bundle Configuration
    bundle_type VARCHAR(50) DEFAULT 'fixed' CHECK (bundle_type IN ('fixed', 'dynamic')),
    min_items INTEGER DEFAULT 1,
    max_items INTEGER,
    
    -- Pricing
    discount_type bundle_discount_type_enum DEFAULT 'percentage',
    discount_value NUMERIC(10,2) DEFAULT 0,
    bundle_price NUMERIC(10,2),
    
    -- Display & Marketing
    image_url TEXT,
    images TEXT[],
    is_featured BOOLEAN DEFAULT false,
    
    -- SEO
    seo_title TEXT,
    seo_description TEXT,
    meta_keywords TEXT[],
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(tenant_id, slug),
    CHECK (discount_value >= 0),
    CHECK (bundle_price >= 0 OR bundle_price IS NULL),
    CHECK (min_items > 0),
    CHECK (max_items IS NULL OR max_items >= min_items)
);

-- =====================================================
-- 3. CREATE PRODUCT_BUNDLE_ITEMS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS product_bundle_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    bundle_id UUID NOT NULL REFERENCES product_bundles(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    variant_id UUID REFERENCES product_variants(id) ON DELETE CASCADE,
    
    -- Item Configuration
    quantity INTEGER NOT NULL DEFAULT 1,
    is_required BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    
    -- Pricing Override (optional)
    override_price NUMERIC(10,2),
    discount_percentage NUMERIC(5,2) DEFAULT 0,
    
    -- Display
    display_name VARCHAR(255),
    description TEXT,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(bundle_id, product_id, variant_id),
    CHECK (quantity > 0),
    CHECK (override_price >= 0 OR override_price IS NULL),
    CHECK (discount_percentage >= 0 AND discount_percentage <= 100)
);

-- =====================================================
-- 4. CREATE BUNDLE_PURCHASE_HISTORY TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS bundle_purchase_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    bundle_id UUID NOT NULL REFERENCES product_bundles(id) ON DELETE CASCADE,
    order_id UUID REFERENCES orders(id) ON DELETE SET NULL,
    
    -- Purchase Details
    bundle_name VARCHAR(255) NOT NULL,
    bundle_configuration JSONB NOT NULL,
    total_price NUMERIC(10,2) NOT NULL,
    discount_applied NUMERIC(10,2) DEFAULT 0,
    
    -- Customer Info
    customer_id UUID REFERENCES customers(id) ON DELETE SET NULL,
    customer_email VARCHAR(255),
    
    -- Timestamps
    purchased_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CHECK (total_price >= 0),
    CHECK (discount_applied >= 0)
);

-- =====================================================
-- 5. CREATE INDEXES FOR PERFORMANCE
-- =====================================================

-- Product Bundles indexes
CREATE INDEX IF NOT EXISTS idx_product_bundles_tenant_id ON product_bundles(tenant_id);
CREATE INDEX IF NOT EXISTS idx_product_bundles_active ON product_bundles(tenant_id, is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_product_bundles_featured ON product_bundles(tenant_id, is_featured) WHERE is_featured = true;
CREATE INDEX IF NOT EXISTS idx_product_bundles_slug ON product_bundles(tenant_id, slug);
CREATE INDEX IF NOT EXISTS idx_product_bundles_bundle_type ON product_bundles(bundle_type);

-- Product Bundle Items indexes
CREATE INDEX IF NOT EXISTS idx_product_bundle_items_tenant_id ON product_bundle_items(tenant_id);
CREATE INDEX IF NOT EXISTS idx_product_bundle_items_bundle_id ON product_bundle_items(bundle_id);
CREATE INDEX IF NOT EXISTS idx_product_bundle_items_product_id ON product_bundle_items(product_id);
CREATE INDEX IF NOT EXISTS idx_product_bundle_items_variant_id ON product_bundle_items(variant_id);
CREATE INDEX IF NOT EXISTS idx_product_bundle_items_sort_order ON product_bundle_items(bundle_id, sort_order);

-- Bundle Purchase History indexes
CREATE INDEX IF NOT EXISTS idx_bundle_purchase_history_tenant_id ON bundle_purchase_history(tenant_id);
CREATE INDEX IF NOT EXISTS idx_bundle_purchase_history_bundle_id ON bundle_purchase_history(bundle_id);
CREATE INDEX IF NOT EXISTS idx_bundle_purchase_history_order_id ON bundle_purchase_history(order_id);
CREATE INDEX IF NOT EXISTS idx_bundle_purchase_history_customer_id ON bundle_purchase_history(customer_id);
CREATE INDEX IF NOT EXISTS idx_bundle_purchase_history_purchased_at ON bundle_purchase_history(purchased_at);

-- GIN index for bundle configuration
CREATE INDEX IF NOT EXISTS idx_bundle_purchase_history_config_gin ON bundle_purchase_history USING GIN(bundle_configuration);

-- =====================================================
-- 6. CREATE FUNCTIONS FOR BUNDLE CALCULATIONS
-- =====================================================

-- Function to calculate bundle price
CREATE OR REPLACE FUNCTION calculate_bundle_price(bundle_id_param UUID)
RETURNS NUMERIC AS $$
DECLARE
    bundle_record RECORD;
    item_record RECORD;
    total_original_price NUMERIC := 0;
    final_price NUMERIC := 0;
BEGIN
    -- Get bundle information
    SELECT * INTO bundle_record FROM product_bundles WHERE id = bundle_id_param;
    
    IF NOT FOUND THEN
        RETURN 0;
    END IF;
    
    -- Calculate total original price of all items
    FOR item_record IN 
        SELECT 
            pbi.*,
            COALESCE(pv.price, p.price) as item_price
        FROM product_bundle_items pbi
        JOIN products p ON pbi.product_id = p.id
        LEFT JOIN product_variants pv ON pbi.variant_id = pv.id
        WHERE pbi.bundle_id = bundle_id_param
    LOOP
        total_original_price := total_original_price + 
            (COALESCE(item_record.override_price, item_record.item_price) * item_record.quantity);
    END LOOP;
    
    -- Apply bundle discount
    CASE bundle_record.discount_type
        WHEN 'fixed' THEN
            final_price := total_original_price - bundle_record.discount_value;
        WHEN 'percentage' THEN
            final_price := total_original_price * (1 - bundle_record.discount_value / 100);
        WHEN 'bundle_price' THEN
            final_price := bundle_record.bundle_price;
    END CASE;
    
    -- Ensure price is not negative
    RETURN GREATEST(final_price, 0);
END;
$$ LANGUAGE plpgsql;

-- Function to get bundle items with pricing
CREATE OR REPLACE FUNCTION get_bundle_items_with_pricing(bundle_id_param UUID)
RETURNS TABLE (
    item_id UUID,
    product_id UUID,
    variant_id UUID,
    product_name VARCHAR,
    variant_name VARCHAR,
    quantity INTEGER,
    original_price NUMERIC,
    final_price NUMERIC,
    is_required BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        pbi.id as item_id,
        pbi.product_id,
        pbi.variant_id,
        p.name as product_name,
        pv.name as variant_name,
        pbi.quantity,
        COALESCE(pv.price, p.price) as original_price,
        CASE 
            WHEN pbi.override_price IS NOT NULL THEN pbi.override_price
            WHEN pbi.discount_percentage > 0 THEN 
                COALESCE(pv.price, p.price) * (1 - pbi.discount_percentage / 100)
            ELSE COALESCE(pv.price, p.price)
        END as final_price,
        pbi.is_required
    FROM product_bundle_items pbi
    JOIN products p ON pbi.product_id = p.id
    LEFT JOIN product_variants pv ON pbi.variant_id = pv.id
    WHERE pbi.bundle_id = bundle_id_param
    ORDER BY pbi.sort_order, p.name;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 7. CREATE TRIGGERS FOR UPDATED_AT
-- =====================================================

-- Trigger for product_bundles
CREATE OR REPLACE FUNCTION update_product_bundles_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_product_bundles_updated_at
    BEFORE UPDATE ON product_bundles
    FOR EACH ROW
    EXECUTE FUNCTION update_product_bundles_updated_at();

-- Trigger for product_bundle_items
CREATE OR REPLACE FUNCTION update_product_bundle_items_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_product_bundle_items_updated_at
    BEFORE UPDATE ON product_bundle_items
    FOR EACH ROW
    EXECUTE FUNCTION update_product_bundle_items_updated_at();

-- =====================================================
-- 8. ADD COMMENTS
-- =====================================================

COMMENT ON TABLE product_bundles IS 'Product bundles for creating combo/package deals';
COMMENT ON TABLE product_bundle_items IS 'Items included in product bundles';
COMMENT ON TABLE bundle_purchase_history IS 'History of bundle purchases for analytics';

COMMENT ON COLUMN product_bundles.bundle_type IS 'Type: fixed (predefined items) or dynamic (customer selects)';
COMMENT ON COLUMN product_bundles.discount_type IS 'How discount is applied: fixed amount, percentage, or bundle price';
COMMENT ON COLUMN product_bundle_items.is_required IS 'Whether this item is required in the bundle';
COMMENT ON COLUMN product_bundle_items.override_price IS 'Override price for this item in the bundle';

-- =====================================================
-- 9. INSERT SAMPLE DATA (Optional)
-- =====================================================

-- Note: This section can be uncommented to insert sample data for testing

/*
-- Sample bundle for testing
INSERT INTO product_bundles (
    tenant_id, name, slug, description, bundle_type, 
    discount_type, discount_value, is_active
) VALUES (
    (SELECT id FROM tenants LIMIT 1),
    'Combo Starter Pack',
    'combo-starter-pack',
    'Perfect starter pack with essential items',
    'fixed',
    'percentage',
    15.00,
    true
);
*/

-- =====================================================
-- ROLLBACK SCRIPT (if needed)
-- =====================================================

/*
-- To rollback this migration:

-- 1. Drop triggers
DROP TRIGGER IF EXISTS trigger_update_product_bundles_updated_at ON product_bundles;
DROP TRIGGER IF EXISTS trigger_update_product_bundle_items_updated_at ON product_bundle_items;

-- 2. Drop functions
DROP FUNCTION IF EXISTS update_product_bundles_updated_at();
DROP FUNCTION IF EXISTS update_product_bundle_items_updated_at();
DROP FUNCTION IF EXISTS calculate_bundle_price(UUID);
DROP FUNCTION IF EXISTS get_bundle_items_with_pricing(UUID);

-- 3. Drop tables
DROP TABLE IF EXISTS bundle_purchase_history;
DROP TABLE IF EXISTS product_bundle_items;
DROP TABLE IF EXISTS product_bundles;

-- 4. Drop enum
DROP TYPE IF EXISTS bundle_discount_type_enum;
*/
