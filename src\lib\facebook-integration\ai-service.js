/**
 * AI Service for Facebook Integration Auto Reply
 * Handles AI response generation for comments and messages
 */

// AI Response Generator
export class FacebookAIService {
  constructor(config = {}) {
    this.config = {
      apiKey: config.apiKey || process.env.OPENAI_API_KEY,
      model: config.model || 'gpt-3.5-turbo',
      maxTokens: config.maxTokens || 150,
      temperature: config.temperature || 0.7,
      ...config
    };
  }

  /**
   * Generate AI response for Facebook/Instagram comment or message
   */
  async generateResponse(input) {
    try {
      const {
        message,
        type, // 'comment' or 'message'
        platform, // 'facebook' or 'instagram'
        config,
        context = {}
      } = input;

      // Build system prompt based on configuration
      const systemPrompt = this.buildSystemPrompt(config, type, platform);
      
      // Build user prompt with context
      const userPrompt = this.buildUserPrompt(message, context, type);

      // Generate response using AI
      const aiResponse = await this.callAI(systemPrompt, userPrompt, config);

      // Post-process response
      const finalResponse = this.postProcessResponse(aiResponse, config);

      return {
        success: true,
        response: finalResponse,
        metadata: {
          model: this.config.model,
          type,
          platform,
          originalMessage: message,
          tokensUsed: aiResponse.tokensUsed || 0
        }
      };

    } catch (error) {
      console.error('💥 AI Service Error:', error);
      
      // Return fallback response
      return {
        success: false,
        response: this.getFallbackResponse(input.config, input.type),
        error: error.message,
        metadata: {
          fallback: true,
          type: input.type,
          platform: input.platform
        }
      };
    }
  }

  /**
   * Build system prompt based on configuration
   */
  buildSystemPrompt(config, type, platform) {
    const {
      reply_tone = 'friendly',
      reply_language = 'vi',
      business_info = '',
      products = '',
      policies = '',
      max_reply_length = 500
    } = config;

    let systemPrompt = `Bạn là trợ lý AI chuyên nghiệp cho doanh nghiệp, chuyên trả lời ${type === 'comment' ? 'bình luận' : 'tin nhắn'} trên ${platform === 'facebook' ? 'Facebook' : 'Instagram'}.

HƯỚNG DẪN:
- Ngôn ngữ: ${reply_language === 'vi' ? 'Tiếng Việt' : 'English'}
- Giọng điệu: ${this.getToneDescription(reply_tone)}
- Độ dài tối đa: ${max_reply_length} ký tự
- Luôn lịch sự, chuyên nghiệp và hữu ích
- Không sử dụng emoji quá nhiều
- Tập trung vào việc hỗ trợ khách hàng`;

    if (business_info) {
      systemPrompt += `\n\nTHÔNG TIN DOANH NGHIỆP:\n${business_info}`;
    }

    if (products) {
      systemPrompt += `\n\nSẢN PHẨM/DỊCH VỤ:\n${products}`;
    }

    if (policies) {
      systemPrompt += `\n\nCHÍNH SÁCH:\n${policies}`;
    }

    systemPrompt += `\n\nLƯU Ý:
- Nếu không có thông tin cụ thể, hãy hướng dẫn khách hàng liên hệ trực tiếp
- Không đưa ra cam kết về giá cả hoặc chính sách nếu không chắc chắn
- Luôn giữ thái độ tích cực và sẵn sàng hỗ trợ`;

    return systemPrompt;
  }

  /**
   * Build user prompt with context
   */
  buildUserPrompt(message, context, type) {
    let prompt = `Khách hàng ${type === 'comment' ? 'bình luận' : 'nhắn tin'}: "${message}"`;

    if (context.postContent) {
      prompt += `\n\nNội dung bài viết liên quan: "${context.postContent}"`;
    }

    if (context.previousMessages && context.previousMessages.length > 0) {
      prompt += `\n\nCuộc trò chuyện trước đó:`;
      context.previousMessages.forEach((msg, index) => {
        prompt += `\n${index + 1}. ${msg.sender}: "${msg.text}"`;
      });
    }

    prompt += `\n\nHãy trả lời một cách phù hợp và hữu ích.`;

    return prompt;
  }

  /**
   * Call AI API to generate response
   */
  async callAI(systemPrompt, userPrompt, config) {
    if (!this.config.apiKey) {
      throw new Error('AI API key not configured');
    }

    try {
      // Use OpenAI API
      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: this.config.model,
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt }
          ],
          max_tokens: Math.min(this.config.maxTokens, config.max_reply_length / 2),
          temperature: this.config.temperature,
          top_p: 0.9,
          frequency_penalty: 0.1,
          presence_penalty: 0.1
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`OpenAI API error: ${errorData.error?.message || response.statusText}`);
      }

      const data = await response.json();
      
      if (!data.choices || data.choices.length === 0) {
        throw new Error('No response generated from AI');
      }

      return {
        text: data.choices[0].message.content.trim(),
        tokensUsed: data.usage?.total_tokens || 0
      };

    } catch (error) {
      console.error('💥 AI API Error:', error);
      throw error;
    }
  }

  /**
   * Post-process AI response
   */
  postProcessResponse(aiResponse, config) {
    let response = aiResponse.text;

    // Limit length
    if (response.length > config.max_reply_length) {
      response = response.substring(0, config.max_reply_length - 3) + '...';
    }

    // Remove any potential harmful content
    response = this.sanitizeResponse(response);

    // Ensure proper formatting
    response = response.trim();

    return response;
  }

  /**
   * Sanitize response to remove harmful content
   */
  sanitizeResponse(response) {
    // Remove potential script tags or harmful content
    return response
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '')
      .trim();
  }

  /**
   * Get fallback response when AI fails
   */
  getFallbackResponse(config, type) {
    const fallbackMessages = {
      comment: [
        'Cảm ơn bạn đã quan tâm! Chúng tôi sẽ liên hệ với bạn sớm nhất có thể.',
        'Xin chào! Cảm ơn bạn đã bình luận. Vui lòng inbox để được hỗ trợ chi tiết.',
        'Chào bạn! Chúng tôi đã nhận được bình luận của bạn và sẽ phản hồi sớm.'
      ],
      message: [
        'Xin chào! Cảm ơn bạn đã nhắn tin. Chúng tôi sẽ phản hồi trong thời gian sớm nhất.',
        'Chào bạn! Chúng tôi đã nhận được tin nhắn và sẽ hỗ trợ bạn ngay.',
        'Xin chào! Cảm ơn bạn đã liên hệ. Vui lòng chờ trong giây lát.'
      ]
    };

    const messages = fallbackMessages[type] || fallbackMessages.comment;
    const randomMessage = messages[Math.floor(Math.random() * messages.length)];

    // Add business info if available
    if (config.business_info) {
      return `${randomMessage}\n\n${config.business_info}`;
    }

    return randomMessage;
  }

  /**
   * Get tone description for system prompt
   */
  getToneDescription(tone) {
    const toneMap = {
      friendly: 'thân thiện, gần gũi',
      professional: 'chuyên nghiệp, trang trọng',
      casual: 'thoải mái, không quá trang trọng',
      enthusiastic: 'nhiệt tình, tích cực',
      helpful: 'hỗ trợ, sẵn sàng giúp đỡ'
    };

    return toneMap[tone] || 'thân thiện, chuyên nghiệp';
  }

  /**
   * Check if message should be excluded from auto reply
   */
  shouldExcludeMessage(message, excludeKeywords = []) {
    if (!message || !Array.isArray(excludeKeywords) || excludeKeywords.length === 0) {
      return false;
    }

    const lowerMessage = message.toLowerCase();
    
    return excludeKeywords.some(keyword => {
      if (typeof keyword === 'string') {
        return lowerMessage.includes(keyword.toLowerCase());
      }
      return false;
    });
  }

  /**
   * Extract context from Facebook/Instagram event
   */
  extractContext(eventData, type) {
    const context = {};

    if (type === 'comment' && eventData.post_id) {
      // For comments, we might want to get the post content
      context.postId = eventData.post_id;
    }

    if (eventData.sender) {
      context.senderId = eventData.sender.id;
      context.senderName = eventData.sender.name;
    }

    if (eventData.message && eventData.message.text) {
      context.messageText = eventData.message.text;
    }

    return context;
  }
}
