'use client';

import { useState } from 'react';

import { adjustInventory } from './inventory-service';
import { callRPC, fetchData } from './supabase-utils';
import { updateOrderStatusEnhanced } from './enhanced-order-service';
import { createOrder, createOrderItem, createOrderHistory, updateOrder as updateOrderService } from './order-service';

/**
 * Hook để thực hiện các thao tác mutation phức tạp với đơn hàng
 * @returns {Object} - C<PERSON>c hàm mutation
 */
export function useOrderMutations() {
  const [isMutating, setIsMutating] = useState(false);
  const [error, setError] = useState(null);

  /**
   * Tạo đơn hàng mới với các sản phẩm
   * @param {Object} orderData - Dữ liệu đơn hàng
   * @returns {Promise<Object>} - <PERSON><PERSON><PERSON> qu<PERSON> từ API
   */
  const createCompleteOrder = async (orderData) => {
    setIsMutating(true);
    setError(null);
    try {
      // Tách dữ liệu đơn hàng và sản phẩm
      const { orderItems, ...orderInfo } = orderData;

      // 1. Tạo đơn hàng cơ bản trước
      const orderResult = await createOrder({
        ...orderInfo,
      });

      if (!orderResult.success) {
        return orderResult;
      }

      const orderId = orderResult.data[0].id;

      // 2. Thêm các sản phẩm vào đơn hàng
      if (orderItems && orderItems.length > 0) {
        const itemPromises = orderItems.map((item) =>
          createOrderItem({
            ...item,
            orderId,
            variantId: item.variantId || null,
          })
        );

        await Promise.all(itemPromises);

        // 3. Tự động trừ inventory và tạo inventory transactions
        await createInventoryTransactionsForOrder(orderId, orderItems);
      }

      // 4. Tạo lịch sử trạng thái đơn hàng
      await createOrderHistory({
        orderId,
        status: orderInfo.status,
        comment: 'Đơn hàng được tạo',
      });

      return {
        success: true,
        data: orderResult.data,
        error: null,
      };
    } catch (err) {
      setError(err);
      return { success: false, error: err, data: null };
    } finally {
      setIsMutating(false);
    }
  };

  /**
   * Cập nhật trạng thái đơn hàng
   * @param {string} orderId - ID đơn hàng
   * @param {string} status - Trạng thái mới của đơn hàng
   * @param {string} previousStatus - Trạng thái trước đó của đơn hàng
   * @param {string} comment - Ghi chú khi cập nhật trạng thái
   * @param {string} userId - ID người dùng thực hiện cập nhật
   * @returns {Promise<Object>} - Kết quả từ API
   */
  const updateOrderStatus = async (
    orderId,
    status,
    previousStatus,
    comment = '',
    userId = null
  ) => {
    setIsMutating(true);
    setError(null);
    try {
      // 1. Cập nhật trạng thái đơn hàng
      const orderResult = await updateOrder(orderId, {
        status: status || null,
      });

      if (!orderResult.success) {
        return orderResult;
      }
      const orderHistoryItem = {
        orderId,
        status: status || null,
        previousStatus: previousStatus || null,
        userId: userId || null,
        comment,
      };

      // 2. Tạo lịch sử trạng thái đơn hàng
      await createOrderHistory(orderHistoryItem);

      return {
        success: true,
        data: orderResult.data,
        error: null,
      };
    } catch (err) {
      setError(err);
      return { success: false, error: err, data: null };
    } finally {
      setIsMutating(false);
    }
  };

  /**
   * Cập nhật thông tin đơn hàng
   * @param {string} orderId - ID đơn hàng
   * @param {Object} orderData - Dữ liệu cập nhật
   * @returns {Promise<Object>} - Kết quả từ API
   */
  const updateOrder = async (orderId, orderData) => {
    setIsMutating(true);
    setError(null);
    try {
      // Tách dữ liệu đơn hàng và sản phẩm
      const { orderItems, ...orderInfo } = orderData;

      // Nếu có cập nhật trạng thái, sử dụng enhanced service
      if (orderData.status) {
        const result = await updateOrderStatusEnhanced(orderId, orderData.status, {
          comment: orderData.notes || 'Cập nhật trạng thái đơn hàng',
          autoInventoryUpdate: true,
          notifyCustomer: true
        });
        return result;
      } else {
        // Cập nhật thông tin đơn hàng cơ bản
        const orderResult = await updateOrderService(orderId, orderInfo);

        if (!orderResult.success) {
          return orderResult;
        }

        // Nếu có orderItems, cập nhật danh sách sản phẩm
        if (orderItems && Array.isArray(orderItems)) {
          await updateOrderItems(orderId, orderItems);
        }

        return orderResult;
      }
    } catch (err) {
      setError(err);
      return { success: false, error: err.message || err, data: null };
    } finally {
      setIsMutating(false);
    }
  };

  return {
    createCompleteOrder,
    updateOrder,
    updateOrderStatus,
    isMutating,
    error,
  };
}

/**
 * Tạo inventory transactions cho đơn hàng
 * @param {string} orderId - ID đơn hàng
 * @param {Array} orderItems - Danh sách sản phẩm trong đơn hàng
 */
async function createInventoryTransactionsForOrder(orderId, orderItems) {
  try {
    for (const item of orderItems) {
      const { productId, variantId, quantity } = item;

      if (!productId || !quantity || quantity <= 0) {
        continue;
      }

      // Lấy thông tin sản phẩm để kiểm tra loại
      const { success: productSuccess, data: productData } = await fetchData('products', {
        filters: { id: productId },
        columns: 'id, name, type, stock_quantity',
        single: true
      });

      if (!productSuccess || !productData) {
        console.warn(`Không thể lấy thông tin sản phẩm ${productId}`);
        continue;
      }

      // Bỏ qua sản phẩm digital và service
      if (['digital', 'service'].includes(productData.type)) {
        continue;
      }

      let previousQuantity = 0;
      let newQuantity = 0;

      if (variantId) {
        // Xử lý sản phẩm có biến thể
        const { success: variantSuccess, data: variantData } = await fetchData('product_variants', {
          filters: { id: variantId },
          columns: 'id, stock_quantity',
          single: true
        });

        if (variantSuccess && variantData) {
          previousQuantity = variantData.stockQuantity || 0;
          newQuantity = Math.max(0, previousQuantity - quantity);

          // Cập nhật stock quantity của variant
          await adjustInventory(productId, variantId, -quantity, {
            notes: `Trừ kho cho đơn hàng ${orderId}`,
            referenceId: orderId,
            referenceType: 'order',
            transactionType: 'sale'
          });
        }
      } else {
        // Xử lý sản phẩm đơn giản
        previousQuantity = productData.stockQuantity || 0;
        newQuantity = Math.max(0, previousQuantity - quantity);

        // Cập nhật stock quantity của product
        await adjustInventory(productId, null, -quantity, {
          notes: `Trừ kho cho đơn hàng ${orderId}`,
          referenceId: orderId,
          referenceType: 'order',
          transactionType: 'sale'
        });
      }

      console.log(`Đã tạo inventory transaction cho ${variantId ? 'variant' : 'product'} ${variantId || productId}: ${previousQuantity} -> ${newQuantity}`);
    }
  } catch (error) {
    console.error('Lỗi khi tạo inventory transactions:', error);
    // Không throw error để không làm fail việc tạo đơn hàng
  }
}

/**
 * Cập nhật danh sách sản phẩm trong đơn hàng
 * @param {string} orderId - ID đơn hàng
 * @param {Array} newOrderItems - Danh sách sản phẩm mới
 */
async function updateOrderItems(orderId, newOrderItems) {
  try {
    // Validate input
    if (!orderId) {
      throw new Error('Order ID là bắt buộc');
    }

    if (!Array.isArray(newOrderItems)) {
      throw new Error('Order items phải là một mảng');
    }

    // Sử dụng utility function để validate và chuẩn hóa
    const { prepareOrderItemsForSubmit } = await import('./order-items-utils');
    const validatedItems = prepareOrderItemsForSubmit(newOrderItems);

    console.log(`Cập nhật ${validatedItems.length} order items cho đơn hàng ${orderId}`);

    // Sử dụng database function để cập nhật order items với inventory tracking
    const result = await callRPC('update_order_items_with_inventory', {
      p_order_id: orderId,
      p_order_items: JSON.stringify(validatedItems)
    });

    if (!result.success) {
      const errorMessage = result.data?.error || result.error || 'Không thể cập nhật danh sách sản phẩm';
      throw new Error(errorMessage);
    }

    console.log(`✅ Đã cập nhật order items cho đơn hàng ${orderId}:`, result.data);
    return result;
  } catch (error) {
    console.error('❌ Lỗi khi cập nhật order items:', error);
    throw error;
  }
}
