'use client';

import { useEffect } from 'react';
import PropTypes from 'prop-types';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import LoadingButton from '@mui/lab/LoadingButton';
import DialogTitle from '@mui/material/DialogTitle';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';

import { useOrderMutations } from 'src/actions/mooly-chatbot/order-mutations';
import { prepareOrderItemsForSubmit } from 'src/actions/mooly-chatbot/order-items-utils';

import { toast } from 'src/components/snackbar';
import { Form } from 'src/components/hook-form';

import { OrderItemsEditSchema } from '../validation/order-edit-schemas';
import { OrderItemsList as OrderItemsFormList } from '../components/OrderItemsList';

// ----------------------------------------------------------------------

export function OrderEditItemsDialog({ open, onClose, order, orderItems, onSuccess }) {
  const { updateOrder, isMutating } = useOrderMutations();

  const defaultValues = {
    orderItems: orderItems?.map(item => ({
      id: item.id,
      productId: item.productId,
      variantId: item.variantId || '',
      name: item.name,
      sku: item.sku,
      quantity: item.quantity,
      unitPrice: item.unitPrice,
      totalPrice: item.totalPrice,
      discountAmount: item.discountAmount || 0,
      taxAmount: item.taxAmount || 0,
      imageUrl: item.imageUrl,
      variantInfo: item.variantInfo,
      productDetail: item.productDetail,
      variantDetail: item.variantDetail,
      product: item.product,
      variant: item.variant,
    })) || [],
  };

  const methods = useForm({
    resolver: zodResolver(OrderItemsEditSchema),
    defaultValues,
  });

  const { handleSubmit, reset } = methods;

  // Reset form khi dialog mở và có dữ liệu mới
  useEffect(() => {
    if (open && orderItems) {
      const newValues = {
        orderItems: orderItems?.map(item => ({
          id: item.id,
          productId: item.productId,
          variantId: item.variantId || '',
          name: item.name,
          sku: item.sku,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          totalPrice: item.totalPrice,
          discountAmount: item.discountAmount || 0,
          taxAmount: item.taxAmount || 0,
          imageUrl: item.imageUrl,
          variantInfo: item.variantInfo,
          productDetail: item.productDetail,
          variantDetail: item.variantDetail,
          product: item.product,
          variant: item.variant,
        })) || [],
      };
      reset(newValues);
    }
  }, [open, orderItems, reset]);

  const onSubmit = handleSubmit(async (data) => {
    try {
      // Sử dụng utility function để chuẩn bị dữ liệu
      const processedOrderItems = prepareOrderItemsForSubmit(data.orderItems);

      const updateData = {
        orderItems: processedOrderItems,
      };

      console.log('Updating order items:', {
        orderId: order.id,
        itemsCount: processedOrderItems.length,
        items: processedOrderItems
      });

      const result = await updateOrder(order.id, updateData);

      if (!result.success) {
        throw new Error(result.error || 'Cập nhật danh sách sản phẩm thất bại');
      }

      toast.success('Cập nhật danh sách sản phẩm thành công!');
      onSuccess?.();
      onClose();
    } catch (error) {
      console.error('Error updating items:', error);
      toast.error(error.message || 'Cập nhật danh sách sản phẩm thất bại!');
    }
  });

  const handleClose = () => {
    reset();
    onClose();
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="lg" fullWidth>
      <DialogTitle>Chỉnh sửa danh sách sản phẩm</DialogTitle>

      <Form methods={methods} onSubmit={onSubmit}>
        <DialogContent>
          <Stack spacing={3} sx={{ pt: 1 }}>
            <OrderItemsFormList />
          </Stack>
        </DialogContent>

        <DialogActions>
          <Button onClick={handleClose} color="inherit">
            Hủy
          </Button>
          <LoadingButton
            type="submit"
            variant="contained"
            loading={isMutating}
          >
            Cập nhật
          </LoadingButton>
        </DialogActions>
      </Form>
    </Dialog>
  );
}

OrderEditItemsDialog.propTypes = {
  open: PropTypes.bool,
  onClose: PropTypes.func,
  order: PropTypes.object,
  orderItems: PropTypes.array,
  onSuccess: PropTypes.func,
};
