import { NextResponse } from 'next/server';

import { withTenantAuth, validateTenantId } from 'src/utils/server-auth';

/**
 * API route để xóa sản phẩm khỏi Weaviate dựa trên product_id
 * @param {Request} request - <PERSON><PERSON><PERSON> cầu HTTP
 * @returns {Promise<NextResponse>} - Phản hồi HTTP
 */
export const DELETE = withTenantAuth(async (request, { tenantId, userId }) => {
  try {
    // Validate request body và kiểm tra tenant_id
    // const validation = await validateTenantId(request, tenantId);
    // if (!validation.valid) {
    //   return NextResponse.json({ error: validation.error }, { status: validation.status });
    // }

    const { product_id } = await request.json();

    // Kiểm tra dữ liệu đầu vào
    if (!product_id) {
      return NextResponse.json(
        { error: 'Missing required field: product_id' },
        { status: 400 }
      );
    }

    // L<PERSON>y URL Weaviate từ biến môi trường
    const BACKEND_API_URL = process.env.BACKEND_API_URL || 'http://localhost:3000';

    // Gọi API Weaviate
    const response = await fetch(`${BACKEND_API_URL}/api/weaviate/products/delete-by-id`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        product_id,
        tenant_id: tenantId, // Sử dụng tenant_id từ xác thực
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      return NextResponse.json(
        { error: errorData.message || 'Failed to delete product by ID' },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in delete-by-id API route:', error);
    return NextResponse.json({ error: error.message || 'Internal server error' }, { status: 500 });
  }
});
