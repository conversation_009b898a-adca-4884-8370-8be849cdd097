'use server';

/**
 * Server actions để tương tác với Weaviate API
 * Đ<PERSON><PERSON> bảo an toàn bằng cách xử lý các yêu cầu ở phía server
 */

// Lấy URL Weaviate từ biến môi trường
const BACKEND_API_URL = process.env.BACKEND_API_URL || 'http://localhost:3000';

/**
 * Thêm nhiều sản phẩm vào Weaviate
 * @param {Array<Object>} products - Danh sách sản phẩm
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function addProducts(products) {
  if (!products || !Array.isArray(products) || products.length === 0) {
    return {
      success: true,
      data: { message: 'No products to add' },
      error: null,
    };
  }

  try {
    const response = await fetch(`${BACKEND_API_URL}/api/weaviate/products/batch`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ products }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to add products to Weaviate');
    }

    const data = await response.json();
    return {
      success: true,
      data,
      error: null,
    };
  } catch (error) {
    console.error('Error adding products to Weaviate:', error);
    return {
      success: false,
      data: null,
      error: error.message,
    };
  }
}

/**
 * Cập nhật sản phẩm trong Weaviate
 * @param {Object} productData - Dữ liệu sản phẩm cần cập nhật
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function updateProduct(productData) {
  try {
    const response = await fetch(`${BACKEND_API_URL}/api/weaviate/products/update`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ data: productData }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to update product in Weaviate');
    }

    const data = await response.json();
    return {
      success: true,
      data,
      error: null,
    };
  } catch (error) {
    console.error('Error updating product in Weaviate:', error);
    return {
      success: false,
      data: null,
      error: error.message,
    };
  }
}

/**
 * Xóa sản phẩm khỏi Weaviate dựa trên URL hình ảnh
 * @param {Object} deleteData - Dữ liệu để xóa sản phẩm
 * @param {string} deleteData.image_url - URL hình ảnh sản phẩm
 * @param {string} deleteData.tenant_id - ID của tenant
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function deleteProduct(deleteData) {
  try {
    const response = await fetch(`${BACKEND_API_URL}/api/weaviate/products/delete`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(deleteData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to delete product from Weaviate');
    }

    const data = await response.json();
    return {
      success: true,
      data,
      error: null,
    };
  } catch (error) {
    console.error('Error deleting product from Weaviate:', error);
    return {
      success: false,
      data: null,
      error: error.message,
    };
  }
}

/**
 * Xóa tất cả sản phẩm khỏi Weaviate dựa trên product_id
 * @param {string} productId - ID của sản phẩm
 * @param {string} tenantId - ID của tenant
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function deleteProductById(productId, tenantId) {
  try {
    const response = await fetch(`${BACKEND_API_URL}/api/weaviate/products/delete-by-id`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        product_id: productId,
        tenant_id: tenantId,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to delete product by ID from Weaviate');
    }

    const data = await response.json();
    return {
      success: true,
      data,
      error: null,
    };
  } catch (error) {
    console.error('Error deleting product by ID from Weaviate:', error);
    return {
      success: false,
      data: null,
      error: error.message,
    };
  }
}

/**
 * Cập nhật thông tin sản phẩm trong Weaviate theo ID
 * @param {Object} updateData - Dữ liệu để cập nhật sản phẩm
 * @param {string} updateData.tenant_id - ID của tenant
 * @param {string} updateData.product_id - ID của sản phẩm
 * @param {boolean} [updateData.is_active] - Trạng thái kích hoạt mới (tùy chọn)
 * @param {Array<string>} [updateData.bot_id] - Danh sách ID của bot (tùy chọn)
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function updateProductById(updateData) {
  try {
    const response = await fetch(`${BACKEND_API_URL}/api/weaviate/products/update-by-id`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updateData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to update product in Weaviate');
    }

    const data = await response.json();
    return {
      success: true,
      data,
      error: null,
    };
  } catch (error) {
    console.error('Error updating product in Weaviate:', error);
    return {
      success: false,
      data: null,
      error: error.message,
    };
  }
}

/**
 * Cập nhật trạng thái kích hoạt của sản phẩm trong Weaviate (Giữ lại để tương thích ngược)
 * @param {Object} toggleData - Dữ liệu để cập nhật trạng thái
 * @param {string} toggleData.tenant_id - ID của tenant
 * @param {boolean} toggleData.is_active - Trạng thái kích hoạt mới
 * @param {string} toggleData.product_id - ID của sản phẩm
 * @returns {Promise<Object>} - Kết quả từ API
 * @deprecated Sử dụng updateProductById thay thế
 */
export async function toggleProductActive(toggleData) {
  return updateProductById(toggleData);
}
