/**
 * Unified Social Media Integration - Main Export
 * Tập hợp tất cả exports cho social media integration
 */

import { getAutoReplyConfig, getFacebookAccounts, logFacebookActivity } from '../facebook-integration-service.js';
import { getFacebookAccountByPageId, upsertAutoReplyConfig, upsertFacebookAccount } from '../facebook-integration/facebook-api.js';
import { getInstagramAccountById, getInstagramAccounts, logInstagramActivity, upsertInstagramAccount } from '../instagram-integration-service.js';
import { PLATFORM_COLORS, PLATFORM_ICONS, PLATFORMS } from './social-media-constants.js';
import { isWithinRateLimit, platformSupportsFeature, validateContent, isPlatformSupported, isTokenExpired, generatePersonalizedReply } from './social-media-utils.js';

// Constants
export {
  PLATFORMS,
  ACCOUNT_TYPES,
  INTERACTION_TYPES,
  WEBHOOK_FIELDS,
  API_ENDPOINTS,
  TABLES,
  ACTIVITY_TYPES,
  REPLY_TONES,
  REPLY_LANGUAGES,
  DEFAULT_CONFIG,
  PLATFORM_FEATURES,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  VALIDATION_RULES,
  RATE_LIMITS,
  OAUTH_SCOPES,
  PLATFORM_COLORS,
  PLATFORM_ICONS,
  WEBHOOK_EVENTS,
  AUTO_REPLY_TEMPLATES
} from './social-media-constants.js';

// Utilities
export {
  isPlatformSupported,
  platformSupportsFeature,
  getPlatformLimit,
  validateContent,
  isWithinRateLimit,
  getRemainingRateLimit,
  isTokenExpired,
  calculateTokenExpiration,
  extractMentions,
  extractHashtags,
  cleanContentForReply,
  getAutoReplyTemplate,
  generatePersonalizedReply,
  extractUrls,
  isLikelySpam,
  formatTimestamp,
  generateInteractionId
} from './social-media-utils.js';

// Hooks
export {
  useSocialMediaAccounts,
  useSocialMediaAccount,
  useSocialMediaConfig,
  useSocialMediaActivityLogs,
  useSocialMediaIntegrationSetup
} from './social-media-hooks.js';

// Mutations
export {
  useSocialMediaAccountMutations,
  useSocialMediaConfigMutations,
  useSocialMediaWebhookMutations
} from './social-media-mutations.js';

// Legacy Facebook Integration (for backward compatibility)
export {
  getFacebookAccounts,
  getFacebookAccountById,
  getFacebookAccountByPageId,
  getAutoReplyConfig,
  getSelectedPageForChatbot,
  upsertFacebookAccount,
  upsertAutoReplyConfig,
  logFacebookActivity,
  useFacebookAccounts,
  useFacebookAccount,
  useAutoReplyConfig,
  useSelectedPageForChatbot,
  useFacebookIntegrationSetup,
  useFacebookAccountMutations,
  useAutoReplyConfigMutations,
  usePageSelectionMutations,
  useFacebookMutations,
  useFacebookPopupAuth,
  useFacebookConnection,
  validateFacebookPermissions,
  formatFacebookError,
  FACEBOOK_TABLES,
  ACTIVITY_TYPES as FACEBOOK_ACTIVITY_TYPES,
  REPLY_TONES as FACEBOOK_REPLY_TONES,
  REPLY_LANGUAGES as FACEBOOK_REPLY_LANGUAGES,
  DEFAULT_CONFIG as FACEBOOK_DEFAULT_CONFIG,
  API_ENDPOINTS as FACEBOOK_API_ENDPOINTS,
  ERROR_MESSAGES as FACEBOOK_ERROR_MESSAGES,
  SUCCESS_MESSAGES as FACEBOOK_SUCCESS_MESSAGES
} from '../facebook-integration/index.js';

// Instagram Integration Services
export {
  getInstagramAccounts,
  getInstagramAccountById,
  upsertInstagramAccount,
  getInstagramBusinessAccountInfo,
  getInstagramMedia,
  replyToInstagramComment,
  sendInstagramDirectMessage,
  subscribeInstagramWebhooks,
  logInstagramActivity,
  refreshInstagramAccessToken
} from '../instagram-integration-service.js';

// Helper functions for unified API
export function getSocialMediaAccounts(platform = null, tenantId) {
  if (platform === 'facebook') {
    return getFacebookAccounts(tenantId);
  } else if (platform === 'instagram') {
    return getInstagramAccounts(tenantId);
  } else {
    // Return all platforms
    return Promise.all([
      getFacebookAccounts(tenantId),
      getInstagramAccounts(tenantId)
    ]).then(([facebookAccounts, instagramAccounts]) => [
      ...facebookAccounts,
      ...instagramAccounts
    ]);
  }
}

export function getSocialMediaAccountById(accountId, platform, tenantId) {
  if (platform === 'facebook') {
    return getFacebookAccountByPageId(accountId, tenantId);
  } else if (platform === 'instagram') {
    return getInstagramAccountById(accountId, tenantId);
  } else {
    throw new Error('Platform must be specified for getSocialMediaAccountById');
  }
}

export function upsertSocialMediaAccount(accountData) {
  if (accountData.platform === 'facebook') {
    return upsertFacebookAccount(accountData);
  } else if (accountData.platform === 'instagram') {
    return upsertInstagramAccount(accountData);
  } else {
    throw new Error(`Unsupported platform: ${accountData.platform}`);
  }
}

export function logSocialMediaActivity(tenantId, accountId, activity, metadata = {}) {
  const platform = metadata.platform || 'facebook';

  if (platform === 'facebook') {
    return logFacebookActivity(tenantId, accountId, activity, metadata);
  } else if (platform === 'instagram') {
    return logInstagramActivity(tenantId, accountId, activity, metadata);
  } else {
    throw new Error(`Unsupported platform: ${platform}`);
  }
}

// Platform-specific OAuth URLs
export function getOAuthUrl(platform, tenantId, returnUrl) {
  const baseUrl = process.env.NEXT_PUBLIC_PUBLIC_SITE_URL || 'http://localhost:3000';

  if (platform === 'facebook') {
    return `${baseUrl}/api/facebook-integration/auth?tenant_id=${tenantId}&return_url=${encodeURIComponent(returnUrl)}`;
  } else if (platform === 'instagram') {
    return `${baseUrl}/api/instagram-integration/auth?tenant_id=${tenantId}&return_url=${encodeURIComponent(returnUrl)}`;
  } else {
    throw new Error(`OAuth not supported for platform: ${platform}`);
  }
}

// Platform-specific webhook URLs
export function getWebhookUrl(platform) {
  const baseUrl = process.env.NEXT_PUBLIC_PUBLIC_SITE_URL || 'http://localhost:3000';

  if (platform === 'facebook') {
    return `${baseUrl}/api/facebook-webhook`;
  } else if (platform === 'instagram') {
    return `${baseUrl}/api/instagram-webhook`;
  } else {
    throw new Error(`Webhook not supported for platform: ${platform}`);
  }
}

// Unified configuration management
export function getUnifiedConfig(accountId, platform, tenantId) {
  // Both Facebook and Instagram use the same config table
  return getAutoReplyConfig(accountId, tenantId);
}

export function saveUnifiedConfig(configData) {
  // Both Facebook and Instagram use the same config table
  return upsertAutoReplyConfig(configData);
}

// Platform feature checks
export function canSendMessages(platform) {
  return platformSupportsFeature(platform, 'SUPPORTS_MESSAGES');
}

export function canReplyToComments(platform) {
  return platformSupportsFeature(platform, 'SUPPORTS_COMMENTS');
}

export function canHandleMentions(platform) {
  return platformSupportsFeature(platform, 'SUPPORTS_MENTIONS');
}

export function canHandleStoryReplies(platform) {
  return platformSupportsFeature(platform, 'SUPPORTS_STORY_REPLIES');
}

export function canAutoLikeComments(platform) {
  return platformSupportsFeature(platform, 'SUPPORTS_AUTO_LIKE');
}

// Content validation helpers
export function validateMessageContent(platform, content) {
  return validateContent(platform, content, 'message');
}

export function validateCommentContent(platform, content) {
  return validateContent(platform, content, 'comment');
}

// Rate limiting helpers
export function checkMessageRateLimit(platform, currentCount) {
  return isWithinRateLimit(platform, 'MESSAGES_PER_HOUR', currentCount);
}

export function checkCommentRateLimit(platform, currentCount) {
  return isWithinRateLimit(platform, 'COMMENTS_PER_HOUR', currentCount);
}

export function checkApiRateLimit(platform, currentCount) {
  return isWithinRateLimit(platform, 'API_CALLS_PER_HOUR', currentCount);
}

// Default exports for easy importing
export default {
  // Constants
  PLATFORMS,
  PLATFORM_COLORS,
  PLATFORM_ICONS,
  
  // Main functions
  getSocialMediaAccounts,
  getSocialMediaAccountById,
  upsertSocialMediaAccount,
  logSocialMediaActivity,
  getOAuthUrl,
  getWebhookUrl,
  
  // Configuration
  getUnifiedConfig,
  saveUnifiedConfig,
  
  // Feature checks
  canSendMessages,
  canReplyToComments,
  canHandleMentions,
  canHandleStoryReplies,
  canAutoLikeComments,
  
  // Validation
  validateMessageContent,
  validateCommentContent,
  
  // Rate limiting
  checkMessageRateLimit,
  checkCommentRateLimit,
  checkApiRateLimit,
  
  // Utilities
  isPlatformSupported,
  platformSupportsFeature,
  validateContent,
  isTokenExpired,
  generatePersonalizedReply
};
