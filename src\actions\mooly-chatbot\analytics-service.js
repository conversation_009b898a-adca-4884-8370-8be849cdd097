'use client';

/**
 * Analytics Service
 * Tập trung các hàm analytics để đảm bảo đồng bộ và tái sử dụng
 * Sử dụng supabase-utils để đảm bảo field conversion và tenant_id handling
 */

import { fetchData } from './supabase-utils';

/**
 * L<PERSON>y dữ liệu analytics tổng quan
 * @param {string} timeRange - K<PERSON>ảng thời gian (7d, 30d, 90d, 1y)
 * @returns {Promise<Object>} Analytics data
 */
export async function getAnalyticsOverview(timeRange = '30d') {
  try {
    const timeRanges = {
      '7d': 7,
      '30d': 30,
      '90d': 90,
      '1y': 365
    };

    const days = timeRanges[timeRange] || 30;
    const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);

    // Fetch orders data với đúng filters
    const ordersResult = await fetchData('orders', {
      filters: {
        createdAt: {
          gte: startDate.toISOString()
        },
        status: {
          operator: 'in',
          value: ['completed', 'delivered', 'paid']
        }
      },
      columns: 'id, total_amount, subtotal, customer_id, created_at, status',
      orderBy: 'createdAt',
      ascending: false
    });

    if (!ordersResult.success) {
      throw new Error('Failed to fetch orders data');
    }

    const orders = ordersResult.data || [];

    // Calculate basic metrics
    const totalRevenue = orders.reduce((sum, order) => sum + (parseFloat(order.totalAmount) || 0), 0);
    const totalOrders = orders.length;
    const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;
    const uniqueCustomers = new Set(orders.map(order => order.customerId).filter(Boolean)).size;

    // Fetch additional data for comprehensive metrics
    const [customersResult, productsResult] = await Promise.all([
      fetchData('customers', {
        columns: 'id',
        count: true
      }),
      fetchData('products', {
        filters: {
          isActive: true
        },
        columns: 'id, stock_quantity, cost_price, price',
        count: true
      })
    ]);

    const totalCustomers = customersResult.count || 0;
    const products = productsResult.data || [];

    // Calculate inventory turnover
    const totalInventoryValue = products.reduce((sum, product) =>
      sum + ((product.stockQuantity || 0) * (parseFloat(product.costPrice) || 0)), 0);

    const inventoryTurnover = totalInventoryValue > 0 ? totalRevenue / totalInventoryValue : 0;

    // Calculate profit margin (simplified estimation)
    const totalCost = orders.reduce((sum, order) =>
      sum + (parseFloat(order.subtotal) || 0) * 0.7, 0); // Estimated 70% cost ratio

    const profitMargin = totalRevenue > 0 ? ((totalRevenue - totalCost) / totalRevenue) * 100 : 0;

    return {
      success: true,
      data: {
        totalRevenue,
        totalOrders,
        averageOrderValue,
        conversionRate: totalCustomers > 0 ? (uniqueCustomers / totalCustomers * 100) : 0,
        inventoryTurnover,
        customerRetention: uniqueCustomers > 0 ? (uniqueCustomers / totalOrders * 100) : 0,
        profitMargin,
        timeRange,
        period: {
          startDate: startDate.toISOString(),
          endDate: new Date().toISOString(),
          days
        }
      }
    };

  } catch (error) {
    console.error('Error in getAnalyticsOverview:', error);
    return {
      success: false,
      error: error.message,
      data: {
        totalRevenue: 0,
        totalOrders: 0,
        averageOrderValue: 0,
        conversionRate: 0,
        inventoryTurnover: 0,
        customerRetention: 0,
        profitMargin: 0
      }
    };
  }
}

/**
 * Lấy danh sách sản phẩm bán chạy
 * @param {string} timeRange - Khoảng thời gian
 * @param {number} limit - Số lượng sản phẩm
 * @returns {Promise<Object>} Best selling products data
 */
export async function getBestSellingProducts(timeRange = '30d', limit = 10) {
  try {
    const timeRanges = {
      '7d': 7,
      '30d': 30,
      '90d': 90,
      '1y': 365
    };

    const days = timeRanges[timeRange] || 30;
    const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);

    // Fetch order items
    const orderItemsResult = await fetchData('order_items', {
      filters: {},
      columns: 'product_id, name, quantity, unit_price, total_price, order_id'
    });

    if (!orderItemsResult.success) {
      throw new Error('Failed to fetch order items');
    }

    const orderItems = orderItemsResult.data || [];

    // Fetch valid orders trong khoảng thời gian
    const ordersResult = await fetchData('orders', {
      filters: {
        createdAt: {
          gte: startDate.toISOString()
        },
        status: {
          operator: 'in',
          value: ['completed', 'delivered', 'paid']
        }
      },
      columns: 'id'
    });

    if (!ordersResult.success) {
      throw new Error('Failed to fetch orders');
    }

    const validOrderIds = new Set(ordersResult.data.map(order => order.id));

    // Filter order items by valid orders
    const filteredOrderItems = orderItems.filter(item => validOrderIds.has(item.orderId));

    // Group by product and calculate totals
    const productSales = {};
    filteredOrderItems.forEach(item => {
      const productId = item.productId;
      if (!productSales[productId]) {
        productSales[productId] = {
          id: productId,
          name: item.name,
          totalSold: 0,
          revenue: 0,
          category: 'N/A'
        };
      }
      productSales[productId].totalSold += item.quantity || 0;
      productSales[productId].revenue += parseFloat(item.totalPrice) || 0;
    });

    // Fetch product categories
    const productIds = Object.keys(productSales);
    if (productIds.length > 0) {
      const productsResult = await fetchData('products', {
        filters: {
          id: {
            operator: 'in',
            value: productIds
          }
        },
        columns: 'id, category_id'
      });

      if (productsResult.success) {
        const productCategories = {};
        productsResult.data.forEach(product => {
          productCategories[product.id] = product.categoryId;
        });

        // Fetch categories
        const categoryIds = [...new Set(Object.values(productCategories).filter(Boolean))];
        if (categoryIds.length > 0) {
          const categoriesResult = await fetchData('product_categories', {
            filters: {
              id: {
                operator: 'in',
                value: categoryIds
              }
            },
            columns: 'id, name'
          });

          if (categoriesResult.success) {
            const categoryNames = {};
            categoriesResult.data.forEach(category => {
              categoryNames[category.id] = category.name;
            });

            // Update product sales with category names
            Object.keys(productSales).forEach(productId => {
              const categoryId = productCategories[productId];
              productSales[productId].category = categoryId ? (categoryNames[categoryId] || 'N/A') : 'N/A';
            });
          }
        }
      }
    }

    // Convert to array and sort by revenue
    const sortedProducts = Object.values(productSales)
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, limit);

    return {
      success: true,
      data: sortedProducts
    };

  } catch (error) {
    console.error('Error in getBestSellingProducts:', error);
    return {
      success: false,
      error: error.message,
      data: []
    };
  }
}

/**
 * Lấy dữ liệu time series cho biểu đồ
 * @param {string} metric - Loại metric (revenue, orders, customers, inventory)
 * @param {string} timeRange - Khoảng thời gian
 * @returns {Promise<Object>} Time series data
 */
export async function getTimeSeriesData(metric = 'revenue', timeRange = '30d') {
  try {
    const timeRanges = {
      '7d': 7,
      '30d': 30,
      '90d': 90,
      '1y': 365
    };

    const days = timeRanges[timeRange] || 30;

    // Generate date labels
    const categories = [];
    const dataPoints = [];

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      categories.push(date.toLocaleDateString('vi-VN', {
        month: 'short',
        day: 'numeric'
      }));

      // Calculate start and end of day for filtering
      const dayStart = new Date(date);
      dayStart.setHours(0, 0, 0, 0);
      const dayEnd = new Date(date);
      dayEnd.setHours(23, 59, 59, 999);

      // Fetch data for this specific day based on metric type
      let value = 0;

      if (metric === 'revenue' || metric === 'orders') {
        const ordersResult = await fetchData('orders', {
          filters: {
            createdAt: {
              gte: dayStart.toISOString(),
              lte: dayEnd.toISOString()
            },
            status: {
              operator: 'in',
              value: ['completed', 'delivered', 'paid']
            }
          },
          columns: 'id, total_amount'
        });

        if (ordersResult.success) {
          const orders = ordersResult.data || [];
          if (metric === 'revenue') {
            value = orders.reduce((sum, order) => sum + (parseFloat(order.totalAmount) || 0), 0);
          } else {
            value = orders.length;
          }
        }
      } else if (metric === 'customers') {
        const customersResult = await fetchData('customers', {
          filters: {
            createdAt: {
              gte: dayStart.toISOString(),
              lte: dayEnd.toISOString()
            }
          },
          columns: 'id'
        });

        if (customersResult.success) {
          value = customersResult.data?.length || 0;
        }
      }

      dataPoints.push(value);
    }

    return {
      success: true,
      data: {
        categories,
        series: [
          {
            name: getMetricName(metric),
            data: dataPoints
          }
        ]
      }
    };

  } catch (error) {
    console.error('Error in getTimeSeriesData:', error);
    return {
      success: false,
      error: error.message,
      data: {
        categories: [],
        series: []
      }
    };
  }
}

/**
 * Helper function để lấy tên metric
 */
function getMetricName(metric) {
  switch (metric) {
    case 'revenue':
      return 'Doanh thu';
    case 'orders':
      return 'Đơn hàng';
    case 'customers':
      return 'Khách hàng mới';
    case 'inventory':
      return 'Tồn kho';
    default:
      return 'Dữ liệu';
  }
}

/**
 * Lấy chỉ số hiệu suất chi tiết từ database
 * @param {string} timeRange - Khoảng thời gian
 * @returns {Promise<Object>} Performance metrics data
 */
export async function getPerformanceMetrics(timeRange = '30d') {
  try {
    const timeRanges = {
      '7d': 7,
      '30d': 30,
      '90d': 90,
      '1y': 365
    };

    const days = timeRanges[timeRange] || 30;
    const currentPeriodStart = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
    const previousPeriodStart = new Date(Date.now() - (days * 2) * 24 * 60 * 60 * 1000);

    // Fetch current period data
    const [currentOrders, previousOrders, totalCustomers, totalProducts] = await Promise.all([
      // Current period orders
      fetchData('orders', {
        filters: {
          createdAt: {
            gte: currentPeriodStart.toISOString()
          },
          status: {
            operator: 'in',
            value: ['completed', 'delivered', 'paid']
          }
        },
        columns: 'id, total_amount, subtotal, customer_id, created_at'
      }),

      // Previous period orders for comparison
      fetchData('orders', {
        filters: {
          createdAt: {
            gte: previousPeriodStart.toISOString(),
            lt: currentPeriodStart.toISOString()
          },
          status: {
            operator: 'in',
            value: ['completed', 'delivered', 'paid']
          }
        },
        columns: 'id, total_amount, subtotal, customer_id'
      }),

      // Total customers for conversion rate
      fetchData('customers', {
        columns: 'id, created_at',
        count: true
      }),

      // Products for inventory analysis
      fetchData('products', {
        filters: {
          isActive: true
        },
        columns: 'id, stock_quantity, cost_price, price'
      })
    ]);

    if (!currentOrders.success || !previousOrders.success) {
      throw new Error('Failed to fetch orders data');
    }

    const currentOrdersData = currentOrders.data || [];
    const previousOrdersData = previousOrders.data || [];
    const productsData = totalProducts.success ? totalProducts.data || [] : [];

    // Calculate current period metrics
    const currentRevenue = currentOrdersData.reduce((sum, order) =>
      sum + (parseFloat(order.totalAmount) || 0), 0);
    const currentOrderCount = currentOrdersData.length;
    const currentAOV = currentOrderCount > 0 ? currentRevenue / currentOrderCount : 0;
    const currentUniqueCustomers = new Set(currentOrdersData.map(order => order.customerId).filter(Boolean)).size;

    // Calculate previous period metrics for comparison
    const previousRevenue = previousOrdersData.reduce((sum, order) =>
      sum + (parseFloat(order.totalAmount) || 0), 0);
    const previousOrderCount = previousOrdersData.length;
    const previousAOV = previousOrderCount > 0 ? previousRevenue / previousOrderCount : 0;
    const previousUniqueCustomers = new Set(previousOrdersData.map(order => order.customerId).filter(Boolean)).size;

    // Calculate growth rates
    const revenueGrowth = previousRevenue > 0 ? ((currentRevenue - previousRevenue) / previousRevenue) * 100 : 0;
    const orderGrowth = previousOrderCount > 0 ? ((currentOrderCount - previousOrderCount) / previousOrderCount) * 100 : 0;
    const aovGrowth = previousAOV > 0 ? ((currentAOV - previousAOV) / previousAOV) * 100 : 0;
    const customerGrowth = previousUniqueCustomers > 0 ? ((currentUniqueCustomers - previousUniqueCustomers) / previousUniqueCustomers) * 100 : 0;

    // Calculate conversion rate
    const totalCustomersCount = totalCustomers.count || 0;
    const conversionRate = totalCustomersCount > 0 ? (currentUniqueCustomers / totalCustomersCount) * 100 : 0;

    // Calculate inventory metrics
    const totalInventoryValue = productsData.reduce((sum, product) =>
      sum + ((product.stockQuantity || 0) * (parseFloat(product.costPrice) || 0)), 0);
    const inventoryTurnover = totalInventoryValue > 0 ? currentRevenue / totalInventoryValue : 0;

    // Calculate profit margin (simplified)
    const totalCost = currentOrdersData.reduce((sum, order) =>
      sum + (parseFloat(order.subtotal) || 0) * 0.7, 0); // Estimated 70% cost ratio
    const profitMargin = currentRevenue > 0 ? ((currentRevenue - totalCost) / currentRevenue) * 100 : 0;

    // Calculate customer retention (simplified)
    const customerRetention = currentUniqueCustomers > 0 ? (currentUniqueCustomers / currentOrderCount) * 100 : 0;

    return {
      success: true,
      data: {
        // Current metrics
        totalRevenue: currentRevenue,
        totalOrders: currentOrderCount,
        averageOrderValue: currentAOV,
        conversionRate,
        inventoryTurnover,
        profitMargin,
        customerRetention,

        // Growth rates
        revenueGrowth,
        orderGrowth,
        aovGrowth,
        customerGrowth,

        // Period info
        timeRange,
        currentPeriod: {
          startDate: currentPeriodStart.toISOString(),
          endDate: new Date().toISOString(),
          days
        },
        previousPeriod: {
          startDate: previousPeriodStart.toISOString(),
          endDate: currentPeriodStart.toISOString(),
          days
        }
      }
    };

  } catch (error) {
    console.error('Error in getPerformanceMetrics:', error);
    return {
      success: false,
      error: error.message,
      data: {
        totalRevenue: 0,
        totalOrders: 0,
        averageOrderValue: 0,
        conversionRate: 0,
        inventoryTurnover: 0,
        profitMargin: 0,
        customerRetention: 0,
        revenueGrowth: 0,
        orderGrowth: 0,
        aovGrowth: 0,
        customerGrowth: 0
      }
    };
  }
}

/**
 * Lấy danh sách sản phẩm sắp hết hàng
 * @param {number} threshold - Ngưỡng cảnh báo
 * @param {number} limit - Số lượng sản phẩm
 * @returns {Promise<Object>} Low stock products data
 */
export async function getLowStockProducts(threshold = 10, limit = 10) {
  try {
    // Fetch products với low stock
    const productsResult = await fetchData('products', {
      filters: {
        stockQuantity: {
          operator: 'lte',
          value: threshold
        },
        isActive: true,
        trackInventory: true
      },
      columns: 'id, name, sku, stock_quantity, type',
      orderBy: 'stockQuantity',
      ascending: true,
      limit: limit * 2 // Lấy nhiều hơn để có thể filter variants
    });

    if (!productsResult.success) {
      throw new Error('Failed to fetch products');
    }

    const products = productsResult.data || [];

    // Fetch variants cho products có variants
    const variantProducts = products.filter(product => product.type === 'variable');
    let variantStockData = [];

    if (variantProducts.length > 0) {
      const variantsResult = await fetchData('product_variants', {
        filters: {
          productId: {
            operator: 'in',
            value: variantProducts.map(p => p.id)
          }
        },
        columns: 'id, product_id, name, sku, stock_quantity'
      });

      if (variantsResult.success) {
        variantStockData = variantsResult.data
          .filter(variant => (variant.stockQuantity || 0) <= threshold)
          .map(variant => ({
            id: `variant-${variant.id}`,
            name: `${variantProducts.find(p => p.id === variant.productId)?.name || 'Unknown'} - ${variant.name}`,
            sku: variant.sku,
            stockQuantity: variant.stockQuantity || 0,
            lowStockThreshold: threshold,
            isVariant: true,
            parentProductId: variant.productId
          }));
      }
    }

    // Combine simple products and variants
    const simpleProducts = products
      .filter(product => product.type === 'simple')
      .map(product => ({
        id: product.id,
        name: product.name,
        sku: product.sku,
        stockQuantity: product.stockQuantity || 0,
        lowStockThreshold: threshold,
        isVariant: false
      }));

    const allLowStockProducts = [...simpleProducts, ...variantStockData]
      .sort((a, b) => a.stockQuantity - b.stockQuantity)
      .slice(0, limit);

    return {
      success: true,
      data: allLowStockProducts
    };

  } catch (error) {
    console.error('Error in getLowStockProducts:', error);
    return {
      success: false,
      error: error.message,
      data: []
    };
  }
}
