import { NextResponse } from 'next/server';

import { withTenantAuth, validateRequestBody } from 'src/utils/server-auth';

// =====================================================
// VÍ DỤ API ROUTE ĐÃ ĐƯỢC TỐI ƯU VỚI FULL RLS TRUST
// =====================================================
// ✅ BEFORE: Logic cũ với manual tenant validation
// ❌ const validation = await validateTenantId(request, tenantId);
// ❌ .eq('tenant_id', tenantId) // Manual tenant filtering
// ❌ productData.tenant_id = tenantId; // Manual tenant setting
//
// ✅ AFTER: Logic mới với full RLS trust
// ✅ const validation = await validateRequestBody(request);
// ✅ Không cần .eq('tenant_id', tenantId) - RLS tự động filter
// ✅ Không cần set tenant_id - Triggers tự động set

/**
 * ✅ OPTIMIZED GET: Lấy danh sách với full RLS trust
 */
export const GET = withTenantAuth(async (request, { userId }) => {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '10', 10);
    const search = searchParams.get('search') || '';

    // ✅ OPTIMIZED: RLS policies tự động filter theo tenant
    // Không cần .eq('tenant_id', tenantId)
    let query = request.supabase
      .from('products')
      .select('*', { count: 'exact' });

    // Thêm search filter
    if (search) {
      query = query.or(`name.ilike.%${search}%,sku.ilike.%${search}%`);
    }

    // Pagination
    const offset = (page - 1) * limit;
    query = query.range(offset, offset + limit - 1);

    const { data, error, count } = await query;

    if (error) {
      console.error('Error fetching products:', error);
      return NextResponse.json({ error: 'Failed to fetch products' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      data,
      pagination: {
        page,
        limit,
        total: count,
        totalPages: Math.ceil(count / limit),
      },
    });

  } catch (error) {
    console.error('Error in products GET route:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
});

/**
 * ✅ OPTIMIZED POST: Tạo mới với full RLS trust
 */
export const POST = withTenantAuth(async (request, { userId }) => {
  try {
    // ✅ OPTIMIZED: Chỉ validate JSON format, không cần tenant validation
    const validation = await validateRequestBody(request);
    if (!validation.valid) {
      return NextResponse.json({ error: validation.error }, { status: validation.status });
    }

    const productData = validation.body;

    // ✅ OPTIMIZED: Không cần set tenant_id
    // Trigger auto_set_tenant_id_simple() sẽ tự động set
    productData.created_by = userId;

    // Kiểm tra SKU unique (RLS sẽ tự động scope trong tenant)
    if (productData.sku) {
      const { data: existingProduct } = await request.supabase
        .from('products')
        .select('id')
        .eq('sku', productData.sku)
        .single();

      if (existingProduct) {
        return NextResponse.json(
          { error: 'SKU đã tồn tại trong hệ thống' },
          { status: 409 }
        );
      }
    }

    // ✅ OPTIMIZED: Insert không cần tenant_id
    // RLS policy sẽ validate, trigger sẽ auto-set
    const { data, error } = await request.supabase
      .from('products')
      .insert([productData])
      .select()
      .single();

    if (error) {
      console.error('Error creating product:', error);
      return NextResponse.json({ error: 'Failed to create product' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      data,
      message: 'Sản phẩm đã được tạo thành công',
    }, { status: 201 });

  } catch (error) {
    console.error('Error in products POST route:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
});

/**
 * ✅ OPTIMIZED PUT: Cập nhật với full RLS trust
 */
export const PUT = withTenantAuth(async (request, { userId }) => {
  try {
    const validation = await validateRequestBody(request);
    if (!validation.valid) {
      return NextResponse.json({ error: validation.error }, { status: validation.status });
    }

    const { id, ...updateData } = validation.body;

    if (!id) {
      return NextResponse.json({ error: 'Product ID is required' }, { status: 400 });
    }

    // ✅ OPTIMIZED: Không cần .eq('tenant_id', tenantId)
    // RLS policy sẽ tự động filter và validate tenant access
    updateData.updated_by = userId;
    updateData.updated_at = new Date().toISOString();

    const { data, error } = await request.supabase
      .from('products')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating product:', error);
      return NextResponse.json({ error: 'Failed to update product' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      data,
      message: 'Sản phẩm đã được cập nhật thành công',
    });

  } catch (error) {
    console.error('Error in products PUT route:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
});

/**
 * ✅ OPTIMIZED DELETE: Xóa với full RLS trust
 */
export const DELETE = withTenantAuth(async (request, { userId }) => {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({ error: 'Product ID is required' }, { status: 400 });
    }

    // ✅ OPTIMIZED: RLS policy tự động validate tenant access
    const { data, error } = await request.supabase
      .from('products')
      .delete()
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error deleting product:', error);
      return NextResponse.json({ error: 'Failed to delete product' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      data,
      message: 'Sản phẩm đã được xóa thành công',
    });

  } catch (error) {
    console.error('Error in products DELETE route:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
});

// =====================================================
// HƯỚNG DẪN TỐI ƯU CHO CÁC API ROUTES KHÁC
// =====================================================
// 1. Thay withTenantAuth(async (request, { tenantId, userId }) => {
//    Thành: withTenantAuth(async (request, { userId }) => {
//
// 2. Thay validateTenantId(request, tenantId)
//    Thành: validateRequestBody(request)
//
// 3. Loại bỏ .eq('tenant_id', tenantId) trong queries
//    RLS policies sẽ tự động filter
//
// 4. Loại bỏ productData.tenant_id = tenantId
//    Triggers sẽ tự động set tenant_id
//
// 5. Trust RLS policies hoàn toàn cho tenant security
//    Database-level security > Client-level validation
