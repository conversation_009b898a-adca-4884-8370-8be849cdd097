import { FACEBOOK_CONFIG } from './facebook-config';

/**
 * Facebook Configuration Validation
 * Kiểm tra và validate cấu hình Facebook integration
 */

export class FacebookConfigValidator {
  /**
   * Validate toàn bộ cấu hình Facebook
   */
  static validateConfig() {
    const errors = [];
    const warnings = [];

    // Kiểm tra environment variables
    if (!FACEBOOK_CONFIG.APP_ID) {
      errors.push('NEXT_PUBLIC_FACEBOOK_APP_ID is required');
    }

    if (!FACEBOOK_CONFIG.APP_SECRET) {
      errors.push('FACEBOOK_APP_SECRET is required');
    }

    if (!FACEBOOK_CONFIG.WEBHOOK_VERIFY_TOKEN) {
      errors.push('FACEBOOK_WEBHOOK_VERIFY_TOKEN is required');
    }

    // Kiểm tra API version
    if (!FACEBOOK_CONFIG.API_VERSION.startsWith('v')) {
      warnings.push('API_VERSION should start with "v" (e.g., v23.0)');
    }

    // Kiểm tra permissions
    if (!Array.isArray(FACEBOOK_CONFIG.PERMISSIONS) || FACEBOOK_CONFIG.PERMISSIONS.length === 0) {
      errors.push('PERMISSIONS array is required and cannot be empty');
    }

    // Kiểm tra required permissions
    const requiredPermissions = ['pages_messaging', 'pages_manage_posts'];
    const missingPermissions = requiredPermissions.filter(
      perm => !FACEBOOK_CONFIG.PERMISSIONS.includes(perm)
    );

    if (missingPermissions.length > 0) {
      warnings.push(`Missing recommended permissions: ${missingPermissions.join(', ')}`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      config: {
        appId: FACEBOOK_CONFIG.APP_ID ? '✓ Configured' : '✗ Missing',
        appSecret: FACEBOOK_CONFIG.APP_SECRET ? '✓ Configured' : '✗ Missing',
        webhookToken: FACEBOOK_CONFIG.WEBHOOK_VERIFY_TOKEN ? '✓ Configured' : '✗ Missing',
        apiVersion: FACEBOOK_CONFIG.API_VERSION,
        permissionsCount: FACEBOOK_CONFIG.PERMISSIONS.length
      }
    };
  }

  /**
   * Validate permissions được grant từ Facebook
   */
  static validateGrantedPermissions(grantedPermissions = []) {
    const required = FACEBOOK_CONFIG.PERMISSIONS;
    const missing = required.filter(perm => !grantedPermissions.includes(perm));
    const extra = grantedPermissions.filter(perm => !required.includes(perm));

    return {
      isValid: missing.length === 0,
      missing,
      extra,
      granted: grantedPermissions.filter(perm => required.includes(perm)),
      coverage: ((required.length - missing.length) / required.length * 100).toFixed(1)
    };
  }

  /**
   * Validate Facebook API response
   */
  static validateApiResponse(response) {
    if (!response) {
      return { isValid: false, error: 'No response received' };
    }

    if (response.error) {
      return {
        isValid: false,
        error: response.error.message || 'Unknown API error',
        errorCode: response.error.code,
        errorType: response.error.type
      };
    }

    return { isValid: true };
  }

  /**
   * Validate token format
   */
  static validateToken(token) {
    if (!token || typeof token !== 'string') {
      return { isValid: false, error: 'Token must be a non-empty string' };
    }

    // Facebook tokens are typically long strings
    if (token.length < 50) {
      return { isValid: false, error: 'Token appears to be too short' };
    }

    // Check for common token patterns
    const patterns = {
      userToken: /^[A-Za-z0-9_-]+$/,
      pageToken: /^[A-Za-z0-9_-]+$/
    };

    const isValidFormat = patterns.userToken.test(token) || patterns.pageToken.test(token);

    return {
      isValid: isValidFormat,
      error: isValidFormat ? null : 'Token format appears invalid',
      length: token.length
    };
  }

  /**
   * Generate configuration report
   */
  static generateReport() {
    const configValidation = this.validateConfig();
    
    const report = {
      timestamp: new Date().toISOString(),
      status: configValidation.isValid ? 'VALID' : 'INVALID',
      summary: {
        errors: configValidation.errors.length,
        warnings: configValidation.warnings.length,
        apiVersion: FACEBOOK_CONFIG.API_VERSION,
        permissionsConfigured: FACEBOOK_CONFIG.PERMISSIONS.length
      },
      details: configValidation,
      recommendations: []
    };

    // Add recommendations
    if (configValidation.errors.length > 0) {
      report.recommendations.push('Fix configuration errors before proceeding');
    }

    if (configValidation.warnings.length > 0) {
      report.recommendations.push('Review and address configuration warnings');
    }

    if (FACEBOOK_CONFIG.PERMISSIONS.length < 5) {
      report.recommendations.push('Consider adding more permissions for full functionality');
    }

    return report;
  }
}

/**
 * Quick validation function for development
 */
export function validateFacebookConfig() {
  return FacebookConfigValidator.validateConfig();
}

/**
 * Log configuration status to console (development only)
 */
export function logConfigStatus() {
  if (process.env.NODE_ENV === 'development') {
    const validation = FacebookConfigValidator.validateConfig();
    
    console.group('🔧 Facebook Configuration Status');
    console.log('Valid:', validation.isValid ? '✅' : '❌');
    
    if (validation.errors.length > 0) {
      console.error('Errors:', validation.errors);
    }
    
    if (validation.warnings.length > 0) {
      console.warn('Warnings:', validation.warnings);
    }
    
    console.log('Config:', validation.config);
    console.groupEnd();
  }
}

// Auto-validate on import in development
if (process.env.NODE_ENV === 'development') {
  logConfigStatus();
}
