import { NextResponse } from 'next/server';

import { snakeToCamelObject } from 'src/utils/format-data/case-converter';

/**
 * API proxy để lấy danh sách kênh từ Mooly API
 * @param {Request} request - <PERSON><PERSON><PERSON> cầu HTTP
 * @returns {Promise<NextResponse>} - <PERSON><PERSON><PERSON> hồi HTTP
 */
export async function GET(request) {
  try {
    // Lấy thông tin từ URL params
    const { searchParams } = new URL(request.url);
    const accountId = searchParams.get('accountId');
    const token = searchParams.get('token');

    // Kiểm tra dữ liệu đầu vào
    if (!accountId || !token) {
      return NextResponse.json(
        { success: false, error: 'Account ID and token are required', data: [] },
        { status: 400 }
      );
    }

    // Gọi API Mooly từ server
    const response = await fetch(`https://app.mooly.vn/api/v1/accounts/${accountId}/inboxes`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        api_access_token: token,
      },
    });
    // Xử lý lỗi từ API
    if (!response.ok) {
      let errorData;
      try {
        errorData = await response.json();
       
      } catch (e) {
        errorData = { message: response.statusText };
      }

      return NextResponse.json(
        {
          success: false,
          error: errorData.message || 'Failed to fetch channels',
          data: [],
        },
        { status: response.status }
      );
    }

    // Trả về dữ liệu thành công
    const data = await response.json();
    return NextResponse.json({ success: true, error: null, data: snakeToCamelObject(data) });
  } catch (error) {
    console.error('Error in Mooly proxy:', error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || 'Failed to fetch channels',
        data: [],
      },
      { status: 500 }
    );
  }
}
