'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import Button from '@mui/material/Button';
import LoadingButton from '@mui/lab/LoadingButton';
import TextField from '@mui/material/TextField';
import FormControl from '@mui/material/FormControl';
import InputLabel from '@mui/material/InputLabel';
import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Chip from '@mui/material/Chip';

import { Form, Field } from 'src/components/hook-form';
import { Iconify } from 'src/components/iconify';
import { toast } from 'src/components/snackbar';

import { addNewStage } from 'src/actions/mooly-chatbot/unified-workflow-service';

// =====================================================
// VALIDATION SCHEMA
// =====================================================

const QuickAddStageSchema = z.object({
  name: z
    .string()
    .min(1, 'Tên giai đoạn là bắt buộc')
    .max(50, 'Tên giai đoạn không được quá 50 ký tự'),
  description: z
    .string()
    .max(200, 'Mô tả không được quá 200 ký tự')
    .optional(),
  color: z
    .string()
    .min(1, 'Màu sắc là bắt buộc'),
  icon: z
    .string()
    .min(1, 'Icon là bắt buộc'),
});

// =====================================================
// PREDEFINED OPTIONS
// =====================================================

const COLOR_OPTIONS = [
  { value: '#e3f2fd', label: 'Xanh nhạt', color: 'info' },
  { value: '#fff3e0', label: 'Cam nhạt', color: 'warning' },
  { value: '#f3e5f5', label: 'Tím nhạt', color: 'secondary' },
  { value: '#e8f5e8', label: 'Xanh lá nhạt', color: 'success' },
  { value: '#ffebee', label: 'Đỏ nhạt', color: 'error' },
  { value: '#fce4ec', label: 'Hồng nhạt', color: 'secondary' },
  { value: '#e0f2f1', label: 'Xanh ngọc nhạt', color: 'success' },
  { value: '#fff8e1', label: 'Vàng nhạt', color: 'warning' },
];

const ICON_OPTIONS = [
  { value: 'solar:star-bold', label: 'Ngôi sao' },
  { value: 'solar:heart-bold', label: 'Trái tim' },
  { value: 'solar:flag-bold', label: 'Cờ' },
  { value: 'solar:award-bold', label: 'Giải thưởng' },
  { value: 'solar:target-bold', label: 'Mục tiêu' },
  { value: 'solar:rocket-bold', label: 'Tên lửa' },
  { value: 'solar:crown-bold', label: 'Vương miện' },
  { value: 'solar:shield-check-bold', label: 'Khiên bảo vệ' },
  { value: 'solar:diamond-bold', label: 'Kim cương' },
  { value: 'solar:gift-bold', label: 'Quà tặng' },
];

// =====================================================
// COMPONENT
// =====================================================

export default function QuickAddStageDialog({ open, onClose, onSuccess, insertAfterIndex = -1 }) {
  const [loading, setLoading] = useState(false);

  const defaultValues = {
    name: '',
    description: '',
    color: '#e3f2fd',
    icon: 'solar:star-bold',
  };

  const methods = useForm({
    resolver: zodResolver(QuickAddStageSchema),
    defaultValues,
  });

  const { handleSubmit, reset, watch, setValue } = methods;
  const watchedColor = watch('color');
  const watchedIcon = watch('icon');

  const onSubmit = handleSubmit(async (data) => {
    setLoading(true);
    try {
      const result = await addNewStage(null, data, insertAfterIndex); // null = global workflow

      if (result.success) {
        toast.success('Đã thêm giai đoạn mới thành công!');
        reset();
        onSuccess?.(result.data);
        onClose();
      } else {
        toast.error(result.error?.message || 'Không thể thêm giai đoạn mới');
      }
    } catch (error) {
      console.error('Error adding stage:', error);
      toast.error('Có lỗi xảy ra khi thêm giai đoạn mới');
    } finally {
      setLoading(false);
    }
  });

  const handleClose = () => {
    reset();
    onClose();
  };

  return (
    <Dialog 
      open={open} 
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: { borderRadius: 2 }
      }}
    >
      <DialogTitle>
        <Box display="flex" alignItems="center" gap={1}>
          <Iconify icon="solar:add-circle-bold" width={24} />
          Thêm Giai Đoạn Mới
        </Box>
      </DialogTitle>

      <Form methods={methods} onSubmit={onSubmit}>
        <DialogContent dividers>
          <Box display="flex" flexDirection="column" gap={3}>
            {/* Tên giai đoạn */}
            <Field.Text
              name="name"
              label="Tên giai đoạn"
              placeholder="VD: Đang xử lý, Chờ phê duyệt..."
              required
              helperText="Tên ngắn gọn để mô tả giai đoạn này"
            />

            {/* Mô tả */}
            <Field.Text
              name="description"
              label="Mô tả"
              placeholder="Mô tả chi tiết về giai đoạn này..."
              multiline
              rows={2}
              helperText="Mô tả chi tiết sẽ giúp team hiểu rõ hơn về giai đoạn"
            />

            {/* Màu sắc */}
            <FormControl fullWidth>
              <InputLabel>Màu sắc</InputLabel>
              <Select
                value={watchedColor}
                onChange={(e) => setValue('color', e.target.value)}
                label="Màu sắc"
              >
                {COLOR_OPTIONS.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    <Box display="flex" alignItems="center" gap={1}>
                      <Chip
                        size="small"
                        sx={{
                          backgroundColor: option.value,
                          width: 24,
                          height: 16,
                          '& .MuiChip-label': { display: 'none' }
                        }}
                      />
                      {option.label}
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            {/* Icon */}
            <FormControl fullWidth>
              <InputLabel>Icon</InputLabel>
              <Select
                value={watchedIcon}
                onChange={(e) => setValue('icon', e.target.value)}
                label="Icon"
              >
                {ICON_OPTIONS.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    <Box display="flex" alignItems="center" gap={1}>
                      <Iconify icon={option.value} width={20} />
                      {option.label}
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            {/* Preview */}
            <Box>
              <Typography variant="subtitle2" gutterBottom color="text.secondary">
                Xem trước:
              </Typography>
              <Chip
                icon={<Iconify icon={watchedIcon} width={16} />}
                label={watch('name') || 'Tên giai đoạn'}
                sx={{
                  backgroundColor: watchedColor,
                  color: 'text.primary',
                  fontWeight: 500,
                }}
              />
            </Box>
          </Box>
        </DialogContent>

        <DialogActions sx={{ px: 3, py: 2 }}>
          <Button onClick={handleClose} disabled={loading}>
            Hủy
          </Button>
          <LoadingButton
            type="submit"
            variant="contained"
            loading={loading}
            startIcon={<Iconify icon="solar:add-circle-bold" />}
          >
            Thêm Giai Đoạn
          </LoadingButton>
        </DialogActions>
      </Form>
    </Dialog>
  );
} 