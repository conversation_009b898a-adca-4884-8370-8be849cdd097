import Box from '@mui/material/Box';
import Link from '@mui/material/Link';

// ----------------------------------------------------------------------

export function SignUpTerms({ sx, ...other }) {
  return (
    <Box
      component="span"
      sx={[
        () => ({
          mt: 3,
          display: 'block',
          textAlign: 'center',
          typography: 'caption',
          color: 'text.secondary',
        }),
        ...(Array.isArray(sx) ? sx : [sx]),
      ]}
      {...other}
    >
      {'Bằng cách đăng ký, tôi đồng ý với '}
      <Link underline="always" color="text.primary">
        Đi<PERSON>u kho<PERSON>n dịch vụ
      </Link>
      {' và '}
      <Link underline="always" color="text.primary">
        <PERSON><PERSON><PERSON> sách bảo mật
      </Link>
      .
    </Box>
  );
}
