'use client';

import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';

import { ConfirmDialog } from 'src/components/custom-dialog';

// ==========================================
// 📦 INVENTORY CONFIRM DIALOG
// ==========================================

export function InventoryConfirmDialog({
  open,
  onClose,
  onConfirm,
  title = 'Xác nhận cập nhật tồn kho',
  message = 'Bạn có muốn cập nhật lại số lượng tồn kho khi hoàn tiền không?'
}) {
  const handleConfirmWithInventory = () => {
    onConfirm(true);
    onClose();
  };

  const handleConfirmWithoutInventory = () => {
    onConfirm(false);
    onClose();
  };

  return (
    <ConfirmDialog
      open={open}
      onClose={onClose}
      title={title}
      content={
        <Stack spacing={2}>
          <Typography>
            {message}
          </Typography>
          <Box component="ul" sx={{ pl: 2, m: 0 }}>
            <Typography component="li" variant="body2" color="text.secondary">
              ✅ <strong>Có</strong>: Sẽ hoàn lại số lượng sản phẩm vào kho
            </Typography>
            <Typography component="li" variant="body2" color="text.secondary">
              ❌ <strong>Không</strong>: Chỉ cập nhật trạng thái đơn hàng, không thay đổi tồn kho
            </Typography>
          </Box>
        </Stack>
      }
      action={
        <Stack direction="row" spacing={1}>
          <Button
            variant="outlined"
            onClick={handleConfirmWithoutInventory}
          >
            Không cập nhật kho
          </Button>
          <Button
            variant="contained"
            color="primary"
            onClick={handleConfirmWithInventory}
          >
            Có, cập nhật kho
          </Button>
        </Stack>
      }
    />
  );
}
