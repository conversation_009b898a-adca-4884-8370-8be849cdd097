/**
 * Instagram Direct API Webhook Handler
 * Handles webhooks from Instagram Direct API (2025) - Not through Facebook
 */

import { NextResponse } from 'next/server';
import crypto from 'crypto';
import { createAdminClient } from 'src/utils/supabase/server';

// Environment variables for Instagram Direct API
const INSTAGRAM_APP_SECRET = process.env.INSTAGRAM_APP_SECRET;
const VERIFY_TOKEN = process.env.INSTAGRAM_WEBHOOK_VERIFY_TOKEN || process.env.FACEBOOK_WEBHOOK_VERIFY_TOKEN || '<EMAIL>';

/**
 * GET - Instagram Direct API Webhook verification
 * Instagram calls this to verify webhook setup
 */
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const mode = searchParams.get('hub.mode');
    const token = searchParams.get('hub.verify_token');
    const challenge = searchParams.get('hub.challenge');

    console.log('📨 Instagram Direct webhook verification:', { 
      mode, 
      tokenMatches: token === VERIFY_TOKEN, 
      challenge: !!challenge,
      receivedToken: token
    });

    if (mode === 'subscribe' && token === VERIFY_TOKEN) {
      console.log('✅ Instagram Direct webhook verified successfully');
      return new Response(challenge, { 
        status: 200,
        headers: { 'Content-Type': 'text/plain' }
      });
    } else {
      console.log('❌ Instagram Direct webhook verification failed');
      console.log('📋 Debug info:', {
        expectedToken: VERIFY_TOKEN,
        receivedToken: token,
        mode,
        challenge
      });
      
      return NextResponse.json({ 
        error: 'Verification failed',
        debug: {
          expected_token_start: VERIFY_TOKEN?.substring(0, 8) + '...',
          received_token_start: token?.substring(0, 8) + '...',
          mode_correct: mode === 'subscribe'
        }
      }, { status: 403 });
    }
  } catch (error) {
    console.error('❌ Error in Instagram Direct webhook verification:', error);
    return NextResponse.json({ 
      error: 'Internal Server Error',
      message: error.message
    }, { status: 500 });
  }
}

/**
 * POST - Process Instagram Direct API webhook events
 * Handles messages, comments, mentions from Instagram Direct API
 */
export async function POST(request) {
  try {
    const body = await request.text();
    const signature = request.headers.get('x-hub-signature-256');

    console.log('📨 Received Instagram Direct webhook event:', {
      hasSignature: !!signature,
      bodyLength: body?.length || 0,
      contentType: request.headers.get('content-type')
    });

    // Verify webhook signature
    if (!signature || !verifyInstagramSignature(body, signature, INSTAGRAM_APP_SECRET)) {
      console.log('❌ Invalid Instagram Direct webhook signature');
      return NextResponse.json({ error: 'Invalid signature' }, { status: 403 });
    }

    const eventData = JSON.parse(body);
    console.log('🔍 Parsed Instagram Direct webhook data:', {
      object: eventData.object,
      entryCount: eventData.entry?.length || 0
    });

    // Process the webhook event
    const result = await processInstagramDirectEvent(eventData);

    if (result.success) {
      console.log('✅ Instagram Direct webhook processed successfully');
      return NextResponse.json({ success: true }, { status: 200 });
    } else {
      console.log('⚠️ Instagram Direct webhook processing failed:', result.error);
      return NextResponse.json({ error: 'Processing failed' }, { status: 500 });
    }
  } catch (error) {
    console.error('❌ Error processing Instagram Direct webhook:', error);
    return NextResponse.json({ 
      error: 'Internal Server Error',
      message: error.message
    }, { status: 500 });
  }
}

/**
 * Verify Instagram Direct API webhook signature
 * @param {string} body - Request body
 * @param {string} signature - X-Hub-Signature-256 header
 * @param {string} appSecret - Instagram app secret
 * @returns {boolean} Is valid
 */
function verifyInstagramSignature(body, signature, appSecret) {
  try {
    if (!appSecret) {
      console.error('❌ Instagram app secret not configured');
      return false;
    }

    const expectedSignature = crypto
      .createHmac('sha256', appSecret)
      .update(body, 'utf8')
      .digest('hex');
    
    const receivedSignature = signature.replace('sha256=', '');
    
    const isValid = crypto.timingSafeEqual(
      Buffer.from(expectedSignature, 'hex'),
      Buffer.from(receivedSignature, 'hex')
    );

    if (!isValid) {
      console.log('🔍 Signature verification failed:', {
        expectedLength: expectedSignature.length,
        receivedLength: receivedSignature.length,
        expectedStart: expectedSignature.substring(0, 8),
        receivedStart: receivedSignature.substring(0, 8)
      });
    }

    return isValid;
  } catch (error) {
    console.error('❌ Error verifying Instagram signature:', error);
    return false;
  }
}

/**
 * Process Instagram Direct API webhook event
 * @param {Object} eventData - Webhook event data
 * @returns {Promise<Object>} Processing result
 */
async function processInstagramDirectEvent(eventData) {
  try {
    // Instagram Direct API uses different object types
    if (!['instagram', 'user'].includes(eventData.object)) {
      return { success: true, message: 'Not an Instagram Direct event' };
    }

    const supabase = createAdminClient();
    const results = [];

    for (const entry of eventData.entry || []) {
      const entryResult = await processInstagramDirectEntry(entry, supabase);
      results.push(entryResult);
    }

    const hasErrors = results.some(result => !result.success);
    
    return {
      success: !hasErrors,
      results,
      error: hasErrors ? 'Some entries failed to process' : null
    };
  } catch (error) {
    console.error('❌ Error processing Instagram Direct event:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Process single Instagram Direct entry
 * @param {Object} entry - Instagram entry
 * @param {Object} supabase - Supabase client
 * @returns {Promise<Object>} Processing result
 */
async function processInstagramDirectEntry(entry, supabase) {
  try {
    const instagramUserId = entry.id;
    console.log('🔄 Processing Instagram Direct entry for user:', instagramUserId);

    // Get Instagram account info from database (using new table structure)
    const { data: accountData, error: accountError } = await supabase
      .from('instagram_business_accounts')
      .select('*')
      .eq('instagram_user_id', instagramUserId)
      .eq('is_active', true)
      .single();

    if (accountError || !accountData) {
      console.log('⚠️ Instagram Direct account not found or inactive:', instagramUserId);
      return { success: true, message: 'Account not found or inactive' };
    }

    // Process messaging events
    const results = [];
    for (const messaging of entry.messaging || []) {
      const messageResult = await processInstagramDirectMessage(messaging, accountData, supabase);
      results.push(messageResult);
    }

    // Process changes (comments, etc.)
    for (const change of entry.changes || []) {
      const changeResult = await processInstagramDirectChange(change, accountData, supabase);
      results.push(changeResult);
    }

    return {
      success: true,
      accountId: instagramUserId,
      results
    };
  } catch (error) {
    console.error('❌ Error processing Instagram Direct entry:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Process Instagram Direct message
 * @param {Object} messaging - Messaging event
 * @param {Object} accountData - Account data
 * @param {Object} supabase - Supabase client
 * @returns {Promise<Object>} Processing result
 */
async function processInstagramDirectMessage(messaging, accountData, supabase) {
  try {
    console.log('📩 Processing Instagram Direct message:', messaging);

    const messageData = messaging.message;
    const sender = messaging.sender;
    const recipient = messaging.recipient;

    // Save to Instagram activity logs
    const { error: insertError } = await supabase
      .from('instagram_activity_logs')
      .insert({
        tenant_id: accountData.tenant_id,
        instagram_account_id: accountData.id,
        activity_type: 'message_received',
        external_id: messageData?.mid,
        sender_instagram_id: sender?.id,
        message_content: messageData?.text,
        raw_webhook_data: messaging,
        occurred_at: new Date(messaging.timestamp || Date.now()).toISOString(),
        processed_at: new Date().toISOString()
      });

    if (insertError) {
      console.error('❌ Error saving Instagram Direct message:', insertError);
    }

    // Check if auto-reply is enabled
    const { data: configData } = await supabase
      .from('instagram_messaging_configs')
      .select('*')
      .eq('tenant_id', accountData.tenant_id)
      .eq('instagram_account_id', accountData.id)
      .single();

    if (configData?.auto_reply_enabled) {
      // TODO: Implement auto-reply logic for Instagram Direct API
      console.log('🤖 Auto-reply enabled for Instagram Direct messages');
    }

    return { success: true, type: 'direct_message', messageId: messageData?.mid };
  } catch (error) {
    console.error('❌ Error processing Instagram Direct message:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Process Instagram Direct change event (comments, etc.)
 * @param {Object} change - Change event
 * @param {Object} accountData - Account data
 * @param {Object} supabase - Supabase client
 * @returns {Promise<Object>} Processing result
 */
async function processInstagramDirectChange(change, accountData, supabase) {
  try {
    const { field, value } = change;
    
    console.log('🔄 Processing Instagram Direct change:', { field, accountId: accountData.instagram_user_id });

    // Save activity log
    const { error: insertError } = await supabase
      .from('instagram_activity_logs')
      .insert({
        tenant_id: accountData.tenant_id,
        instagram_account_id: accountData.id,
        activity_type: field === 'comments' ? 'comment_received' : 'activity_received',
        external_id: value?.id,
        message_content: value?.text || value?.message,
        raw_webhook_data: change,
        occurred_at: new Date().toISOString(),
        processed_at: new Date().toISOString()
      });

    if (insertError) {
      console.error('❌ Error saving Instagram Direct change:', insertError);
    }

    return { success: true, type: field, changeId: value?.id };
  } catch (error) {
    console.error('❌ Error processing Instagram Direct change:', error);
    return { success: false, error: error.message };
  }
} 