'use client';

import React from 'react';
import PropTypes from 'prop-types';

import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import LoadingButton from '@mui/lab/LoadingButton';

/**
 * Component quản lý action buttons cho order form
 * Tách riêng để dễ quản lý và tái sử dụng
 */
export function OrderActionButtons({
  isLoading,
  isSubmitting,
  onSubmit,
  onCancel,
  submitText = 'Lưu đơn hàng',
  cancelText = 'Hủy'
}) {
  return (
    <Stack
      direction="row"
      spacing={2}
      justifyContent="flex-end"
      sx={{ width: '100%' }}
    >
      {onCancel && (
        <Button
          variant="outlined"
          color="inherit"
          onClick={onCancel}
          disabled={isLoading || isSubmitting}
          size="large"
        >
          {cancelText}
        </Button>
      )}

      <LoadingButton
        type="submit"
        variant="contained"
        loading={isSubmitting}
        disabled={isLoading}
        onClick={onSubmit}
        size="large"
        sx={{ minWidth: 140 }}
      >
        {submitText}
      </LoadingButton>
    </Stack>
  );
}

OrderActionButtons.propTypes = {
  isLoading: PropTypes.bool,
  isSubmitting: PropTypes.bool,
  onSubmit: PropTypes.func.isRequired,
  onCancel: PropTypes.func,
  submitText: PropTypes.string,
  cancelText: PropTypes.string,
};
