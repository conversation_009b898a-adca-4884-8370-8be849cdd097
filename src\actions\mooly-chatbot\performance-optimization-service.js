'use client';

import { executeQuery } from './supabase-utils';

/**
 * Performance Optimization Service
 * Tối ưu hiệu suất hệ thống và database
 */

// =====================================================
// UTILITY FUNCTIONS
// =====================================================

/**
 * Thực hiện SQL query với error handling chuẩn
 * @param {string} query - SQL query
 * @param {string} errorContext - Context cho error logging
 * @returns {Promise<Array>} - Kết quả query hoặc mảng rỗng
 */
async function executeQuerySafely(query, errorContext) {
  try {
    const result = await executeQuery(query);
    return result.success ? (result.data || []) : [];
  } catch (error) {
    console.error(`Error ${errorContext}:`, error);
    return [];
  }
}

/**
 * Tạo response chuẩn cho các hàm service
 * @param {boolean} success - Trạng thái thành công
 * @param {any} data - Dữ liệu trả về
 * @param {string|Error} error - Lỗi nếu có
 * @returns {Object} - Response object chuẩn
 */
function createServiceResponse(success, data = null, error = null) {
  return {
    success,
    data,
    error: error ? (typeof error === 'string' ? error : error.message) : null
  };
}

// =====================================================
// 1. PERFORMANCE MONITORING
// =====================================================

/**
 * Giám sát hiệu suất database
 */
export async function monitorDatabasePerformance() {
  try {
    const metrics = await Promise.all([
      getQueryPerformanceMetrics(),
      getTableSizeMetrics(),
      getIndexUsageMetrics(),
      getConnectionMetrics()
    ]);

    return createServiceResponse(true, {
      queryPerformance: metrics[0],
      tableSize: metrics[1],
      indexUsage: metrics[2],
      connections: metrics[3],
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error monitoring database performance:', error);
    return createServiceResponse(false, null, error);
  }
}

/**
 * Lấy metrics hiệu suất query
 */
async function getQueryPerformanceMetrics() {
  const query = `
    SELECT
      schemaname,
      tablename,
      attname,
      n_distinct,
      correlation
    FROM pg_stats
    WHERE schemaname = 'public'
    ORDER BY tablename, attname
    LIMIT 50
  `;

  return executeQuerySafely(query, 'getting query performance metrics');
}

/**
 * Lấy metrics kích thước bảng
 */
async function getTableSizeMetrics() {
  const query = `
    SELECT
      schemaname,
      tablename,
      pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
      pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
    FROM pg_tables
    WHERE schemaname = 'public'
    ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
    LIMIT 20
  `;

  return executeQuerySafely(query, 'getting table size metrics');
}

/**
 * Lấy metrics sử dụng index
 */
async function getIndexUsageMetrics() {
  const query = `
    SELECT
      schemaname,
      tablename,
      indexname,
      idx_scan,
      idx_tup_read,
      idx_tup_fetch
    FROM pg_stat_user_indexes
    WHERE schemaname = 'public'
    ORDER BY idx_scan DESC
    LIMIT 20
  `;

  return executeQuerySafely(query, 'getting index usage metrics');
}

/**
 * Lấy metrics kết nối
 */
async function getConnectionMetrics() {
  const query = `
    SELECT
      state,
      COUNT(*) as connection_count
    FROM pg_stat_activity
    WHERE datname = current_database()
    GROUP BY state
  `;

  return executeQuerySafely(query, 'getting connection metrics');
}

// =====================================================
// 2. QUERY OPTIMIZATION
// =====================================================

/**
 * Phân tích và tối ưu queries chậm
 */
export async function analyzeSlowQueries() {
  try {
    const slowQueries = await getSlowQueries();
    const optimizationSuggestions = slowQueries.map(query => ({
      query: query.query,
      executionTime: query.mean_exec_time,
      suggestions: generateOptimizationSuggestions(query)
    }));

    return createServiceResponse(true, optimizationSuggestions);
  } catch (error) {
    console.error('Error analyzing slow queries:', error);
    return createServiceResponse(false, null, error);
  }
}

/**
 * Lấy danh sách queries chậm
 */
async function getSlowQueries() {
  const query = `
    SELECT
      query,
      calls,
      total_exec_time,
      mean_exec_time,
      rows
    FROM pg_stat_statements
    WHERE mean_exec_time > 100
    ORDER BY mean_exec_time DESC
    LIMIT 10
  `;

  return executeQuerySafely(query, 'getting slow queries');
}

/**
 * Tạo gợi ý tối ưu cho query
 */
function generateOptimizationSuggestions(queryInfo) {
  const suggestions = [];

  // Kiểm tra execution time
  if (queryInfo.mean_exec_time > 1000) {
    suggestions.push({
      type: 'critical',
      message: 'Query có thời gian thực thi rất chậm (>1s)',
      recommendation: 'Cần review và tối ưu ngay lập tức'
    });
  } else if (queryInfo.mean_exec_time > 500) {
    suggestions.push({
      type: 'warning',
      message: 'Query có thời gian thực thi chậm (>500ms)',
      recommendation: 'Nên cân nhắc tối ưu'
    });
  }

  // Kiểm tra số lần gọi
  if (queryInfo.calls > 1000) {
    suggestions.push({
      type: 'info',
      message: 'Query được gọi rất nhiều lần',
      recommendation: 'Cân nhắc caching hoặc tối ưu query'
    });
  }

  // Gợi ý chung
  suggestions.push({
    type: 'general',
    message: 'Gợi ý tối ưu chung',
    recommendation: 'Kiểm tra indexes, LIMIT clauses, và JOIN conditions'
  });

  return suggestions;
}

// =====================================================
// 3. INDEX OPTIMIZATION
// =====================================================

/**
 * Phân tích và gợi ý indexes
 */
export async function analyzeIndexOptimization() {
  try {
    const [unusedIndexes, missingIndexes, duplicateIndexes] = await Promise.all([
      findUnusedIndexes(),
      suggestMissingIndexes(),
      findDuplicateIndexes()
    ]);

    const data = {
      unusedIndexes,
      missingIndexes,
      duplicateIndexes,
      recommendations: generateIndexRecommendations(unusedIndexes, missingIndexes, duplicateIndexes)
    };

    return createServiceResponse(true, data);
  } catch (error) {
    console.error('Error analyzing index optimization:', error);
    return createServiceResponse(false, null, error);
  }
}

/**
 * Tìm indexes không được sử dụng
 */
async function findUnusedIndexes() {
  const query = `
    SELECT
      schemaname,
      tablename,
      indexname,
      idx_scan
    FROM pg_stat_user_indexes
    WHERE idx_scan = 0
      AND schemaname = 'public'
    ORDER BY tablename, indexname
  `;

  return executeQuerySafely(query, 'finding unused indexes');
}

/**
 * Gợi ý indexes thiếu
 */
async function suggestMissingIndexes() {
  // Phân tích các queries thường dùng để gợi ý indexes
  const commonQueries = [
    {
      table: 'products',
      columns: ['tenant_id', 'is_active'],
      reason: 'Thường filter theo tenant và trạng thái'
    },
    {
      table: 'orders',
      columns: ['tenant_id', 'status', 'created_at'],
      reason: 'Thường filter theo tenant, status và thời gian'
    },
    {
      table: 'customers',
      columns: ['tenant_id', 'email'],
      reason: 'Thường tìm kiếm theo email'
    }
  ];

  const suggestions = [];

  for (const suggestion of commonQueries) {
    const indexExists = await checkIndexExists(suggestion.table, suggestion.columns);
    if (!indexExists) {
      suggestions.push(suggestion);
    }
  }

  return suggestions;
}

/**
 * Tìm indexes trùng lặp
 */
async function findDuplicateIndexes() {
  const query = `
    SELECT
      schemaname,
      tablename,
      array_agg(indexname) as index_names,
      array_agg(indexdef) as index_definitions
    FROM pg_indexes
    WHERE schemaname = 'public'
    GROUP BY schemaname, tablename, indexdef
    HAVING COUNT(*) > 1
  `;

  return executeQuerySafely(query, 'finding duplicate indexes');
}

/**
 * Kiểm tra index có tồn tại không
 */
async function checkIndexExists(tableName, columns) {
  try {
    const query = `
      SELECT 1
      FROM pg_indexes
      WHERE tablename = '${tableName}'
        AND indexdef LIKE '%${columns.join('%')}%'
      LIMIT 1
    `;

    const result = await executeQuery(query);
    return result.success && result.data && result.data.length > 0;
  } catch {
    return false;
  }
}

/**
 * Tạo recommendations cho indexes
 */
function generateIndexRecommendations(unusedIndexes, missingIndexes, duplicateIndexes) {
  const recommendations = [];

  // Unused indexes
  if (unusedIndexes.length > 0) {
    recommendations.push({
      type: 'warning',
      category: 'unused_indexes',
      message: `Có ${unusedIndexes.length} indexes không được sử dụng`,
      action: 'Cân nhắc xóa để tiết kiệm storage và cải thiện performance INSERT/UPDATE'
    });
  }

  // Missing indexes
  if (missingIndexes.length > 0) {
    recommendations.push({
      type: 'info',
      category: 'missing_indexes',
      message: `Gợi ý tạo ${missingIndexes.length} indexes mới`,
      action: 'Tạo indexes để cải thiện performance SELECT queries'
    });
  }

  // Duplicate indexes
  if (duplicateIndexes.length > 0) {
    recommendations.push({
      type: 'warning',
      category: 'duplicate_indexes',
      message: `Có ${duplicateIndexes.length} indexes trùng lặp`,
      action: 'Xóa indexes trùng lặp để tiết kiệm storage'
    });
  }

  return recommendations;
}

// =====================================================
// 4. CACHING OPTIMIZATION
// =====================================================

/**
 * Phân tích và tối ưu caching
 */
export async function analyzeCachingOptimization() {
  try {
    const cacheMetrics = await getCacheMetrics();
    const suggestions = generateCachingSuggestions(cacheMetrics);

    return createServiceResponse(true, {
      metrics: cacheMetrics,
      suggestions
    });
  } catch (error) {
    console.error('Error analyzing caching optimization:', error);
    return createServiceResponse(false, null, error);
  }
}

/**
 * Lấy metrics về caching
 */
async function getCacheMetrics() {
  try {
    // Phân tích các queries có thể cache được
    const frequentQueries = await getFrequentQueries();
    const staticDataTables = await getStaticDataTables();

    return {
      frequentQueries,
      staticDataTables,
      cacheOpportunities: identifyCacheOpportunities(frequentQueries, staticDataTables)
    };
  } catch (error) {
    console.error('Error getting cache metrics:', error);
    return {};
  }
}

/**
 * Lấy queries thường xuyên
 */
async function getFrequentQueries() {
  const query = `
    SELECT
      query,
      calls,
      mean_exec_time,
      total_exec_time
    FROM pg_stat_statements
    WHERE calls > 100
    ORDER BY calls DESC
    LIMIT 20
  `;

  return executeQuerySafely(query, 'getting frequent queries');
}

/**
 * Lấy bảng dữ liệu tĩnh
 */
async function getStaticDataTables() {
  // Các bảng thường có dữ liệu tĩnh
  const staticTables = ['categories', 'product_attributes', 'shipping_zones'];
  const tableStats = [];

  for (const table of staticTables) {
    const query = `
      SELECT
        '${table}' as table_name,
        COUNT(*) as row_count,
        pg_size_pretty(pg_total_relation_size('${table}')) as size
      FROM ${table}
    `;

    try {
      const result = await executeQuery(query);
      if (result.success && result.data && result.data.length > 0) {
        tableStats.push(result.data[0]);
      }
    } catch {
      // Table might not exist, skip
    }
  }

  return tableStats;
}

/**
 * Xác định cơ hội caching
 */
function identifyCacheOpportunities(frequentQueries, staticDataTables) {
  const opportunities = [];

  // Frequent queries có thể cache
  frequentQueries.forEach(query => {
    if (query.calls > 500 && query.mean_exec_time > 50) {
      opportunities.push({
        type: 'query_cache',
        target: 'Frequent query',
        reason: `Query được gọi ${query.calls} lần với thời gian TB ${query.mean_exec_time}ms`,
        recommendation: 'Cân nhắc cache kết quả query'
      });
    }
  });

  // Static data tables có thể cache
  staticDataTables.forEach(table => {
    opportunities.push({
      type: 'data_cache',
      target: table.table_name,
      reason: `Bảng dữ liệu tĩnh với ${table.row_count} records`,
      recommendation: 'Cache toàn bộ dữ liệu bảng'
    });
  });

  return opportunities;
}

/**
 * Tạo gợi ý caching
 */
function generateCachingSuggestions(cacheMetrics) {
  const suggestions = [];

  if (cacheMetrics.cacheOpportunities?.length > 0) {
    suggestions.push({
      type: 'info',
      message: `Có ${cacheMetrics.cacheOpportunities.length} cơ hội caching`,
      recommendations: cacheMetrics.cacheOpportunities.map(opp => opp.recommendation)
    });
  }

  // Gợi ý chung về caching
  suggestions.push({
    type: 'general',
    message: 'Gợi ý caching chung',
    recommendations: [
      'Sử dụng Redis cho session và temporary data',
      'Cache API responses cho external services',
      'Implement browser caching cho static assets',
      'Cache database query results cho read-heavy operations'
    ]
  });

  return suggestions;
}

// =====================================================
// 5. SYSTEM OPTIMIZATION RECOMMENDATIONS
// =====================================================

/**
 * Tạo báo cáo tối ưu tổng thể
 */
export async function generateOptimizationReport() {
  try {
    const [dbPerformance, queryAnalysis, indexAnalysis, cachingAnalysis] = await Promise.all([
      monitorDatabasePerformance(),
      analyzeSlowQueries(),
      analyzeIndexOptimization(),
      analyzeCachingOptimization()
    ]);

    const overallScore = calculatePerformanceScore({
      dbPerformance,
      queryAnalysis,
      indexAnalysis,
      cachingAnalysis
    });

    const prioritizedRecommendations = prioritizeRecommendations({
      dbPerformance,
      queryAnalysis,
      indexAnalysis,
      cachingAnalysis
    });

    return {
      success: true,
      data: {
        overallScore,
        dbPerformance,
        queryAnalysis,
        indexAnalysis,
        cachingAnalysis,
        prioritizedRecommendations,
        generatedAt: new Date().toISOString()
      }
    };
  } catch (error) {
    console.error('Error generating optimization report:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Tính điểm hiệu suất tổng thể
 */
function calculatePerformanceScore(analyses) {
  let score = 100;

  // Deduct points based on issues found
  if (analyses.queryAnalysis.success && analyses.queryAnalysis.data.length > 0) {
    score -= analyses.queryAnalysis.data.length * 5; // -5 points per slow query
  }

  if (analyses.indexAnalysis.success) {
    const { unusedIndexes, duplicateIndexes } = analyses.indexAnalysis.data;
    score -= (unusedIndexes?.length || 0) * 2; // -2 points per unused index
    score -= (duplicateIndexes?.length || 0) * 3; // -3 points per duplicate index
  }

  return Math.max(0, Math.min(100, score));
}

/**
 * Ưu tiên các recommendations
 */
function prioritizeRecommendations(analyses) {
  const recommendations = [];

  // Critical issues first
  if (analyses.queryAnalysis.success) {
    analyses.queryAnalysis.data.forEach(query => {
      if (query.executionTime > 1000) {
        recommendations.push({
          priority: 'critical',
          category: 'query_performance',
          message: `Query chậm: ${query.executionTime}ms`,
          action: 'Tối ưu ngay lập tức'
        });
      }
    });
  }

  // Index issues
  if (analyses.indexAnalysis.success) {
    const { recommendations: indexRecs } = analyses.indexAnalysis.data;
    indexRecs.forEach(rec => {
      recommendations.push({
        priority: rec.type === 'warning' ? 'high' : 'medium',
        category: 'index_optimization',
        message: rec.message,
        action: rec.action
      });
    });
  }

  // Caching opportunities
  if (analyses.cachingAnalysis.success) {
    const { suggestions } = analyses.cachingAnalysis.data;
    suggestions.forEach(suggestion => {
      recommendations.push({
        priority: 'medium',
        category: 'caching',
        message: suggestion.message,
        action: 'Implement caching strategy'
      });
    });
  }

  // Sort by priority
  const priorityOrder = { critical: 0, high: 1, medium: 2, low: 3 };
  recommendations.sort((a, b) => priorityOrder[a.priority] - priorityOrder[b.priority]);

  return recommendations;
}

// =====================================================
// 6. EXPORT FUNCTIONS
// =====================================================

// export {
//   analyzeSlowQueries,
//   analyzeIndexOptimization,
//   analyzeCachingOptimization,
//   generateOptimizationReport
// };
