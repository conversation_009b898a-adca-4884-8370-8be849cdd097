import 'src/global.css';

import InitColorSchemeScript from '@mui/material/InitColorSchemeScript';
// Removed AppRouterCacheProvider for better performance

import { HydrationHandler } from 'src/utils/handle-hydration';

import { CONFIG } from 'src/global-config';
import { primary } from 'src/theme/core/palette';
import { LocalizationProvider } from 'src/locales';
import { detectLanguage } from 'src/locales/server';
import { themeConfig, ThemeProvider } from 'src/theme';
import { DocumentHydrationFix } from 'src/app/document';
import { I18nProvider } from 'src/locales/i18n-provider';
import { BusinessConfigProvider } from 'src/actions/mooly-chatbot/business-config-service';

import { Snackbar } from 'src/components/snackbar';
// import { StoreProvider } from 'src/contexts/store-context';
import { ProgressBar } from 'src/components/progress-bar';
import { MotionLazy } from 'src/components/animate/motion-lazy';
import { detectSettings } from 'src/components/settings/server';
import { HydrationFix } from 'src/components/hydration-fix/hydration-fix';
import { SettingsDrawer, defaultSettings, SettingsProvider } from 'src/components/settings';

import { CheckoutProvider } from 'src/sections/checkout/context';

import { AuthProvider as JwtAuthProvider } from 'src/auth/context/jwt';
import { AuthProvider as Auth0AuthProvider } from 'src/auth/context/auth0';
import { AuthProvider as AmplifyAuthProvider } from 'src/auth/context/amplify';
import { AuthProvider as SupabaseAuthProvider } from 'src/auth/context/supabase';
import { AuthProvider as FirebaseAuthProvider } from 'src/auth/context/firebase';

// ----------------------------------------------------------------------

const AuthProvider =
  (CONFIG.auth.method === 'amplify' && AmplifyAuthProvider) ||
  (CONFIG.auth.method === 'firebase' && FirebaseAuthProvider) ||
  (CONFIG.auth.method === 'supabase' && SupabaseAuthProvider) ||
  (CONFIG.auth.method === 'auth0' && Auth0AuthProvider) ||
  JwtAuthProvider;

// ----------------------------------------------------------------------

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  themeColor: primary.main,
};

// ----------------------------------------------------------------------

export const metadata = {
  icons: [
    {
      rel: 'icon',
      url: `${CONFIG.assetsDir}/favicon.ico`,
    },
  ],
};

async function getAppConfig() {
  const [lang, settings] = await Promise.all([detectLanguage(), detectSettings()]);

  return {
    lang: lang ?? 'vi',
    i18nLang: lang ?? 'vi',
    cookieSettings: settings,
    dir: settings.direction,
  };
}

// ----------------------------------------------------------------------

export default async function RootLayout({ children }) {
  const appConfig = await getAppConfig();

  return (
    <html lang={appConfig.lang} dir={appConfig.dir} suppressHydrationWarning>
      <body suppressHydrationWarning>
        <InitColorSchemeScript
          modeStorageKey={themeConfig.modeStorageKey}
          attribute={themeConfig.cssVariables.colorSchemeSelector}
          defaultMode={themeConfig.enableSystemMode ? 'system' : themeConfig.defaultMode}
        />

        <I18nProvider lang={appConfig.i18nLang}>
          <AuthProvider>
            <BusinessConfigProvider>
              <SettingsProvider
                cookieSettings={appConfig.cookieSettings}
                defaultSettings={defaultSettings}
              >
                <LocalizationProvider>
                  {/* Removed AppRouterCacheProvider for better performance */}
                  <ThemeProvider
                    modeStorageKey={themeConfig.modeStorageKey}
                    defaultMode={themeConfig.enableSystemMode ? 'system' : themeConfig.defaultMode}
                  >
                      <HydrationFix>
                        <DocumentHydrationFix>
                          <HydrationHandler>
                            <MotionLazy>
                              <CheckoutProvider>
                                {/* <StoreProvider> */}
                                <Snackbar />
                                <ProgressBar />
                                <SettingsDrawer defaultSettings={defaultSettings} />
                                {children}
                                {/* </StoreProvider> */}
                              </CheckoutProvider>
                            </MotionLazy>
                          </HydrationHandler>
                        </DocumentHydrationFix>
                      </HydrationFix>
                    </ThemeProvider>
                  {/* Removed AppRouterCacheProvider closing tag */}
                </LocalizationProvider>
              </SettingsProvider>
            </BusinessConfigProvider>
          </AuthProvider>
        </I18nProvider>
      </body>
    </html>
  );
}
