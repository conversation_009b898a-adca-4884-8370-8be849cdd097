'use client';

import { useState, useEffect } from 'react';

import {
  Box,
  Grid,
  Paper,
  Alert,
  Button,
  Typography,
  CircularProgress,
} from '@mui/material';

import { DashboardContent } from 'src/layouts/dashboard';
import { fetchData } from 'src/actions/mooly-chatbot/supabase-utils';

// Danh sách các bảng để kiểm tra
const TABLES_TO_CHECK = [
  'users',
  'products',
  'categories',
  'stores',
  'orders',
  'customers',
  'credit_packages',
  'credit_transactions',
];

export default function TenantTestPage() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [tableInfo, setTableInfo] = useState({});
  const [selectedTable, setSelectedTable] = useState(null);
  const [tableData, setTableData] = useState([]);
  const [loadingTableData, setLoadingTableData] = useState(false);
  const [rlsTestResults, setRlsTestResults] = useState({});

  useEffect(() => {
    async function testRLSSystem() {
      try {
        setLoading(true);

        // Test RLS system bằng cách thử fetch data từ các bảng
        const tablesInfo = {};
        const rlsResults = {};

        for (const table of TABLES_TO_CHECK) {
          try {
            // Test fetch data với RLS
            const result = await fetchData(table, {
              limit: 1,
              columns: 'id'
            });

            tablesInfo[table] = {
              name: table,
              hasRLS: true,
              accessible: result.success,
              dataCount: result.data?.length || 0,
              error: result.error?.message || null
            };

            rlsResults[table] = {
              success: result.success,
              dataCount: result.data?.length || 0,
              error: result.error?.message || null
            };
          } catch (err) {
            tablesInfo[table] = {
              name: table,
              hasRLS: false,
              accessible: false,
              dataCount: 0,
              error: err.message
            };

            rlsResults[table] = {
              success: false,
              dataCount: 0,
              error: err.message
            };
          }
        }

        setTableInfo(tablesInfo);
        setRlsTestResults(rlsResults);
      } catch (err) {
        console.error('Lỗi khi test RLS system:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }

    testRLSSystem();
  }, []);

  const handleFetchTableData = async (tableName) => {
    try {
      setSelectedTable(tableName);
      setLoadingTableData(true);
      
      // Lấy 5 bản ghi đầu tiên từ bảng
      const result = await fetchData(tableName, { limit: 5 });
      
      if (!result.success) {
        throw new Error(result.error || `Lỗi khi lấy dữ liệu từ bảng ${tableName}`);
      }
      
      setTableData(result.data || []);
    } catch (err) {
      console.error(`Lỗi khi lấy dữ liệu từ bảng ${tableName}:`, err);
      setError(err.message);
      setTableData([]);
    } finally {
      setLoadingTableData(false);
    }
  };

  return (
    <DashboardContent>
      <Typography variant="h4" sx={{ mb: 3 }}>
        Kiểm tra RLS System
      </Typography>

      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 5 }}>
          <CircularProgress />
        </Box>
      )}

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {!loading && !error && (
        <>
          <Alert severity="info" sx={{ mb: 3 }}>
            RLS System đã được kích hoạt - tenant_id được xử lý tự động từ JWT token
          </Alert>

          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" sx={{ mb: 2 }}>
              Kết quả test RLS cho các bảng
            </Typography>

            <Grid container spacing={2}>
              {Object.values(tableInfo).map((table) => (
                <Grid item size={{xs: 12, sm: 6, md: 4}} key={table.name}>
                  <Paper
                    elevation={3}
                    sx={{
                      p: 2,
                      display: 'flex',
                      flexDirection: 'column',
                      height: '100%',
                      bgcolor: table.accessible ? 'success.lighter' : 'error.lighter'
                    }}
                  >
                    <Typography variant="subtitle1" sx={{ mb: 1 }}>
                      {table.name}
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      RLS: {table.hasRLS ? '✅ Enabled' : '❌ Disabled'}
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      Access: {table.accessible ? '✅ Success' : '❌ Failed'}
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 2, flex: 1 }}>
                      Data: {table.dataCount} records
                    </Typography>
                    {table.error && (
                      <Typography variant="caption" color="error" sx={{ mb: 1 }}>
                        Error: {table.error}
                      </Typography>
                    )}
                    <Button
                      variant="contained"
                      size="small"
                      onClick={() => handleFetchTableData(table.name)}
                      disabled={!table.accessible}
                    >
                      Xem dữ liệu
                    </Button>
                  </Paper>
                </Grid>
              ))}
            </Grid>
          </Paper>
          
          {selectedTable && (
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" sx={{ mb: 2 }}>
                Dữ liệu từ bảng {selectedTable}
              </Typography>
              
              {loadingTableData ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', my: 3 }}>
                  <CircularProgress />
                </Box>
              ) : tableData.length > 0 ? (
                <pre style={{ overflow: 'auto', maxHeight: '400px' }}>
                  {JSON.stringify(tableData, null, 2)}
                </pre>
              ) : (
                <Alert severity="info">Không có dữ liệu trong bảng này</Alert>
              )}
            </Paper>
          )}
        </>
      )}
    </DashboardContent>
  );
}
