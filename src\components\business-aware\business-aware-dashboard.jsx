'use client';

import { useMemo } from 'react';

import { Box, Grid, Card, Stack, Typography, CardContent } from '@mui/material';

import {
  useBusinessConfigContext,
  getDashboardConfig,
  shouldShowUIElement
} from 'src/actions/mooly-chatbot/business-config-service';
import { BusinessConfigGuard } from 'src/components/business-config-guard/business-config-guard';
import { useAppInitialization } from 'src/hooks/use-app-initialization';

import { Iconify } from 'src/components/iconify';

/**
 * Business-aware dashboard component
 * Shows different widgets based on business type
 */
export function BusinessAwareDashboard({ children }) {
  const { businessType, ready } = useAppInitialization();

  const dashboardConfig = useMemo(() => {
    if (!ready || !businessType) {
      return getDefaultDashboardConfig();
    }

    return getDashboardConfig(businessType);
  }, [businessType, ready]);

  const visibleWidgets = useMemo(() => {
    if (!businessType) return [];

    const allWidgets = [
      'inventoryLevels',
      'orderStatus',
      'salesMetrics',
      'lowStockAlerts',
      'downloadStats',
      'licenseUsage',
      'customerActivity',
      'appointmentCalendar',
      'staffUtilization',
      'serviceMetrics',
      'customerSatisfaction',
      'businessOverview',
      'mixedMetrics',
      'smartInsights',
      'adaptiveRecommendations'
    ];

    return allWidgets.filter(widget =>
      dashboardConfig.widgets?.includes(widget) ||
      shouldShowUIElement(businessType, widget, 'dashboard')
    );
  }, [businessType, dashboardConfig]);

  return (
    <BusinessConfigGuard fallback={<Box>Đang tải dashboard...</Box>}>
      {businessType ? (
        <Box>
          <BusinessTypeHeader businessType={businessType} />
          <Grid container spacing={3}>
            {visibleWidgets.map((widget, index) => (
              <Grid item size={{xs: 12, sm: 6, md: 4}} key={widget}>
                <DashboardWidget
                  type={widget}
                  businessType={businessType}
                  priority={dashboardConfig.priority?.indexOf(widget) || index}
                />
              </Grid>
            ))}
            {children}
          </Grid>
        </Box>
      ) : null}
    </BusinessConfigGuard>
  );
}

/**
 * Business type header component
 */
function BusinessTypeHeader({ businessType }) {
  const getBusinessTypeInfo = (type) => {
    switch (type) {
      case 'retail':
        return {
          title: 'Dashboard Bán lẻ',
          subtitle: 'Quản lý sản phẩm vật lý và tồn kho',
          icon: 'eva:shopping-bag-fill',
          color: 'primary'
        };
      case 'digital':
        return {
          title: 'Dashboard Sản phẩm số',
          subtitle: 'Quản lý downloads và licenses',
          icon: 'eva:monitor-fill',
          color: 'info'
        };
      case 'services':
        return {
          title: 'Dashboard Dịch vụ',
          subtitle: 'Quản lý lịch hẹn và nhân viên',
          icon: 'eva:calendar-fill',
          color: 'success'
        };
      case 'hybrid':
        return {
          title: 'Dashboard Đa dạng',
          subtitle: 'Quản lý tất cả loại hình kinh doanh',
          icon: 'eva:grid-fill',
          color: 'warning'
        };
      default:
        return {
          title: 'Dashboard',
          subtitle: 'Tổng quan kinh doanh',
          icon: 'eva:pie-chart-fill',
          color: 'default'
        };
    }
  };

  const { title, subtitle, icon, color } = getBusinessTypeInfo(businessType);

  return (
    <Card sx={{ mb: 3, background: `linear-gradient(135deg, ${color}.light, ${color}.main)` }}>
      <CardContent>
        <Stack direction="row" alignItems="center" spacing={2}>
          <Box
            sx={{
              width: 48,
              height: 48,
              borderRadius: 1,
              bgcolor: 'background.paper',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            <Iconify icon={icon} width={24} />
          </Box>
          <Box>
            <Typography variant="h5" color="white">
              {title}
            </Typography>
            <Typography variant="body2" color="white" sx={{ opacity: 0.8 }}>
              {subtitle}
            </Typography>
          </Box>
        </Stack>
      </CardContent>
    </Card>
  );
}

/**
 * Dashboard widget component
 */
function DashboardWidget({ type, businessType, priority }) {
  const getWidgetConfig = (widgetType) => {
    const configs = {
      inventoryLevels: {
        title: 'Tồn kho',
        icon: 'eva:cube-fill',
        color: 'warning',
        value: '1,234',
        subtitle: 'Sản phẩm trong kho'
      },
      orderStatus: {
        title: 'Đơn hàng',
        icon: 'eva:shopping-cart-fill',
        color: 'success',
        value: '89',
        subtitle: 'Đơn hàng mới'
      },
      salesMetrics: {
        title: 'Doanh thu',
        icon: 'eva:trending-up-fill',
        color: 'primary',
        value: '₫12.5M',
        subtitle: 'Tháng này'
      },
      lowStockAlerts: {
        title: 'Cảnh báo tồn kho',
        icon: 'eva:alert-triangle-fill',
        color: 'error',
        value: '5',
        subtitle: 'Sản phẩm sắp hết'
      },
      downloadStats: {
        title: 'Downloads',
        icon: 'eva:download-fill',
        color: 'info',
        value: '2,456',
        subtitle: 'Lượt tải xuống'
      },
      licenseUsage: {
        title: 'Licenses',
        icon: 'eva:shield-fill',
        color: 'success',
        value: '78%',
        subtitle: 'Đang sử dụng'
      },
      appointmentCalendar: {
        title: 'Lịch hẹn',
        icon: 'eva:calendar-fill',
        color: 'primary',
        value: '24',
        subtitle: 'Hôm nay'
      },
      staffUtilization: {
        title: 'Nhân viên',
        icon: 'eva:people-fill',
        color: 'success',
        value: '85%',
        subtitle: 'Tỷ lệ sử dụng'
      },
      businessOverview: {
        title: 'Tổng quan',
        icon: 'eva:pie-chart-fill',
        color: 'primary',
        value: '₫45.2M',
        subtitle: 'Tổng doanh thu'
      }
    };

    return configs[widgetType] || {
      title: 'Widget',
      icon: 'eva:question-mark-circle-fill',
      color: 'default',
      value: '-',
      subtitle: 'Không có dữ liệu'
    };
  };

  const config = getWidgetConfig(type);

  return (
    <Card
      sx={{
        height: '100%',
        border: priority < 2 ? 2 : 1,
        borderColor: priority < 2 ? `${config.color}.main` : 'divider'
      }}
    >
      <CardContent>
        <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 2 }}>
          <Box
            sx={{
              width: 40,
              height: 40,
              borderRadius: 1,
              bgcolor: `${config.color}.lighter`,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            <Iconify icon={config.icon} width={20} sx={{ color: `${config.color}.main` }} />
          </Box>
          {priority < 2 && (
            <Typography variant="caption" color="primary.main" fontWeight={600}>
              Ưu tiên
            </Typography>
          )}
        </Stack>

        <Typography variant="h4" sx={{ mb: 1 }}>
          {config.value}
        </Typography>

        <Typography variant="h6" color="text.primary" sx={{ mb: 0.5 }}>
          {config.title}
        </Typography>

        <Typography variant="body2" color="text.secondary">
          {config.subtitle}
        </Typography>
      </CardContent>
    </Card>
  );
}

/**
 * Get default dashboard configuration
 */
function getDefaultDashboardConfig() {
  return {
    priority: ['salesMetrics', 'orderStatus'],
    widgets: ['salesMetrics', 'orderStatus', 'inventoryLevels']
  };
}

/**
 * Business-aware quick actions component
 */
export function BusinessAwareQuickActions() {
  const { businessType } = useAppInitialization();

  return (
    <BusinessConfigGuard
      fallback={
        <Card>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2 }}>
              Thao tác nhanh
            </Typography>
            <Box>Đang tải...</Box>
          </CardContent>
        </Card>
      }
    >
      {businessType ? <QuickActionsContent businessType={businessType} /> : null}
    </BusinessConfigGuard>
  );
}

function QuickActionsContent({ businessType }) {
  const getQuickActions = (type) => {
    const actions = {
      retail: [
        { title: 'Thêm sản phẩm', icon: 'eva:plus-fill', path: '/products/new' },
        { title: 'Kiểm tra tồn kho', icon: 'eva:cube-fill', path: '/inventory' },
        { title: 'Xử lý đơn hàng', icon: 'eva:shopping-cart-fill', path: '/orders' }
      ],
      digital: [
        { title: 'Upload sản phẩm số', icon: 'eva:cloud-upload-fill', path: '/products/new' },
        { title: 'Quản lý license', icon: 'eva:shield-fill', path: '/licenses' },
        { title: 'Thống kê download', icon: 'eva:bar-chart-fill', path: '/analytics' }
      ],
      services: [
        { title: 'Tạo dịch vụ mới', icon: 'eva:plus-fill', path: '/services/new' },
        { title: 'Xem lịch hẹn', icon: 'eva:calendar-fill', path: '/appointments' },
        { title: 'Quản lý nhân viên', icon: 'eva:people-fill', path: '/staff' }
      ],
      hybrid: [
        { title: 'Thêm sản phẩm/dịch vụ', icon: 'eva:plus-fill', path: '/products/new' },
        { title: 'Tổng quan kinh doanh', icon: 'eva:pie-chart-fill', path: '/overview' },
        { title: 'Phân tích thông minh', icon: 'eva:brain-fill', path: '/analytics' }
      ]
    };

    return actions[type] || actions.retail;
  };

  const quickActions = getQuickActions(businessType);

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" sx={{ mb: 2 }}>
          Thao tác nhanh
        </Typography>
        <Stack spacing={1}>
          {quickActions.map((action, index) => (
            <Box
              key={index}
              sx={{
                p: 1.5,
                borderRadius: 1,
                bgcolor: 'background.neutral',
                cursor: 'pointer',
                '&:hover': {
                  bgcolor: 'primary.lighter'
                }
              }}
            >
              <Stack direction="row" alignItems="center" spacing={2}>
                <Iconify icon={action.icon} width={20} />
                <Typography variant="body2">{action.title}</Typography>
              </Stack>
            </Box>
          ))}
        </Stack>
      </CardContent>
    </Card>
  );
}
