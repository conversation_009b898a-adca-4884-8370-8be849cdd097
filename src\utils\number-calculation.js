/**
 * Utility functions for safe number calculations
 * <PERSON><PERSON><PERSON> bảo tất cả phép tính được thực hiện với số thay vì string
 */

/**
 * Chuyển đổi giá trị thành số an toàn
 * @param {any} value - <PERSON><PERSON><PERSON> trị cần chuyển đổi
 * @param {number} defaultValue - Giá trị mặc định nếu không thể chuyển đổi
 * @returns {number} - Số đã được chuyển đổi
 */
export function toNumber(value, defaultValue = 0) {
  if (value === null || value === undefined || value === '') {
    return defaultValue;
  }
  
  const num = parseFloat(value);
  return isNaN(num) ? defaultValue : num;
}

/**
 * Cộng hai số an toàn
 * @param {any} a - <PERSON><PERSON> thứ nhất
 * @param {any} b - Số thứ hai
 * @returns {number} - <PERSON><PERSON><PERSON> quả phép cộng
 */
export function safeAdd(a, b) {
  return toNumber(a) + toNumber(b);
}

/**
 * Trừ hai số an toàn
 * @param {any} a - S<PERSON> bị trừ
 * @param {any} b - Số trừ
 * @returns {number} - Kết quả phép trừ
 */
export function safeSubtract(a, b) {
  return toNumber(a) - toNumber(b);
}

/**
 * Nhân hai số an toàn
 * @param {any} a - Số thứ nhất
 * @param {any} b - Số thứ hai
 * @returns {number} - Kết quả phép nhân
 */
export function safeMultiply(a, b) {
  return toNumber(a) * toNumber(b);
}

/**
 * Chia hai số an toàn
 * @param {any} a - Số bị chia
 * @param {any} b - Số chia
 * @returns {number} - Kết quả phép chia
 */
export function safeDivide(a, b) {
  const divisor = toNumber(b);
  return divisor === 0 ? 0 : toNumber(a) / divisor;
}

/**
 * Tính tổng của một mảng số
 * @param {Array} numbers - Mảng các số
 * @returns {number} - Tổng
 */
export function safeSum(numbers) {
  if (!Array.isArray(numbers)) return 0;
  return numbers.reduce((sum, num) => sum + toNumber(num), 0);
}

/**
 * Tính tổng tiền của các item trong đơn hàng
 * @param {Array} items - Mảng các item
 * @param {string} priceField - Tên field chứa giá (mặc định: 'totalPrice')
 * @returns {number} - Tổng tiền
 */
export function calculateItemsTotal(items, priceField = 'totalPrice') {
  if (!Array.isArray(items)) return 0;
  return items.reduce((total, item) => {
    const itemPrice = toNumber(item?.[priceField]);
    return total + itemPrice;
  }, 0);
}

/**
 * Tính tổng tiền đơn hàng
 * @param {Object} params - Các tham số tính toán
 * @param {number} params.subtotal - Tổng tiền hàng
 * @param {number} params.shippingAmount - Phí vận chuyển
 * @param {number} params.discountAmount - Giảm giá
 * @param {number} params.taxAmount - Thuế
 * @returns {number} - Tổng tiền cuối cùng
 */
export function calculateOrderTotal({ subtotal, shippingAmount, discountAmount, taxAmount }) {
  const subtotalNum = toNumber(subtotal);
  const shippingNum = toNumber(shippingAmount);
  const discountNum = toNumber(discountAmount);
  const taxNum = toNumber(taxAmount);
  
  return subtotalNum + shippingNum - discountNum + taxNum;
}

/**
 * Làm tròn số đến số chữ số thập phân chỉ định
 * @param {any} value - Giá trị cần làm tròn
 * @param {number} decimals - Số chữ số thập phân (mặc định: 2)
 * @returns {number} - Số đã được làm tròn
 */
export function roundToDecimals(value, decimals = 2) {
  const num = toNumber(value);
  const factor = Math.pow(10, decimals);
  return Math.round(num * factor) / factor;
}

/**
 * So sánh hai số với độ chính xác
 * @param {any} a - Số thứ nhất
 * @param {any} b - Số thứ hai
 * @param {number} tolerance - Độ chính xác (mặc định: 0.01)
 * @returns {boolean} - True nếu hai số bằng nhau trong phạm vi độ chính xác
 */
export function numbersEqual(a, b, tolerance = 0.01) {
  return Math.abs(toNumber(a) - toNumber(b)) <= tolerance;
}
