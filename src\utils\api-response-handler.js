/**
 * Utility functions để xử lý API responses một cách nhất quán
 */

/**
 * Xử lý response từ external API với error handling tốt hơn
 * @param {Response} response - Fetch response object
 * @param {string} context - Context để logging (ví dụ: 'Haravan sync', 'Sapo sync')
 * @returns {Promise<Object>} - Parsed response data hoặc throw error
 */
export async function handleApiResponse(response, context = 'API call') {
  console.log(`${context} - Response status: ${response.status}, Content-Type: ${response.headers.get('content-type')}`);

  // Kiểm tra Content-Type trước khi parse JSON
  const contentType = response.headers.get('content-type');
  if (!contentType || !contentType.includes('application/json')) {
    const textResponse = await response.text();
    console.warn(`${context} - Backend returned non-JSON response:`, textResponse.substring(0, 200));
    throw new Error(`Backend returned ${contentType || 'unknown content type'} instead of JSON`);
  }

  // Parse JSON với error handling
  let responseData;
  try {
    responseData = await response.json();
  } catch (jsonError) {
    console.error(`${context} - Failed to parse JSON response:`, jsonError);
    throw new Error('Backend returned invalid JSON response');
  }

  // Kiểm tra status code
  if (!response.ok) {
    const errorMessage = responseData.message || responseData.error || `${context} failed with status ${response.status}`;
    throw new Error(errorMessage);
  }

  console.log(`${context} - Success:`, responseData.message || 'Operation completed');
  return responseData;
}

/**
 * Xử lý fetch request với timeout và error handling
 * @param {string} url - URL để gọi
 * @param {Object} options - Fetch options
 * @param {number} timeout - Timeout in milliseconds (default: 30000)
 * @param {string} context - Context để logging
 * @returns {Promise<Response>} - Fetch response
 */
export async function fetchWithTimeout(url, options = {}, timeout = 30000, context = 'API call') {
  console.log(`${context} - Attempting to call: ${url}`);

  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    const response = await fetch(url, {
      ...options,
      signal: controller.signal,
    });
    clearTimeout(timeoutId);
    return response;
  } catch (error) {
    clearTimeout(timeoutId);
    
    if (error.name === 'AbortError') {
      throw new Error(`${context} timed out after ${timeout}ms`);
    }
    
    console.error(`${context} - Network error:`, error);
    throw new Error(`Network error: ${error.message}`);
  }
}

/**
 * Wrapper function để gọi external API với full error handling
 * @param {string} url - URL để gọi
 * @param {Object} options - Fetch options
 * @param {string} context - Context để logging
 * @param {number} timeout - Timeout in milliseconds
 * @returns {Promise<Object>} - Parsed response data
 */
export async function callExternalApi(url, options = {}, context = 'API call', timeout = 30000) {
  try {
    const response = await fetchWithTimeout(url, options, timeout, context);
    return await handleApiResponse(response, context);
  } catch (error) {
    console.error(`${context} - Error:`, error.message);
    if (error.stack) {
      console.error(`${context} - Stack trace:`, error.stack);
    }
    throw error;
  }
}

/**
 * Tạo error response nhất quán cho API routes
 * @param {Error} error - Error object
 * @param {string} defaultMessage - Default error message
 * @param {number} status - HTTP status code
 * @returns {Object} - Error response object
 */
export function createErrorResponse(error, defaultMessage = 'Internal server error', status = 500) {
  return {
    success: false,
    error: error.message || defaultMessage,
    status,
  };
}

/**
 * Tạo success response nhất quán cho API routes
 * @param {Object} data - Response data
 * @param {string} message - Success message
 * @returns {Object} - Success response object
 */
export function createSuccessResponse(data, message = 'Operation successful') {
  return {
    success: true,
    message,
    data,
  };
}
