import { useState, useCallback, useRef, useEffect } from 'react';
import { ERROR_MESSAGES, POPUP_CONFIG, FACEBOOK_PERMISSIONS } from './facebook-constants';
import { FACEBOOK_CONFIG } from 'src/lib/facebook-config';

/**
 * Generate secure state for OAuth
 */
function generateSecureState() {
  const array = new Uint32Array(4);
  crypto.getRandomValues(array);
  return Array.from(array, dec => dec.toString(16)).join('');
}

/**
 * Hook để quản lý Facebook popup authentication
 * @returns {Object} - State và functions
 */
export function useFacebookPopupAuth() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const popupRef = useRef(null);
  const intervalRef = useRef(null);

  const cleanup = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    
    if (popupRef.current && !popupRef.current.closed) {
      popupRef.current.close();
      popupRef.current = null;
    }
    
    if (window.facebookAuthCallback) {
      delete window.facebookAuthCallback;
    }
    
    setLoading(false);
  }, []);

  const openFacebookPopup = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Get Facebook App ID from environment
      const appId = process.env.NEXT_PUBLIC_FACEBOOK_APP_ID;
      if (!appId) {
        throw new Error('Facebook App ID không được cấu hình');
      }

      // Build callback URL
      const baseUrl = process.env.NEXT_PUBLIC_PUBLIC_SITE_URL || window.location.origin;
      const callbackUrl = `${baseUrl}/api/facebook-integration/popup-callback`;

      // Build Facebook OAuth URL using centralized config
      const facebookAuthUrl = new URL(FACEBOOK_CONFIG.getOAuthDialogUrl());
      facebookAuthUrl.searchParams.set('client_id', appId);
      facebookAuthUrl.searchParams.set('redirect_uri', callbackUrl);
      facebookAuthUrl.searchParams.set('scope', FACEBOOK_PERMISSIONS.join(','));
      facebookAuthUrl.searchParams.set('response_type', 'code');
      facebookAuthUrl.searchParams.set('state', generateSecureState());
      facebookAuthUrl.searchParams.set('display', 'popup');

      console.log('🔗 Opening Facebook OAuth popup:', {
        appId: appId,
        callbackUrl: callbackUrl,
        permissions: FACEBOOK_PERMISSIONS.length
      });

      // Calculate popup position
      const left = window.screen.width / 2 - FACEBOOK_CONFIG.POPUP.WIDTH / 2;
      const top = window.screen.height / 2 - FACEBOOK_CONFIG.POPUP.HEIGHT / 2;

      // Open popup window
      const popup = window.open(
        facebookAuthUrl.toString(),
        'facebook-oauth',
        `width=${FACEBOOK_CONFIG.POPUP.WIDTH},height=${FACEBOOK_CONFIG.POPUP.HEIGHT},left=${left},top=${top},${FACEBOOK_CONFIG.POPUP.FEATURES}`
      );

      if (!popup || popup.closed) {
        throw new Error(ERROR_MESSAGES.POPUP_BLOCKED);
      }

      popupRef.current = popup;

      // Return promise that resolves when popup completes
      return new Promise((resolve, reject) => {
        // Set up global callback for popup to call
        window.facebookAuthCallback = (result) => {
          cleanup();
          
          if (result.success) {
            resolve(result);
          } else {
            reject(new Error(result.message || ERROR_MESSAGES.CONNECTION_FAILED));
          }
        };

        // Monitor popup for completion
        intervalRef.current = setInterval(() => {
          try {
            // Check if popup is closed
            if (popup.closed) {
              cleanup();
              reject(new Error('Người dùng đã đóng popup. Vui lòng thử lại.'));
              return;
            }

            // Try to access popup URL to detect callback completion
            try {
              const popupUrl = popup.location.href;
              const url = new URL(popupUrl);
              
              // Check for success/error parameters
              const success = url.searchParams.get('success');
              const message = url.searchParams.get('message');
              
              if (success !== null) {
                cleanup();
                
                if (success === 'true') {
                  resolve({
                    success: true,
                    message: decodeURIComponent(message || 'Facebook connection successful')
                  });
                } else {
                  reject(new Error(decodeURIComponent(message || error || ERROR_MESSAGES.CONNECTION_FAILED)));
                }
              }
            } catch (crossOriginError) {
              // Expected when popup is on different domain (Facebook)
              // Continue monitoring
            }
          } catch (monitorError) {
            console.error('Popup monitoring error:', monitorError);
          }
        }, POPUP_CONFIG.POLL_INTERVAL);

        // Timeout after configured duration
        setTimeout(() => {
          if (intervalRef.current) {
            cleanup();
            reject(new Error('Đăng nhập Facebook timeout. Vui lòng thử lại.'));
          }
        }, POPUP_CONFIG.TIMEOUT);
      });

    } catch (err) {
      setLoading(false);
      setError(err);
      throw err;
    }
  }, [cleanup]);

  // Cleanup on unmount
  useEffect(() => cleanup, [cleanup]);

  return {
    loading,
    error,
    openFacebookPopup,
    cleanup
  };
}

/**
 * Hook để quản lý Facebook connection với callback handling
 * @param {Function} onSuccess - Callback khi kết nối thành công
 * @param {Function} onError - Callback khi có lỗi
 * @returns {Object} - State và functions
 */
export function useFacebookConnection(onSuccess, onError) {
  const { loading, error, openFacebookPopup, cleanup } = useFacebookPopupAuth();

  const handleConnect = useCallback(async () => {
    try {
      const result = await openFacebookPopup();
      
      if (result.success) {
        onSuccess?.(result);
      }
    } catch (err) {
      console.error('Facebook connection error:', err);
      onError?.(err);
    }
  }, [openFacebookPopup, onSuccess, onError]);

  return {
    loading,
    error,
    handleConnect,
    cleanup
  };
}

/**
 * Utility function để validate Facebook permissions
 * @param {Array} grantedPermissions - Permissions được cấp
 * @param {Array} requiredPermissions - Permissions cần thiết
 * @returns {Object} - Validation result
 */
export function validateFacebookPermissions(grantedPermissions = [], requiredPermissions = FACEBOOK_PERMISSIONS) {
  const missing = requiredPermissions.filter(permission => 
    !grantedPermissions.includes(permission)
  );

  return {
    isValid: missing.length === 0,
    missing,
    granted: grantedPermissions.filter(permission => 
      requiredPermissions.includes(permission)
    )
  };
}

/**
 * Utility function để format Facebook error messages
 * @param {Object} error - Facebook API error
 * @returns {string} - Formatted error message
 */
export function formatFacebookError(error) {
  if (!error) return ERROR_MESSAGES.CONNECTION_FAILED;

  // Handle specific Facebook error codes
  switch (error.code) {
    case 4:
      return 'Quá nhiều yêu cầu. Vui lòng thử lại sau.';
    case 10:
      return 'Quyền truy cập bị từ chối. Vui lòng cấp đầy đủ quyền cho ứng dụng.';
    case 190:
      return ERROR_MESSAGES.INVALID_TOKEN;
    case 200:
      return 'Quyền truy cập Page bị từ chối.';
    default:
      return error.message || ERROR_MESSAGES.CONNECTION_FAILED;
  }
}
