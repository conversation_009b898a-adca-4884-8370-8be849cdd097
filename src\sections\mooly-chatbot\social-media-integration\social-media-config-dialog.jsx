/**
 * Social Media Configuration Dialog
 * Dialog để cấu hình auto-reply cho social media accounts
 */

import { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Stack,
  Typography,
  Switch,
  FormControlLabel,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  Box,
  Alert,
  Divider
} from '@mui/material';
import { LoadingButton } from '@mui/lab';

import { Iconify } from 'src/components/iconify';
import { 
  REPLY_TONES, 
  REPLY_LANGUAGES,
  PLATFORM_COLORS,
  PLATFORM_ICONS 
} from 'src/actions/mooly-chatbot/social-media-integration/social-media-constants';
import { 
  useSocialMediaConfig,
  useSocialMediaConfigMutations 
} from 'src/actions/mooly-chatbot/social-media-integration';

export function SocialMediaConfigDialog({ 
  open, 
  onClose, 
  onSuccess, 
  account 
}) {
  const [config, setConfig] = useState({
    enableCommentReply: false,
    enableMessageReply: false,
    enableInstagramComments: false,
    enableInstagramMessages: false,
    enableInstagramStoryReplies: false,
    autoPrivateReply: false,
    enableAutoLikeComments: false,
    replyPrompt: '',
    replyTone: REPLY_TONES.FRIENDLY,
    replyLanguage: REPLY_LANGUAGES.VIETNAMESE,
    maxReplyLength: 500,
    instagramReplyDelaySeconds: 5,
    businessInfo: '',
    products: '',
    policies: '',
    excludeKeywords: []
  });

  const [newKeyword, setNewKeyword] = useState('');
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);

  // Hooks - sử dụng pageId đúng format
  const { 
    config: existingConfig, 
    loading: configLoading 
  } = useSocialMediaConfig(account?.pageId || account?.page_id || account?.id, account?.platform);

  const { saveConfig } = useSocialMediaConfigMutations();

  // Load existing config
  useEffect(() => {
    if (existingConfig) {
      setConfig({
        enableCommentReply: existingConfig.enableCommentReply || false,
        enableMessageReply: existingConfig.enableMessageReply || false,
        enableInstagramComments: existingConfig.enableInstagramComments || false,
        enableInstagramMessages: existingConfig.enableInstagramMessages || false,
        enableInstagramStoryReplies: existingConfig.enableInstagramStoryReplies || false,
        autoPrivateReply: existingConfig.autoPrivateReply || false,
        enableAutoLikeComments: existingConfig.enableAutoLikeComments || false,
        replyPrompt: existingConfig.replyPrompt || '',
        replyTone: existingConfig.replyTone || REPLY_TONES.FRIENDLY,
        replyLanguage: existingConfig.replyLanguage || REPLY_LANGUAGES.VIETNAMESE,
        maxReplyLength: existingConfig.maxReplyLength || 500,
        instagramReplyDelaySeconds: existingConfig.instagramReplyDelaySeconds || 5,
        businessInfo: existingConfig.businessInfo || '',
        products: existingConfig.products || '',
        policies: existingConfig.policies || '',
        excludeKeywords: existingConfig.excludeKeywords || []
      });
    }
  }, [existingConfig]);

  const handleSave = async () => {
    try {
      setSaving(true);
      setError(null);

      // Debug logging
      console.log('💾 Saving social media config for account:', account);

      // Validate required fields - chỉ cần pageId, tenant sẽ được database tự động xử lý
      const pageId = account.pageId || account.page_id || account.id;

      if (!pageId) {
        throw new Error('Không tìm thấy page ID. Vui lòng kiểm tra kết nối tài khoản.');
      }

      // Data theo supabase-utils pattern (camelCase) - database sẽ auto-convert và set tenant
      const configData = {
        pageId: pageId,
        enableCommentReply: config.enableCommentReply,
        enableMessageReply: config.enableMessageReply,
        enableInstagramComments: config.enableInstagramComments,
        enableInstagramMessages: config.enableInstagramMessages,
        enableInstagramStoryReplies: config.enableInstagramStoryReplies,
        autoPrivateReply: config.autoPrivateReply,
        enableAutoLikeComments: config.enableAutoLikeComments,
        replyPrompt: config.replyPrompt,
        replyTone: config.replyTone,
        replyLanguage: config.replyLanguage,
        maxReplyLength: config.maxReplyLength,
        instagramReplyDelaySeconds: config.instagramReplyDelaySeconds,
        businessInfo: config.businessInfo,
        products: config.products,
        policies: config.policies,
        excludeKeywords: config.excludeKeywords
      };

      console.log('📤 Sending config data to service:', configData);

      const result = await saveConfig(configData);
      
      console.log('📥 Service response:', result);
      
      if (result.success) {
        onSuccess?.();
      } else {
        setError(result.error || 'Không thể lưu cấu hình');
      }
    } catch (err) {
      console.error('Error saving config:', err);
      setError(err.message || 'Có lỗi xảy ra khi lưu cấu hình');
    } finally {
      setSaving(false);
    }
  };

  const handleAddKeyword = () => {
    if (newKeyword.trim() && !config.excludeKeywords.includes(newKeyword.trim())) {
      setConfig(prev => ({
        ...prev,
        excludeKeywords: [...prev.excludeKeywords, newKeyword.trim()]
      }));
      setNewKeyword('');
    }
  };

  const handleRemoveKeyword = (keyword) => {
    setConfig(prev => ({
      ...prev,
      excludeKeywords: prev.excludeKeywords.filter(k => k !== keyword)
    }));
  };

  if (!account) return null;

  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      maxWidth="md"
      fullWidth
    >
      <DialogTitle>
        <Stack direction="row" alignItems="center" spacing={2}>
          <Iconify 
            icon={PLATFORM_ICONS[account.platform]} 
            sx={{ color: PLATFORM_COLORS[account.platform] }}
          />
          <Box>
            <Typography variant="h6">
              Cấu hình Auto-Reply
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {account.page_name || account.username}
            </Typography>
          </Box>
        </Stack>
      </DialogTitle>

      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        <Stack spacing={3}>
          {/* Basic Settings */}
          <Box>
            <Typography variant="subtitle1" gutterBottom>
              Cài đặt cơ bản
            </Typography>
            <Stack spacing={2}>
              <FormControlLabel
                control={
                  <Switch
                    checked={config.enableCommentReply}
                    onChange={(e) => setConfig(prev => ({ ...prev, enableCommentReply: e.target.checked }))}
                  />
                }
                label="Tự động trả lời comments"
              />
              
              <FormControlLabel
                control={
                  <Switch
                    checked={config.enableMessageReply}
                    onChange={(e) => setConfig(prev => ({ ...prev, enableMessageReply: e.target.checked }))}
                  />
                }
                label="Tự động trả lời tin nhắn"
              />

              {account.platform === 'instagram' && (
                <>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={config.enableInstagramStoryReplies}
                        onChange={(e) => setConfig(prev => ({ ...prev, enableInstagramStoryReplies: e.target.checked }))}
                      />
                    }
                    label="Tự động trả lời story replies"
                  />
                  
                  <FormControlLabel
                    control={
                      <Switch
                        checked={config.enableAutoLikeComments}
                        onChange={(e) => setConfig(prev => ({ ...prev, enableAutoLikeComments: e.target.checked }))}
                      />
                    }
                    label="Tự động like comments"
                  />
                </>
              )}
            </Stack>
          </Box>

          <Divider />

          {/* AI Configuration */}
          <Box>
            <Typography variant="subtitle1" gutterBottom>
              Cấu hình AI
            </Typography>
            <Stack spacing={2}>
              <FormControl fullWidth>
                <InputLabel>Tone trả lời</InputLabel>
                <Select
                  value={config.replyTone}
                  onChange={(e) => setConfig(prev => ({ ...prev, replyTone: e.target.value }))}
                  label="Tone trả lời"
                >
                  {Object.entries(REPLY_TONES).map(([key, value]) => (
                    <MenuItem key={key} value={value}>
                      {value === 'friendly' && 'Thân thiện'}
                      {value === 'professional' && 'Chuyên nghiệp'}
                      {value === 'casual' && 'Thoải mái'}
                      {value === 'enthusiastic' && 'Nhiệt tình'}
                      {value === 'helpful' && 'Hữu ích'}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              <FormControl fullWidth>
                <InputLabel>Ngôn ngữ</InputLabel>
                <Select
                  value={config.replyLanguage}
                  onChange={(e) => setConfig(prev => ({ ...prev, replyLanguage: e.target.value }))}
                  label="Ngôn ngữ"
                >
                  <MenuItem value="vi">Tiếng Việt</MenuItem>
                  <MenuItem value="en">English</MenuItem>
                  <MenuItem value="auto">Tự động phát hiện</MenuItem>
                </Select>
              </FormControl>

              <TextField
                fullWidth
                multiline
                rows={3}
                label="Prompt tùy chỉnh"
                value={config.replyPrompt}
                onChange={(e) => setConfig(prev => ({ ...prev, replyPrompt: e.target.value }))}
                placeholder="Nhập hướng dẫn cho AI về cách trả lời..."
              />
            </Stack>
          </Box>

          <Divider />

          {/* Business Information */}
          <Box>
            <Typography variant="subtitle1" gutterBottom>
              Thông tin doanh nghiệp
            </Typography>
            <Stack spacing={2}>
              <TextField
                fullWidth
                multiline
                rows={2}
                label="Thông tin doanh nghiệp"
                value={config.businessInfo}
                onChange={(e) => setConfig(prev => ({ ...prev, businessInfo: e.target.value }))}
                placeholder="Mô tả về doanh nghiệp của bạn..."
              />

              <TextField
                fullWidth
                multiline
                rows={2}
                label="Sản phẩm/Dịch vụ"
                value={config.products}
                onChange={(e) => setConfig(prev => ({ ...prev, products: e.target.value }))}
                placeholder="Danh sách sản phẩm/dịch vụ..."
              />
            </Stack>
          </Box>
        </Stack>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} disabled={saving}>
          Hủy
        </Button>
        <LoadingButton
          loading={saving}
          onClick={handleSave}
          variant="contained"
        >
          Lưu cấu hình
        </LoadingButton>
      </DialogActions>
    </Dialog>
  );
}
