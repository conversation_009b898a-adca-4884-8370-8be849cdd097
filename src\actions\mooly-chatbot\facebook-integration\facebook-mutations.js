import { useState, useCallback } from 'react';
import { 
  upsertFacebookAccount,
  updateFacebookAccount,
  deleteFacebookAccount,
  upsertAutoReplyConfig,
  deleteAutoReplyConfig,
  logFacebookActivity
} from './facebook-api';
import { ACTIVITY_TYPES, SUCCESS_MESSAGES, ERROR_MESSAGES } from './facebook-constants';

/**
 * Hook để quản lý mutations cho Facebook accounts
 * @returns {Object} - Mutation functions và states
 */
export function useFacebookAccountMutations() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const createOrUpdateAccount = useCallback(async (accountData) => {
    try {
      setLoading(true);
      setError(null);

      const result = await upsertFacebookAccount(accountData);
      
      if (!result.success) {
        setError(result.error);
      }
      
      return result;
    } catch (err) {
      setError(err);
      return { success: false, error: err, data: null };
    } finally {
      setLoading(false);
    }
  }, []);

  const updateAccount = useCallback(async (accountId, accountData) => {
    try {
      setLoading(true);
      setError(null);

      const result = await updateFacebookAccount(accountId, accountData);
      
      if (!result.success) {
        setError(result.error);
      }
      
      return result;
    } catch (err) {
      setError(err);
      return { success: false, error: err, data: null };
    } finally {
      setLoading(false);
    }
  }, []);

  const deleteAccount = useCallback(async (accountId) => {
    try {
      setLoading(true);
      setError(null);

      const result = await deleteFacebookAccount(accountId);
      
      if (!result.success) {
        setError(result.error);
      }
      
      return result;
    } catch (err) {
      setError(err);
      return { success: false, error: err, data: null };
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    createOrUpdateAccount,
    updateAccount,
    deleteAccount,
    loading,
    error
  };
}

/**
 * Hook để quản lý mutations cho Auto Reply Config
 * @returns {Object} - Mutation functions và states
 */
export function useAutoReplyConfigMutations() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const saveConfig = useCallback(async (configData) => {
    try {
      setLoading(true);
      setError(null);

      const result = await upsertAutoReplyConfig(configData);
      
      if (!result.success) {
        setError(result.error);
      }
      
      return result;
    } catch (err) {
      setError(err);
      return { success: false, error: err, data: null };
    } finally {
      setLoading(false);
    }
  }, []);

  const deleteConfig = useCallback(async (pageId) => {
    try {
      setLoading(true);
      setError(null);

      const result = await deleteAutoReplyConfig(pageId);
      
      if (!result.success) {
        setError(result.error);
      }
      
      return result;
    } catch (err) {
      setError(err);
      return { success: false, error: err, data: null };
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    saveConfig,
    deleteConfig,
    loading,
    error
  };
}

/**
 * Hook để quản lý page selection cho chatbot
 * @returns {Object} - Mutation functions và states
 */
export function usePageSelectionMutations() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const selectPageForChatbot = useCallback(async (pageId, chatbotId, pageName) => {
    try {
      setLoading(true);
      setError(null);

      // Log page selection activity
      const result = await logFacebookActivity(
        pageId,
        ACTIVITY_TYPES.PAGE_SELECTED,
        { chatbotId, pageName }
      );
      
      if (!result.success) {
        setError(result.error);
      }
      
      return result;
    } catch (err) {
      setError(err);
      return { success: false, error: err, data: null };
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    selectPageForChatbot,
    loading,
    error
  };
}

/**
 * Hook tổng hợp cho tất cả Facebook mutations
 * @returns {Object} - Tất cả mutation functions và states
 */
export function useFacebookMutations() {
  const accountMutations = useFacebookAccountMutations();
  const configMutations = useAutoReplyConfigMutations();
  const selectionMutations = usePageSelectionMutations();

  const loading = accountMutations.loading || configMutations.loading || selectionMutations.loading;
  const error = accountMutations.error || configMutations.error || selectionMutations.error;

  // Combined mutation for complete setup flow
  const setupFacebookIntegration = useCallback(async (setupData) => {
    try {
      const { accountData, configData, chatbotId } = setupData;

      // 1. Create/Update Facebook account
      const accountResult = await accountMutations.createOrUpdateAccount(accountData);
      if (!accountResult.success) {
        return accountResult;
      }

      // 2. Select page for chatbot
      const selectionResult = await selectionMutations.selectPageForChatbot(
        accountData.pageId,
        chatbotId,
        accountData.pageName
      );
      if (!selectionResult.success) {
        return selectionResult;
      }

      // 3. Save auto reply config if provided
      if (configData) {
        const configResult = await configMutations.saveConfig({
          ...configData,
          pageId: accountData.pageId
        });
        if (!configResult.success) {
          return configResult;
        }
      }

      return {
        success: true,
        data: {
          account: accountResult.data,
          config: configData ? await configMutations.saveConfig(configData) : null
        },
        error: null
      };
    } catch (err) {
      return { success: false, error: err, data: null };
    }
  }, [accountMutations, configMutations, selectionMutations]);

  return {
    // Account mutations
    createOrUpdateAccount: accountMutations.createOrUpdateAccount,
    updateAccount: accountMutations.updateAccount,
    deleteAccount: accountMutations.deleteAccount,
    
    // Config mutations
    saveConfig: configMutations.saveConfig,
    deleteConfig: configMutations.deleteConfig,
    
    // Selection mutations
    selectPageForChatbot: selectionMutations.selectPageForChatbot,
    
    // Combined mutations
    setupFacebookIntegration,
    
    // States
    loading,
    error
  };
}
