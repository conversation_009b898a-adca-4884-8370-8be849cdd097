'use client';

/**
 * Message Formatting Service
 * Quản lý định dạng tin nhắn với hình ảnh và nút bấm
 */

// =====================================================
// 1. CONSTANTS & TYPES
// =====================================================

export const MESSAGE_TYPES = {
  TEXT: 'text',
  FORMATTED: 'formatted'
};

export const BUTTON_TYPES = {
  LINK: 'link',
  POSTBACK: 'postback'
};

// Giới hạn theo yêu cầu
export const FORMATTING_LIMITS = {
  MAX_IMAGES: 3,
  MAX_BUTTONS: 3,
  MAX_BUTTON_TITLE_LENGTH: 50,
  MAX_MESSAGE_LENGTH: 2000
};

// =====================================================
// 2. VALIDATION FUNCTIONS
// =====================================================

/**
 * Validate message formatting data
 * @param {Object} messageData - Dữ liệu tin nhắn
 * @returns {Object} - Validation result
 */
export function validateMessageFormatting(messageData) {
  const errors = [];

  // Validate message content
  if (!messageData.content || messageData.content.trim().length === 0) {
    errors.push('Nội dung tin nhắn không được để trống');
  }

  if (messageData.content && messageData.content.length > FORMATTING_LIMITS.MAX_MESSAGE_LENGTH) {
    errors.push(`Nội dung tin nhắn không được vượt quá ${FORMATTING_LIMITS.MAX_MESSAGE_LENGTH} ký tự`);
  }

  // Validate images
  if (messageData.images && messageData.images.length > FORMATTING_LIMITS.MAX_IMAGES) {
    errors.push(`Chỉ được upload tối đa ${FORMATTING_LIMITS.MAX_IMAGES} hình ảnh`);
  }

  // Validate each image object
  if (messageData.images) {
    messageData.images.forEach((image, index) => {
      if (!isValidImageObject(image)) {
        errors.push(`Hình ảnh ${index + 1} không hợp lệ`);
      }
    });
  }

  // Validate buttons
  if (messageData.buttons && messageData.buttons.length > FORMATTING_LIMITS.MAX_BUTTONS) {
    errors.push(`Chỉ được tạo tối đa ${FORMATTING_LIMITS.MAX_BUTTONS} nút bấm`);
  }

  // Validate each button
  if (messageData.buttons) {
    messageData.buttons.forEach((button, index) => {
      if (!button.title || button.title.trim().length === 0) {
        errors.push(`Tiêu đề nút ${index + 1} không được để trống`);
      }

      if (button.title && button.title.length > FORMATTING_LIMITS.MAX_BUTTON_TITLE_LENGTH) {
        errors.push(`Tiêu đề nút ${index + 1} không được vượt quá ${FORMATTING_LIMITS.MAX_BUTTON_TITLE_LENGTH} ký tự`);
      }

      if (!button.type || !Object.values(BUTTON_TYPES).includes(button.type)) {
        errors.push(`Loại nút ${index + 1} không hợp lệ`);
      }

      if (button.type === BUTTON_TYPES.LINK && (!button.url || !isValidUrl(button.url))) {
        errors.push(`URL nút ${index + 1} không hợp lệ`);
      }

      if (button.type === BUTTON_TYPES.POSTBACK && (!button.payload || button.payload.trim().length === 0)) {
        errors.push(`Payload nút ${index + 1} không được để trống`);
      }
    });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate URL format
 * @param {string} url - URL to validate
 * @returns {boolean} - Is valid URL
 */
function isValidUrl(url) {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

// =====================================================
// 3. MESSAGE FORMATTING FUNCTIONS
// =====================================================

/**
 * Tạo message formatting object mới
 * @param {string} content - Nội dung tin nhắn
 * @param {Array} images - Danh sách hình ảnh
 * @param {Array} buttons - Danh sách nút bấm
 * @returns {Object} - Message formatting object
 */
export function createMessageFormatting(content = '', images = [], buttons = []) {
  return {
    type: MESSAGE_TYPES.FORMATTED,
    content: content.trim(),
    images: images.slice(0, FORMATTING_LIMITS.MAX_IMAGES),
    buttons: buttons.slice(0, FORMATTING_LIMITS.MAX_BUTTONS),
    createdAt: new Date().toISOString()
  };
}

/**
 * Tạo button object mới
 * @param {string} title - Tiêu đề nút
 * @param {string} type - Loại nút (link/postback)
 * @param {string} value - URL hoặc payload
 * @returns {Object} - Button object
 */
export function createButton(title, type, value) {
  const button = {
    id: `btn_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
    title: title.trim(),
    type
  };

  if (type === BUTTON_TYPES.LINK) {
    button.url = value;
  } else if (type === BUTTON_TYPES.POSTBACK) {
    button.payload = value;
  }

  return button;
}

/**
 * Tạo image object mới (deprecated - use upload service instead)
 * @param {string} url - URL hình ảnh
 * @param {string} alt - Alt text
 * @returns {Object} - Image object
 */
export function createImage(url, alt = '') {
  return {
    id: `img_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
    url,
    alt: alt.trim(),
    uploadedAt: new Date().toISOString()
  };
}

/**
 * Validate image object from upload service
 * @param {Object} imageObj - Image object from upload service
 * @returns {boolean} - Is valid image object
 */
export function isValidImageObject(imageObj) {
  return imageObj &&
         typeof imageObj === 'object' &&
         imageObj.id &&
         imageObj.url &&
         typeof imageObj.url === 'string' &&
         imageObj.url.startsWith('http');
}

// =====================================================
// 4. FOLLOWUP RULE EXTENSIONS
// =====================================================

/**
 * Mở rộng createFollowupRule để hỗ trợ message formatting
 * @param {number} delayMinutes - Thời gian delay (phút)
 * @param {string|Object} messageData - Nội dung tin nhắn hoặc message formatting object
 * @returns {Object} - Extended rule object
 */
export function createFormattedFollowupRule(delayMinutes, messageData) {
  const rule = {
    id: `rule_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
    delayMinutes: parseInt(delayMinutes, 10),
    isEnabled: true,
    order: 1, // Temporary order, sẽ được cập nhật khi sort
    createdAt: new Date().toISOString()
  };

  // Xử lý message data
  if (typeof messageData === 'string') {
    // Text message đơn giản
    rule.message = messageData.trim();
    rule.messageType = MESSAGE_TYPES.TEXT;
  } else if (messageData && typeof messageData === 'object') {
    // Formatted message với images và buttons
    rule.message = messageData.content || '';
    rule.messageType = MESSAGE_TYPES.FORMATTED;
    rule.messageFormatting = {
      images: messageData.images || [],
      buttons: messageData.buttons || []
    };
  }

  return rule;
}

/**
 * Convert rule cũ sang format mới (backward compatibility)
 * @param {Object} rule - Rule object cũ
 * @returns {Object} - Rule object đã convert
 */
export function convertLegacyRule(rule) {
  if (!rule.messageType) {
    // Rule cũ chỉ có text message
    return {
      ...rule,
      messageType: MESSAGE_TYPES.TEXT,
      messageFormatting: null
    };
  }
  return rule;
}

/**
 * Check if rule có message formatting
 * @param {Object} rule - Rule object
 * @returns {boolean} - Has formatting
 */
export function hasMessageFormatting(rule) {
  return rule.messageType === MESSAGE_TYPES.FORMATTED && 
         rule.messageFormatting && 
         (rule.messageFormatting.images?.length > 0 || rule.messageFormatting.buttons?.length > 0);
}

// =====================================================
// 5. PREVIEW FUNCTIONS
// =====================================================

/**
 * Generate preview text cho formatted message
 * @param {Object} rule - Rule object
 * @returns {string} - Preview text
 */
export function generateMessagePreview(rule) {
  let preview = rule.message || '';
  
  if (hasMessageFormatting(rule)) {
    const { images, buttons } = rule.messageFormatting;
    
    if (images?.length > 0) {
      preview += ` [${images.length} hình ảnh]`;
    }
    
    if (buttons?.length > 0) {
      preview += ` [${buttons.length} nút bấm]`;
    }
  }
  
  return preview.length > 100 ? `${preview.substring(0, 100)}...` : preview;
}

// =====================================================
// 6. EXPORT CONSTANTS
// =====================================================

export const BUTTON_TYPE_OPTIONS = [
  { value: BUTTON_TYPES.LINK, label: 'Liên kết (Link)', icon: 'eva:external-link-fill' },
  { value: BUTTON_TYPES.POSTBACK, label: 'Hành động (Postback)', icon: 'eva:flash-fill' }
];

export const MESSAGE_TEMPLATES_FORMATTED = [
  {
    content: 'Xin chào! Bạn có cần hỗ trợ gì thêm không?',
    messageType: MESSAGE_TYPES.FORMATTED,
    messageFormatting: {
      images: [],
      buttons: [
        { id: 'btn_1', title: 'Tư vấn sản phẩm', type: BUTTON_TYPES.POSTBACK, payload: 'PRODUCT_CONSULTATION' },
        { id: 'btn_2', title: 'Liên hệ hotline', type: BUTTON_TYPES.LINK, url: 'tel:+84123456789' }
      ]
    }
  },
  {
    content: 'Cảm ơn bạn đã quan tâm! Chúng tôi có thể giúp gì cho bạn?',
    messageType: MESSAGE_TYPES.FORMATTED,
    messageFormatting: {
      images: [],
      buttons: [
        { id: 'btn_3', title: 'Xem sản phẩm', type: BUTTON_TYPES.POSTBACK, payload: 'VIEW_PRODUCTS' },
        { id: 'btn_4', title: 'Báo giá', type: BUTTON_TYPES.POSTBACK, payload: 'REQUEST_QUOTE' },
        { id: 'btn_5', title: 'Website', type: BUTTON_TYPES.LINK, url: 'https://example.com' }
      ]
    }
  }
];
