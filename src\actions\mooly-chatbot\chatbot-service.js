'use client';

import { useMemo, useState, useEffect, useCallback } from 'react';

import { useAuthContext } from 'src/auth/hooks';

import storageService from './storage-service';
import { fetchData, createData, updateData, deleteData, upsertData } from './supabase-utils';

// Tên bảng trong Supabase
const TABLE_NAME = 'chatbot_configurations';

/**
 * Lấy danh sách cấu hình chatbot với các tùy chọn lọc
 * @param {Object} options - <PERSON><PERSON><PERSON> tùy chọn truy vấn
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getChatbots(options = {}) {
  return fetchData(TABLE_NAME, options);
}

/**
 * Lấy chi tiết một cấu hình chatbot theo ID
 * @param {string} chatbotId - ID của cấu hình chatbot
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getChatbotById(chatbotId) {
  if (!chatbotId) {
    return { success: false, error: 'Chatbot ID is required', data: null };
  }

  try {
    const result = await fetchData(TABLE_NAME, {
      filters: { id: chatbotId },
      single: true,
    });
    return result;
  } catch (error) {
    return { success: false, error, data: null };
  }
}

/**
 * Lấy cấu hình chatbot theo store ID
 * @param {string} storeId - ID của cửa hàng
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getChatbotByStoreId(storeId) {
  if (!storeId) return { success: false, error: 'Store ID is required', data: null };

  return fetchData(TABLE_NAME, {
    filters: { store_id: storeId },
    single: true,
  });
}

/**
 * Lấy danh sách tools có sẵn từ database
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getAvailableTools() {
  try {
    const result = await fetchData('chatbot_tools_configuration', {
      columns: 'tool_id, tool_name, tool_description',
      distinct: true,
      orderBy: 'tool_name',
      ascending: true
    });

    if (!result.success) {
      return { success: false, error: result.error, data: [] };
    }

    // Convert database tools to frontend format
    const tools = result.data?.map(tool => ({
      id: tool.toolId,
      label: tool.toolName,
      description: tool.toolDescription,
      // Thêm hasDetailConfig nếu cần thiết cho một số tools
      hasDetailConfig: tool.toolId === 'product_card_display'
    })) || [];

    return { success: true, data: tools };
  } catch (error) {
    console.error('Error fetching available tools:', error);
    return { success: false, error, data: [] };
  }
}

/**
 * Tạo cấu hình chatbot mới
 * @param {Object} chatbotData - Dữ liệu cấu hình chatbot
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function createChatbot(chatbotData) {
  return createData(TABLE_NAME, chatbotData);
}

/**
 * Cập nhật cấu hình chatbot
 * @param {string} chatbotId - ID của cấu hình chatbot
 * @param {Object} chatbotData - Dữ liệu cập nhật
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function updateChatbot(chatbotId, chatbotData) {
  if (!chatbotId) return { success: false, error: 'Chatbot ID is required', data: null };

  return updateData(TABLE_NAME, chatbotData, { id: chatbotId });
}

/**
 * Xóa cấu hình chatbot
 * @param {string} chatbotId - ID của cấu hình chatbot
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function deleteChatbot(chatbotId) {
  if (!chatbotId) return { success: false, error: 'Chatbot ID is required', data: null };

  return deleteData(TABLE_NAME, { id: chatbotId });
}

/**
 * Tạo hoặc cập nhật cấu hình chatbot
 * @param {Object} chatbotData - Dữ liệu cấu hình chatbot
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function upsertChatbot(chatbotData) {
  return upsertData(TABLE_NAME, chatbotData);
}

/**
 * Hook để lấy danh sách cấu hình chatbot
 * @param {Object} options - Các tùy chọn truy vấn
 * @returns {Object} - Kết quả và các hàm điều khiển
 */
export function useChatbots(options = {}) {
  const [data, setData] = useState(null);
  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isValidating, setIsValidating] = useState(false);

  // Memoize stable options để tránh re-render không cần thiết
  const memoizedOptions = useMemo(() => {
    // Nếu options là null, không fetch data
    if (options === null) return null;
    
    // Đảm bảo options stable bằng cách normalize
    const stable = {};
    
    // Filters
    if (options.filters && typeof options.filters === 'object') {
      stable.filters = options.filters;
    }
    
    // OrderBy
    if (options.orderBy && Array.isArray(options.orderBy)) {
      stable.orderBy = options.orderBy;
    }
    
    // Pagination
    if (options.pagination && typeof options.pagination === 'object') {
      stable.pagination = options.pagination;
    }
    
    return stable;
  }, [
    JSON.stringify(options?.filters || {}),
    JSON.stringify(options?.orderBy || []),
    JSON.stringify(options?.pagination || {}),
  ]);

  const fetchChatbots = useCallback(async () => {
    // Không fetch nếu options là null
    if (memoizedOptions === null) {
      setData(null);
      setError(null);
      setIsLoading(false);
      setIsValidating(false);
      return { success: true, data: [], error: null };
    }

    setIsValidating(true);
    try {
      const result = await getChatbots(memoizedOptions);
      setData(result);
      setError(result.error || null);
      return result;
    } catch (err) {
      setError(err);
      return { success: false, error: err, data: null };
    } finally {
      setIsValidating(false);
      setIsLoading(false);
    }
  }, [memoizedOptions]);

  // Hàm để tải lại dữ liệu
  const mutate = useCallback(async () => fetchChatbots(), [fetchChatbots]);

  // Tải dữ liệu khi component mount, chỉ khi có options
  useEffect(() => {
    if (memoizedOptions !== null) {
      setIsLoading(true);
      fetchChatbots();
    }
  }, [fetchChatbots, memoizedOptions]);

  const memoizedValue = useMemo(
    () => ({
      chatbots: data?.data || [],
      totalCount: data?.count || 0,
      isLoading,
      isError: !!error,
      error,
      isValidating,
      mutate,
      isEmpty: !isLoading && !isValidating && (!data?.data || data.data.length === 0),
    }),
    [data, error, isLoading, isValidating, mutate]
  );

  return memoizedValue;
}

/**
 * Hook để lấy chi tiết cấu hình chatbot
 * @param {string} chatbotId - ID của cấu hình chatbot
 * @returns {Object} - Kết quả và các hàm điều khiển
 */
export function useChatbot(chatbotId) {
  const [data, setData] = useState(null);
  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isValidating, setIsValidating] = useState(false);

  const fetchChatbot = useCallback(async () => {
    if (!chatbotId) {
      console.log('useChatbot: chatbotId is empty or null');
      setData(null);
      setError(null);
      setIsLoading(false);
      setIsValidating(false);
      return null;
    }

    setIsValidating(true);
    try {
      const result = await getChatbotById(chatbotId);
      setData(result);
      setError(result.error || null);
      return result;
    } catch (err) {
      setError(err);
      return { success: false, error: err, data: null };
    } finally {
      setIsValidating(false);
      setIsLoading(false);
    }
  }, [chatbotId]);

  // Hàm để tải lại dữ liệu
  const mutate = useCallback(async () => fetchChatbot(), [fetchChatbot]);

  // Tải dữ liệu khi component mount hoặc chatbotId thay đổi
  useEffect(() => {
    if (chatbotId) {
      setIsLoading(true);
      fetchChatbot();
    }
  }, [fetchChatbot, chatbotId]);

  const memoizedValue = useMemo(
    () => ({
      chatbot: data?.data || null,
      isLoading,
      isError: !!error,
      error,
      isValidating,
      mutate,
    }),
    [data, error, isLoading, isValidating, mutate]
  );

  return memoizedValue;
}

/**
 * Hook để lấy cấu hình chatbot theo store ID
 * @param {string} storeId - ID của cửa hàng
 * @returns {Object} - Kết quả và các hàm điều khiển
 */
export function useChatbotByStore(storeId) {
  const [data, setData] = useState(null);
  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isValidating, setIsValidating] = useState(false);

  const fetchChatbotByStore = useCallback(async () => {
    if (!storeId) {
      setData(null);
      setError(null);
      setIsLoading(false);
      setIsValidating(false);
      return null;
    }

    setIsValidating(true);
    try {
      const result = await getChatbotByStoreId(storeId);
      setData(result);
      setError(result.error || null);
      return result;
    } catch (err) {
      setError(err);
      return { success: false, error: err, data: null };
    } finally {
      setIsValidating(false);
      setIsLoading(false);
    }
  }, [storeId]);

  // Hàm để tải lại dữ liệu
  const mutate = useCallback(async () => fetchChatbotByStore(), [fetchChatbotByStore]);

  // Tải dữ liệu khi component mount hoặc storeId thay đổi
  useEffect(() => {
    if (storeId) {
      setIsLoading(true);
      fetchChatbotByStore();
    }
  }, [fetchChatbotByStore, storeId]);

  const memoizedValue = useMemo(
    () => ({
      chatbot: data?.data || null,
      isLoading,
      isError: !!error,
      error,
      isValidating,
      mutate,
    }),
    [data, error, isLoading, isValidating, mutate]
  );

  return memoizedValue;
}

/**
 * Hook để tạo, cập nhật, xóa cấu hình chatbot
 * @returns {Object} - Các hàm mutation
 */
export function useChatbotMutations() {
  const [loadingStates, setLoadingStates] = useState({
    creating: false,
    updating: false,
    deleting: false,
    upserting: false,
    uploadingAvatar: false,
    deletingAvatar: false,
  });

  // ✅ OPTIMIZED: No need to get tenant_id on client-side
  // Database triggers and RLS policies handle tenant isolation automatically

  // Helper function to handle loading state and error handling
  const withLoadingState = async (stateKey, asyncFn) => {
    setLoadingStates((prev) => ({ ...prev, [stateKey]: true }));
    try {
      return await asyncFn();
    } finally {
      setLoadingStates((prev) => ({ ...prev, [stateKey]: false }));
    }
  };

  // Mutation functions using the helper
  const createChatbotMutation = (chatbotData) =>
    withLoadingState('creating', () => createChatbot(chatbotData));

  const updateChatbotMutation = (id, data) =>
    withLoadingState('updating', () => updateChatbot(id, data));

  const deleteChatbotMutation = (chatbotId) =>
    withLoadingState('deleting', () => deleteChatbot(chatbotId));

  const upsertChatbotMutation = (chatbotData) =>
    withLoadingState('upserting', () => upsertChatbot(chatbotData));

  /**
   * Xử lý tải lên avatar chatbot
   * @param {File} avatarFile - File avatar cần tải lên
   * @param {string|null} oldAvatarUrl - URL avatar cũ (nếu có)
   * @returns {Promise<Object>} - Kết quả từ API với URL avatar
   */
  const uploadChatbotAvatarMutation = async (avatarFile, oldAvatarUrl) =>
    withLoadingState('uploadingAvatar', async () => {
      try {
        if (!avatarFile) {
          return { success: false, error: 'Missing required file', avatarUrl: null };
        }

        // Xóa avatar cũ nếu có
        if (oldAvatarUrl) {
          await deleteChatbotAvatarMutation(oldAvatarUrl);
        }

        // ✅ OPTIMIZED: No tenant_id validation needed on client-side
        // Database RLS policies will handle tenant isolation automatically

        // Tạo tên file duy nhất
        const fileName = storageService.generateUniqueFileName(avatarFile.name);
        // Tạo đường dẫn lưu trữ (đồng bộ với product service)
        const filePath = `chatbots/${fileName}`;

        // Tải lên avatar mới (đồng bộ với product service)
        const uploadResult = await storageService.uploadFile('public', filePath, avatarFile, {
          upsert: true,
          cacheControl: '3600',
        });

        if (uploadResult.success) {
          return { success: true, avatarUrl: uploadResult.publicUrl };
        }

        return {
          success: false,
          error: uploadResult.error,
          avatarUrl: null,
        };
      } catch (error) {
        return { success: false, error, avatarUrl: null };
      }
    });

  /**
   * Xóa avatar chatbot
   * @param {string} avatarUrl - URL avatar cần xóa
   * @returns {Promise<Object>} - Kết quả từ API
   */
  const deleteChatbotAvatarMutation = (avatarUrl) =>
    withLoadingState('deletingAvatar', async () => {
      try {
        if (!avatarUrl) return { success: true };

        // Trích xuất đường dẫn từ URL
        const avatarPath = storageService.extractPathFromUrl(avatarUrl);
        if (!avatarPath) {
          console.warn('Could not extract path from avatar URL:', avatarUrl);
          return { success: false, error: 'Invalid avatar URL format' };
        }

        // Kiểm tra xem đường dẫn có chứa 'public/' ở đầu không
        const cleanPath = avatarPath.startsWith('public/')
          ? avatarPath.substring(7) // Cắt bỏ 'public/' ở đầu nếu có
          : avatarPath;

        // Xóa file từ storage
        const deleteResult = await storageService.deleteFiles('public', cleanPath);

        if (deleteResult.success) {
          return { success: true };
        }

        return { success: false, error: deleteResult.error };
      } catch (error) {
        return { success: false, error };
      }
    });

  // Calculate if any mutation is in progress
  const isMutating = Object.values(loadingStates).some(Boolean);

  return {
    createChatbot: createChatbotMutation,
    updateChatbot: updateChatbotMutation,
    deleteChatbot: deleteChatbotMutation,
    upsertChatbot: upsertChatbotMutation,
    uploadChatbotAvatar: uploadChatbotAvatarMutation,
    deleteChatbotAvatar: deleteChatbotAvatarMutation,
    isCreating: loadingStates.creating,
    isUpdating: loadingStates.updating,
    isDeleting: loadingStates.deleting,
    isUpserting: loadingStates.upserting,
    isMutating,
  };
}
