# 🧪 HƯỚNG DẪN TEST HỆ THỐNG ĐƠN HÀNG TỐI ƯU

## 🚀 **CÁCH TRUY CẬP**

### 1. **Danh sách đơn hàng Enhanced**
```
URL: /dashboard/mooly-chatbot/orders
```
- Giao diện danh sách đơn hàng với enhanced features
- Quick actions trong table rows
- Batch operations
- Real-time analytics toggle

### 2. **Dashboard đơn hàng**
```
URL: /dashboard/mooly-chatbot/orders/dashboard
```
- Tổng quan thống kê
- Quick stats cards
- Status overview
- Quick actions

## 🔧 **CÁC TÍNH NĂNG CẦN TEST**

### ✅ **1. Enhanced Order Status Dialog**
**Cách test:**
1. Vào danh sách đơn hàng
2. Click vào icon settings (⚙️) trong table row
3. Hoặc click "Cập nhật trạng thái" trong menu actions

**Kiểm tra:**
- [ ] Dialog mở đúng
- [ ] Hiển thị thông tin đơn hàng
- [ ] Dropdown trạng thái chỉ hiển thị trạng thái hợp lệ
- [ ] Preview inventory impact
- [ ] Toggle auto inventory update
- [ ] Toggle customer notification

### ✅ **2. Quick Actions trong Table Row**
**Cách test:**
1. Vào danh sách đơn hàng
2. Xem các button nhỏ dưới status chip

**Kiểm tra:**
- [ ] Hiển thị tối đa 2 trạng thái tiếp theo
- [ ] Click button cập nhật trạng thái nhanh
- [ ] Loading state khi đang cập nhật
- [ ] Toast notification khi thành công

### ✅ **3. Batch Operations**
**Cách test:**
1. Chọn nhiều đơn hàng (checkbox)
2. Xem thanh batch operations xuất hiện

**Kiểm tra:**
- [ ] Thanh batch operations hiển thị
- [ ] Hiển thị số lượng đơn hàng được chọn
- [ ] Các button: Cập nhật trạng thái, Xác nhận, Hủy, Hoàn thành
- [ ] Confirmation dialog trước khi thực hiện
- [ ] Progress và kết quả batch operation

### ✅ **4. Enhanced Order History**
**Cách test:**
1. Vào chi tiết đơn hàng
2. Xem tab lịch sử

**Kiểm tra:**
- [ ] Timeline hiển thị đúng
- [ ] Kết hợp status changes và inventory transactions
- [ ] Hiển thị user thực hiện thay đổi
- [ ] Timestamps chính xác
- [ ] Icons và colors phù hợp

### ✅ **5. Order Analytics**
**Cách test:**
1. Vào danh sách đơn hàng
2. Click icon chart để toggle analytics
3. Hoặc vào dashboard đơn hàng

**Kiểm tra:**
- [ ] Charts hiển thị đúng
- [ ] Stats cards với số liệu chính xác
- [ ] Filter theo date range
- [ ] Business type specific insights
- [ ] Real-time updates

### ✅ **6. Inventory Tracking**
**Cách test:**
1. Tạo đơn hàng với sản phẩm có inventory
2. Thay đổi trạng thái: pending → confirmed
3. Kiểm tra inventory đã giảm
4. Thay đổi sang cancelled
5. Kiểm tra inventory đã tăng lại

**Kiểm tra:**
- [ ] Inventory giảm khi confirmed
- [ ] Inventory tăng khi cancelled/refunded
- [ ] Inventory transactions được tạo
- [ ] Không ảnh hưởng đến digital products

## 🎯 **TEST SCENARIOS**

### **Scenario 1: Retail Business Flow**
```
Business Type: Retail
Product: Sản phẩm vật lý có inventory
Flow: pending → confirmed → paid → processing → packaging → shipping → delivered → completed
```

**Steps:**
1. Set business type = retail
2. Tạo đơn hàng với sản phẩm vật lý
3. Test từng bước chuyển trạng thái
4. Kiểm tra inventory tracking

### **Scenario 2: Digital Business Flow**
```
Business Type: Digital
Product: Sản phẩm số
Flow: pending → confirmed → paid → preparing → ready_download → sent → completed
```

**Steps:**
1. Set business type = digital
2. Tạo đơn hàng với sản phẩm số
3. Test flow khác với retail
4. Kiểm tra không có inventory tracking

### **Scenario 3: Batch Operations**
```
Chọn 5 đơn hàng với trạng thái pending
Batch update sang confirmed
```

**Steps:**
1. Chọn nhiều đơn hàng pending
2. Click "Xác nhận" trong batch operations
3. Confirm trong dialog
4. Kiểm tra tất cả đã chuyển sang confirmed
5. Kiểm tra inventory đã được cập nhật

## 🐛 **COMMON ISSUES & FIXES**

### **Issue 1: Business type không load**
**Fix:** Kiểm tra business-config-service và đảm bảo tenant có business_type

### **Issue 2: Inventory không cập nhật**
**Fix:** Kiểm tra database functions đã được tạo và có permissions

### **Issue 3: Quick actions không hiển thị**
**Fix:** Kiểm tra STATUS_FLOWS config và business type

### **Issue 4: Analytics không load**
**Fix:** Kiểm tra get_order_analytics function và RLS policies

## 📊 **PERFORMANCE TESTING**

### **Load Testing**
- Test với 100+ đơn hàng
- Batch operations với 20+ đơn hàng
- Analytics với large dataset

### **Real-time Updates**
- Mở 2 tabs, cập nhật ở tab 1, kiểm tra tab 2
- Test concurrent updates

## 🔒 **SECURITY TESTING**

### **RLS Policies**
- Test với different tenants
- Đảm bảo không thấy data của tenant khác

### **Permissions**
- Test với different user roles
- Kiểm tra function permissions

## 📝 **TEST CHECKLIST**

### **UI/UX**
- [ ] Responsive design
- [ ] Loading states
- [ ] Error handling
- [ ] Toast notifications
- [ ] Confirmation dialogs

### **Functionality**
- [ ] Status validation
- [ ] Inventory tracking
- [ ] Batch operations
- [ ] Analytics accuracy
- [ ] Real-time updates

### **Performance**
- [ ] Fast loading
- [ ] Smooth animations
- [ ] No memory leaks
- [ ] Efficient queries

### **Security**
- [ ] RLS working
- [ ] No data leaks
- [ ] Proper permissions
- [ ] Input validation

## 🚨 **EMERGENCY ROLLBACK**

Nếu có vấn đề nghiêm trọng:

1. **Revert page:**
```javascript
// Trong src/app/dashboard/mooly-chatbot/orders/page.jsx
import OrderListView from 'src/sections/mooly-chatbot/orders/view/order-list-view';
export default function OrdersPage() {
  return <OrderListView />;
}
```

2. **Revert navigation:**
```javascript
// Xóa children trong nav-config
{
  title: 'Đơn hàng',
  path: paths.dashboard.moolyChatbot.orders.root,
  icon: ICONS.order,
}
```

---

**Happy Testing! 🎉**
