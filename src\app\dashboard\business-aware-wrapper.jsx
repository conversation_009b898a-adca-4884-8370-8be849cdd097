'use client';

import { useMemo } from 'react';

import { DashboardLayout } from 'src/layouts/dashboard';
import { useOptimizedNavData } from 'src/layouts/nav-config-dashboard';

import { WelcomeCreditsNotification } from 'src/components/welcome-credits-notification';
import { OAuthUserSetup } from 'src/components/auth/oauth-user-setup';

import { AuthGuard } from 'src/auth/guard';

// ----------------------------------------------------------------------

/**
 * Optimized Dashboard Layout
 * Uses single navigation configuration for consistent UX
 * Removed business-aware complexity for better performance
 */
function EnhancedDashboardLayout({ children }) {
  // Use optimized navigation hook for better performance and consistency
  // This eliminates loading states and reload issues completely
  const { navData } = useOptimizedNavData();

  const slotProps = useMemo(() => ({
    nav: {
      data: navData
    }
  }), [navData]);

  // Always render immediately since navigation is always ready
  // useOptimizedNavData always returns loading: false and isReady: true
  return (
    <DashboardLayout slotProps={slotProps}>
      <OAuthUserSetup>
        {children}
        <WelcomeCreditsNotification />
      </OAuthUserSetup>
    </DashboardLayout>
  );
}

/**
 * Optimized Dashboard Wrapper
 * Handles authentication with consistent navigation
 * Eliminates loading issues and provides stable performance
 */
export function BusinessAwareDashboardWrapper({ children, withAuth = false }) {
  if (withAuth) {
    return (
      <AuthGuard>
        <EnhancedDashboardLayout>{children}</EnhancedDashboardLayout>
      </AuthGuard>
    );
  }

  return <EnhancedDashboardLayout>{children}</EnhancedDashboardLayout>;
}
