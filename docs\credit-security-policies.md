# Credit Security Policies Documentation

## Overview
Hệ thống credit đã được cấu hình với RLS policies nghiêm ngặt để đảm bảo chỉ admin (service role) có thể thực hiện các thao tác thêm/sửa/xóa credit. Người dùng chỉ có thể xem credit và tạo payment requests.

## RLS Policies Summary

### 1. tenant_credits
| Operation | Role | Permission | Description |
|-----------|------|------------|-------------|
| SELECT | authenticated | ✅ | Users can view their tenant's credit balance |
| INSERT/UPDATE/DELETE | service_role | ✅ | Only admin can modify credit balances |
| INSERT/UPDATE/DELETE | authenticated | ❌ | Users cannot modify credits directly |

### 2. credit_transactions
| Operation | Role | Permission | Description |
|-----------|------|------------|-------------|
| SELECT | authenticated | ✅ | Users can view their tenant's transactions |
| INSERT | service_role | ✅ | Only admin can create transactions |
| UPDATE | service_role | ✅ | Only admin can update transactions |
| INSERT/UPDATE/DELETE | authenticated | ❌ | Users cannot modify transactions |

### 3. credit_payments
| Operation | Role | Permission | Description |
|-----------|------|------------|-------------|
| SELECT | authenticated | ✅ | Users can view their tenant's payments |
| INSERT | authenticated | ✅ | Users can create payment requests (pending status only) |
| UPDATE | service_role | ✅ | Only admin can update payment status |
| DELETE | service_role | ✅ | Only admin can delete payments |

### 4. credit_audit_logs
| Operation | Role | Permission | Description |
|-----------|------|------------|-------------|
| SELECT | authenticated | ✅ | Only tenant owners can view audit logs |
| INSERT | service_role | ✅ | Only admin can create audit logs |
| UPDATE/DELETE | ALL | ❌ | Audit logs are immutable |

### 5. credit_packages
| Operation | Role | Permission | Description |
|-----------|------|------------|-------------|
| SELECT | public | ✅ | All users can view available packages |
| INSERT/UPDATE/DELETE | admin | ✅ | Only admin can manage packages |

## Admin Functions

### admin_add_tenant_credit()
- **Purpose**: Add credits to tenant account
- **Access**: Service role only
- **Parameters**: tenant_id, amount, description, reference_type, reference_id, created_by
- **Returns**: JSON with transaction details
- **Security**: Validates caller role, creates audit trail

### admin_deduct_tenant_credit()
- **Purpose**: Deduct credits from tenant account
- **Access**: Service role only
- **Parameters**: tenant_id, amount, description, reference_type, reference_id, created_by
- **Returns**: JSON with transaction details
- **Security**: Validates caller role, checks sufficient balance, creates audit trail

## API Endpoints

### User APIs
- `GET /api/credit-balance` - View credit balance
- `POST /api/use-credit` - Use credits (calls admin function internally)
- `GET /api/credit-transactions` - View transaction history

### Admin APIs
- `POST /api/admin/complete-payment` - Complete payment and add credits

## Security Features

### 1. Role-based Access Control
- **Users (authenticated)**: Read-only access + payment creation
- **Admin (service_role)**: Full access to all credit operations

### 2. Tenant Isolation
- All policies enforce tenant_id filtering
- Users can only access their own tenant's data

### 3. Audit Trail
- All credit operations are logged in credit_audit_logs
- Includes user_id, IP address, metadata

### 4. Data Integrity
- Transactions and audit logs are immutable
- Credit balances can only be modified through admin functions
- Payment status can only be updated by admin

### 5. Validation
- Payment requests must be positive amounts
- Credit deductions check sufficient balance
- All operations validate tenant ownership

## Usage Examples

### For Users
```javascript
// View credit balance
const balance = await getTenantCredit();

// Create payment request
const payment = await createCreditPayment({
  packageId: 'pkg-123',
  amount: 50000,
  creditAmount: 200,
  paymentMethod: 'payos'
});

// Use credits (internally calls admin function)
const result = await useCredit(1, 'Chatbot response');
```

### For Admin/System
```sql
-- Add credits after successful payment
SELECT admin_add_tenant_credit(
  'tenant-uuid',
  200,
  'Payment completed',
  'payment',
  'payment-uuid',
  'user-uuid'
);

-- Deduct credits for usage
SELECT admin_deduct_tenant_credit(
  'tenant-uuid',
  1,
  'Chatbot usage',
  'chatbot_usage',
  'session-uuid',
  'user-uuid'
);
```

## Migration Notes

### Changes Made
1. Removed broad "ALL" policies for users
2. Created specific policies for each operation
3. Restricted credit modifications to service role only
4. Added admin-only functions for credit operations
5. Enhanced audit logging

### Breaking Changes
- Users can no longer directly modify credit_transactions
- Users can no longer directly modify tenant_credits
- All credit modifications must go through admin functions

### Backward Compatibility
- User APIs remain the same (internally use admin functions)
- Payment flow unchanged for users
- Credit balance viewing unchanged

## Monitoring

### Key Metrics to Monitor
1. Failed credit operations (insufficient permissions)
2. Suspicious payment patterns
3. Credit balance discrepancies
4. Audit log completeness

### Alerts to Set Up
1. Multiple failed credit operations from same user
2. Large credit transactions
3. Credit operations outside business hours
4. Missing audit log entries

## Troubleshooting

### Common Issues
1. **"Unauthorized: Only admin can add credits"**
   - Solution: Ensure API calls use service role or admin functions

2. **"Insufficient credit balance"**
   - Solution: Check tenant credit balance, add credits if needed

3. **"Payment not found"**
   - Solution: Verify payment ID and tenant ownership

### Debug Queries
```sql
-- Check user permissions
SELECT has_credit_permission('view_credits');

-- View recent credit operations
SELECT * FROM credit_audit_logs 
WHERE tenant_id = 'your-tenant-id' 
ORDER BY created_at DESC LIMIT 10;

-- Check credit balance
SELECT * FROM tenant_credits 
WHERE tenant_id = 'your-tenant-id';
```
