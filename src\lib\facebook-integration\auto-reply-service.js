import { FacebookAIService } from './ai-service.js';
import { createClient } from 'src/utils/supabase/server';

/**
 * Facebook Auto Reply Service
 * Main service to handle auto replies for Facebook/Instagram
 */

export class FacebookAutoReplyService {
  constructor() {
    this.aiService = new FacebookAIService();
  }

  /**
   * Process webhook event and generate auto reply if needed
   */
  async processWebhookEvent(pageId, eventType, eventData) {
    try {
      console.log('🔄 Processing webhook event:', { pageId, eventType });

      // Get auto reply configuration
      const config = await this.getAutoReplyConfig(pageId);
      if (!config) {
        console.log('ℹ️ No auto reply config found for page:', pageId);
        return { processed: false, reason: 'No config found' };
      }

      // Check if auto reply is enabled for this event type
      if (!this.isAutoReplyEnabled(config, eventType)) {
        console.log('ℹ️ Auto reply not enabled for event type:', eventType);
        return { processed: false, reason: 'Auto reply disabled' };
      }

      // Extract message content
      const messageContent = this.extractMessageContent(eventData, eventType);
      if (!messageContent) {
        console.log('ℹ️ No message content found in event');
        return { processed: false, reason: 'No message content' };
      }

      // Check exclude keywords
      if (this.shouldExcludeMessage(messageContent, config.exclude_keywords)) {
        console.log('ℹ️ Message excluded due to keywords:', messageContent);
        await this.logActivity(pageId, 'auto_reply_excluded', {
          eventType,
          message: messageContent,
          reason: 'excluded_keywords'
        });
        return { processed: false, reason: 'Message excluded' };
      }

      // Generate AI response
      const aiResult = await this.generateAIResponse(config, eventType, messageContent, eventData);
      if (!aiResult.success) {
        console.error('❌ Failed to generate AI response:', aiResult.error);
        await this.logActivity(pageId, 'auto_reply_failed', {
          eventType,
          message: messageContent,
          error: aiResult.error
        });
        return { processed: false, reason: 'AI generation failed', error: aiResult.error };
      }

      // Send the reply
      const sendResult = await this.sendReply(pageId, eventType, eventData, aiResult.response);
      if (!sendResult.success) {
        console.error('❌ Failed to send reply:', sendResult.error);
        await this.logActivity(pageId, 'auto_reply_failed', {
          eventType,
          message: messageContent,
          response: aiResult.response,
          error: sendResult.error
        });
        return { processed: false, reason: 'Send failed', error: sendResult.error };
      }

      // Log successful auto reply
      await this.logActivity(pageId, 'auto_reply_sent', {
        eventType,
        message: messageContent,
        response: aiResult.response,
        metadata: aiResult.metadata
      });

      console.log('✅ Auto reply sent successfully');
      return {
        processed: true,
        response: aiResult.response,
        metadata: aiResult.metadata
      };

    } catch (error) {
      console.error('💥 Error processing webhook event:', error);
      await this.logActivity(pageId, 'auto_reply_error', {
        eventType,
        error: error.message
      });
      return { processed: false, reason: 'Processing error', error: error.message };
    }
  }

  /**
   * Get auto reply configuration for a page
   */
  async getAutoReplyConfig(pageId) {
    try {
      const supabase = await createClient();
      
      const { data, error } = await supabase
        .from('facebook_auto_reply_config')
        .select('*')
        .eq('page_id', pageId)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('❌ Error fetching auto reply config:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('💥 Error getting auto reply config:', error);
      return null;
    }
  }

  /**
   * Check if auto reply is enabled for event type
   */
  isAutoReplyEnabled(config, eventType) {
    switch (eventType) {
      case 'facebook_comment':
        return config.enable_comment_reply;
      case 'facebook_message':
        return config.enable_message_reply;
      case 'instagram_comment':
        return config.enable_instagram_comments;
      case 'instagram_message':
        return config.enable_instagram_messages;
      default:
        return false;
    }
  }

  /**
   * Extract message content from event data
   */
  extractMessageContent(eventData, eventType) {
    if (eventType.includes('comment')) {
      return eventData.message || eventData.text || eventData.comment_text;
    } else if (eventType.includes('message')) {
      return eventData.message?.text || eventData.text;
    }
    return null;
  }

  /**
   * Check if message should be excluded
   */
  shouldExcludeMessage(message, excludeKeywords = []) {
    return this.aiService.shouldExcludeMessage(message, excludeKeywords);
  }

  /**
   * Generate AI response
   */
  async generateAIResponse(config, eventType, messageContent, eventData) {
    try {
      const platform = eventType.includes('instagram') ? 'instagram' : 'facebook';
      const type = eventType.includes('comment') ? 'comment' : 'message';
      
      const context = this.aiService.extractContext(eventData, type);

      const aiInput = {
        message: messageContent,
        type,
        platform,
        config,
        context
      };

      return await this.aiService.generateResponse(aiInput);
    } catch (error) {
      console.error('💥 Error generating AI response:', error);
      return {
        success: false,
        error: error.message,
        response: this.aiService.getFallbackResponse(config, eventType.includes('comment') ? 'comment' : 'message')
      };
    }
  }

  /**
   * Send reply via Facebook Graph API
   */
  async sendReply(pageId, eventType, eventData, message) {
    try {
      // Get page access token
      const pageData = await this.getPageData(pageId);
      if (!pageData) {
        throw new Error('Page data not found');
      }

      let apiUrl = '';
      let payload = {};

      if (eventType.includes('comment')) {
        // Reply to comment
        const commentId = eventData.comment_id || eventData.id;
        if (!commentId) {
          throw new Error('Comment ID not found');
        }

        apiUrl = `https://graph.facebook.com/v19.0/${commentId}/comments`;
        payload = {
          message: message,
          access_token: pageData.access_token
        };
      } else if (eventType.includes('message')) {
        // Reply to private message
        const senderId = eventData.sender?.id || eventData.from?.id;
        if (!senderId) {
          throw new Error('Sender ID not found');
        }

        apiUrl = `https://graph.facebook.com/v19.0/me/messages`;
        payload = {
          recipient: { id: senderId },
          message: { text: message },
          access_token: pageData.access_token
        };
      } else {
        throw new Error('Unsupported event type for reply');
      }

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload)
      });

      const data = await response.json();

      if (data.error) {
        throw new Error(`Facebook API error: ${data.error.message}`);
      }

      return {
        success: true,
        data: data
      };

    } catch (error) {
      console.error('💥 Error sending reply:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get page data including access token
   */
  async getPageData(pageId) {
    try {
      const supabase = await createClient();
      
      const { data, error } = await supabase
        .from('facebook_accounts')
        .select('access_token, page_name, instagram_account_id')
        .eq('page_id', pageId)
        .eq('is_active', true)
        .single();

      if (error) {
        console.error('❌ Error fetching page data:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('💥 Error getting page data:', error);
      return null;
    }
  }

  /**
   * Log activity to database
   */
  async logActivity(pageId, activity, metadata = {}) {
    try {
      const supabase = await createClient();
      
      await supabase
        .from('facebook_activity_logs')
        .insert({
          page_id: pageId,
          activity,
          metadata
        });

      console.log('📊 Activity logged:', activity);
    } catch (error) {
      console.error('💥 Error logging activity:', error);
    }
  }

  /**
   * Test auto reply configuration
   */
  async testAutoReply(pageId, testMessage, eventType = 'facebook_comment') {
    try {
      console.log('🧪 Testing auto reply for page:', pageId);

      const mockEventData = {
        message: testMessage,
        comment_id: 'test_comment_id',
        sender: { id: 'test_sender_id' },
        test: true
      };

      const result = await this.processWebhookEvent(pageId, eventType, mockEventData);
      
      return {
        success: true,
        result,
        message: result.processed ? 'Auto reply test successful' : 'Auto reply test completed (not processed)'
      };

    } catch (error) {
      console.error('💥 Error testing auto reply:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}
