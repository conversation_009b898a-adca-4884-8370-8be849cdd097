'use client';

import { useMemo } from 'react';

import { Box, Card, Collapse, Typography, CardContent } from '@mui/material';

import {
  isFeatureEnabled,
  useBusinessConfigContext,
  shouldShowUIElement,
  getProductFormConfig
} from 'src/actions/mooly-chatbot/business-config-service';

/**
 * Business-aware product form wrapper
 * Shows/hides form sections based on business type
 */
export function BusinessAwareProductForm({ children, onFieldVisibilityChange }) {
  const { businessType, loading } = useBusinessConfigContext();

  const formConfig = useMemo(() => {
    if (loading || !businessType) {
      return getDefaultFormConfig();
    }

    return getProductFormConfig(businessType);
  }, [businessType, loading]);

  const fieldVisibility = useMemo(() => {
    if (!businessType) return {};

    return {
      // Basic fields (always visible)
      name: true,
      description: true,
      category: true,

      // Media fields (always visible for all business types)
      images: true,
      avatar: true,

      // Physical product fields
      weight: shouldShowUIElement(businessType, 'weightDimensions', 'productForm'),
      dimensions: shouldShowUIElement(businessType, 'weightDimensions', 'productForm'),
      sku: true, // SKU luôn hiển thị cho tất cả business types

      // Inventory fields
      inventory: shouldShowUIElement(businessType, 'inventory', 'productForm'),
      stockQuantity: shouldShowUIElement(businessType, 'inventory', 'productForm'),
      lowStockThreshold: shouldShowUIElement(businessType, 'inventory', 'productForm'),

      // Digital product fields
      digitalFiles: shouldShowUIElement(businessType, 'digitalProducts', 'productForm'),
      downloadLimits: shouldShowUIElement(businessType, 'digitalProducts', 'productForm'),
      licenseKeys: shouldShowUIElement(businessType, 'licenseManagement', 'productForm'),

      // Service fields
      serviceInfo: shouldShowUIElement(businessType, 'services', 'productForm'),
      duration: shouldShowUIElement(businessType, 'timeBasedPricing', 'productForm'),
      staffAssignment: shouldShowUIElement(businessType, 'staffManagement', 'productForm'),

      // Variant fields
      variants: shouldShowUIElement(businessType, 'variants', 'productForm'),
      attributes: shouldShowUIElement(businessType, 'variants', 'productForm'),

      // Shipping fields
      shipping: shouldShowUIElement(businessType, 'shipping', 'productForm'),
      shippingClass: shouldShowUIElement(businessType, 'shipping', 'productForm'),

      // Tags and labels (always visible)
      tags: true,
      labels: true,
      pricing: true,
      seo: true,
    };
  }, [businessType]);

  // Notify parent about field visibility changes
  useMemo(() => {
    if (onFieldVisibilityChange) {
      onFieldVisibilityChange(fieldVisibility);
    }
  }, [fieldVisibility, onFieldVisibilityChange]);

  if (loading) {
    return <Box>Đang tải cấu hình...</Box>;
  }

  return (
    <BusinessFormProvider value={{ fieldVisibility, formConfig, businessType }}>
      {children}
    </BusinessFormProvider>
  );
}

/**
 * Business form context
 */
import { useContext, createContext } from 'react';

const BusinessFormContext = createContext(null);

function BusinessFormProvider({ children, value }) {
  return (
    <BusinessFormContext.Provider value={value}>
      {children}
    </BusinessFormContext.Provider>
  );
}

export function useBusinessFormContext() {
  const context = useContext(BusinessFormContext);
  if (!context) {
    throw new Error('useBusinessFormContext must be used within BusinessAwareProductForm');
  }
  return context;
}

/**
 * Conditional form section component
 */
export function ConditionalFormSection({
  field,
  title,
  children,
  emphasize = false,
  defaultOpen = true
}) {
  const { fieldVisibility, formConfig } = useBusinessFormContext();

  const isVisible = fieldVisibility[field];
  const isEmphasized = formConfig.emphasize?.includes(field) || emphasize;

  if (!isVisible) {
    return null;
  }

  return (
    <Card
      sx={{
        mb: 2,
        border: isEmphasized ? 2 : 1,
        borderColor: isEmphasized ? 'primary.main' : 'divider',
        boxShadow: isEmphasized ? 2 : 1
      }}
    >
      <CardContent>
        {title && (
          <Typography
            variant="h6"
            sx={{
              mb: 2,
              color: isEmphasized ? 'primary.main' : 'text.primary',
              fontWeight: isEmphasized ? 600 : 500
            }}
          >
            {title}
            {isEmphasized && (
              <Typography
                component="span"
                variant="caption"
                sx={{ ml: 1, color: 'primary.main' }}
              >
                (Quan trọng)
              </Typography>
            )}
          </Typography>
        )}
        <Collapse in={defaultOpen}>
          {children}
        </Collapse>
      </CardContent>
    </Card>
  );
}

/**
 * Conditional form field component
 */
export function ConditionalFormField({ field, children, fallback = null }) {
  const { fieldVisibility } = useBusinessFormContext();

  const isVisible = fieldVisibility[field];

  if (!isVisible) {
    return fallback;
  }

  return children;
}

/**
 * Business type indicator component
 */
export function BusinessTypeIndicator() {
  const { businessType } = useBusinessFormContext();

  if (!businessType) return null;

  const getBusinessTypeInfo = (type) => {
    switch (type) {
      case 'retail':
        return { label: 'Bán lẻ', color: 'primary' };
      case 'digital':
        return { label: 'Sản phẩm số', color: 'info' };
      case 'services':
        return { label: 'Dịch vụ', color: 'success' };
      case 'hybrid':
        return { label: 'Đa dạng', color: 'warning' };
      default:
        return { label: 'Không xác định', color: 'default' };
    }
  };

  const { label, color } = getBusinessTypeInfo(businessType);

  return (
    <Box sx={{ mb: 2, p: 2, bgcolor: 'background.neutral', borderRadius: 1 }}>
      <Typography variant="body2" color="text.secondary">
        Loại hình kinh doanh:
        <Typography
          component="span"
          variant="body2"
          sx={{
            ml: 1,
            fontWeight: 600,
            color: `${color}.main`
          }}
        >
          {label}
        </Typography>
      </Typography>
      <Typography variant="caption" color="text.secondary">
        Form được tối ưu cho loại hình kinh doanh này
      </Typography>
    </Box>
  );
}

/**
 * Get default form configuration
 */
function getDefaultFormConfig() {
  return {
    emphasize: [],
    hide: [],
    conditional: {}
  };
}

/**
 * Smart field ordering based on business type
 */
export function useBusinessAwareFieldOrder() {
  const { businessType, formConfig } = useBusinessFormContext();

  return useMemo(() => {
    if (!businessType) return [];

    const baseOrder = ['name', 'description', 'category'];
    const mediaFields = ['images', 'avatar']; // Media fields should come after basic info
    const emphasizedFields = formConfig.emphasize || [];
    const otherFields = [
      'price', 'sku', 'weight', 'dimensions',
      'inventory', 'variants', 'digitalFiles', 'serviceInfo', 'shipping',
      'pricing', 'tags', 'labels', 'seo'
    ];

    // Put emphasized fields first, then base fields, then media, then others
    return [
      ...emphasizedFields.filter(field =>
        !baseOrder.includes(field) && !mediaFields.includes(field)
      ),
      ...baseOrder,
      ...mediaFields,
      ...otherFields.filter(field =>
        !emphasizedFields.includes(field) &&
        !baseOrder.includes(field) &&
        !mediaFields.includes(field)
      )
    ];
  }, [businessType, formConfig]);
}

/**
 * Business-aware form validation
 */
export function useBusinessAwareValidation() {
  const { businessType } = useBusinessFormContext();

  return useMemo(() => {
    if (!businessType) return {};

    const validationRules = {};

    // Add business-specific validation rules
    if (isFeatureEnabled(businessType, 'inventory')) {
      validationRules.stockQuantity = {
        required: 'Số lượng tồn kho là bắt buộc',
        min: { value: 0, message: 'Số lượng không được âm' }
      };
    }

    if (isFeatureEnabled(businessType, 'weightDimensions')) {
      validationRules.weight = {
        required: 'Trọng lượng là bắt buộc cho sản phẩm vật lý',
        min: { value: 0, message: 'Trọng lượng không được âm' }
      };
    }

    if (isFeatureEnabled(businessType, 'digitalProducts')) {
      validationRules.digitalFiles = {
        required: 'File số là bắt buộc cho sản phẩm số'
      };
    }

    // Basic validation rules for all business types
    validationRules.name = {
      required: 'Tên sản phẩm là bắt buộc',
      minLength: { value: 2, message: 'Tên sản phẩm phải có ít nhất 2 ký tự' }
    };

    validationRules.sku = {
      required: 'Mã SKU là bắt buộc',
      pattern: {
        value: /^[A-Za-z0-9-_]+$/,
        message: 'Mã SKU chỉ được chứa chữ, số, dấu gạch ngang và gạch dưới'
      }
    };

    validationRules.price = {
      required: 'Giá sản phẩm là bắt buộc',
      min: { value: 0, message: 'Giá không được âm' }
    };

    validationRules.categoryId = {
      required: 'Danh mục là bắt buộc'
    };

    // Media validation rules (always required)
    validationRules.images = {
      required: 'Ít nhất một hình ảnh sản phẩm là bắt buộc',
      validate: {
        notEmpty: (value) => {
          if (!Array.isArray(value) || value.length === 0) {
            return 'Vui lòng thêm ít nhất một hình ảnh cho sản phẩm';
          }
          return true;
        }
      }
    };

    if (isFeatureEnabled(businessType, 'services')) {
      validationRules.duration = {
        required: 'Thời lượng là bắt buộc cho dịch vụ',
        min: { value: 1, message: 'Thời lượng phải lớn hơn 0' }
      };
    }

    return validationRules;
  }, [businessType]);
}
