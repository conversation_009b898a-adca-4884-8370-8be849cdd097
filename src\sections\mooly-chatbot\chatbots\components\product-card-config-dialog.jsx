'use client';

import { useState, useEffect } from 'react';
import { useForm, Controller } from 'react-hook-form';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Radio from '@mui/material/Radio';
import Dialog from '@mui/material/Dialog';
import Button from '@mui/material/Button';
import Switch from '@mui/material/Switch';
import Divider from '@mui/material/Divider';
import TextField from '@mui/material/TextField';
import RadioGroup from '@mui/material/RadioGroup';
import Typography from '@mui/material/Typography';
import DialogTitle from '@mui/material/DialogTitle';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import LoadingButton from '@mui/lab/LoadingButton';
import FormControlLabel from '@mui/material/FormControlLabel';
import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';

import { Iconify } from 'src/components/iconify';
import { toast } from 'src/components/snackbar';

import { useChatbotMutations } from 'src/actions/mooly-chatbot/chatbot-service';

// ----------------------------------------------------------------------

const DEFAULT_CONFIG = {
  enabled: false,
  cardStyle: 'compact',
  autoShowPrice: true,
  autoShowStock: true,
  autoShowDescription: false,
  maxButtons: 3,
  buttons: {
    detailButton: {
      enabled: true,
      title: 'Xem chi tiết',
      type: 'postback', // 'postback' hoặc 'website'
      value: 'VIEW_PRODUCT_DETAIL'
    },
    orderButton: {
      enabled: true,
      title: 'Mua hàng ngay',
      type: 'postback', // 'postback' hoặc 'website'
      value: 'ORDER_PRODUCT'
    },
    contactButton: {
      enabled: false,
      title: 'Liên hệ',
      type: 'postback', // 'postback' hoặc 'website'
      value: 'CONTACT_SUPPORT'
    }
  }
};

const CARD_STYLE_OPTIONS = [
  {
    value: 'compact',
    label: 'Gọn nhẹ',
    description: 'Hiển thị thông tin cơ bản: tên, giá, hình ảnh'
  },
  {
    value: 'detailed',
    label: 'Chi tiết',
    description: 'Hiển thị đầy đủ: tên, giá, mô tả, đánh giá, tồn kho'
  }
];

const BUTTON_TYPE_OPTIONS = [
  {
    value: 'postback',
    label: 'Postback',
    description: 'Gửi dữ liệu về chatbot để xử lý',
    icon: 'eva:message-circle-fill'
  },
  {
    value: 'website',
    label: 'Website',
    description: 'Mở liên kết website',
    icon: 'eva:external-link-fill'
  }
];

const BUTTON_CONFIGS = [
  {
    key: 'detailButton',
    label: 'Nút xem chi tiết',
    description: 'Nút để xem thông tin chi tiết sản phẩm',
    defaultTitle: 'Xem chi tiết',
    defaultPostback: 'VIEW_PRODUCT_DETAIL'
  },
  {
    key: 'orderButton',
    label: 'Nút mua hàng',
    description: 'Nút để đặt mua sản phẩm ngay',
    defaultTitle: 'Mua hàng ngay',
    defaultPostback: 'ORDER_PRODUCT'
  },
  {
    key: 'contactButton',
    label: 'Nút liên hệ',
    description: 'Nút để liên hệ hỗ trợ về sản phẩm',
    defaultTitle: 'Liên hệ',
    defaultPostback: 'CONTACT_SUPPORT'
  }
];

// ----------------------------------------------------------------------

export default function ProductCardConfigDialog({
  open,
  onClose,
  chatbot,
  onDataChange
}) {
  const { updateChatbot } = useChatbotMutations();
  const [isUpdating, setIsUpdating] = useState(false);

  const { control, handleSubmit, reset, watch } = useForm({
    defaultValues: DEFAULT_CONFIG
  });

  // Cập nhật form khi chatbot thay đổi
  useEffect(() => {
    if (chatbot?.productCardConfig) {
      const configData = {
        ...DEFAULT_CONFIG,
        ...chatbot.productCardConfig
      };

      // Xử lý migration từ cấu trúc cũ sang mới
      if (chatbot.productCardConfig.buttonTexts && !chatbot.productCardConfig.buttons) {
        const { buttonTexts } = chatbot.productCardConfig;
        configData.buttons = {
          detailButton: {
            enabled: true,
            title: buttonTexts.detailButton || DEFAULT_CONFIG.buttons.detailButton.title,
            type: 'postback',
            value: 'VIEW_PRODUCT_DETAIL'
          },
          orderButton: {
            enabled: true,
            title: buttonTexts.orderButton || DEFAULT_CONFIG.buttons.orderButton.title,
            type: 'postback',
            value: 'ORDER_PRODUCT'
          },
          contactButton: {
            enabled: false,
            title: buttonTexts.contactButton || DEFAULT_CONFIG.buttons.contactButton.title,
            type: 'postback',
            value: 'CONTACT_SUPPORT'
          }
        };
      }

      reset(configData);
    } else {
      reset(DEFAULT_CONFIG);
    }
  }, [chatbot, reset]);

  // Watch form values
  const watchedValues = watch();

  const onSubmit = async (data) => {
    setIsUpdating(true);

    try {
      const updatePayload = {
        productCardConfig: data
      };
      
      const result = await updateChatbot(chatbot.id, updatePayload);

      if (result.success) {
        if (onDataChange) {
          onDataChange();
        }
        toast.success('Đã cập nhật cấu hình product card thành công');
        onClose();
      } else {
        toast.error(result.error?.message || 'Có lỗi xảy ra khi cập nhật cấu hình');
      }
    } catch (error) {
      console.error('Error updating product card config:', error);
      toast.error('Có lỗi xảy ra khi cập nhật cấu hình');
    } finally {
      setIsUpdating(false);
    }
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      slotProps={{
        paper: { sx: { borderRadius: 2 } }
      }}
    >
      <DialogTitle>
        <Stack direction="row" alignItems="center" spacing={2}>
          <Box sx={{ 
            p: 1, 
            borderRadius: 1.5, 
            bgcolor: 'primary.lighter',
            color: 'primary.main'
          }}>
            <Iconify icon="eva:cube-fill" width={24} />
          </Box>
          <Box>
            <Typography variant="h6">
              Cấu hình Product Card
            </Typography>
            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              Tùy chỉnh cách hiển thị thông tin sản phẩm cho khách hàng
            </Typography>
          </Box>
        </Stack>
      </DialogTitle>

      <Divider />

      <DialogContent sx={{ p: 3 }}>
        <form onSubmit={handleSubmit(onSubmit)}>
          <Stack spacing={4}>
            
            {/* Enable/Disable */}
            <Card variant="outlined" sx={{ p: 3 }}>
              <Controller
                name="enabled"
                control={control}
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Switch
                        checked={field.value}
                        onChange={field.onChange}
                        color="primary"
                      />
                    }
                    label={
                      <Box>
                        <Typography variant="subtitle1">
                          Kích hoạt tính năng Product Card
                        </Typography>
                        <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                          Cho phép chatbot gửi thông tin sản phẩm dưới dạng card đẹp mắt
                        </Typography>
                      </Box>
                    }
                    sx={{ m: 0, alignItems: 'flex-start', gap: 2 }}
                  />
                )}
              />
            </Card>

            {/* Configuration - Only show when enabled */}
            {watchedValues.enabled && (
              <>
                {/* Card Style */}
                <Card variant="outlined" sx={{ p: 3 }}>
                  <Typography variant="subtitle1" gutterBottom>
                    Kiểu hiển thị card
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'text.secondary', mb: 3 }}>
                    Chọn mức độ chi tiết thông tin hiển thị trên product card
                  </Typography>

                  <Controller
                    name="cardStyle"
                    control={control}
                    render={({ field }) => (
                      <RadioGroup
                        value={field.value}
                        onChange={field.onChange}
                        row
                      >
                        {CARD_STYLE_OPTIONS.map((option) => (
                          <Card 
                            key={option.value}
                            variant={field.value === option.value ? "elevation" : "outlined"}
                            sx={{ 
                              p: 2, 
                              mr: 2,
                              cursor: 'pointer',
                              minWidth: 200,
                              border: field.value === option.value ? 
                                '2px solid' : '1px solid',
                              borderColor: field.value === option.value ? 
                                'primary.main' : 'divider',
                              bgcolor: field.value === option.value ? 
                                'primary.lighter' : 'transparent',
                              '&:hover': {
                                borderColor: 'primary.main'
                              }
                            }}
                            onClick={() => field.onChange(option.value)}
                          >
                            <Stack spacing={1} alignItems="center">
                              <Radio
                                checked={field.value === option.value}
                                value={option.value}
                                sx={{ p: 0 }}
                              />
                              <Typography variant="subtitle2" textAlign="center">
                                {option.label}
                              </Typography>
                              <Typography variant="caption" textAlign="center" sx={{ color: 'text.secondary' }}>
                                {option.description}
                              </Typography>
                            </Stack>
                          </Card>
                        ))}
                      </RadioGroup>
                    )}
                  />
                </Card>

                {/* Display Options */}
                <Card variant="outlined" sx={{ p: 3 }}>
                  <Typography variant="subtitle1" gutterBottom>
                    Tùy chọn hiển thị
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'text.secondary', mb: 3 }}>
                    Cấu hình thông tin nào sẽ được hiển thị tự động trên card
                  </Typography>

                  <Stack spacing={2}>
                    <Controller
                      name="autoShowPrice"
                      control={control}
                      render={({ field }) => (
                        <FormControlLabel
                          control={
                            <Switch
                              checked={field.value}
                              onChange={field.onChange}
                              color="primary"
                            />
                          }
                          label={
                            <Box>
                              <Typography variant="body1">
                                Hiển thị giá sản phẩm
                              </Typography>
                              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                                Tự động hiển thị giá bán trên product card
                              </Typography>
                            </Box>
                          }
                        />
                      )}
                    />

                    <Controller
                      name="autoShowStock"
                      control={control}
                      render={({ field }) => (
                        <FormControlLabel
                          control={
                            <Switch
                              checked={field.value}
                              onChange={field.onChange}
                              color="primary"
                            />
                          }
                          label={
                            <Box>
                              <Typography variant="body1">
                                Hiển thị trạng thái tồn kho
                              </Typography>
                              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                                Hiển thị số lượng còn lại hoặc trạng thái hết hàng
                              </Typography>
                            </Box>
                          }
                        />
                      )}
                    />


                  </Stack>
                </Card>

                {/* Button Configuration */}
                <Card variant="outlined" sx={{ p: 3 }}>
                  <Typography variant="subtitle1" gutterBottom>
                    Cấu hình các nút hành động
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'text.secondary', mb: 3 }}>
                    Tùy chỉnh các nút hiển thị trên product card và hành động của chúng
                  </Typography>

                  <Stack spacing={3}>
                    {BUTTON_CONFIGS.map((buttonConfig) => (
                      <Accordion key={buttonConfig.key} variant="outlined">
                        <AccordionSummary
                          expandIcon={<Iconify icon="eva:arrow-ios-downward-fill" />}
                          sx={{
                            '& .MuiAccordionSummary-content': {
                              alignItems: 'center',
                              gap: 2
                            }
                          }}
                        >
                          <Controller
                            name={`buttons.${buttonConfig.key}.enabled`}
                            control={control}
                            render={({ field }) => (
                              <Switch
                                checked={field.value}
                                onChange={field.onChange}
                                onClick={(e) => e.stopPropagation()}
                                color="primary"
                                size="small"
                              />
                            )}
                          />
                          <Box>
                            <Typography variant="subtitle2">
                              {buttonConfig.label}
                            </Typography>
                            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                              {buttonConfig.description}
                            </Typography>
                          </Box>
                        </AccordionSummary>

                        <AccordionDetails>
                          <Stack spacing={3}>
                            {/* Button Title */}
                            <Controller
                              name={`buttons.${buttonConfig.key}.title`}
                              control={control}
                              render={({ field }) => (
                                <TextField
                                  {...field}
                                  label="Tiêu đề nút"
                                  placeholder={buttonConfig.defaultTitle}
                                  fullWidth
                                  size="small"
                                />
                              )}
                            />

                            {/* Button Type */}
                            <Box>
                              <Typography variant="body2" gutterBottom sx={{ fontWeight: 600 }}>
                                Loại hành động
                              </Typography>
                              <Controller
                                name={`buttons.${buttonConfig.key}.type`}
                                control={control}
                                render={({ field }) => (
                                  <RadioGroup
                                    value={field.value}
                                    onChange={field.onChange}
                                    row
                                  >
                                    {BUTTON_TYPE_OPTIONS.map((option) => (
                                      <Card
                                        key={option.value}
                                        variant={field.value === option.value ? "elevation" : "outlined"}
                                        sx={{
                                          p: 2,
                                          mr: 2,
                                          cursor: 'pointer',
                                          minWidth: 160,
                                          border: field.value === option.value ?
                                            '2px solid' : '1px solid',
                                          borderColor: field.value === option.value ?
                                            'primary.main' : 'divider',
                                          bgcolor: field.value === option.value ?
                                            'primary.lighter' : 'transparent',
                                          '&:hover': {
                                            borderColor: 'primary.main'
                                          }
                                        }}
                                        onClick={() => field.onChange(option.value)}
                                      >
                                        <Stack spacing={1} alignItems="center">
                                          <Iconify
                                            icon={option.icon}
                                            width={24}
                                            sx={{
                                              color: field.value === option.value ?
                                                'primary.main' : 'text.secondary'
                                            }}
                                          />
                                          <Radio
                                            checked={field.value === option.value}
                                            value={option.value}
                                            sx={{ p: 0 }}
                                          />
                                          <Typography variant="subtitle2" textAlign="center">
                                            {option.label}
                                          </Typography>
                                          <Typography variant="caption" textAlign="center" sx={{ color: 'text.secondary' }}>
                                            {option.description}
                                          </Typography>
                                        </Stack>
                                      </Card>
                                    ))}
                                  </RadioGroup>
                                )}
                              />
                            </Box>

                            {/* Button Value */}
                            <Controller
                              name={`buttons.${buttonConfig.key}.value`}
                              control={control}
                              render={({ field }) => (
                                <TextField
                                  {...field}
                                  label={
                                    watchedValues.buttons?.[buttonConfig.key]?.type === 'website'
                                      ? "URL Website"
                                      : "Postback Value"
                                  }
                                  placeholder={
                                    watchedValues.buttons?.[buttonConfig.key]?.type === 'website'
                                      ? "https://example.com/product/{product_id}"
                                      : buttonConfig.defaultPostback
                                  }
                                  fullWidth
                                  size="small"
                                  helperText={
                                    watchedValues.buttons?.[buttonConfig.key]?.type === 'website'
                                      ? "Sử dụng {product_id} để thay thế ID sản phẩm trong URL"
                                      : "Giá trị sẽ được gửi về chatbot khi người dùng nhấn nút"
                                  }
                                />
                              )}
                            />
                          </Stack>
                        </AccordionDetails>
                      </Accordion>
                    ))}
                  </Stack>
                </Card>
              </>
            )}
          </Stack>
        </form>
      </DialogContent>

      <Divider />

      <DialogActions sx={{ p: 3, gap: 1 }}>
        <Button 
          variant="outlined" 
          onClick={handleClose}
          disabled={isUpdating}
        >
          Hủy
        </Button>
        <LoadingButton
          variant="contained"
          onClick={handleSubmit(onSubmit)}
          loading={isUpdating}
          startIcon={<Iconify icon="eva:save-fill" width={16} />}
        >
          Lưu cấu hình
        </LoadingButton>
      </DialogActions>
    </Dialog>
  );
} 
