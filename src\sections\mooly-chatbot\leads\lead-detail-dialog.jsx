'use client';

import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>ton,
  Avatar,
  Typography,
  DialogTitle,
  DialogContent,
  DialogActions,
  Divider,
  Grid,
  Card,
} from '@mui/material';

import { Label } from 'src/components/label';
import { Iconify } from 'src/components/iconify';

import { getLeadStatusOptions } from 'src/actions/mooly-chatbot/chatbot-lead-service';

// ----------------------------------------------------------------------

// Utility function to format date
const formatDate = (date) => {
  const d = new Date(date);
  return d.toLocaleDateString('vi-VN', {
    day: '2-digit',
    month: '2-digit', 
    year: 'numeric'
  });
};

const formatDateTime = (date) => {
  const d = new Date(date);
  return d.toLocaleString('vi-VN', {
    day: '2-digit',
    month: '2-digit', 
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// ----------------------------------------------------------------------

export default function LeadDetailDialog({ open, onClose, currentLead, onEdit }) {
  if (!currentLead) return null;

  const statusOptions = getLeadStatusOptions();
  const statusOption = statusOptions.find((option) => option.value === currentLead.status);

  let leadData = {};
  try {
    if (currentLead.leadData) {
      // Nếu leadData đã là object, sử dụng trực tiếp
      if (typeof currentLead.leadData === 'object') {
        leadData = currentLead.leadData;
      } else if (typeof currentLead.leadData === 'string') {
        // Nếu là string, parse JSON
        leadData = JSON.parse(currentLead.leadData);
      }
    }
  } catch (error) {
    // Nếu parse lỗi, sử dụng object rỗng
    leadData = {};
  }

  const sourceOptions = {
    website: 'Website',
    facebook: 'Facebook',
    zalo: 'Zalo',
    google: 'Google',
    manual: 'Thủ công',
    other: 'Khác',
  };

  const renderInfoItem = (label, value, icon) => (
    <Stack direction="row" spacing={2} alignItems="center">
      {icon && <Iconify icon={icon} width={20} sx={{ color: 'text.secondary' }} />}
      <Box>
        <Typography variant="caption" sx={{ color: 'text.secondary' }}>
          {label}
        </Typography>
        <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
          {value || 'Chưa có thông tin'}
        </Typography>
      </Box>
    </Stack>
  );

  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      maxWidth="md"
      fullWidth
    >
      <DialogTitle>
        <Stack direction="row" alignItems="center" justifyContent="space-between">
          <Typography variant="h6">Chi Tiết Lead</Typography>
          <Label
            variant="soft"
            color={statusOption?.color || 'default'}
          >
            {statusOption?.label || currentLead.status}
          </Label>
        </Stack>
      </DialogTitle>

      <DialogContent>
        <Stack spacing={3}>
          {/* Header với avatar và thông tin cơ bản */}
          <Card sx={{ p: 3 }}>
            <Stack direction="row" spacing={3} alignItems="center">
              <Avatar 
                sx={{ 
                  width: 80, 
                  height: 80, 
                  bgcolor: 'primary.main',
                  fontSize: '2rem'
                }}
              >
                {currentLead.fullName?.charAt(0) || currentLead.email?.charAt(0) || '?'}
              </Avatar>
              
              <Stack spacing={1} flexGrow={1}>
                <Typography variant="h5">
                  {currentLead.fullName || 'Chưa có tên'}
                </Typography>
                <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                  ID: {currentLead.id}
                </Typography>
                <Stack direction="row" spacing={1} alignItems="center">
                  <Chip
                    size="small"
                    variant="outlined"
                    label={sourceOptions[currentLead.source] || currentLead.source || 'Unknown'}
                  />
                  <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                    • Tạo lúc {formatDateTime(currentLead.createdAt)}
                  </Typography>
                </Stack>
              </Stack>
            </Stack>
          </Card>

          {/* Thông tin liên hệ */}
          <Card sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Thông tin liên hệ
            </Typography>
            <Divider sx={{ mb: 2 }} />
            
            <Grid container spacing={3}>
              <Grid size={{ xs: 12, md: 6 }}>
                {renderInfoItem(
                  'Số điện thoại',
                  currentLead.phone,
                  'solar:phone-bold'
                )}
              </Grid>
              
              <Grid size={{ xs: 12, md: 6 }}>
                {renderInfoItem(
                  'Email',
                  currentLead.email,
                  'solar:mailbox-bold'
                )}
              </Grid>
              
              <Grid size={{ xs: 12, md: 6 }}>
                {renderInfoItem(
                  'Công ty',
                  leadData.company,
                  'solar:buildings-2-bold'
                )}
              </Grid>
              
              <Grid size={{ xs: 12, md: 6 }}>
                {renderInfoItem(
                  'Phụ trách',
                  currentLead.assignedTo,
                  'solar:user-bold'
                )}
              </Grid>
              
              <Grid size={{ xs: 12 }}>
                {renderInfoItem(
                  'Địa chỉ',
                  leadData.address,
                  'solar:map-point-bold'
                )}
              </Grid>
            </Grid>
          </Card>

          {/* Thông tin theo dõi */}
          <Card sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Theo dõi & Ghi chú
            </Typography>
            <Divider sx={{ mb: 2 }} />
            
            <Grid container spacing={3}>
              <Grid size={{ xs: 12, md: 6 }}>
                {renderInfoItem(
                  'Lần liên hệ cuối',
                  currentLead.lastContactAt ? formatDateTime(currentLead.lastContactAt) : null,
                  'solar:calendar-bold'
                )}
              </Grid>
              
              <Grid size={{ xs: 12, md: 6 }}>
                {renderInfoItem(
                  'Lịch theo dõi tiếp theo',
                  currentLead.nextFollowUpAt ? formatDateTime(currentLead.nextFollowUpAt) : null,
                  'solar:calendar-mark-bold'
                )}
              </Grid>
              
              <Grid size={{ xs: 12 }}>
                <Stack spacing={1}>
                  <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                    Ghi chú
                  </Typography>
                  <Box
                    sx={{
                      p: 2,
                      border: '1px solid',
                      borderColor: 'divider',
                      borderRadius: 1,
                      bgcolor: 'grey.50',
                      minHeight: 60,
                    }}
                  >
                    <Typography variant="body2">
                      {currentLead.notes || 'Chưa có ghi chú nào'}
                    </Typography>
                  </Box>
                </Stack>
              </Grid>
            </Grid>
          </Card>

          {/* Dữ liệu JSON bổ sung */}
          {Object.keys(leadData).length > 0 && (
            <Card sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Dữ liệu bổ sung
              </Typography>
              <Divider sx={{ mb: 2 }} />
              
              <Box
                sx={{
                  p: 2,
                  bgcolor: 'grey.50',
                  borderRadius: 1,
                  border: '1px solid',
                  borderColor: 'divider',
                }}
              >
                <pre style={{ margin: 0, fontSize: '0.875rem', overflow: 'auto' }}>
                  {JSON.stringify(leadData, null, 2)}
                </pre>
              </Box>
            </Card>
          )}
        </Stack>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose}>
          Đóng
        </Button>
        <Button 
          variant="contained" 
          startIcon={<Iconify icon="solar:pen-bold" />}
          onClick={() => {
            onClose();
            onEdit(currentLead);
          }}
        >
          Chỉnh sửa
        </Button>
      </DialogActions>
    </Dialog>
  );
} 