'use client';

import { <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Container, Typography } from '@mui/material';

import { DashboardContent } from 'src/layouts/dashboard';

import { Iconify } from 'src/components/iconify';
import { CustomBreadcrumbs } from 'src/components/custom-breadcrumbs';

// ----------------------------------------------------------------------

export default function DigitalDeliveryPage() {
  return (
    <DashboardContent>
      <Container maxWidth="xl">
        <CustomBreadcrumbs
          heading="Giao hàng số"
          subheading="Quản lý các sản phẩm số và quy trình giao hàng tự động"
          links={[
            { name: 'Dashboard', href: '/dashboard' },
            { name: 'Giao hàng số' },
          ]}
          sx={{ mb: 3 }}
        />

        <Card sx={{ p: 6 }}>
          <Stack alignItems="center" spacing={3}>
            <Box
              sx={{
                width: 96,
                height: 96,
                borderRadius: '50%',
                bgcolor: 'primary.lighter',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <Iconify icon="eva:cloud-download-fill" width={48} sx={{ color: 'primary.main' }} />
            </Box>

            <Typography variant="h5" sx={{ textAlign: 'center' }}>
              Tính năng Giao hàng số
            </Typography>

            <Typography variant="body1" sx={{ textAlign: 'center', maxWidth: 480, color: 'text.secondary' }}>
              Quản lý sản phẩm số, tự động gửi link download và theo dõi việc tải xuống của khách hàng.
            </Typography>

            <Stack spacing={2} sx={{ maxWidth: 400, width: '100%' }}>
              {[
                'Tự động gửi link download sau thanh toán',
                'Quản lý thời hạn download',
                'Theo dõi lượt tải xuống',
                'Bảo mật file với mã token',
              ].map((feature, index) => (
                <Stack key={index} direction="row" alignItems="center" spacing={1}>
                  <Iconify icon="eva:checkmark-circle-2-fill" sx={{ color: 'success.main' }} />
                  <Typography variant="body2">{feature}</Typography>
                </Stack>
              ))}
            </Stack>

            <Button variant="contained" size="large" sx={{ mt: 4 }}>
              Cấu hình giao hàng số
            </Button>
          </Stack>
        </Card>
      </Container>
    </DashboardContent>
  );
}
