'use client';

import { useCallback } from 'react';

import {
  Box,
  Stack,
  Button,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tooltip,
  IconButton,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';

import { Iconify } from 'src/components/iconify';

import { getLeadStatusOptions } from 'src/actions/mooly-chatbot/chatbot-lead-service';

// ----------------------------------------------------------------------

export default function LeadsTableToolbar({
  filters,
  onFilters,
  onResetFilters,
  selectedLeads,
  onDeleteSelected,
}) {
  const handleFilterName = useCallback(
    (event) => {
      onFilters('name', event.target.value);
    },
    [onFilters]
  );

  const handleFilterStatus = useCallback(
    (event) => {
      onFilters('status', event.target.value);
    },
    [onFilters]
  );

  const handleFilterStartDate = useCallback(
    (newValue) => {
      onFilters('startDate', newValue);
    },
    [onFilters]
  );

  const handleFilterEndDate = useCallback(
    (newValue) => {
      onFilters('endDate', newValue);
    },
    [onFilters]
  );

  const isFiltered = 
    filters.name !== '' ||
    filters.status !== 'all' ||
    !!filters.startDate ||
    !!filters.endDate;

  const statusOptions = [
    { value: 'all', label: 'Tất cả trạng thái' },
    ...getLeadStatusOptions(),
  ];

  return (
    <>
      <Stack
        spacing={2}
        alignItems={{ xs: 'flex-end', md: 'center' }}
        direction={{
          xs: 'column',
          md: 'row',
        }}
        sx={{
          p: 2.5,
          pr: { xs: 2.5, md: 1 },
        }}
      >
        {/* Search by name/email/phone */}
        <Stack direction="row" alignItems="center" spacing={2} flexGrow={1} width={1}>
          <TextField
            fullWidth
            value={filters.name}
            onChange={handleFilterName}
            placeholder="Tìm kiếm theo tên, email, số điện thoại..."
            slotProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Iconify icon="eva:search-fill" sx={{ color: 'text.disabled' }} />
                </InputAdornment>
              ),
            }}
          />

          {/* Status filter */}
          <FormControl sx={{ minWidth: 200 }}>
            <InputLabel>Trạng thái</InputLabel>
            <Select
              value={filters.status}
              onChange={handleFilterStatus}
              label="Trạng thái"
            >
              {statusOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Stack>

        {/* Date filters */}
        <Stack direction="row" alignItems="center" spacing={2}>
          <DatePicker
            label="Từ ngày"
            value={filters.startDate}
            onChange={handleFilterStartDate}
            slotProps={{
              textField: {
                size: 'small',
                sx: { minWidth: 140 },
              },
            }}
          />

          <DatePicker
            label="Đến ngày"
            value={filters.endDate}
            onChange={handleFilterEndDate}
            slotProps={{
              textField: {
                size: 'small',
                sx: { minWidth: 140 },
              },
            }}
          />
        </Stack>

        {/* Actions */}
        <Stack direction="row" alignItems="center" spacing={1}>
          {isFiltered && (
            <Tooltip title="Xóa bộ lọc">
              <IconButton onClick={onResetFilters}>
                <Iconify icon="solar:filter-bold" />
              </IconButton>
            </Tooltip>
          )}

          {selectedLeads.length > 0 && (
            <Tooltip title={`Xóa ${selectedLeads.length} leads đã chọn`}>
              <IconButton color="error" onClick={onDeleteSelected}>
                <Iconify icon="solar:trash-bin-trash-bold" />
              </IconButton>
            </Tooltip>
          )}
        </Stack>
      </Stack>

      {/* Selected info */}
      {selectedLeads.length > 0 && (
        <Box
          sx={{
            px: 2.5,
            py: 1.5,
            bgcolor: 'primary.lighter',
            borderTop: (theme) => `solid 1px ${theme.palette.divider}`,
          }}
        >
          <Stack direction="row" alignItems="center" justifyContent="space-between">
            <Box sx={{ typography: 'subtitle2' }}>
              Đã chọn {selectedLeads.length} leads
            </Box>

            <Button
              size="small"
              color="error"
              variant="contained"
              startIcon={<Iconify icon="solar:trash-bin-trash-bold" />}
              onClick={onDeleteSelected}
            >
              Xóa tất cả
            </Button>
          </Stack>
        </Box>
      )}
    </>
  );
} 