import { NextResponse } from 'next/server';
import { createClient } from 'src/utils/supabase/server';

/**
 * Facebook Auto Reply Configuration API
 * Manages auto reply settings for Facebook/Instagram
 */

// GET: Fetch auto reply configuration
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const pageId = searchParams.get('pageId');
    
    if (!pageId) {
      return NextResponse.json({ error: 'Page ID required' }, { status: 400 });
    }
    
    const supabase = await createClient();
    
    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }
    
    // Get auto reply configuration
    const { data, error } = await supabase
      .from('facebook_auto_reply_config')
      .select('*')
      .eq('page_id', pageId)
      .single();
    
    if (error && error.code !== 'PGRST116') {
      console.error('❌ Error fetching auto reply config:', error);
      return NextResponse.json({ error: 'Failed to fetch configuration' }, { status: 500 });
    }
    
    // If no config exists, return default values
    if (!data) {
      return NextResponse.json({
        success: true,
        data: {
          pageId,
          enableCommentReply: false,
          enableMessageReply: false,
          enableInstagramComments: false,
          enableInstagramMessages: false,
          autoPrivateReply: false,
          replyPrompt: '',
          replyTone: 'friendly',
          replyLanguage: 'vi',
          maxReplyLength: 500,
          businessInfo: '',
          products: '',
          policies: '',
          excludeKeywords: []
        }
      });
    }
    
    // Transform snake_case to camelCase for frontend
    const transformedData = {
      pageId: data.page_id,
      enableCommentReply: data.enable_comment_reply,
      enableMessageReply: data.enable_message_reply,
      enableInstagramComments: data.enable_instagram_comments,
      enableInstagramMessages: data.enable_instagram_messages,
      autoPrivateReply: data.auto_private_reply,
      replyPrompt: data.reply_prompt,
      replyTone: data.reply_tone,
      replyLanguage: data.reply_language,
      maxReplyLength: data.max_reply_length,
      businessInfo: data.business_info,
      products: data.products,
      policies: data.policies,
      excludeKeywords: data.exclude_keywords || []
    };
    
    return NextResponse.json({
      success: true,
      data: transformedData
    });
    
  } catch (error) {
    console.error('💥 Error in GET auto reply config:', error);
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error.message 
    }, { status: 500 });
  }
}

// POST: Create or update auto reply configuration
export async function POST(request) {
  try {
    const body = await request.json();
    const {
      pageId,
      enableCommentReply = false,
      enableMessageReply = false,
      enableInstagramComments = false,
      enableInstagramMessages = false,
      autoPrivateReply = false,
      replyPrompt = '',
      replyTone = 'friendly',
      replyLanguage = 'vi',
      maxReplyLength = 500,
      businessInfo = '',
      products = '',
      policies = '',
      excludeKeywords = []
    } = body;
    
    if (!pageId) {
      return NextResponse.json({ error: 'Page ID required' }, { status: 400 });
    }
    
    const supabase = await createClient();
    
    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }
    
    // Verify user owns this page
    const { data: pageData, error: pageError } = await supabase
      .from('facebook_accounts')
      .select('id')
      .eq('page_id', pageId)
      .single();
    
    if (pageError || !pageData) {
      return NextResponse.json({ error: 'Page not found or access denied' }, { status: 404 });
    }
    
    // Transform camelCase to snake_case for database
    const configData = {
      page_id: pageId,
      enable_comment_reply: enableCommentReply,
      enable_message_reply: enableMessageReply,
      enable_instagram_comments: enableInstagramComments,
      enable_instagram_messages: enableInstagramMessages,
      auto_private_reply: autoPrivateReply,
      reply_prompt: replyPrompt,
      reply_tone: replyTone,
      reply_language: replyLanguage,
      max_reply_length: maxReplyLength,
      business_info: businessInfo,
      products: products,
      policies: policies,
      exclude_keywords: excludeKeywords,
      updated_at: new Date().toISOString()
    };
    
    // Upsert configuration
    const { data, error } = await supabase
      .from('facebook_auto_reply_config')
      .upsert(configData, { 
        onConflict: 'page_id',
        ignoreDuplicates: false 
      })
      .select()
      .single();
    
    if (error) {
      console.error('❌ Error saving auto reply config:', error);
      return NextResponse.json({ 
        error: 'Failed to save configuration',
        details: error.message 
      }, { status: 500 });
    }
    
    // Log the configuration change
    await supabase
      .from('facebook_activity_logs')
      .insert({
        page_id: pageId,
        activity: 'config_updated',
        metadata: { 
          enabledFeatures: {
            commentReply: enableCommentReply,
            messageReply: enableMessageReply,
            instagramComments: enableInstagramComments,
            instagramMessages: enableInstagramMessages
          }
        }
      });
    
    console.log('✅ Auto reply configuration saved for page:', pageId);
    
    return NextResponse.json({
      success: true,
      message: 'Configuration saved successfully',
      data: {
        pageId: data.page_id,
        enableCommentReply: data.enable_comment_reply,
        enableMessageReply: data.enable_message_reply,
        enableInstagramComments: data.enable_instagram_comments,
        enableInstagramMessages: data.enable_instagram_messages,
        autoPrivateReply: data.auto_private_reply,
        replyPrompt: data.reply_prompt,
        replyTone: data.reply_tone,
        replyLanguage: data.reply_language,
        maxReplyLength: data.max_reply_length,
        businessInfo: data.business_info,
        products: data.products,
        policies: data.policies,
        excludeKeywords: data.exclude_keywords || []
      }
    });
    
  } catch (error) {
    console.error('💥 Error in POST auto reply config:', error);
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error.message 
    }, { status: 500 });
  }
}

// DELETE: Remove auto reply configuration
export async function DELETE(request) {
  try {
    const { searchParams } = new URL(request.url);
    const pageId = searchParams.get('pageId');
    
    if (!pageId) {
      return NextResponse.json({ error: 'Page ID required' }, { status: 400 });
    }
    
    const supabase = await createClient();
    
    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }
    
    // Delete configuration
    const { error } = await supabase
      .from('facebook_auto_reply_config')
      .delete()
      .eq('page_id', pageId);
    
    if (error) {
      console.error('❌ Error deleting auto reply config:', error);
      return NextResponse.json({ 
        error: 'Failed to delete configuration',
        details: error.message 
      }, { status: 500 });
    }
    
    // Log the deletion
    await supabase
      .from('facebook_activity_logs')
      .insert({
        page_id: pageId,
        activity: 'config_deleted',
        metadata: {}
      });
    
    console.log('✅ Auto reply configuration deleted for page:', pageId);
    
    return NextResponse.json({
      success: true,
      message: 'Configuration deleted successfully'
    });
    
  } catch (error) {
    console.error('💥 Error in DELETE auto reply config:', error);
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error.message 
    }, { status: 500 });
  }
}
