'use client';

import { useSortable } from '@dnd-kit/sortable';
import { useBoolean } from 'minimal-shared/hooks';
import { useState, useEffect, useCallback } from 'react';

import Box from '@mui/material/Box';
import Chip from '@mui/material/Chip';
import Stack from '@mui/material/Stack';
import Tooltip from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';
import { alpha } from '@mui/material/styles';

import { Iconify } from 'src/components/iconify';
import { Label } from 'src/components/label';

import LeadsKanbanDetails from './leads-kanban-details';

// =====================================================
// UTILITY FUNCTIONS
// =====================================================

/**
 * Format giá trị field để hiển thị
 */
const formatFieldValue = (value, fieldType, fieldName) => {
  if (!value) return null;

  switch (fieldType) {
    case 'date':
      return new Date(value).toLocaleDateString('vi-VN');
    case 'number':
      if (fieldName === 'lead_score') {
        return `${value}/100`;
      }
      return value.toString();
    case 'status':
      return value;
    default:
      return value.toString();
  }
};

/**
 * Render field với icon và label
 */
const renderField = (fieldName, value, fieldSettings, leadData, statusOptions = []) => {
  if (!value && fieldName !== 'leadScore' && fieldName !== 'lead_score') return null;

  // Tìm setting với nhiều tên có thể - support legacy data
  const setting = fieldSettings[fieldName] || 
                 fieldSettings[fieldName.replace(/([A-Z])/g, '_$1').toLowerCase()] ||
                 fieldSettings[fieldName.replace(/_([a-z])/g, (match, letter) => letter.toUpperCase())];
                 
  if (!setting) {
    return null;
  }

  const formattedValue = formatFieldValue(value, setting.type || 'text', fieldName);
  if (!formattedValue && fieldName !== 'leadScore' && fieldName !== 'lead_score') return null;

  // Special rendering cho status
  if (fieldName === 'status') {
    const statusOption = statusOptions.find(option => option.value === value);
    return (
      <Stack key={fieldName} direction="row" alignItems="center" spacing={0.5}>
        <Iconify icon={setting.icon} width={14} sx={{ color: 'text.secondary' }} />
        <Label 
          size="small" 
          color={statusOption?.color || 'default'}
          sx={{ fontSize: '0.75rem' }}
        >
          {statusOption?.label || formattedValue}
        </Label>
      </Stack>
    );
  }

  // Special rendering cho lead_score/leadScore
  if (fieldName === 'lead_score' || fieldName === 'leadScore') {
    const score = Number(value) || 0;
    const getScoreColor = (scoreValue) => {
      if (scoreValue >= 80) return 'success';
      if (scoreValue >= 60) return 'warning';
      if (scoreValue >= 40) return 'info';
      return 'default';
    };

    return (
      <Stack key={fieldName} direction="row" alignItems="center" spacing={0.5}>
        <Iconify icon={setting.icon} width={14} sx={{ color: 'text.secondary' }} />
        <Chip
          label={`${score}/100`}
          size="small"
          color={getScoreColor(score)}
          variant="outlined"
          sx={{ 
            height: 20, 
            fontSize: '0.7rem',
            '& .MuiChip-label': { px: 0.5 }
          }}
        />
      </Stack>
    );
  }

  // Special rendering cho phone và email
  if (fieldName === 'phone' || fieldName === 'email') {
    const maxLength = fieldName === 'phone' ? 12 : 20;
    const displayValue = formattedValue.length > maxLength 
      ? `${formattedValue.substring(0, maxLength)}...` 
      : formattedValue;

    return (
      <Tooltip key={fieldName} title={formattedValue} arrow>
        <Stack direction="row" alignItems="center" spacing={0.5}>
          <Iconify icon={setting.icon} width={14} sx={{ color: 'text.secondary' }} />
          <Typography 
            variant="caption" 
            sx={{ 
              color: 'text.secondary',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              maxWidth: '120px',
            }}
          >
            {displayValue}
          </Typography>
        </Stack>
      </Tooltip>
    );
  }

  // Default rendering
  return (
    <Stack key={fieldName} direction="row" alignItems="center" spacing={0.5}>
      <Iconify icon={setting.icon} width={14} sx={{ color: 'text.secondary' }} />
      <Typography 
        variant="caption" 
        sx={{ 
          color: 'text.secondary',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap',
          maxWidth: '100px',
        }}
      >
        {formattedValue}
      </Typography>
    </Stack>
  );
};

// ----------------------------------------------------------------------

export default function LeadsKanbanTaskItem({ 
  task, 
  disabled, 
  columnId, 
  displayConfig = null,
  statusOptions = [],
  onEdit,
  onView,
  onDelete,
  sx 
}) {
  const taskDetailsDialog = useBoolean();

  const { setNodeRef, listeners, isDragging, isSorting, transform, transition } = useSortable({
    id: task?.id,
  });

  const mounted = useMountStatus();
  const mountedWhileDragging = isDragging && !mounted;

  const handleEditLead = useCallback(() => {
    if (task?.leadData) {
      onEdit({ leadData: task.leadData });
    }
  }, [task, onEdit]);

  const handleViewLead = useCallback(() => {
    taskDetailsDialog.onTrue();
  }, [taskDetailsDialog]);

  const handleDeleteLead = useCallback(() => {
    if (task?.leadData) {
      onDelete({ leadData: task.leadData });
    }
  }, [task, onDelete]);

  const renderTaskDetailsDialog = () => (
    <LeadsKanbanDetails
      task={task}
      open={taskDetailsDialog.value}
      onClose={taskDetailsDialog.onFalse}
      onEdit={handleEditLead}
      onDelete={handleDeleteLead}
    />
  );

  // Custom render function để thêm warning cho uncategorized leads
  const renderCustomContent = () => {
    if (!task?.isUncategorized) {
      return null;
    }

    return (
      <Box
        sx={{
          position: 'absolute',
          top: 8,
          left: 8,
          zIndex: 1,
        }}
      >
        <Tooltip 
          title="Lead này có trạng thái không khớp với workflow hiện tại. Vui lòng cập nhật trạng thái phù hợp."
          arrow
        >
          <Chip
            size="small"
            icon={<Iconify icon="solar:danger-triangle-bold" width={14} />}
            label="Cần cập nhật"
            color="warning"
            variant="filled"
            sx={{
              height: 20,
              fontSize: '0.75rem',
              fontWeight: 600,
              '& .MuiChip-icon': {
                fontSize: 14,
              },
            }}
          />
        </Tooltip>
      </Box>
    );
  };

  // Render additional fields theo cấu hình
  const renderAdditionalFields = () => {
    if (!displayConfig || !task?.leadData) {
      return null;
    }

    const leadData = task.leadData;
    const fieldSettings = displayConfig.fieldSettings || displayConfig.field_settings || {};
    const visibleFields = displayConfig.visibleFields || displayConfig.visible_fields || [];

    if (!visibleFields.length) {
      return null;
    }

    return (
      <Stack spacing={0.5} sx={{ mt: 1 }}>
        {visibleFields.map((fieldName) => {
          // Map field name to actual data - support both snake_case và camelCase
          let fieldValue = null;
          let normalizedFieldName = fieldName;
          
          switch (fieldName) {
            case 'full_name':
            case 'fullName':
              fieldValue = leadData.fullName || leadData.full_name;
              normalizedFieldName = 'fullName';
              break;
            case 'email':
              fieldValue = leadData.email;
              break;
            case 'phone':
              fieldValue = leadData.phone;
              break;
            case 'company':
              fieldValue = leadData.company;
              break;
            case 'address':
              fieldValue = leadData.address;
              break;
            case 'source':
              fieldValue = leadData.source;
              break;
            case 'status':
              fieldValue = leadData.status;
              break;
            case 'lead_score':
            case 'leadScore':
              fieldValue = leadData.leadScore || leadData.lead_score || 0;
              normalizedFieldName = 'leadScore';
              break;
            case 'created_at':
            case 'createdAt':
              fieldValue = leadData.createdAt || leadData.created_at;
              normalizedFieldName = 'createdAt';
              break;
            case 'updated_at':
            case 'updatedAt':
              fieldValue = leadData.updatedAt || leadData.updated_at;
              normalizedFieldName = 'updatedAt';
              break;
            case 'next_follow_up_at':
            case 'nextFollowUpAt':
              fieldValue = leadData.nextFollowUpAt || leadData.next_follow_up_at;
              normalizedFieldName = 'nextFollowUpAt';
              break;
            default:
              // Thử cả camelCase và snake_case
              fieldValue = leadData[fieldName] || leadData[fieldName.replace(/([A-Z])/g, '_$1').toLowerCase()];
          }

          // Tìm field settings với cả original và normalized name
          const fieldSetting = fieldSettings[fieldName] || fieldSettings[normalizedFieldName];
          
          if (!fieldSetting) {
            return null;
          }

          return renderField(normalizedFieldName, fieldValue, fieldSettings, leadData, statusOptions);
        })}
      </Stack>
    );
  };

  return (
    <>
      <Box sx={{ position: 'relative' }}>
        <Box
          ref={disabled ? undefined : setNodeRef}
          onClick={handleViewLead}
          sx={[
            {
              width: '100%',
              cursor: 'grab',
              outline: 'none',
              overflow: 'hidden',
              position: 'relative',
              borderRadius: 'var(--item-radius)',
              bgcolor: 'background.paper',
              boxShadow: (theme) => theme.customShadows?.z1 || 1,
              transition: (theme) => theme.transitions.create(['box-shadow']),
              '&:hover': {
                boxShadow: (theme) => theme.customShadows?.z8 || 2,
              },
              ...(isDragging && {
                opacity: 0.2,
                filter: 'grayscale(1)',
              }),
              ...(isSorting && {
                transition: 'none',
              }),
                             ...(transform && {
                 transform: `translate3d(${Math.round(transform.x)}px, ${Math.round(transform.y)}px, 0) scaleX(${transform.scaleX}) scaleY(${transform.scaleY})`,
               }),
               ...(transition && {
                 transition,
               }),
            },
            // Thêm border warning cho uncategorized leads
            task?.isUncategorized && {
              '&::before': {
                content: '""',
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                borderRadius: 'var(--item-radius)',
                border: (theme) => `2px solid ${alpha(theme.palette.warning.main, 0.5)}`,
                pointerEvents: 'none',
                zIndex: 1,
              },
            },
            ...(Array.isArray(sx) ? sx : [sx]),
          ]}
          {...listeners}
        >
          <Box sx={{ p: 2.5, position: 'relative' }}>
            {/* Task Title */}
            <Typography 
              variant="subtitle2" 
              sx={{ 
                fontWeight: 'medium',
                lineHeight: 1.5,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
              }}
            >
              {task.name}
            </Typography>

            {/* Additional Fields */}
            {renderAdditionalFields()}

            {/* Task Info (comments, attachments, assignee) */}
            {(task?.comments?.length > 0 || task?.attachments?.length > 0 || task?.assignee?.length > 0) && (
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>
                <Box
                  sx={{
                    flexGrow: 1,
                    display: 'flex',
                    alignItems: 'center',
                    typography: 'caption',
                    color: 'text.disabled',
                  }}
                >
                  {!!task?.comments?.length && (
                    <>
                      <Iconify width={16} icon="solar:chat-round-dots-bold" sx={{ mr: 0.25 }} />
                      <Box component="span" sx={{ mr: 1 }}>
                        {task?.comments?.length}
                      </Box>
                    </>
                  )}

                  {!!task?.attachments?.length && (
                    <>
                      <Iconify width={16} icon="eva:attach-2-fill" sx={{ mr: 0.25 }} />
                      <Box component="span">{task?.attachments?.length}</Box>
                    </>
                  )}
                </Box>

                {/* Assignee Avatars */}
                {task?.assignee?.length > 0 && (
                  <Stack direction="row" spacing={-0.5}>
                    {task.assignee.slice(0, 3).map((user, index) => (
                      <Tooltip key={user.id} title={user.name} arrow>
                        <Box
                          sx={{
                            width: 24,
                            height: 24,
                            borderRadius: '50%',
                            bgcolor: 'primary.main',
                            color: 'primary.contrastText',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            fontSize: '0.75rem',
                            fontWeight: 'bold',
                            border: 2,
                            borderColor: 'background.paper',
                            zIndex: task.assignee.length - index,
                          }}
                        >
                          {user.name.charAt(0).toUpperCase()}
                        </Box>
                      </Tooltip>
                    ))}
                    {task.assignee.length > 3 && (
                      <Tooltip title={`+${task.assignee.length - 3} more`} arrow>
                        <Box
                          sx={{
                            width: 24,
                            height: 24,
                            borderRadius: '50%',
                            bgcolor: 'text.secondary',
                            color: 'background.paper',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            fontSize: '0.7rem',
                            fontWeight: 'bold',
                            border: 2,
                            borderColor: 'background.paper',
                          }}
                        >
                          +{task.assignee.length - 3}
                        </Box>
                      </Tooltip>
                    )}
                  </Stack>
                )}
              </Box>
            )}
          </Box>
        </Box>
        {renderCustomContent()}
      </Box>

      {renderTaskDetailsDialog()}
    </>
  );
}

// ----------------------------------------------------------------------

function useMountStatus() {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    const timeout = setTimeout(() => setIsMounted(true), 500);

    return () => clearTimeout(timeout);
  }, []);

  return isMounted;
} 