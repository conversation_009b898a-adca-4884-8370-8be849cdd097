-- Migration: 003_improve_variants_system.sql
-- Description: Improve product variants system with better attributes and media management
-- Author: Development Team
-- Date: $(date)

-- =====================================================
-- 1. CREATE PRODUCT_ATTRIBUTES TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS product_attributes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    
    -- Attribute Information
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) NOT NULL,
    type VARCHAR(50) DEFAULT 'text' CHECK (type IN ('text', 'number', 'color', 'image', 'boolean', 'select')),
    
    -- Configuration
    is_required BOOLEAN DEFAULT false,
    is_variation BOOLEAN DEFAULT false, -- Can be used for product variations
    is_visible BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    
    -- Options for select type
    options JSONB DEFAULT '[]',
    
    -- Validation rules
    validation_rules JSONB DEFAULT '{}',
    
    -- Display settings
    display_settings JSONB DEFAULT '{}',
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(tenant_id, slug)
);

-- =====================================================
-- 2. CREATE PRODUCT_ATTRIBUTE_VALUES TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS product_attribute_values (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    attribute_id UUID NOT NULL REFERENCES product_attributes(id) ON DELETE CASCADE,
    
    -- Value Information
    value TEXT NOT NULL,
    display_value TEXT,
    color_code VARCHAR(7), -- For color attributes
    image_url TEXT, -- For image attributes
    
    -- Configuration
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(attribute_id, value)
);

-- =====================================================
-- 3. CREATE PRODUCT_VARIANT_ATTRIBUTES TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS product_variant_attributes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    variant_id UUID NOT NULL REFERENCES product_variants(id) ON DELETE CASCADE,
    attribute_id UUID NOT NULL REFERENCES product_attributes(id) ON DELETE CASCADE,
    attribute_value_id UUID REFERENCES product_attribute_values(id) ON DELETE CASCADE,
    
    -- Value (for custom values not in predefined list)
    custom_value TEXT,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(variant_id, attribute_id),
    CHECK (attribute_value_id IS NOT NULL OR custom_value IS NOT NULL)
);

-- =====================================================
-- 4. CREATE PRODUCT_VARIANT_MEDIA TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS product_variant_media (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    variant_id UUID NOT NULL REFERENCES product_variants(id) ON DELETE CASCADE,
    
    -- Media Information
    media_type VARCHAR(20) DEFAULT 'image' CHECK (media_type IN ('image', 'video', 'document')),
    url TEXT NOT NULL,
    thumbnail_url TEXT,
    alt_text VARCHAR(255),
    
    -- File Information
    file_name VARCHAR(255),
    file_size INTEGER,
    mime_type VARCHAR(100),
    
    -- Display Settings
    sort_order INTEGER DEFAULT 0,
    is_primary BOOLEAN DEFAULT false,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- 5. ENHANCE PRODUCT_VARIANTS TABLE
-- =====================================================

DO $$ 
BEGIN
    -- Add enhanced fields to product_variants
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'product_variants' AND column_name = 'variant_type'
    ) THEN
        ALTER TABLE product_variants ADD COLUMN variant_type VARCHAR(50) DEFAULT 'standard';
        COMMENT ON COLUMN product_variants.variant_type IS 'Type of variant: standard, digital, service';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'product_variants' AND column_name = 'digital_info'
    ) THEN
        ALTER TABLE product_variants ADD COLUMN digital_info JSONB DEFAULT '{}';
        COMMENT ON COLUMN product_variants.digital_info IS 'Digital variant specific information';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'product_variants' AND column_name = 'service_info'
    ) THEN
        ALTER TABLE product_variants ADD COLUMN service_info JSONB DEFAULT '{}';
        COMMENT ON COLUMN product_variants.service_info IS 'Service variant specific information';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'product_variants' AND column_name = 'dimensions'
    ) THEN
        ALTER TABLE product_variants ADD COLUMN dimensions JSONB DEFAULT '{}';
        COMMENT ON COLUMN product_variants.dimensions IS 'Variant specific dimensions';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'product_variants' AND column_name = 'inventory_settings'
    ) THEN
        ALTER TABLE product_variants ADD COLUMN inventory_settings JSONB DEFAULT '{}';
        COMMENT ON COLUMN product_variants.inventory_settings IS 'Variant specific inventory settings';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'product_variants' AND column_name = 'pricing_settings'
    ) THEN
        ALTER TABLE product_variants ADD COLUMN pricing_settings JSONB DEFAULT '{}';
        COMMENT ON COLUMN product_variants.pricing_settings IS 'Variant specific pricing settings';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'product_variants' AND column_name = 'visibility_settings'
    ) THEN
        ALTER TABLE product_variants ADD COLUMN visibility_settings JSONB DEFAULT '{}';
        COMMENT ON COLUMN product_variants.visibility_settings IS 'Variant visibility and display settings';
    END IF;
END $$;

-- =====================================================
-- 6. CREATE INDEXES FOR PERFORMANCE
-- =====================================================

-- Product Attributes indexes
CREATE INDEX IF NOT EXISTS idx_product_attributes_tenant_id ON product_attributes(tenant_id);
CREATE INDEX IF NOT EXISTS idx_product_attributes_slug ON product_attributes(tenant_id, slug);
CREATE INDEX IF NOT EXISTS idx_product_attributes_type ON product_attributes(type);
CREATE INDEX IF NOT EXISTS idx_product_attributes_variation ON product_attributes(is_variation) WHERE is_variation = true;
CREATE INDEX IF NOT EXISTS idx_product_attributes_active ON product_attributes(tenant_id, is_active) WHERE is_active = true;

-- Product Attribute Values indexes
CREATE INDEX IF NOT EXISTS idx_product_attribute_values_tenant_id ON product_attribute_values(tenant_id);
CREATE INDEX IF NOT EXISTS idx_product_attribute_values_attribute_id ON product_attribute_values(attribute_id);
CREATE INDEX IF NOT EXISTS idx_product_attribute_values_active ON product_attribute_values(attribute_id, is_active) WHERE is_active = true;

-- Product Variant Attributes indexes
CREATE INDEX IF NOT EXISTS idx_product_variant_attributes_tenant_id ON product_variant_attributes(tenant_id);
CREATE INDEX IF NOT EXISTS idx_product_variant_attributes_variant_id ON product_variant_attributes(variant_id);
CREATE INDEX IF NOT EXISTS idx_product_variant_attributes_attribute_id ON product_variant_attributes(attribute_id);

-- Product Variant Media indexes
CREATE INDEX IF NOT EXISTS idx_product_variant_media_tenant_id ON product_variant_media(tenant_id);
CREATE INDEX IF NOT EXISTS idx_product_variant_media_variant_id ON product_variant_media(variant_id);
CREATE INDEX IF NOT EXISTS idx_product_variant_media_type ON product_variant_media(media_type);
CREATE INDEX IF NOT EXISTS idx_product_variant_media_primary ON product_variant_media(variant_id, is_primary) WHERE is_primary = true;

-- Enhanced product_variants indexes
CREATE INDEX IF NOT EXISTS idx_product_variants_variant_type ON product_variants(variant_type);
CREATE INDEX IF NOT EXISTS idx_product_variants_tenant_active ON product_variants(tenant_id, is_active) WHERE is_active = true;

-- GIN indexes for JSONB fields
CREATE INDEX IF NOT EXISTS idx_product_attributes_options_gin ON product_attributes USING GIN(options);
CREATE INDEX IF NOT EXISTS idx_product_attributes_validation_gin ON product_attributes USING GIN(validation_rules);
CREATE INDEX IF NOT EXISTS idx_product_variants_digital_info_gin ON product_variants USING GIN(digital_info);
CREATE INDEX IF NOT EXISTS idx_product_variants_service_info_gin ON product_variants USING GIN(service_info);
CREATE INDEX IF NOT EXISTS idx_product_variants_dimensions_gin ON product_variants USING GIN(dimensions);
CREATE INDEX IF NOT EXISTS idx_product_variants_inventory_gin ON product_variants USING GIN(inventory_settings);
CREATE INDEX IF NOT EXISTS idx_product_variants_pricing_gin ON product_variants USING GIN(pricing_settings);

-- =====================================================
-- 7. CREATE FUNCTIONS FOR VARIANT MANAGEMENT
-- =====================================================

-- Function to get variant with all attributes
CREATE OR REPLACE FUNCTION get_variant_with_attributes(variant_id_param UUID)
RETURNS TABLE (
    variant_id UUID,
    variant_name TEXT,
    variant_sku VARCHAR,
    attribute_name VARCHAR,
    attribute_type VARCHAR,
    attribute_value TEXT,
    display_value TEXT,
    color_code VARCHAR,
    image_url TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        pv.id as variant_id,
        pv.name as variant_name,
        pv.sku as variant_sku,
        pa.name as attribute_name,
        pa.type as attribute_type,
        COALESCE(pav.value, pva.custom_value) as attribute_value,
        pav.display_value,
        pav.color_code,
        pav.image_url
    FROM product_variants pv
    LEFT JOIN product_variant_attributes pva ON pv.id = pva.variant_id
    LEFT JOIN product_attributes pa ON pva.attribute_id = pa.id
    LEFT JOIN product_attribute_values pav ON pva.attribute_value_id = pav.id
    WHERE pv.id = variant_id_param
    ORDER BY pa.sort_order, pav.sort_order;
END;
$$ LANGUAGE plpgsql;

-- Function to generate variant combinations
CREATE OR REPLACE FUNCTION generate_variant_combinations(product_id_param UUID)
RETURNS TABLE (
    combination_key TEXT,
    attribute_combinations JSONB
) AS $$
DECLARE
    attribute_record RECORD;
    combinations JSONB := '[]';
BEGIN
    -- This is a simplified version - full implementation would generate all possible combinations
    FOR attribute_record IN 
        SELECT pa.id, pa.name, pa.slug, array_agg(pav.value ORDER BY pav.sort_order) as values
        FROM product_attributes pa
        JOIN product_attribute_values pav ON pa.id = pav.attribute_id
        WHERE pa.is_variation = true AND pa.is_active = true AND pav.is_active = true
        GROUP BY pa.id, pa.name, pa.slug
    LOOP
        -- Add logic to generate combinations
        combinations := combinations || jsonb_build_object(
            'attribute_id', attribute_record.id,
            'attribute_name', attribute_record.name,
            'values', attribute_record.values
        );
    END LOOP;
    
    RETURN QUERY SELECT 'generated'::TEXT, combinations;
END;
$$ LANGUAGE plpgsql;

-- Function to validate variant attributes
CREATE OR REPLACE FUNCTION validate_variant_attributes(variant_id_param UUID)
RETURNS BOOLEAN AS $$
DECLARE
    is_valid BOOLEAN := true;
    required_attr RECORD;
BEGIN
    -- Check if all required attributes are set
    FOR required_attr IN 
        SELECT pa.id, pa.name
        FROM product_attributes pa
        JOIN products p ON true -- This would need proper product-attribute relationship
        WHERE pa.is_required = true AND pa.is_active = true
    LOOP
        IF NOT EXISTS (
            SELECT 1 FROM product_variant_attributes pva 
            WHERE pva.variant_id = variant_id_param 
            AND pva.attribute_id = required_attr.id
        ) THEN
            is_valid := false;
            EXIT;
        END IF;
    END LOOP;
    
    RETURN is_valid;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 8. CREATE TRIGGERS
-- =====================================================

-- Trigger for product_attributes updated_at
CREATE OR REPLACE FUNCTION update_product_attributes_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_product_attributes_updated_at
    BEFORE UPDATE ON product_attributes
    FOR EACH ROW
    EXECUTE FUNCTION update_product_attributes_updated_at();

-- Trigger for product_attribute_values updated_at
CREATE OR REPLACE FUNCTION update_product_attribute_values_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_product_attribute_values_updated_at
    BEFORE UPDATE ON product_attribute_values
    FOR EACH ROW
    EXECUTE FUNCTION update_product_attribute_values_updated_at();

-- Trigger for product_variant_media updated_at
CREATE OR REPLACE FUNCTION update_product_variant_media_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_product_variant_media_updated_at
    BEFORE UPDATE ON product_variant_media
    FOR EACH ROW
    EXECUTE FUNCTION update_product_variant_media_updated_at();

-- =====================================================
-- 9. ADD COMMENTS
-- =====================================================

COMMENT ON TABLE product_attributes IS 'Product attributes for creating variants and product specifications';
COMMENT ON TABLE product_attribute_values IS 'Predefined values for product attributes';
COMMENT ON TABLE product_variant_attributes IS 'Attributes assigned to specific product variants';
COMMENT ON TABLE product_variant_media IS 'Media files specific to product variants';

-- =====================================================
-- 10. MIGRATE EXISTING DATA
-- =====================================================

-- Migrate existing variant attributes from JSONB to new structure
-- This would be done carefully in production with proper data validation

-- =====================================================
-- ROLLBACK SCRIPT (if needed)
-- =====================================================

/*
-- To rollback this migration:

-- 1. Drop triggers
DROP TRIGGER IF EXISTS trigger_update_product_attributes_updated_at ON product_attributes;
DROP TRIGGER IF EXISTS trigger_update_product_attribute_values_updated_at ON product_attribute_values;
DROP TRIGGER IF EXISTS trigger_update_product_variant_media_updated_at ON product_variant_media;

-- 2. Drop functions
DROP FUNCTION IF EXISTS update_product_attributes_updated_at();
DROP FUNCTION IF EXISTS update_product_attribute_values_updated_at();
DROP FUNCTION IF EXISTS update_product_variant_media_updated_at();
DROP FUNCTION IF EXISTS get_variant_with_attributes(UUID);
DROP FUNCTION IF EXISTS generate_variant_combinations(UUID);
DROP FUNCTION IF EXISTS validate_variant_attributes(UUID);

-- 3. Remove new columns from product_variants
ALTER TABLE product_variants DROP COLUMN IF EXISTS variant_type;
ALTER TABLE product_variants DROP COLUMN IF EXISTS digital_info;
ALTER TABLE product_variants DROP COLUMN IF EXISTS service_info;
ALTER TABLE product_variants DROP COLUMN IF EXISTS dimensions;
ALTER TABLE product_variants DROP COLUMN IF EXISTS inventory_settings;
ALTER TABLE product_variants DROP COLUMN IF EXISTS pricing_settings;
ALTER TABLE product_variants DROP COLUMN IF EXISTS visibility_settings;

-- 4. Drop tables
DROP TABLE IF EXISTS product_variant_media;
DROP TABLE IF EXISTS product_variant_attributes;
DROP TABLE IF EXISTS product_attribute_values;
DROP TABLE IF EXISTS product_attributes;
*/
