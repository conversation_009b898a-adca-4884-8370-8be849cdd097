/**
 * Unified Social Media Service
 * Manages both Facebook and Instagram accounts in a single interface
 * Provides unified hooks and API functions for social media integration
 * 
 * DATABASE FIELDS MAPPING:
 * ========================
 * 
 * FACEBOOK_ACCOUNTS TABLE:
 * - id (uuid, primary key)
 * - tenant_id (uuid, not null)
 * - page_id (varchar, not null) 
 * - page_name (varchar, not null)
 * - access_token (text, not null)
 * - token_expires_at (timestamptz)
 * - instagram_account_id (varchar, nullable)
 * - instagram_username (varchar, nullable)
 * - is_active (boolean, default true)
 * - connected_at (timestamptz, default now())
 * - last_sync_at (timestamptz, default now())
 * - created_at (timestamptz, default now())
 * - updated_at (timestamptz, default now())
 * - avatar_url (text, nullable)
 * - platform (varchar, default 'facebook')
 * - account_type (varchar, default 'page')
 * - username (varchar, nullable)
 * - follower_count (integer, default 0)
 * - is_verified (boolean, default false)
 * 
 * INSTAGRAM_ACCOUNTS TABLE:
 * - id (uuid, primary key)
 * - tenant_id (uuid, not null)
 * - account_id (varchar, not null) - Instagram Business Account ID
 * - username (varchar, not null)
 * - account_type (varchar, default 'BUSINESS')
 * - access_token (text, not null)
 * - token_expires_at (timestamptz)
 * - is_active (boolean, default true)
 * - connected_at (timestamptz, default now())
 * - last_sync_at (timestamptz, default now())
 * - created_at (timestamptz, default now())
 * - updated_at (timestamptz, default now())
 * - avatar_url (text, nullable) - profile_picture_url field in DB
 * - follower_count (integer, default 0)
 * - is_verified (boolean, default false)
 * - media_count (integer, default 0)
 * - bio (text, nullable)
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { fetchData, updateData, deleteData } from '../supabase-utils';

// Tables
const FACEBOOK_ACCOUNTS_TABLE = 'facebook_accounts';
const INSTAGRAM_ACCOUNTS_TABLE = 'instagram_accounts';

/**
 * Unified hook to manage all social media accounts (Facebook + Instagram)
 * @param {Object} options - Configuration options
 * @returns {Object} - State and functions
 */
export function useSocialMediaAccounts(options = {}) {
  const [accounts, setAccounts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [lastFetch, setLastFetch] = useState(null);

  // Memoize options để tránh re-render không cần thiết
  const memoizedOptions = useMemo(() => ({
    includeConfig: false,
    includeActivity: false,
    platforms: ['facebook', 'instagram'], // Default to all platforms
    ...options
  }), [JSON.stringify(options)]);

  const fetchAccounts = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('🔄 Fetching social media accounts...', memoizedOptions);

      // Parallel fetch from both tables
      const promises = [];
      
      if (memoizedOptions.platforms.includes('facebook')) {
        promises.push(fetchFacebookAccounts());
      }
      
      if (memoizedOptions.platforms.includes('instagram')) {
        promises.push(fetchInstagramAccounts());
      }

      const results = await Promise.allSettled(promises);
      
      // Combine results
      let allAccounts = [];
      
      results.forEach((result, index) => {
        if (result.status === 'fulfilled' && result.value.success) {
          allAccounts = allAccounts.concat(result.value.data || []);
        } else {
          console.error(`❌ Failed to fetch accounts from source ${index}:`, result.reason || result.value?.error);
        }
      });

      // Sort by connection date (newest first)
      allAccounts.sort((a, b) => new Date(b.connectedAt) - new Date(a.connectedAt));

      setAccounts(allAccounts);
      setLastFetch(Date.now());
      console.log('✅ Social media accounts fetched successfully:', allAccounts.length);

    } catch (err) {
      console.error('💥 Error fetching social media accounts:', err);
      setError(err);
      setAccounts([]);
    } finally {
      setLoading(false);
    }
  }, [memoizedOptions]);

  // Initial fetch
  useEffect(() => {
    fetchAccounts();
  }, [fetchAccounts]);

  // Refetch function
  const refetch = useCallback(async () => {
    await fetchAccounts();
  }, [fetchAccounts]);

  // Account summary stats
  const stats = useMemo(() => {
    const totalAccounts = accounts.length;
    const activeAccounts = accounts.filter(acc => acc.isActive).length;
    const platformCounts = accounts.reduce((acc, account) => {
      acc[account.platform] = (acc[account.platform] || 0) + 1;
      return acc;
    }, {});
    
    return {
      totalAccounts,
      activeAccounts,
      inactiveAccounts: totalAccounts - activeAccounts,
      platformCounts
    };
  }, [accounts]);

  // Accounts grouped by platform
  const accountsByPlatform = useMemo(() => accounts.reduce((acc, account) => {
    if (!acc[account.platform]) {
      acc[account.platform] = [];
    }
    acc[account.platform].push(account);
    return acc;
  }, {}), [accounts]);

  return {
    accounts,
    accountsByPlatform,
    stats,
    loading,
    error,
    refetch,
    lastFetch: lastFetch ? new Date(lastFetch) : null
  };
}

/**
 * Fetch Facebook accounts from facebook_accounts table
 * Transform to unified format với field mapping chính xác
 */
async function fetchFacebookAccounts() {
  try {
    console.log('🔄 Fetching Facebook accounts...');
    
    const result = await fetchData(FACEBOOK_ACCOUNTS_TABLE, {
      filters: { isActive: true },
      orderBy: 'connectedAt',
      ascending: false
    });

    console.log('📊 Facebook fetchData result:', result);

    if (result.success) {
      // Transform to unified format với field mapping chính xác từ snake_case DB
      const transformedAccounts = (result.data || []).map(account => {
        console.log('🔄 Transforming Facebook account:', account);
        return {
          // Core ID fields
          id: account.id,
          tenantId: account.tenantId,
          platform: account.platform || 'facebook',
          
          // Platform-specific IDs
          pageId: account.pageId,           // Facebook Page ID
          pageName: account.pageName,       // Facebook Page Name
          
          // Instagram integration (via Facebook connection)
          instagramAccountId: account.instagramAccountId,
          instagramUsername: account.instagramUsername,
          hasInstagram: !!(account.instagramAccountId),
          
          // Status và timestamps
          isActive: account.isActive,
          connectedAt: account.connectedAt,
          lastSyncAt: account.lastSyncAt,
          tokenExpiresAt: account.tokenExpiresAt,
          
          // Profile info
          displayName: account.pageName,    // Primary display name
          username: account.instagramUsername || account.username || '',
          avatarUrl: account.avatarUrl,
          accountType: account.accountType || 'page',
          followerCount: account.followerCount || 0,
          isVerified: account.isVerified || false,
          
          // Platform-specific data
          platformData: {
            pageAccessToken: account.pageAccessToken,
            userAccessToken: account.userAccessToken,
            accessToken: account.accessToken,
            // Additional Facebook-specific fields
          }
        };
      });

      console.log('✅ Facebook accounts transformed:', transformedAccounts);
      return { success: true, data: transformedAccounts };
    }

    console.log('❌ Facebook fetchData failed:', result);
    return result;
  } catch (error) {
    console.error('❌ Error fetching Facebook accounts:', error);
    return { success: false, error, data: [] };
  }
}

/**
 * Fetch Instagram accounts from instagram_accounts table (new direct integration)
 * Transform to unified format với field mapping chính xác
 */
async function fetchInstagramAccounts() {
  try {
    console.log('🔄 Fetching Instagram accounts...');
    
    const result = await fetchData(INSTAGRAM_ACCOUNTS_TABLE, {
      filters: { isActive: true },
      orderBy: 'connectedAt',
      ascending: false
    });

    console.log('📊 Instagram fetchData result:', result);

    if (result.success) {
      // Transform to unified format với field mapping chính xác từ snake_case DB
      const transformedAccounts = (result.data || []).map(account => {
        console.log('🔄 Transforming Instagram account:', account);
        return {
          // Core ID fields
          id: account.id,
          tenantId: account.tenantId,
          platform: 'instagram',
          
          // Platform-specific IDs (Instagram Business Account ID)
          pageId: account.accountId,        // Instagram Business Account ID
          pageName: `@${account.username}`, // Display as @username
          
          // Instagram-specific fields
          instagramAccountId: account.accountId,
          instagramUsername: account.username,
          hasInstagram: true,              // Always true for direct Instagram
          
          // Status và timestamps
          isActive: account.isActive,
          connectedAt: account.connectedAt,
          lastSyncAt: account.lastSyncAt,
          tokenExpiresAt: account.tokenExpiresAt,
          
          // Profile info với mapping chính xác từ database
          displayName: `@${account.username}`,
          username: account.username,
          avatarUrl: account.avatarUrl,    // Maps to profile_picture_url in DB
          accountType: account.accountType || 'BUSINESS',
          followerCount: account.followerCount || 0,
          isVerified: account.isVerified || false,
          
          // Instagram-specific metrics
          mediaCount: account.mediaCount || 0,
          bio: account.bio,
          
          // Platform-specific data
          platformData: {
            accessToken: account.accessToken, // Instagram Business API token
            // Additional Instagram-specific fields
          }
        };
      });

      console.log('✅ Instagram accounts transformed:', transformedAccounts);
      return { success: true, data: transformedAccounts };
    }

    console.log('❌ Instagram fetchData failed:', result);
    return result;
  } catch (error) {
    console.error('❌ Error fetching Instagram accounts:', error);
    return { success: false, error, data: [] };
  }
}

/**
 * Account mutation functions
 * Cung cấp functions để thêm/sửa/xóa accounts
 */
export function useSocialMediaAccountMutations() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const disconnectAccount = useCallback(async (accountId, platform) => {
    try {
      setLoading(true);
      setError(null);

      const table = platform === 'facebook' ? FACEBOOK_ACCOUNTS_TABLE : INSTAGRAM_ACCOUNTS_TABLE;
      
      const result = await updateData(table, 
        { 
          isActive: false,
          lastSyncAt: new Date().toISOString()
        },
        { id: accountId }
      );

      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to disconnect account');
      }

      return { success: true };
    } catch (err) {
      console.error('❌ Error disconnecting account:', err);
      setError(err.message);
      return { success: false, error: err.message };
    } finally {
      setLoading(false);
    }
  }, []);

  const deleteAccount = useCallback(async (accountId, platform) => {
    try {
      setLoading(true);
      setError(null);

      const table = platform === 'facebook' ? FACEBOOK_ACCOUNTS_TABLE : INSTAGRAM_ACCOUNTS_TABLE;
      
      const result = await deleteData(table, { id: accountId });

      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to delete account');
      }

      return { success: true };
    } catch (err) {
      console.error('❌ Error deleting account:', err);
      setError(err.message);
      return { success: false, error: err.message };
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    loading,
    error,
    disconnectAccount,
    deleteAccount
  };
}

/**
 * Utility functions for UI display
 */
export function getPlatformIcon(platform) {
  const icons = {
    facebook: 'mdi:facebook',
    instagram: 'mdi:instagram'
  };
  return icons[platform] || 'mdi:social-media';
}

export function getPlatformColor(platform) {
  const colors = {
    facebook: '#1877F2',
    instagram: '#E4405F'
  };
  return colors[platform] || '#9CA3AF';
}

export function formatAccountName(account) {
  if (account.platform === 'instagram') {
    return `@${account.username}`;
  }
  return account.pageName || account.displayName;
}

export function getAccountStatus(account) {
  if (!account.isActive) return 'disconnected';
  
  if (account.tokenExpiresAt) {
    const expiresAt = new Date(account.tokenExpiresAt);
    const now = new Date();
    const daysUntilExpiry = Math.ceil((expiresAt - now) / (1000 * 60 * 60 * 24));
    
    if (daysUntilExpiry <= 0) return 'expired';
    if (daysUntilExpiry <= 7) return 'expiring_soon';
  }
  
  return 'active';
}

/**
 * Export unified hooks for use in components
 */
export {
  useSocialMediaAccounts as useFacebookAccounts, // Backward compatibility
  useSocialMediaAccountMutations as useFacebookAccountMutations // Backward compatibility
}; 