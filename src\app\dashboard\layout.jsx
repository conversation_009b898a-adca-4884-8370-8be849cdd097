import { CONFIG } from 'src/global-config';

import { BusinessAwareDashboardWrapper } from './business-aware-wrapper.jsx';

// ----------------------------------------------------------------------

export default function Layout({ children }) {
  if (CONFIG.auth.skip) {
    return <BusinessAwareDashboardWrapper>{children}</BusinessAwareDashboardWrapper>;
  }

  return <BusinessAwareDashboardWrapper withAuth>{children}</BusinessAwareDashboardWrapper>;
}
