'use client';

import { Controller } from 'react-hook-form';

import {
  Box,
  Card,
  Chip,
  Stack,
  Select,
  MenuItem,
  TextField,
  Typography,
  InputLabel,
  FormControl,
  FormHelperText
} from '@mui/material';

// ----------------------------------------------------------------------

const SCHEDULE_TYPES = [
  { value: 'daily', label: 'Hàng ngày' },
  { value: 'weekly', label: 'Hàng tuần' },
  { value: 'monthly', label: 'Hàng tháng' },
  { value: 'custom', label: 'Tùy chỉnh' }
];

const DAYS_OF_WEEK = [
  { value: 0, label: 'Chủ nhật' },
  { value: 1, label: 'Thứ 2' },
  { value: 2, label: 'Thứ 3' },
  { value: 3, label: 'Thứ 4' },
  { value: 4, label: 'Thứ 5' },
  { value: 5, label: 'Thứ 6' },
  { value: 6, label: 'Thứ 7' }
];

export function ScheduledMessageForm({ control, errors }) {
  return (
    <Stack spacing={3}>
      {/* Follow-up Message */}
      <Card sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          Tin Nhắn Follow-up
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          Nội dung tin nhắn sẽ được gửi cho khách hàng theo lịch trình
        </Typography>

        <Controller
          name="followUpMessage"
          control={control}
          rules={{ required: 'Tin nhắn follow-up là bắt buộc' }}
          render={({ field }) => (
            <TextField
              {...field}
              label="Nội Dung Tin Nhắn"
              placeholder="VD: Xin chào! Chúng tôi có ưu đãi đặc biệt dành cho bạn..."
              multiline
              rows={4}
              error={!!errors.followUpMessage}
              helperText={errors.followUpMessage?.message}
              fullWidth
            />
          )}
        />

        <Box sx={{ mt: 2 }}>
          <Typography variant="caption" color="text.secondary">
            💡 Bạn có thể sử dụng các biến động: {'{customer_name}'}, {'{last_order_date}'}, {'{total_orders}'}
          </Typography>
        </Box>
      </Card>

      {/* Schedule Configuration */}
      <Card sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          Cấu Hình Lịch Trình
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Thiết lập thời gian và tần suất gửi tin nhắn
        </Typography>

        <Stack spacing={3}>
          {/* Schedule Type */}
          <Controller
            name="scheduleConfig.type"
            control={control}
            rules={{ required: 'Vui lòng chọn loại lịch trình' }}
            render={({ field }) => (
              <FormControl fullWidth error={!!errors.scheduleConfig?.type}>
                <InputLabel>Loại Lịch Trình</InputLabel>
                <Select {...field} label="Loại Lịch Trình">
                  {SCHEDULE_TYPES.map((type) => (
                    <MenuItem key={type.value} value={type.value}>
                      {type.label}
                    </MenuItem>
                  ))}
                </Select>
                {errors.scheduleConfig?.type && (
                  <FormHelperText>{errors.scheduleConfig.type.message}</FormHelperText>
                )}
              </FormControl>
            )}
          />

          {/* Time */}
          <Controller
            name="scheduleConfig.time"
            control={control}
            rules={{ required: 'Vui lòng chọn giờ gửi' }}
            render={({ field }) => (
              <TextField
                {...field}
                label="Giờ Gửi"
                type="time"
                error={!!errors.scheduleConfig?.time}
                helperText={errors.scheduleConfig?.time?.message}
                InputLabelProps={{
                  shrink: true,
                }}
                fullWidth
              />
            )}
          />

          {/* Days of Week (for weekly schedule) */}
          <Controller
            name="scheduleConfig.daysOfWeek"
            control={control}
            render={({ field: { value = [], onChange } }) => (
              <FormControl fullWidth>
                <Typography variant="subtitle2" gutterBottom>
                  Ngày Trong Tuần (cho lịch hàng tuần)
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                  {DAYS_OF_WEEK.map((day) => (
                    <Chip
                      key={day.value}
                      label={day.label}
                      clickable
                      color={value.includes(day.value) ? 'primary' : 'default'}
                      variant={value.includes(day.value) ? 'filled' : 'outlined'}
                      onClick={() => {
                        const newValue = value.includes(day.value)
                          ? value.filter(d => d !== day.value)
                          : [...value, day.value];
                        onChange(newValue);
                      }}
                    />
                  ))}
                </Box>
              </FormControl>
            )}
          />

          {/* Day of Month (for monthly schedule) */}
          <Controller
            name="scheduleConfig.dayOfMonth"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                label="Ngày Trong Tháng (cho lịch hàng tháng)"
                type="number"
                inputProps={{ min: 1, max: 31 }}
                placeholder="15"
                helperText="Ngày trong tháng (1-31)"
                fullWidth
              />
            )}
          />

          {/* Custom Interval */}
          <Controller
            name="scheduleConfig.intervalDays"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                label="Khoảng Cách (ngày) - cho lịch tùy chỉnh"
                type="number"
                inputProps={{ min: 1 }}
                placeholder="7"
                helperText="Gửi tin nhắn mỗi X ngày"
                fullWidth
              />
            )}
          />
        </Stack>
      </Card>

      {/* Customer Filters */}
      <Card sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          Bộ Lọc Khách Hàng (Tùy chọn)
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          Cấu hình điều kiện để gửi tin nhắn cho nhóm khách hàng cụ thể
        </Typography>

        <Stack spacing={2}>
          <Controller
            name="customerFilters.minOrderValue"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                label="Giá trị đơn hàng tối thiểu"
                type="number"
                placeholder="0"
                InputProps={{
                  endAdornment: 'đ'
                }}
                fullWidth
              />
            )}
          />

          <Controller
            name="customerFilters.orderCount"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                label="Số đơn hàng tối thiểu"
                type="number"
                placeholder="0"
                fullWidth
              />
            )}
          />

          <Controller
            name="customerFilters.lastOrderDays"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                label="Số ngày từ đơn hàng cuối"
                type="number"
                placeholder="30"
                helperText="Gửi cho khách hàng có đơn hàng cuối cách đây X ngày"
                fullWidth
              />
            )}
          />

          <Controller
            name="customerFilters.noRecentContact"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                label="Không liên hệ trong X ngày"
                type="number"
                placeholder="7"
                helperText="Chỉ gửi cho khách hàng chưa được liên hệ trong X ngày gần đây"
                fullWidth
              />
            )}
          />
        </Stack>
      </Card>
    </Stack>
  );
}
