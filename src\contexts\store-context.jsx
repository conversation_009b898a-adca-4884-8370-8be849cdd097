'use client';

import PropTypes from 'prop-types';
import { createContext, useContext } from 'react';

// Create context
export const StoreContext = createContext(undefined);

// Custom hook to use the store context
export function useStoreContext() {
  const context = useContext(StoreContext);

  if (!context) {
    throw new Error('useStoreContext must be used within a StoreProvider');
  }

  return context;
}

// Provider component - simplified version without store functionality
export function StoreProvider({ children }) {
  // Simplified context value without store functionality
  const contextValue = {
    // Dummy data to prevent errors in components that still use this context
    stores: [],
    currentStoreId: null,
    currentStore: null,
    isLoadingStores: false,

    // Dummy action that does nothing
    changeStore: () => {},
  };

  return <StoreContext.Provider value={contextValue}>{children}</StoreContext.Provider>;
}

StoreProvider.propTypes = {
  children: PropTypes.node.isRequired,
};
