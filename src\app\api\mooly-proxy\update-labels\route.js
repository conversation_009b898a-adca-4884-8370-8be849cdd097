import { NextResponse } from 'next/server';

import { withTenantAuth } from 'src/utils/server-auth';
import { createClient } from 'src/utils/supabase/server';

/**
 * L<PERSON>y thông tin tài khoản <PERSON>oly từ database
 * @param {string} accountId - ID tài khoản <PERSON>oly
 * @param {string} tenantId - ID của tenant
 * @returns {Promise<Object>} - Thông tin tài khoản
 */
async function getMoolyAccount(accountId, tenantId) {
  try {
    const supabase = await createClient();
    const { data, error } = await supabase
      .from('mooly_accounts')
      .select('*')
      .eq('account_id', accountId)
      .eq('tenant_id', tenantId)
      .single();

    if (error) {
      console.error('Error fetching Mooly account:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Exception when fetching Mooly account:', error);
    return null;
  }
}

/**
 * Cập nhật hoặc tạo mới label
 * @param {string} token - Token xác thực
 * @param {string} accountId - ID tài khoản
 * @param {Object} label - Thông tin label
 * @param {string} tenantId - ID của tenant
 * @returns {Promise<Object>} - Kết quả tạo/cập nhật label
 */
async function updateOrCreateLabel(token, accountId, label, tenantId) {
  const host = process.env.MOOLY_API_HOST || 'https://app.mooly.vn';
  const apiVersion = process.env.MOOLY_API_VERSION || 'api/v1';

  try {
    // Kiểm tra xem label đã tồn tại chưa
    const existingLabelsConfig = await getLabelsConfig(accountId, tenantId);
    const existingLabel = existingLabelsConfig ?
      Object.values(existingLabelsConfig).find(l => l.title === label.title) :
      null;

    if (existingLabel && existingLabel.id) {
      // Cập nhật label hiện có
      const updateResponse = await fetch(`${host}/${apiVersion}/accounts/${accountId}/labels/${existingLabel.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'api_access_token': token
        },
        body: JSON.stringify({ label })
      });

      if (updateResponse.ok) {
        const data = await updateResponse.json();
        return {
          id: data.id,
          title: label.title,
          system_label: label.system_label || false,
          updated: true
        };
      }
    }

    // Tạo label mới nếu không tồn tại hoặc cập nhật thất bại
    const createResponse = await fetch(`${host}/${apiVersion}/accounts/${accountId}/labels`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'api_access_token': token
      },
      body: JSON.stringify({ label })
    });

    if (createResponse.ok) {
      const data = await createResponse.json();
      return {
        id: data.id,
        title: label.title,
        system_label: label.system_label || false,
        created: true
      };
    }

    throw new Error(`Failed to create/update label: ${label.title}`);
  } catch (error) {
    console.error(`Error updating/creating label ${label.title}:`, error);
    return null;
  }
}

/**
 * Lấy cấu hình label từ database
 * @param {string} accountId - ID tài khoản Mooly
 * @param {string} tenantId - ID của tenant
 * @returns {Promise<Object>} - Cấu hình label
 */
async function getLabelsConfig(accountId, tenantId) {
  const account = await getMoolyAccount(accountId, tenantId);
  return account?.labels_config || null;
}

/**
 * Lưu cấu hình label vào database
 * @param {string} accountId - ID tài khoản Chatwoot
 * @param {Object} labelConfig - Cấu hình label
 * @param {string} tenantId - ID của tenant
 * @param {string} userId - ID của người dùng thực hiện cập nhật
 */
async function saveLabelsConfig(accountId, labelConfig, tenantId, userId) {
  try {
    const supabase = await createClient();
    const { error } = await supabase
      .from('mooly_accounts')
      .update({
        labels_config: labelConfig,
        updated_at: new Date().toISOString(),
        updated_by: userId
      })
      .eq('account_id', accountId)
      .eq('tenant_id', tenantId);

    if (error) {
      console.error('Error saving label config to database:', error);
    }
  } catch (error) {
    console.error('Exception when saving label config:', error);
  }
}

/**
 * API endpoint để cập nhật labels cho tài khoản Mooly
 * @param {Request} request - Yêu cầu HTTP
 * @returns {Promise<NextResponse>} - Phản hồi HTTP
 */
export const POST = withTenantAuth(async (request, { tenantId, userId }) => {
  try {
    // Lấy thông tin từ body
    const body = await request.json();
    const { account_id, labels } = body;

    // Kiểm tra dữ liệu đầu vào
    if (!account_id || !labels || !Array.isArray(labels)) {
      return NextResponse.json(
        { success: false, error: 'Account ID and labels array are required' },
        { status: 400 }
      );
    }

    // Lấy thông tin tài khoản
    const account = await getMoolyAccount(account_id, tenantId);
    if (!account) {
      return NextResponse.json(
        { success: false, error: 'Account not found' },
        { status: 404 }
      );
    }

    // Lấy token từ tài khoản
    const { token } = account;

    // Cập nhật hoặc tạo mới từng label
    const results = {};
    for (const label of labels) {
      const result = await updateOrCreateLabel(token, account_id, label, tenantId);
      if (result) {
        results[label.title] = result;
      }
    }

    // Lấy cấu hình label hiện tại
    const currentLabelsConfig = await getLabelsConfig(account_id, tenantId) || {};

    // Cập nhật cấu hình label
    const updatedLabelsConfig = {
      ...currentLabelsConfig,
      ...results
    };

    // Lưu cấu hình label vào database
    await saveLabelsConfig(account_id, updatedLabelsConfig, tenantId, userId);

    // Trả về kết quả
    return NextResponse.json({
      success: true,
      error: null,
      data: {
        updated: Object.keys(results).length,
        labels: results
      }
    });
  } catch (error) {
    console.error('Error in update labels:', error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || 'Failed to update labels',
        data: null,
      },
      { status: 500 }
    );
  }
});
