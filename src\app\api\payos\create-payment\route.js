import axios from 'axios';
import { NextResponse } from 'next/server';

import { createSignature } from 'src/utils/payos-utils';

/**
 * API route để tạo link thanh toán PayOS
 * Chuyển logic từ client-side sang server-side để bảo mật thông tin xác thực
 * @param {Request} request - Request object
 * @returns {Promise<Response>} - Response object
 */
export async function POST(request) {
  try {
    // Lấy dữ liệu từ request
    const paymentData = await request.json();

    // Kiểm tra các tham số bắt buộc
    const {
      orderCode,
      amount,
      description,
      buyerName,
      buyerEmail,
      buyerPhone,
      buyerAddress,
      returnUrl,
      cancelUrl,
      items = [],
      expiredAt,
    } = paymentData;

    if (!orderCode || !amount || !description || !returnUrl || !cancelUrl) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required parameters',
          data: null,
        },
        { status: 400 }
      );
    }

    // Kiểm tra orderCode phải là số dương
    if (typeof orderCode !== 'number' || orderCode <= 0 || !Number.isInteger(orderCode)) {
      return NextResponse.json(
        {
          success: false,
          error: 'orderCode must be a positive integer',
          data: null,
        },
        { status: 400 }
      );
    }

    // Kiểm tra độ dài mô tả
    if (description.length > 25) {
      return NextResponse.json(
        {
          success: false,
          error: 'Description must not exceed 25 characters',
          data: null,
        },
        { status: 400 }
      );
    }

    // Cấu hình PayOS - sử dụng biến môi trường server-side
    const PAYOS_CONFIG = {
      clientId: process.env.PAYOS_CLIENT_ID || '',
      apiKey: process.env.PAYOS_API_KEY || '',
      checksumKey: process.env.PAYOS_CHECKSUM_KEY || '',
      baseUrl: 'https://api-merchant.payos.vn',
    };

    // Kiểm tra xem các khóa API có tồn tại không
    if (!PAYOS_CONFIG.clientId || !PAYOS_CONFIG.apiKey || !PAYOS_CONFIG.checksumKey) {
      return NextResponse.json(
        {
          success: false,
          error: 'PayOS configuration is missing',
          data: null,
        },
        { status: 500 }
      );
    }

    // Chuẩn bị dữ liệu gửi đến PayOS theo cấu trúc mới nhất
    const requestData = {
      orderCode,
      amount,
      description,
      cancelUrl,
      returnUrl,
    };

    // Thêm danh sách items nếu có
    if (items && items.length > 0) {
      requestData.items = items.map(item => ({
        name: item.name,
        quantity: item.quantity || 1,
        price: item.price,
      }));
    }

    // Thêm thông tin người mua nếu có
    if (buyerName) requestData.buyerName = buyerName;
    if (buyerEmail) requestData.buyerEmail = buyerEmail;
    if (buyerPhone) requestData.buyerPhone = buyerPhone;
    if (buyerAddress) requestData.buyerAddress = buyerAddress;

    // Thêm thời gian hết hạn nếu có
    if (expiredAt) requestData.expiredAt = expiredAt;

    // Tạo chữ ký cho request
    requestData.signature = createSignature(requestData, PAYOS_CONFIG.checksumKey);

    // Gọi API PayOS để tạo link thanh toán
    const response = await axios.post(
      `${PAYOS_CONFIG.baseUrl}/v2/payment-requests`,
      requestData,
      {
        headers: {
          'x-client-id': PAYOS_CONFIG.clientId,
          'x-api-key': PAYOS_CONFIG.apiKey,
          'Content-Type': 'application/json',
        },
      }
    );

    // Kiểm tra kết quả từ PayOS
    if (response.data && response.data.code === '00') {
      // Trả về dữ liệu thành công với cấu trúc nhất quán
      return NextResponse.json({
        success: true,
        data: response.data.data,
        error: null,
      });
    }

    // Trả về lỗi nếu không thành công
    return NextResponse.json(
      {
        success: false,
        error: response.data.desc || 'Failed to create payment link',
        data: null,
      },
      { status: 400 }
    );
  } catch (error) {
    console.error('PayOS payment link creation error:', error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || 'Failed to create payment link',
        data: null,
      },
      { status: 500 }
    );
  }
}
