'use client';

import PropTypes from 'prop-types';
import { useState, useEffect } from 'react';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Table from '@mui/material/Table';
import Chip from '@mui/material/Chip';
import TableRow from '@mui/material/TableRow';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import Typography from '@mui/material/Typography';
import CardHeader from '@mui/material/CardHeader';
import TableContainer from '@mui/material/TableContainer';

import { fDateTime } from 'src/utils/format-time';
import { fNumber } from 'src/utils/format-number';

import { Scrollbar } from 'src/components/scrollbar';
import { TableNoData , TablePaginationCustom } from 'src/components/table';

// ----------------------------------------------------------------------

export default function CreditAuditLogs({ auditLogs, loading }) {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setPage(0);
    setRowsPerPage(parseInt(event.target.value, 10));
  };

  const getActionColor = (action) => {
    switch (action) {
      case 'credit_usage':
        return 'error';
      case 'credit_addition':
        return 'success';
      case 'credit_refund':
        return 'warning';
      default:
        return 'default';
    }
  };

  const getActionLabel = (action) => {
    switch (action) {
      case 'credit_usage':
        return 'Sử dụng Credit';
      case 'credit_addition':
        return 'Thêm Credit';
      case 'credit_refund':
        return 'Hoàn Credit';
      default:
        return action;
    }
  };

  const displayedLogs = auditLogs.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);
  const notFound = !loading && !auditLogs.length;
  const totalItems = auditLogs.length;

  return (
    <Card>
      <CardHeader title="Audit Logs" subheader="Lịch sử hoạt động credit chi tiết" />

      <TableContainer sx={{ position: 'relative', overflow: 'unset' }}>
        <Scrollbar>
          <Table size="small" sx={{ minWidth: 800 }}>
            <TableHead>
              <TableRow>
                <TableCell>Thời gian</TableCell>
                <TableCell>Hành động</TableCell>
                <TableCell align="right">Số tiền</TableCell>
                <TableCell align="right">Số dư trước</TableCell>
                <TableCell align="right">Số dư sau</TableCell>
                <TableCell>IP Address</TableCell>
                <TableCell>User Agent</TableCell>
              </TableRow>
            </TableHead>

            <TableBody>
              {displayedLogs.map((log) => (
                <TableRow key={log.id} hover>
                  <TableCell>
                    <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                      {fDateTime(log.createdAt)}
                    </Typography>
                  </TableCell>

                  <TableCell>
                    <Chip
                      size="small"
                      variant="soft"
                      color={getActionColor(log.action)}
                      label={getActionLabel(log.action)}
                    />
                  </TableCell>

                  <TableCell align="right">
                    <Typography
                      variant="body2"
                      sx={{
                        color: log.amount > 0 ? 'success.main' : 'error.main',
                        fontWeight: 'fontWeightMedium',
                      }}
                    >
                      {log.amount > 0 ? '+' : ''}
                      {fNumber(log.amount)}
                    </Typography>
                  </TableCell>

                  <TableCell align="right">
                    <Typography variant="body2">
                      {fNumber(log.balanceBefore)}
                    </Typography>
                  </TableCell>

                  <TableCell align="right">
                    <Typography variant="body2">
                      {fNumber(log.balanceAfter)}
                    </Typography>
                  </TableCell>

                  <TableCell>
                    <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                      {log.ipAddress || '-'}
                    </Typography>
                  </TableCell>

                  <TableCell>
                    <Typography 
                      variant="body2" 
                      sx={{ 
                        color: 'text.secondary',
                        maxWidth: 200,
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap'
                      }}
                    >
                      {log.userAgent || '-'}
                    </Typography>
                  </TableCell>
                </TableRow>
              ))}

              <TableNoData notFound={notFound} />
            </TableBody>
          </Table>
        </Scrollbar>
      </TableContainer>

      <TablePaginationCustom
        count={totalItems}
        page={page}
        rowsPerPage={rowsPerPage}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
        rowsPerPageOptions={[5, 10, 25, 50]}
        dense
      />
    </Card>
  );
}

CreditAuditLogs.propTypes = {
  auditLogs: PropTypes.array,
  loading: PropTypes.bool,
};
