'use client';

import {
  <PERSON>,
  Chip,
  Stack,
  Dialog,
  Typography,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button
} from '@mui/material';

import { Iconify } from 'src/components/iconify';

// ----------------------------------------------------------------------

export function AutomationRuleDetailDialog({ open, onClose, rule }) {
  if (!rule) return null;

  const getFollowUpTypeLabel = (type) => {
    const labels = {
      ai_offer_selection: '<PERSON> Chọn Offer',
      scheduled_message: 'Tin Nhắn <PERSON>'
    };
    return labels[type] || type;
  };

  const formatScheduleConfig = (config) => {
    if (!config || Object.keys(config).length === 0) return 'Không có cấu hình';
    
    const { type, time, daysOfWeek, dayOfMonth, intervalDays } = config;
    let schedule = '';
    
    switch (type) {
      case 'daily':
        schedule = `Hàng ngày lúc ${time}`;
        break;
      case 'weekly':
        { const days = daysOfWeek?.map(day => {
          const dayNames = ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'];
          return dayNames[day];
        }).join(', ');
        schedule = `Hàng tuần vào ${days} lúc ${time}`;
        break; }
      case 'monthly':
        schedule = `Hàng tháng ngày ${dayOfMonth} lúc ${time}`;
        break;
      case 'custom':
        schedule = `Mỗi ${intervalDays} ngày lúc ${time}`;
        break;
      default:
        schedule = JSON.stringify(config);
    }
    
    return schedule;
  };

  const formatCustomerFilters = (filters) => {
    if (!filters || Object.keys(filters).length === 0) return 'Không có bộ lọc';
    
    const conditions = [];
    if (filters.minOrderValue) {
      conditions.push(`Đơn hàng tối thiểu: ${Number(filters.minOrderValue).toLocaleString()}đ`);
    }
    if (filters.orderCount) {
      conditions.push(`Số đơn tối thiểu: ${filters.orderCount}`);
    }
    if (filters.lastOrderDays) {
      conditions.push(`Đơn cuối cách đây: ${filters.lastOrderDays} ngày`);
    }
    if (filters.noRecentContact) {
      conditions.push(`Không liên hệ trong: ${filters.noRecentContact} ngày`);
    }
    
    return conditions.length > 0 ? conditions.join(', ') : 'Không có bộ lọc';
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
    >
      <DialogTitle>
        <Stack direction="row" alignItems="center" spacing={2}>
          <Iconify icon="solar:settings-bold" width={24} />
          <Box>
            <Typography variant="h6">
              Chi Tiết Automation Rule
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {rule.name}
            </Typography>
          </Box>
        </Stack>
      </DialogTitle>

      <DialogContent>
        <Stack spacing={3}>
          {/* Basic Info */}
          <Box>
            <Typography variant="subtitle2" gutterBottom>
              Thông Tin Cơ Bản
            </Typography>
            <Stack spacing={1}>
              <Typography variant="body2">
                <strong>Tên:</strong> {rule.name}
              </Typography>
              <Typography variant="body2">
                <strong>Mô tả:</strong> {rule.description || 'Không có mô tả'}
              </Typography>
              <Stack direction="row" spacing={1}>
                <Chip
                  label={rule.is_active ? 'Đang hoạt động' : 'Tạm dừng'}
                  color={rule.is_active ? 'success' : 'default'}
                  size="small"
                />
                <Chip
                  label={`Ưu tiên: ${rule.priority || 1}`}
                  variant="outlined"
                  size="small"
                />
              </Stack>
            </Stack>
          </Box>

          {/* Follow-up Type */}
          {rule.follow_up_type && (
            <Box>
              <Typography variant="subtitle2" gutterBottom>
                Loại Bám Đuổi
              </Typography>
              <Chip
                label={getFollowUpTypeLabel(rule.follow_up_type)}
                color="primary"
                variant="soft"
              />
            </Box>
          )}

          {/* AI Offer Selection */}
          {rule.follow_up_type === 'ai_offer_selection' && (
            <>
              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  Ý Định Khách Hàng
                </Typography>
                <Typography variant="body2" sx={{ 
                  p: 2, 
                  bgcolor: 'grey.50', 
                  borderRadius: 1,
                  fontStyle: rule.customer_intent ? 'normal' : 'italic'
                }}>
                  {rule.customer_intent || 'Không có mô tả ý định'}
                </Typography>
              </Box>

              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  Offers Được Chọn
                </Typography>
                {rule.target_offers && rule.target_offers.length > 0 ? (
                  <Typography variant="body2">
                    {rule.target_offers.length} offer(s) được cấu hình
                  </Typography>
                ) : (
                  <Typography variant="body2" color="text.secondary" fontStyle="italic">
                    Chưa có offers nào được chọn
                  </Typography>
                )}
              </Box>
            </>
          )}

          {/* Scheduled Message */}
          {rule.follow_up_type === 'scheduled_message' && (
            <>
              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  Tin Nhắn Follow-up
                </Typography>
                <Typography variant="body2" sx={{ 
                  p: 2, 
                  bgcolor: 'grey.50', 
                  borderRadius: 1,
                  fontStyle: rule.follow_up_message ? 'normal' : 'italic'
                }}>
                  {rule.follow_up_message || 'Không có tin nhắn'}
                </Typography>
              </Box>

              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  Lịch Trình
                </Typography>
                <Typography variant="body2">
                  {formatScheduleConfig(rule.schedule_config)}
                </Typography>
              </Box>
            </>
          )}

          {/* Customer Filters */}
          <Box>
            <Typography variant="subtitle2" gutterBottom>
              Bộ Lọc Khách Hàng
            </Typography>
            <Typography variant="body2">
              {formatCustomerFilters(rule.customer_filters)}
            </Typography>
          </Box>

          {/* Actions */}
          {rule.actions && rule.actions.length > 0 && (
            <Box>
              <Typography variant="subtitle2" gutterBottom>
                Hành Động
              </Typography>
              <Stack spacing={1}>
                {rule.actions.map((action, index) => (
                  <Chip
                    key={index}
                    label={action.type}
                    variant="outlined"
                    size="small"
                  />
                ))}
              </Stack>
            </Box>
          )}
        </Stack>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose}>
          Đóng
        </Button>
      </DialogActions>
    </Dialog>
  );
}
