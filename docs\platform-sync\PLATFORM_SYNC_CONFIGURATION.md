# Cấu hình Đồng bộ Sản phẩm từ Nền tảng Khác

## Tổng quan

Hệ thống đồng bộ sản phẩm cho phép import sản phẩm từ các nền tảng e-commerce khác như <PERSON>van và Sapo vào hệ thống Mooly Chatbot.

## Các nền tảng được hỗ trợ

### 1. <PERSON>van
- **Yêu cầu**: Token API Haravan
- **Endpoint**: `/api/platform-sync/haravan/products`
- **Phương thức**: POST

### 2. Sapo
- **Yêu cầu**: Sapo API URL (với username:password)
- **Endpoint**: `/api/platform-sync/sapo/products`
- **Phương thức**: POST

## Cấu hình Business Types

Feature `platformSync` đã được thêm vào tất cả business types:

```javascript
// retail, digital, services, hybrid
features: {
  // ... other features
  platformSync: true
}
```

## C<PERSON><PERSON> sử dụng

### 1. <PERSON><PERSON><PERSON> cập tính năng đồng bộ
- V<PERSON><PERSON> trang **<PERSON><PERSON>n phẩm** trong dashboard
- Nhấn nút **"Đồng bộ sản phẩm"** ở góc trên bên phải
- Chọn nền tảng muốn đồng bộ (Haravan hoặc Sapo)

### 2. Đồng bộ từ Haravan
1. Chọn tab **Haravan**
2. Nhập **Token Haravan** của bạn
3. Chọn **"Đồng bộ toàn bộ"** nếu muốn đồng bộ tất cả sản phẩm
4. Nhấn **"Đồng bộ"**

### 3. Đồng bộ từ Sapo
1. Chọn tab **Sapo**
2. Nhập **Sapo URL** theo format: `https://username:<EMAIL>/admin/orders.json`
3. Chọn **"Đồng bộ toàn bộ"** nếu muốn đồng bộ tất cả sản phẩm
4. Nhấn **"Đồng bộ"**

## Cấu hình Backend

### Biến môi trường
```env
# URL của backend API thực (tùy chọn)
BACKEND_API_URL=https://your-backend-api.com

# URL của site hiện tại (để tránh circular calls)
NEXT_PUBLIC_SITE_URL=https://your-frontend.com
```

### Logic hoạt động
1. **Nếu có BACKEND_API_URL**: Gọi đến backend thực để xử lý đồng bộ
2. **Nếu không có hoặc backend fail**: Sử dụng mock implementation để demo

## API Response Format

### Thành công
```json
{
  "success": true,
  "message": "Đã đồng bộ thành công X sản phẩm từ [Platform]",
  "data": {
    "synced_count": 5,
    "total_available": 50,
    "platform": "haravan|sapo",
    "sync_type": "full|incremental",
    "tenant_id": "tenant_id"
  }
}
```

### Lỗi
```json
{
  "success": false,
  "error": "Error message"
}
```

## Tính năng

### 1. Auto-refresh Product List
- Sau khi đồng bộ thành công, danh sách sản phẩm sẽ tự động refresh
- Không cần reload trang

### 2. Loading States
- Hiển thị loading toast trong quá trình đồng bộ
- Disable form trong khi đang xử lý

### 3. Error Handling
- Hiển thị thông báo lỗi chi tiết
- Fallback từ backend thực sang mock nếu cần

### 4. Validation
- Validate token/URL trước khi gửi request
- Kiểm tra quyền truy cập tenant

## Troubleshooting

### Lỗi "Token is required"
- Đảm bảo đã nhập token Haravan hợp lệ

### Lỗi "Sapo URL is required"
- Đảm bảo đã nhập Sapo URL đúng format
- Format: `https://username:<EMAIL>/admin/orders.json`

### Nút đồng bộ không hiển thị
- Kiểm tra business type có enable feature `platformSync`
- Kiểm tra `useBusinessConfig` hook

### Backend connection failed
- Hệ thống sẽ tự động fallback sang mock implementation
- Kiểm tra BACKEND_API_URL trong environment variables

## Development Notes

### Mock Implementation
- Được sử dụng khi không có backend thực hoặc backend fail
- Simulate 2 giây processing time
- Random số lượng sản phẩm đồng bộ (1-10 cho Haravan, 1-8 cho Sapo)

### Real Backend Integration
- Cần cấu hình BACKEND_API_URL
- Backend phải implement cùng API contract
- Sử dụng tenant-based authentication

## Files liên quan

- `src/actions/mooly-chatbot/business-config-service.js` - Business type configuration
- `src/sections/mooly-chatbot/platform-sync/` - UI components
- `src/app/api/platform-sync/` - API routes
- `src/actions/mooly-chatbot/platform-sync-service.js` - Client service
