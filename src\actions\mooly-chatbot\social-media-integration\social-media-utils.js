/**
 * Unified Social Media Integration Utilities
 * Các utility functions chung cho tất cả social media platforms
 */

import { 
  PLATFORMS, 
  PLATFORM_FEATURES, 
  RATE_LIMITS, 
  ERROR_MESSAGES,
  AUTO_REPLY_TEMPLATES,
  REPLY_LANGUAGES
} from './social-media-constants.js';

/**
 * Platform Validation
 */

/**
 * Check if platform is supported
 * @param {string} platform - Platform name
 * @returns {boolean} Is supported
 */
export function isPlatformSupported(platform) {
  return Object.values(PLATFORMS).includes(platform);
}

/**
 * Check if platform supports specific feature
 * @param {string} platform - Platform name
 * @param {string} feature - Feature name (e.g., 'SUPPORTS_COMMENTS')
 * @returns {boolean} Is supported
 */
export function platformSupportsFeature(platform, feature) {
  if (!isPlatformSupported(platform)) return false;
  return PLATFORM_FEATURES[platform]?.[feature] || false;
}

/**
 * Get platform-specific limits
 * @param {string} platform - Platform name
 * @param {string} limitType - Limit type (e.g., 'MAX_MESSAGE_LENGTH')
 * @returns {number} Limit value
 */
export function getPlatformLimit(platform, limitType) {
  if (!isPlatformSupported(platform)) return 0;
  return PLATFORM_FEATURES[platform]?.[limitType] || 0;
}

/**
 * Content Validation
 */

/**
 * Validate message content for platform
 * @param {string} platform - Platform name
 * @param {string} content - Message content
 * @param {string} type - Content type ('message' or 'comment')
 * @returns {Object} Validation result
 */
export function validateContent(platform, content, type = 'message') {
  if (!isPlatformSupported(platform)) {
    return {
      isValid: false,
      error: ERROR_MESSAGES.PLATFORM_NOT_SUPPORTED
    };
  }

  if (!content || content.trim().length === 0) {
    return {
      isValid: false,
      error: 'Nội dung không được để trống'
    };
  }

  const maxLength = type === 'message' 
    ? getPlatformLimit(platform, 'MAX_MESSAGE_LENGTH')
    : getPlatformLimit(platform, 'MAX_COMMENT_LENGTH');

  if (content.length > maxLength) {
    return {
      isValid: false,
      error: `Nội dung vượt quá ${maxLength} ký tự cho ${platform}`
    };
  }

  return {
    isValid: true,
    trimmedContent: content.trim()
  };
}

/**
 * Rate Limiting
 */

/**
 * Check if action is within rate limits
 * @param {string} platform - Platform name
 * @param {string} actionType - Action type ('COMMENTS_PER_HOUR', 'MESSAGES_PER_HOUR', etc.)
 * @param {number} currentCount - Current action count in the time window
 * @returns {boolean} Is within limits
 */
export function isWithinRateLimit(platform, actionType, currentCount) {
  if (!isPlatformSupported(platform)) return false;
  
  const limit = RATE_LIMITS[platform]?.[actionType];
  if (!limit) return true; // No limit defined
  
  return currentCount < limit;
}

/**
 * Get remaining rate limit
 * @param {string} platform - Platform name
 * @param {string} actionType - Action type
 * @param {number} currentCount - Current action count
 * @returns {number} Remaining count
 */
export function getRemainingRateLimit(platform, actionType, currentCount) {
  if (!isPlatformSupported(platform)) return 0;
  
  const limit = RATE_LIMITS[platform]?.[actionType] || 0;
  return Math.max(0, limit - currentCount);
}

/**
 * Token Management
 */

/**
 * Check if token is expired
 * @param {string} expiresAt - Token expiration timestamp
 * @param {number} bufferMinutes - Buffer time in minutes before expiration
 * @returns {boolean} Is expired
 */
export function isTokenExpired(expiresAt, bufferMinutes = 60) {
  if (!expiresAt) return false; // No expiration set
  
  const expirationTime = new Date(expiresAt).getTime();
  const bufferTime = bufferMinutes * 60 * 1000; // Convert to milliseconds
  const currentTime = Date.now();
  
  return currentTime >= (expirationTime - bufferTime);
}

/**
 * Calculate token expiration time
 * @param {number} expiresInSeconds - Seconds until expiration
 * @returns {string} ISO timestamp
 */
export function calculateTokenExpiration(expiresInSeconds) {
  const expirationTime = Date.now() + (expiresInSeconds * 1000);
  return new Date(expirationTime).toISOString();
}

/**
 * Content Processing
 */

/**
 * Extract mentions from content
 * @param {string} content - Content to analyze
 * @returns {Array<string>} Array of mentioned usernames
 */
export function extractMentions(content) {
  if (!content) return [];
  
  const mentionRegex = /@([a-zA-Z0-9._]+)/g;
  const mentions = [];
  let match;
  
  while ((match = mentionRegex.exec(content)) !== null) {
    mentions.push(match[1]);
  }
  
  return mentions;
}

/**
 * Extract hashtags from content
 * @param {string} content - Content to analyze
 * @returns {Array<string>} Array of hashtags
 */
export function extractHashtags(content) {
  if (!content) return [];
  
  const hashtagRegex = /#([a-zA-Z0-9_]+)/g;
  const hashtags = [];
  let match;
  
  while ((match = hashtagRegex.exec(content)) !== null) {
    hashtags.push(match[1]);
  }
  
  return hashtags;
}

/**
 * Clean content for auto-reply
 * @param {string} content - Original content
 * @returns {string} Cleaned content
 */
export function cleanContentForReply(content) {
  if (!content) return '';
  
  return content
    .trim()
    .replace(/\s+/g, ' ') // Replace multiple spaces with single space
    .replace(/[^\w\s\u00C0-\u024F\u1E00-\u1EFF]/g, '') // Keep only letters, numbers, spaces, and Vietnamese characters
    .substring(0, 500); // Limit length
}

/**
 * Auto-reply Generation
 */

/**
 * Get auto-reply template
 * @param {string} templateType - Template type ('GREETING', 'RECEIVED', 'THANK_YOU')
 * @param {string} language - Language code
 * @returns {string} Template text
 */
export function getAutoReplyTemplate(templateType, language = REPLY_LANGUAGES.VIETNAMESE) {
  const template = AUTO_REPLY_TEMPLATES[templateType];
  if (!template) return '';
  
  return template[language] || template[REPLY_LANGUAGES.VIETNAMESE] || '';
}

/**
 * Generate personalized auto-reply
 * @param {Object} options - Reply options
 * @param {string} options.userName - User name
 * @param {string} options.businessName - Business name
 * @param {string} options.language - Language code
 * @param {string} options.tone - Reply tone
 * @returns {string} Personalized reply
 */
export function generatePersonalizedReply(options) {
  const { userName, businessName, language = 'vi', tone = 'friendly' } = options;
  
  let reply = getAutoReplyTemplate('GREETING', language);
  
  // Personalize with user name
  if (userName) {
    reply = reply.replace(/Xin chào!/g, `Xin chào ${userName}!`);
    reply = reply.replace(/Hello!/g, `Hello ${userName}!`);
  }
  
  // Add business name
  if (businessName) {
    if (language === 'vi') {
      reply += ` Đây là ${businessName}.`;
    } else {
      reply += ` This is ${businessName}.`;
    }
  }
  
  return reply;
}

/**
 * URL and Link Processing
 */

/**
 * Extract URLs from content
 * @param {string} content - Content to analyze
 * @returns {Array<string>} Array of URLs
 */
export function extractUrls(content) {
  if (!content) return [];
  
  const urlRegex = /(https?:\/\/[^\s]+)/g;
  return content.match(urlRegex) || [];
}

/**
 * Check if content contains spam indicators
 * @param {string} content - Content to check
 * @param {Array<string>} excludeKeywords - Keywords to exclude
 * @returns {boolean} Is likely spam
 */
export function isLikelySpam(content, excludeKeywords = []) {
  if (!content) return false;
  
  const lowerContent = content.toLowerCase();
  
  // Check exclude keywords
  for (const keyword of excludeKeywords) {
    if (lowerContent.includes(keyword.toLowerCase())) {
      return true;
    }
  }
  
  // Common spam indicators
  const spamIndicators = [
    'click here',
    'free money',
    'make money fast',
    'limited time',
    'act now',
    'congratulations',
    'you have won'
  ];
  
  return spamIndicators.some(indicator => 
    lowerContent.includes(indicator)
  );
}

/**
 * Format timestamp for display
 * @param {string} timestamp - ISO timestamp
 * @param {string} locale - Locale for formatting
 * @returns {string} Formatted timestamp
 */
export function formatTimestamp(timestamp, locale = 'vi-VN') {
  if (!timestamp) return '';
  
  const date = new Date(timestamp);
  return date.toLocaleString(locale, {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
}

/**
 * Generate unique interaction ID
 * @param {string} platform - Platform name
 * @param {string} externalId - External ID from platform
 * @returns {string} Unique interaction ID
 */
export function generateInteractionId(platform, externalId) {
  return `${platform}_${externalId}_${Date.now()}`;
}
