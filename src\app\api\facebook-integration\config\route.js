import { NextResponse } from 'next/server';
import { createClient } from 'src/utils/supabase/server';

/**
 * Facebook Integration Configuration API
 * Get overall configuration status for chatbot setup
 */

// GET: Fetch configuration status
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const chatbotId = searchParams.get('chatbotId');
    
    console.log('🔍 Fetching config status for chatbot:', chatbotId);
    
    const supabase = await createClient();
    
    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      console.error('❌ Authentication error:', userError);
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }
    
    console.log('✅ User authenticated:', user.id);
    
    // Get selected page for this chatbot
    let selectedPage = null;
    let aiConfig = null;
    let featureSettings = null;
    
    if (chatbotId) {
      // Check activity logs for page selection
      const { data: selectionLog } = await supabase
        .from('facebook_activity_logs')
        .select('page_id, metadata')
        .eq('activity', 'chatbot_page_selected')
        .contains('metadata', { chatbotId })
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      if (selectionLog) {
        console.log('📋 Found selected page:', selectionLog.page_id);
        
        // Get page details
        const { data: pageData } = await supabase
          .from('facebook_accounts')
          .select('*')
          .eq('page_id', selectionLog.page_id)
          .single();

        if (pageData) {
          selectedPage = {
            pageId: pageData.page_id,
            pageName: pageData.page_name,
            instagramAccountId: pageData.instagram_account_id,
            hasInstagram: !!pageData.instagram_account_id
          };
          
          // Get AI configuration for this page
          const { data: configData } = await supabase
            .from('facebook_auto_reply_config')
            .select('*')
            .eq('page_id', selectionLog.page_id)
            .single();

          if (configData) {
            aiConfig = {
              autoReplyComments: configData.enable_comment_reply,
              autoReplyMessages: configData.enable_message_reply,
              instagramComments: configData.enable_instagram_comments,
              instagramMessages: configData.enable_instagram_messages,
              autoPrivateReply: configData.auto_private_reply,
              prompt: configData.reply_prompt,
              tone: configData.reply_tone,
              language: configData.reply_language,
              maxLength: configData.max_reply_length,
              businessInfo: configData.business_info,
              products: configData.products,
              policies: configData.policies,
              excludeKeywords: configData.exclude_keywords || []
            };
            
            // Determine feature settings based on config
            featureSettings = {
              facebookComments: configData.enable_comment_reply,
              instagramComments: configData.enable_instagram_comments,
              facebookMessages: configData.enable_message_reply,
              instagramMessages: configData.enable_instagram_messages,
              isComplete: true // Mark as complete if config exists
            };
          }
        }
      }
    }
    
    console.log('📊 Config status:', {
      hasSelectedPage: !!selectedPage,
      hasAiConfig: !!aiConfig,
      hasFeatureSettings: !!featureSettings
    });
    
    return NextResponse.json({
      success: true,
      selectedPage,
      aiConfig,
      featureSettings
    });
    
  } catch (error) {
    console.error('❌ Facebook config API error:', error);
    return NextResponse.json({ 
      success: false,
      error: 'Internal server error',
      details: error.message
    }, { status: 500 });
  }
}
