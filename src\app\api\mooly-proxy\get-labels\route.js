import { NextResponse } from 'next/server';

import { withTenantAuth } from 'src/utils/server-auth';
import { createClient } from 'src/utils/supabase/server';

/**
 * L<PERSON>y thông tin tài khoản <PERSON>oly từ database
 * @param {string} accountId - ID tài khoản <PERSON>oly
 * @param {string} tenantId - ID của tenant
 * @returns {Promise<Object>} - Thông tin tài khoản
 */
async function getMoolyAccount(accountId, tenantId) {
  try {
    const supabase = await createClient();
    const { data, error } = await supabase
      .from('mooly_accounts')
      .select('*')
      .eq('account_id', accountId)
      .eq('tenant_id', tenantId)
      .single();

    if (error) {
      console.error('Error fetching Mooly account:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Exception when fetching Mooly account:', error);
    return null;
  }
}

/**
 * <PERSON><PERSON><PERSON> sách labels từ API Mooly
 * @param {string} token - Token xác thực
 * @param {string} accountId - ID tài khoản
 * @returns {Promise<Array>} - <PERSON>h sách labels
 */
async function fetchLabelsFromAPI(token, accountId) {
  const host = process.env.MOOLY_API_HOST || 'https://app.mooly.vn';
  const apiVersion = process.env.MOOLY_API_VERSION || 'api/v1';

  try {
    const response = await fetch(`${host}/${apiVersion}/accounts/${accountId}/labels`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'api_access_token': token
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch labels: ${response.statusText}`);
    }

    const data = await response.json();
    return data.payload || [];
  } catch (error) {
    console.error('Error fetching labels from API:', error);
    return [];
  }
}

/**
 * API endpoint để lấy danh sách labels của tài khoản Mooly
 * @param {Request} request - Yêu cầu HTTP
 * @returns {Promise<NextResponse>} - Phản hồi HTTP
 */
export const GET = withTenantAuth(async (request, { tenantId }) => {
  try {
    // Lấy thông tin từ URL params
    const { searchParams } = new URL(request.url);
    const accountId = searchParams.get('account_id');

    // Kiểm tra dữ liệu đầu vào
    if (!accountId) {
      return NextResponse.json(
        { success: false, error: 'Account ID is required', data: null },
        { status: 400 }
      );
    }

    // Lấy thông tin tài khoản
    const account = await getMoolyAccount(accountId, tenantId);
    if (!account) {
      return NextResponse.json(
        { success: false, error: 'Account not found', data: null },
        { status: 404 }
      );
    }

    // Lấy token từ tài khoản
    const { token } = account;

    // Lấy danh sách labels từ API
    const apiLabels = await fetchLabelsFromAPI(token, accountId);

    // Lấy cấu hình labels từ database
    const labelsConfig = account.labels_config || {};

    // Kết hợp thông tin từ API và database
    const combinedLabels = apiLabels.map(label => {
      const configLabel = Object.values(labelsConfig).find(l => l.id === label.id);
      return {
        ...label,
        system_label: configLabel?.system_label || false
      };
    });

    // Trả về kết quả
    return NextResponse.json({
      success: true,
      error: null,
      data: {
        labels: combinedLabels,
        config: labelsConfig
      }
    });
  } catch (error) {
    console.error('Error in get labels:', error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || 'Failed to get labels',
        data: null,
      },
      { status: 500 }
    );
  }
});
