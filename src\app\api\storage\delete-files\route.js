import { NextResponse } from 'next/server';

import { withTenantAuth, validateTenantId } from 'src/utils/server-auth';
import storageService from 'src/actions/mooly-chatbot/storage-service';

/**
 * API route để xóa nhiều files từ Supabase Storage
 * @param {Request} request - <PERSON><PERSON><PERSON> cầu HTTP
 * @returns {Promise<NextResponse>} - Phản hồi HTTP
 */
export const DELETE = withTenantAuth(async (request, { tenantId, userId }) => {
  try {
    // Validate request body và kiểm tra tenant_id
    const validation = await validateTenantId(request, tenantId);
    if (!validation.valid) {
      return NextResponse.json({ error: validation.error }, { status: validation.status });
    }

    const { bucket, file_urls } = validation.body;

    // Kiểm tra dữ liệu đầu vào
    if (!bucket) {
      return NextResponse.json(
        { error: 'Missing required field: bucket' },
        { status: 400 }
      );
    }

    if (!file_urls || !Array.isArray(file_urls) || file_urls.length === 0) {
      return NextResponse.json(
        { error: 'Missing or invalid file_urls array' },
        { status: 400 }
      );
    }

    // Lọc bỏ các URL rỗng hoặc không hợp lệ
    const validUrls = file_urls.filter(
      (url) => url && typeof url === 'string' && url.trim() !== ''
    );

    if (validUrls.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No valid URLs to delete',
        data: {
          processed: 0,
          successful: 0,
          failed: 0,
          errors: []
        }
      });
    }

    // Xóa files từ storage
    const deleteResult = await storageService.deleteFilesWithPublicUrl(bucket, validUrls);

    if (!deleteResult.success) {
      console.error('Error deleting files from storage:', deleteResult.error);
      return NextResponse.json(
        { 
          error: 'Failed to delete files from storage', 
          details: deleteResult.error?.message || 'Unknown error',
          data: {
            processed: validUrls.length,
            successful: 0,
            failed: validUrls.length,
            errors: [deleteResult.error?.message || 'Unknown error']
          }
        },
        { status: 500 }
      );
    }

    // Tính toán kết quả
    const processedUrls = deleteResult.processedUrls || validUrls.length;
    const successfullyProcessed = deleteResult.successfullyProcessed || 0;
    const failedUrls = deleteResult.failedUrls || [];

    return NextResponse.json({
      success: true,
      message: `Successfully processed ${processedUrls} URLs, deleted ${successfullyProcessed} files`,
      data: {
        processed: processedUrls,
        successful: successfullyProcessed,
        failed: failedUrls.length,
        errors: failedUrls.length > 0 ? [`Failed to process ${failedUrls.length} URLs`] : []
      }
    });

  } catch (error) {
    console.error('Error in storage delete-files route:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error', 
        details: error.message,
        data: {
          processed: 0,
          successful: 0,
          failed: 0,
          errors: [error.message]
        }
      },
      { status: 500 }
    );
  }
});
