'use client';

import { useEffect } from 'react';
import PropTypes from 'prop-types';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import LoadingButton from '@mui/lab/LoadingButton';
import DialogTitle from '@mui/material/DialogTitle';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';

import { useOrderMutations } from 'src/actions/mooly-chatbot/order-mutations';

import { toast } from 'src/components/snackbar';
import { Form, Field } from 'src/components/hook-form';

import { OrderBasicEditSchema } from '../validation/order-edit-schemas';

// ----------------------------------------------------------------------

export function OrderEditBasicDialog({ open, onClose, order, onSuccess }) {
  const { updateOrder, isMutating } = useOrderMutations();

  const defaultValues = {
    customerName: order?.customerName || '',
    customerPhone: order?.customerPhone || '',
    customerEmail: order?.customerEmail || '',
    notes: order?.notes || '',
  };

  const methods = useForm({
    resolver: zodResolver(OrderBasicEditSchema),
    defaultValues,
  });

  const { handleSubmit, reset } = methods;

  // Reset form khi dialog mở và có dữ liệu mới
  useEffect(() => {
    if (open && order) {
      const newValues = {
        customerName: order?.customerName || '',
        customerPhone: order?.customerPhone || '',
        customerEmail: order?.customerEmail || '',
        notes: order?.notes || '',
      };
      reset(newValues);
    }
  }, [open, order, reset]);

  const onSubmit = handleSubmit(async (data) => {
    try {
      const result = await updateOrder(order.id, data);

      if (!result.success) {
        throw new Error(result.error || 'Cập nhật thông tin thất bại');
      }

      toast.success('Cập nhật thông tin khách hàng thành công!');
      onSuccess?.();
      onClose();
    } catch (error) {
      console.error('Error updating basic info:', error);
      toast.error(error.message || 'Cập nhật thông tin thất bại!');
    }
  });

  const handleClose = () => {
    reset();
    onClose();
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle>Chỉnh sửa thông tin khách hàng</DialogTitle>

      <Form methods={methods} onSubmit={onSubmit}>
        <DialogContent>
          <Stack spacing={3} sx={{ pt: 1 }}>
            <Field.Text
              name="customerName"
              label="Tên khách hàng"
              required
              placeholder="Nhập tên khách hàng"
            />

            <Field.Text
              name="customerPhone"
              label="Số điện thoại"
              required
              placeholder="Nhập số điện thoại"
            />

            <Field.Text
              name="customerEmail"
              label="Email"
              type="email"
              placeholder="Nhập email (tùy chọn)"
            />

            <Field.Text
              name="notes"
              label="Ghi chú"
              multiline
              rows={3}
              placeholder="Nhập ghi chú cho đơn hàng"
            />
          </Stack>
        </DialogContent>

        <DialogActions>
          <Button onClick={handleClose} color="inherit">
            Hủy
          </Button>
          <LoadingButton
            type="submit"
            variant="contained"
            loading={isMutating}
          >
            Cập nhật
          </LoadingButton>
        </DialogActions>
      </Form>
    </Dialog>
  );
}

OrderEditBasicDialog.propTypes = {
  open: PropTypes.bool,
  onClose: PropTypes.func,
  order: PropTypes.object,
  onSuccess: PropTypes.func,
};
