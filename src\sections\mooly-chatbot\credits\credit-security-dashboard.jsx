'use client';

import PropTypes from 'prop-types';
import { useState, useEffect } from 'react';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Grid from '@mui/material/Grid';
import Alert from '@mui/material/Alert';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import CardContent from '@mui/material/CardContent';

import { fNumber } from 'src/utils/format-number';
import { fDateTime } from 'src/utils/format-time';

import { Iconify } from 'src/components/iconify';

import CreditAuditLogs from './credit-audit-logs';

// ----------------------------------------------------------------------

export default function CreditSecurityDashboard({ 
  creditBalance, 
  auditLogs, 
  securityMetrics,
  loading 
}) {
  const [alerts, setAlerts] = useState([]);

  useEffect(() => {
    // Analyze security metrics and generate alerts
    const newAlerts = [];

    if (securityMetrics?.suspiciousActivity > 5) {
      newAlerts.push({
        severity: 'warning',
        message: `Phát hiện ${securityMetrics.suspiciousActivity} hoạt động đáng ngờ trong 24h qua`,
      });
    }

    if (securityMetrics?.failedAttempts > 10) {
      newAlerts.push({
        severity: 'error',
        message: `${securityMetrics.failedAttempts} lần thử truy cập credit thất bại trong 1h qua`,
      });
    }

    if (creditBalance < 10) {
      newAlerts.push({
        severity: 'info',
        message: 'Số dư credit thấp, nên mua thêm để đảm bảo hoạt động liên tục',
      });
    }

    setAlerts(newAlerts);
  }, [securityMetrics, creditBalance]);

  const securityCards = [
    {
      title: 'Số dư Credit',
      value: fNumber(creditBalance),
      icon: 'eva:credit-card-fill',
      color: creditBalance > 50 ? 'success' : creditBalance > 10 ? 'warning' : 'error',
    },
    {
      title: 'Giao dịch hôm nay',
      value: securityMetrics?.todayTransactions || 0,
      icon: 'eva:activity-fill',
      color: 'info',
    },
    {
      title: 'Hoạt động đáng ngờ',
      value: securityMetrics?.suspiciousActivity || 0,
      icon: 'eva:alert-triangle-fill',
      color: securityMetrics?.suspiciousActivity > 0 ? 'error' : 'success',
    },
    {
      title: 'Lần cập nhật cuối',
      value: securityMetrics?.lastUpdate ? fDateTime(securityMetrics.lastUpdate) : 'Chưa có',
      icon: 'eva:clock-fill',
      color: 'default',
    },
  ];

  return (
    <Stack spacing={3}>
      {/* Security Alerts */}
      {alerts.length > 0 && (
        <Stack spacing={2}>
          {alerts.map((alert, index) => (
            <Alert key={index} severity={alert.severity}>
              {alert.message}
            </Alert>
          ))}
        </Stack>
      )}

      {/* Security Metrics Cards */}
      <Grid container spacing={3}>
        {securityCards.map((card, index) => (
          <Grid size={{ xs: 12, sm: 6, md: 3 }} key={index}>
            <Card>
              <CardContent>
                <Stack direction="row" alignItems="center" spacing={2}>
                  <Box
                    sx={{
                      width: 48,
                      height: 48,
                      borderRadius: 1.5,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      bgcolor: `${card.color}.lighter`,
                      color: `${card.color}.main`,
                    }}
                  >
                    <Iconify icon={card.icon} width={24} />
                  </Box>
                  <Stack spacing={0.5} sx={{ flexGrow: 1 }}>
                    <Typography variant="h4">{card.value}</Typography>
                    <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                      {card.title}
                    </Typography>
                  </Stack>
                </Stack>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Audit Logs */}
      <CreditAuditLogs auditLogs={auditLogs} loading={loading} />

      {/* Security Recommendations */}
      <Card>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            Khuyến nghị bảo mật
          </Typography>
          <Stack spacing={1}>
            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              • Tất cả giao dịch credit được mã hóa và bảo vệ bởi RLS policies
            </Typography>
            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              • Hệ thống tự động ghi log tất cả hoạt động credit
            </Typography>
            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              • Rate limiting được áp dụng để ngăn chặn abuse
            </Typography>
            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              • Database functions đảm bảo tính toàn vẹn dữ liệu
            </Typography>
          </Stack>
        </CardContent>
      </Card>
    </Stack>
  );
}

CreditSecurityDashboard.propTypes = {
  creditBalance: PropTypes.number,
  auditLogs: PropTypes.array,
  securityMetrics: PropTypes.object,
  loading: PropTypes.bool,
};
