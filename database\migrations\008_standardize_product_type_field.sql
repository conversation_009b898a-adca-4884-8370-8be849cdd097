-- =====================================================
-- MIGRATION: Standardize Product Type Field
-- Mục đích: Đồng bộ toàn bộ hệ thống sử dụng field 'type' thay vì 'hasVariants', 'has_variants', 'product_type_new'
-- =====================================================

-- =====================================================
-- 1. MIGRATE DATA FROM product_type_new TO type
-- =====================================================

-- Cập nhật field 'type' từ 'product_type_new' nếu tồn tại
DO $$ 
BEGIN
    -- Ki<PERSON>m tra xem cột product_type_new có tồn tại không
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'products' AND column_name = 'product_type_new'
    ) THEN
        -- Cập nhật type từ product_type_new
        UPDATE products 
        SET type = product_type_new::text 
        WHERE product_type_new IS NOT NULL;
        
        RAISE NOTICE 'Migrated product_type_new to type field';
    END IF;
END $$;

-- =====================================================
-- 2. ENSURE type FIELD EXISTS AND HAS CORRECT VALUES
-- =====================================================

-- Thêm cột type nếu chưa có
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'products' AND column_name = 'type'
    ) THEN
        ALTER TABLE products ADD COLUMN type VARCHAR(50) DEFAULT 'simple';
        RAISE NOTICE 'Added type column to products table';
    END IF;
END $$;

-- Cập nhật type mặc định cho các sản phẩm chưa có type
UPDATE products 
SET type = 'simple' 
WHERE type IS NULL OR type = '';

-- =====================================================
-- 3. MIGRATE hasVariants LOGIC TO type FIELD
-- =====================================================

-- Cập nhật type = 'variable' cho các sản phẩm có hasVariants = true
DO $$ 
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'products' AND column_name = 'hasVariants'
    ) THEN
        UPDATE products 
        SET type = 'variable' 
        WHERE hasVariants = true AND type = 'simple';
        
        RAISE NOTICE 'Updated products with hasVariants=true to type=variable';
    END IF;
END $$;

-- Cập nhật type = 'variable' cho các sản phẩm có has_variants = true
DO $$ 
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'products' AND column_name = 'has_variants'
    ) THEN
        UPDATE products 
        SET type = 'variable' 
        WHERE has_variants = true AND type = 'simple';
        
        RAISE NOTICE 'Updated products with has_variants=true to type=variable';
    END IF;
END $$;

-- =====================================================
-- 4. UPDATE hasVariants BASED ON type FIELD
-- =====================================================

-- Cập nhật hasVariants dựa trên type để đảm bảo tính nhất quán
DO $$ 
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'products' AND column_name = 'hasVariants'
    ) THEN
        UPDATE products 
        SET hasVariants = (type = 'variable');
        
        RAISE NOTICE 'Updated hasVariants field based on type field';
    END IF;
END $$;

-- Cập nhật has_variants dựa trên type để đảm bảo tính nhất quán
DO $$ 
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'products' AND column_name = 'has_variants'
    ) THEN
        UPDATE products 
        SET has_variants = (type = 'variable');
        
        RAISE NOTICE 'Updated has_variants field based on type field';
    END IF;
END $$;

-- =====================================================
-- 5. ADD CONSTRAINTS AND VALIDATIONS
-- =====================================================

-- Thêm constraint cho type field
ALTER TABLE products DROP CONSTRAINT IF EXISTS check_product_type;
ALTER TABLE products ADD CONSTRAINT check_product_type 
CHECK (type IN ('simple', 'variable', 'digital', 'service', 'bundle', 'subscription'));

-- =====================================================
-- 6. CREATE INDEXES FOR PERFORMANCE
-- =====================================================

-- Index cho type field
CREATE INDEX IF NOT EXISTS idx_products_type ON products(type);

-- Index cho variable products
CREATE INDEX IF NOT EXISTS idx_products_variable ON products(tenant_id, type) WHERE type = 'variable';

-- =====================================================
-- 7. UPDATE COMMENTS
-- =====================================================

COMMENT ON COLUMN products.type IS 'Product type: simple, variable, digital, service, bundle, subscription. Use this field to determine if product has variants (type=variable)';

-- =====================================================
-- ROLLBACK SCRIPT (for reference)
-- =====================================================

/*
-- To rollback this migration:

-- Remove constraints
ALTER TABLE products DROP CONSTRAINT IF EXISTS check_product_type;

-- Remove indexes
DROP INDEX IF EXISTS idx_products_type;
DROP INDEX IF EXISTS idx_products_variable;

-- Reset type field (optional)
-- UPDATE products SET type = 'simple';
*/
