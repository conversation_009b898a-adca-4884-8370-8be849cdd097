'use client';

import { useState, useCallback, useEffect, useMemo } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';

import {
  Box,
  Card,
  Chip,
  Stack,
  Button,
  Dialog,
  Switch,
  Divider,
  Typography,
  IconButton,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControlLabel,
  Alert,
  CircularProgress,
} from '@mui/material';
import { LoadingButton } from '@mui/lab';

import { useBoolean } from 'src/hooks/use-boolean';

import { Iconify } from 'src/components/iconify';
import { toast } from 'src/components/snackbar';
import { Scrollbar } from 'src/components/scrollbar';

import {
  useKanbanDisplayConfig,
  updateKanbanDisplayConfig,
  getKanbanFieldOptions,
  getDefaultKanbanConfig,
} from 'src/actions/mooly-chatbot/chatbot-lead-service';

// ----------------------------------------------------------------------

export default function KanbanDisplayConfigDialog({ open, onClose }) {
  const [saving, setSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  const { config, loading, mutate } = useKanbanDisplayConfig();
  const fieldOptions = getKanbanFieldOptions();

  // Default config để fallback
  const defaultConfig = useMemo(() => getDefaultKanbanConfig(), []);

  // Current config (từ server hoặc default)
  const currentConfig = useMemo(() => config || defaultConfig, [config, defaultConfig]);

  // Form với React Hook Form
  const {
    control,
    watch,
    setValue,
    handleSubmit,
    reset,
    formState: { isDirty },
  } = useForm({
    defaultValues: {
      visibleFields: [],
      fieldSettings: {},
    },
  });

  const watchedConfig = watch();

  // Reset form khi config thay đổi hoặc dialog mở
  useEffect(() => {
    if (open && currentConfig) {
      
      reset({
        visibleFields: currentConfig.visibleFields || [],
        fieldSettings: currentConfig.fieldSettings || {},
      });
      setHasChanges(false);
    }
  }, [open, currentConfig, reset]);

  // Reset khi đóng dialog
  useEffect(() => {
    if (!open) {
      reset({
        visibleFields: [],
        fieldSettings: {},
      });
      setHasChanges(false);
    }
  }, [open, reset]);

  // Handle toggle field visibility
  const handleToggleField = useCallback((fieldName, show) => {
    const currentVisibleFields = watchedConfig.visibleFields || [];
    const currentFieldSettings = watchedConfig.fieldSettings || {};

    // Update field settings
    const newFieldSettings = {
      ...currentFieldSettings,
      [fieldName]: {
        ...fieldOptions.find(opt => opt.value === fieldName),
        ...currentFieldSettings[fieldName],
        show,
      },
    };

    // Update visible fields
    const newVisibleFields = show 
      ? [...currentVisibleFields, fieldName].filter((v, i, arr) => arr.indexOf(v) === i)
      : currentVisibleFields.filter(f => f !== fieldName);

    setValue('fieldSettings', newFieldSettings, { shouldDirty: true });
    setValue('visibleFields', newVisibleFields, { shouldDirty: true });
    setHasChanges(true);
  }, [watchedConfig, setValue, fieldOptions]);

  // Handle drag end (reorder fields)
  const handleDragEnd = useCallback((result) => {
    if (!result.destination) return;

    const currentVisibleFields = [...(watchedConfig.visibleFields || [])];
    const [reorderedField] = currentVisibleFields.splice(result.source.index, 1);
    currentVisibleFields.splice(result.destination.index, 0, reorderedField);

    setValue('visibleFields', currentVisibleFields, { shouldDirty: true });
    setHasChanges(true);
  }, [watchedConfig, setValue]);

  // Handle save configuration
  const onSubmit = useCallback(async (formData) => {
    try {
      setSaving(true);

      const result = await updateKanbanDisplayConfig(formData);

      if (result.success) {
        await mutate();
        toast.success('Đã lưu cấu hình hiển thị Kanban');
        setHasChanges(false);
        onClose();
      } else {
        toast.error(result.error?.message || 'Không thể lưu cấu hình');
      }
    } catch (error) {
      toast.error('Có lỗi xảy ra khi lưu cấu hình');
    } finally {
      setSaving(false);
    }
  }, [mutate, onClose]);

  // Get visible and hidden fields
  const visibleFields = useMemo(() => {
    const visibleFieldNames = watchedConfig.visibleFields || [];
    return visibleFieldNames
      .map(fieldName => fieldOptions.find(opt => opt.value === fieldName))
      .filter(Boolean);
  }, [watchedConfig.visibleFields, fieldOptions]);

  const hiddenFields = useMemo(() => {
    const visibleFieldNames = watchedConfig.visibleFields || [];
    return fieldOptions.filter(field => !visibleFieldNames.includes(field.value));
  }, [watchedConfig.visibleFields, fieldOptions]);

  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      maxWidth="md"
      fullWidth
      slotProps={{
        backdrop: {
          sx: {
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            backdropFilter: 'blur(3px)',
          }
        }
      }}
    >
      <DialogTitle>
        <Stack direction="row" alignItems="center" spacing={2}>
          <Iconify icon="solar:widget-5-bold" width={24} />
          <Typography variant="h6">Cấu hình hiển thị Kanban</Typography>
        </Stack>
      </DialogTitle>

      <DialogContent sx={{ flex: 1, p: 0 }}>
        <Scrollbar sx={{ height: 1 }}>
          <Box sx={{ p: 3 }}>
            {loading && (
              <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                <CircularProgress />
              </Box>
            )}

            {!loading && (
            <Stack spacing={3}>

              {/* Hướng dẫn */}
              <Alert severity="info">
                <Typography variant="body2">
                  <strong>Hướng dẫn:</strong> Bật/tắt các trường thông tin hiển thị trên thẻ lead trong view Kanban. 
                  Kéo thả để sắp xếp thứ tự hiển thị.
                </Typography>
              </Alert>

              {/* Thống kê */}
              <Card sx={{ p: 2 }}>
                <Stack direction="row" alignItems="center" justifyContent="space-between">
                  <Typography variant="subtitle2">
                    Trạng thái cấu hình
                  </Typography>
                  <Stack direction="row" spacing={2}>
                    <Chip 
                      label={`${visibleFields.length} trường hiển thị`} 
                      color="success" 
                      size="small" 
                    />
                    <Chip 
                      label={`${hiddenFields.length} trường ẩn`} 
                      color="default" 
                      size="small" 
                    />
                  </Stack>
                </Stack>
              </Card>

              {/* Visible Fields - Có thể kéo thả */}
              <Box>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  Trường hiển thị ({visibleFields.length})
                </Typography>

                <DragDropContext onDragEnd={handleDragEnd}>
                  <Droppable droppableId="visible-fields">
                    {(provided) => (
                      <Stack 
                        spacing={1}
                        ref={provided.innerRef}
                        {...provided.droppableProps}
                      >
                        {visibleFields.map((field, index) => (
                          <Draggable 
                            key={field.value} 
                            draggableId={field.value} 
                            index={index}
                          >
                            {(dragProvided, snapshot) => (
                              <Card
                                ref={dragProvided.innerRef}
                                {...dragProvided.draggableProps}
                                sx={{
                                  p: 2,
                                  cursor: 'grab',
                                  bgcolor: snapshot.isDragging ? 'action.hover' : 'background.paper',
                                  '&:hover': { bgcolor: 'action.hover' },
                                }}
                              >
                                <Stack direction="row" alignItems="center" spacing={2}>
                                  <Box {...dragProvided.dragHandleProps}>
                                    <Iconify icon="solar:hamburger-menu-bold" width={16} />
                                  </Box>

                                  <Iconify icon={field.icon} width={20} />

                                  <Box sx={{ flex: 1 }}>
                                    <Typography variant="body2" fontWeight="medium">
                                      {field.label}
                                    </Typography>
                                    <Typography variant="caption" color="text.secondary">
                                      {field.value} • {field.type}
                                    </Typography>
                                  </Box>

                                  <Chip 
                                    label={`#${index + 1}`} 
                                    size="small" 
                                    color="primary" 
                                    variant="outlined" 
                                  />

                                  <Switch
                                    checked
                                    onChange={(e) => handleToggleField(field.value, e.target.checked)}
                                    size="small"
                                  />
                                </Stack>
                              </Card>
                            )}
                          </Draggable>
                        ))}
                        {provided.placeholder}

                        {visibleFields.length === 0 && (
                          <Box 
                            sx={{ 
                              p: 4, 
                              textAlign: 'center', 
                              color: 'text.secondary',
                              border: '2px dashed',
                              borderColor: 'divider',
                              borderRadius: 1,
                            }}
                          >
                            <Typography variant="body2">
                              Chưa có trường nào được hiển thị
                            </Typography>
                          </Box>
                        )}
                      </Stack>
                    )}
                  </Droppable>
                </DragDropContext>
              </Box>

              <Divider />

              {/* Hidden Fields */}
              <Box>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  Trường ẩn ({hiddenFields.length})
                </Typography>

                <Stack spacing={1}>
                  {hiddenFields.map((field) => (
                    <Card key={field.value} sx={{ p: 2 }}>
                      <Stack direction="row" alignItems="center" spacing={2}>
                        <Iconify icon={field.icon} width={20} sx={{ color: 'text.disabled' }} />

                        <Box sx={{ flex: 1 }}>
                          <Typography variant="body2" color="text.secondary">
                            {field.label}
                          </Typography>
                          <Typography variant="caption" color="text.disabled">
                            {field.value} • {field.type}
                          </Typography>
                        </Box>

                        <Switch
                          checked={false}
                          onChange={(e) => handleToggleField(field.value, e.target.checked)}
                          size="small"
                        />
                      </Stack>
                    </Card>
                  ))}

                  {hiddenFields.length === 0 && (
                    <Box sx={{ p: 2, textAlign: 'center', color: 'text.secondary' }}>
                      <Typography variant="body2">
                        Tất cả trường đều được hiển thị
                      </Typography>
                    </Box>
                  )}
                </Stack>
              </Box>
            </Stack>
            )}
          </Box>
        </Scrollbar>
      </DialogContent>

      <DialogActions sx={{ borderTop: 1, borderColor: 'divider', p: 3 }}>
        <Button variant="outlined" onClick={onClose}>
          Hủy
        </Button>
        <LoadingButton
          variant="contained"
          onClick={handleSubmit(onSubmit)}
          loading={saving}
          disabled={!hasChanges && !isDirty}
        >
          Lưu cấu hình
        </LoadingButton>
      </DialogActions>
    </Dialog>
  );
} 