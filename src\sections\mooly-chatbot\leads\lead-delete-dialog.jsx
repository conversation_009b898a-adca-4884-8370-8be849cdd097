'use client';

import { useState } from 'react';

import {
  <PERSON>,
  <PERSON>ack,
  Dialog,
  Button,
  Typography,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
} from '@mui/material';
import { LoadingButton } from '@mui/lab';

import { Iconify } from 'src/components/iconify';
import { toast } from 'src/components/snackbar';

import { deleteLead, deleteMultipleLeads } from 'src/actions/mooly-chatbot/chatbot-lead-service';

// ----------------------------------------------------------------------

export default function LeadDeleteDialog({ 
  open, 
  onClose, 
  currentLead, 
  selectedLeads, 
  onSuccess 
}) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const isMultiple = selectedLeads && selectedLeads.length > 0;
  const deleteCount = isMultiple ? selectedLeads.length : 1;
  const leadToDelete = isMultiple ? null : currentLead;

  const handleDelete = async () => {
    try {
      setLoading(true);
      setError(null);

      if (isMultiple) {
        // Delete multiple leads
        const result = await deleteMultipleLeads(selectedLeads);
        
        if (result.success) {
          toast.success(`Đã xóa ${selectedLeads.length} leads thành công!`);
          onSuccess(selectedLeads);
        } else {
          setError(result.error);
        }
      } else if (leadToDelete) {
        // Delete single lead
        const result = await deleteLead(leadToDelete.id);
        
        if (result.success) {
          toast.success('Xóa lead thành công!');
          onSuccess(leadToDelete.id);
        } else {
          setError(result.error);
        }
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      setError(null);
      onClose();
    }
  };

  return (
    <Dialog 
      open={open} 
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle>
        <Stack direction="row" alignItems="center" spacing={2}>
          <Box
            sx={{
              p: 1,
              borderRadius: '50%',
              bgcolor: 'error.lighter',
              color: 'error.main',
            }}
          >
            <Iconify icon="solar:trash-bin-trash-bold" width={24} />
          </Box>
          <Typography variant="h6">
            Xóa {isMultiple ? `${deleteCount} leads` : 'lead'}
          </Typography>
        </Stack>
      </DialogTitle>

      <DialogContent>
        <Stack spacing={3}>
          {error && (
            <Alert severity="error" onClose={() => setError(null)}>
              {error}
            </Alert>
          )}

          <Box>
            <Typography variant="body1" gutterBottom>
              {isMultiple ? (
                <>
                  Bạn có chắc chắn muốn xóa <strong>{deleteCount}</strong> leads đã chọn?
                </>
              ) : (
                <>
                  Bạn có chắc chắn muốn xóa lead <strong>{leadToDelete?.fullName || 'này'}</strong>?
                </>
              )}
            </Typography>
            
            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              Hành động này không thể hoàn tác. Tất cả thông tin của lead sẽ bị xóa vĩnh viễn.
            </Typography>
          </Box>

          {!isMultiple && leadToDelete && (
            <Box
              sx={{
                p: 2,
                borderRadius: 1,
                border: '1px solid',
                borderColor: 'divider',
                bgcolor: 'grey.50',
              }}
            >
              <Stack spacing={1}>
                <Typography variant="subtitle2">
                  Thông tin lead sẽ bị xóa:
                </Typography>
                
                <Stack spacing={0.5}>
                  <Typography variant="body2">
                    • <strong>Tên:</strong> {leadToDelete.fullName || 'Chưa có tên'}
                  </Typography>
                  {leadToDelete.email && (
                    <Typography variant="body2">
                      • <strong>Email:</strong> {leadToDelete.email}
                    </Typography>
                  )}
                  {leadToDelete.phone && (
                    <Typography variant="body2">
                      • <strong>Điện thoại:</strong> {leadToDelete.phone}
                    </Typography>
                  )}
                  <Typography variant="body2">
                    • <strong>Trạng thái:</strong> {leadToDelete.status}
                  </Typography>
                </Stack>
              </Stack>
            </Box>
          )}
        </Stack>
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose} disabled={loading}>
          Hủy bỏ
        </Button>
        <LoadingButton
          variant="contained"
          color="error"
          loading={loading}
          startIcon={<Iconify icon="solar:trash-bin-trash-bold" />}
          onClick={handleDelete}
        >
          {isMultiple ? `Xóa ${deleteCount} leads` : 'Xóa lead'}
        </LoadingButton>
      </DialogActions>
    </Dialog>
  );
} 