'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typo<PERSON>,
  But<PERSON>,
  Al<PERSON>,
  Step<PERSON>,
  <PERSON>,
  StepL<PERSON>l,
  StepContent,
  CircularProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  TextField,
  Link,
  Divider,
} from '@mui/material';
import { LoadingButton } from '@mui/lab';
import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';
import { DashboardContent } from 'src/layouts/dashboard';
import {
  useFacebookIntegrationSetup,
  useFacebookMutations,
  useFacebookPopupAuth,
  DEFAULT_CONFIG,
  SUCCESS_MESSAGES,
  ERROR_MESSAGES
} from 'src/actions/mooly-chatbot/facebook-integration';

const steps = [
  {
    label: 'Kết nối Facebook Page',
    description: 'Đăng nhập Facebook và chọn Page muốn kết nối'
  },
  {
    label: '<PERSON><PERSON><PERSON> hình AI Reply',
    description: '<PERSON>hi<PERSON><PERSON> lập auto reply và cấu hình chatbot AI'
  },
  {
    label: '<PERSON>hiết lập hoạt động',
    description: '<PERSON><PERSON><PERSON> hình tính năng comment và tin nhắn riêng'
  },
  {
    label: 'Hoàn tất',
    description: 'Kiểm tra và bắt đầu sử dụng'
  }
];

export default function FacebookSetupWizard({ onComplete, chatbotId }) {
  const [activeStep, setActiveStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [setupData, setSetupData] = useState({
    connectedPages: [],
    selectedPage: null,
    aiConfig: DEFAULT_CONFIG,
    featureSettings: null
  });

  // Use Facebook integration hooks
  const {
    accounts: connectedPages,
    selectedPage,
    config,
    loading: integrationLoading,
    refetchAll
  } = useFacebookIntegrationSetup(chatbotId);

  const {
    setupFacebookIntegration,
    selectPageForChatbot,
    saveConfig,
    loading: mutationLoading
  } = useFacebookMutations();

  // Use popup auth instead of SDK
  const { loading: popupLoading, openFacebookPopup, cleanup } = useFacebookPopupAuth();

  // Combined loading state
  const isLoading = loading || integrationLoading || mutationLoading || popupLoading;

  // Load setup status on mount
  useEffect(() => {
    checkSetupStatus();
  }, []);

  // Cleanup on unmount
  useEffect(() => cleanup, [cleanup]);

  // Update setup data when hooks data changes
  useEffect(() => {
    setSetupData(prev => ({
      ...prev,
      connectedPages,
      selectedPage,
      aiConfig: config || DEFAULT_CONFIG
    }));

    // Determine current step based on setup status
    if (!selectedPage) {
      setActiveStep(0);
    } else if (!config) {
      setActiveStep(1);
    } else {
      setActiveStep(3); // Skip to completion if everything is set up
    }
  }, [connectedPages, selectedPage, config]);

  const checkSetupStatus = async () => {
    try {
      setLoading(true);
      console.log('🔍 Refreshing Facebook setup status for chatbot:', chatbotId);

      // Refetch all data using hooks
      await refetchAll();

      console.log('📊 Setup data refreshed:', {
        pagesCount: connectedPages.length,
        hasSelectedPage: !!selectedPage,
        hasConfig: !!config
      });

    } catch (error) {
      console.error('Setup check error:', error);
      toast.error('Lỗi khi kiểm tra trạng thái kết nối');
    } finally {
      setLoading(false);
    }
  };

  const handleConnectFacebook = async () => {
    try {
      setLoading(true);

      console.log('🔗 Bắt đầu Facebook Login với popup...');

      // Use popup auth
      const result = await openFacebookPopup();
      
      if (result.success) {
        toast.success(result.message);
        
        // Reload connected accounts
        await checkSetupStatus();
      }

    } catch (error) {
      console.error('Facebook popup connect error:', error);
      toast.error(error.message || 'Lỗi khi kết nối Facebook');
    } finally {
      setLoading(false);
    }
  };



  const handleSelectPage = async (pageId) => {
    try {
      setLoading(true);

      console.log('📋 Selecting Facebook Page:', pageId);

      const response = await fetch('/api/facebook-integration/select-page', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          chatbotId,
          pageId
        })
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Đã chọn Facebook Page thành công!');

        // Update selected page - handle both page_id and id fields
        const foundPage = setupData.connectedPages.find(page =>
          (page.page_id === pageId) || (page.id === pageId)
        );

        console.log('✅ Selected page found:', foundPage);

        setSetupData(prev => ({
          ...prev,
          selectedPage: foundPage
        }));

        // Move to AI config step
        setActiveStep(1);

      } else {
        throw new Error(data.error || 'Không thể chọn Facebook Page');
      }

    } catch (error) {
      console.error('Select page error:', error);
      toast.error(error.message || 'Lỗi khi chọn Facebook Page');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveAIConfig = async () => {
    try {
      setLoading(true);

      console.log('💾 Saving AI config for page:', setupData.selectedPage);

      const response = await fetch('/api/facebook-integration/auto-reply-config', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          pageId: setupData.selectedPage?.pageId || setupData.selectedPage?.page_id,
          enableCommentReply: setupData.aiConfig?.autoReplyComments || false,
          enableMessageReply: setupData.aiConfig?.autoReplyMessages || false,
          enableInstagramComments: setupData.aiConfig?.instagramComments || false,
          enableInstagramMessages: setupData.aiConfig?.instagramMessages || false,
          autoPrivateReply: setupData.aiConfig?.autoPrivateReply || false,
          replyPrompt: setupData.aiConfig?.prompt || '',
          replyTone: setupData.aiConfig?.tone || 'friendly',
          replyLanguage: setupData.aiConfig?.language || 'vi',
          maxReplyLength: setupData.aiConfig?.maxLength || 500,
          businessInfo: setupData.aiConfig?.businessInfo || '',
          products: setupData.aiConfig?.products || '',
          policies: setupData.aiConfig?.policies || '',
          excludeKeywords: setupData.aiConfig?.excludeKeywords || []
        })
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Đã lưu cấu hình AI thành công!');
        await checkSetupStatus();
        setActiveStep(2);
      } else {
        throw new Error(data.error || 'Không thể lưu cấu hình AI');
      }

    } catch (error) {
      console.error('AI config save error:', error);
      toast.error('Lỗi khi lưu cấu hình AI');
    } finally {
      setLoading(false);
    }
  };

  const handleToggleFeature = (featureName) => {
    setSetupData(prev => ({
      ...prev,
      featureSettings: {
        ...prev.featureSettings,
        [featureName]: !prev.featureSettings?.[featureName]
      }
    }));
  };

  const handleSaveFeatureSettings = async () => {
    try {
      setLoading(true);

      const response = await fetch('/api/facebook-integration/features', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          chatbotId,
          featureSettings: {
            ...setupData.featureSettings,
            isComplete: true
          }
        })
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Đã lưu cấu hình tính năng thành công!');
        await checkSetupStatus();
        setActiveStep(3);
      } else {
        throw new Error(data.error || 'Không thể lưu cấu hình tính năng');
      }

    } catch (error) {
      console.error('Feature settings save error:', error);
      toast.error('Lỗi khi lưu cấu hình tính năng');
    } finally {
      setLoading(false);
    }
  };

  const handleNext = () => {
    setActiveStep((prevStep) => prevStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  const renderFacebookConnectionStep = () => {
    const { connectedPages: setupConnectedPages, selectedPage: setupSelectedPage } = setupData;

    return (
      <Box>
        <Alert severity="info" sx={{ mb: 3 }}>
          <Typography variant="subtitle2" gutterBottom>
            🔗 Quyền truy cập cần thiết
          </Typography>
          <Typography variant="body2">
            Để tính năng Auto Reply hoạt động, chúng tôi cần các quyền sau từ Facebook:
          </Typography>
        </Alert>

        {/* Permissions List */}
        <Box sx={{ mb: 3 }}>
          <List dense>
            <ListItem>
              <ListItemIcon>
                <Iconify icon="mdi:account" sx={{ color: 'primary.main' }} />
              </ListItemIcon>
              <ListItemText
                primary="Thông tin cơ bản"
                secondary="Truy cập thông tin profile và email"
              />
            </ListItem>
            <ListItem>
              <ListItemIcon>
                <Iconify icon="mdi:facebook" sx={{ color: 'primary.main' }} />
              </ListItemIcon>
              <ListItemText
                primary="Quản lý Facebook Pages"
                secondary="Đọc, đăng bài và trả lời comment trên Pages"
              />
            </ListItem>
            <ListItem>
              <ListItemIcon>
                <Iconify icon="mdi:message" sx={{ color: 'primary.main' }} />
              </ListItemIcon>
              <ListItemText
                primary="Messenger"
                secondary="Gửi và nhận tin nhắn qua Messenger"
              />
            </ListItem>
            <ListItem>
              <ListItemIcon>
                <Iconify icon="mdi:instagram" sx={{ color: 'primary.main' }} />
              </ListItemIcon>
              <ListItemText
                primary="Instagram Business"
                secondary="Trả lời comment và Direct Message trên Instagram"
              />
            </ListItem>
          </List>
        </Box>

        {popupLoading && (
          <Box textAlign="center" py={2}>
            <CircularProgress size={24} sx={{ mr: 1 }} />
            <Typography variant="body2" color="text.secondary">
              Đang kết nối Facebook...
            </Typography>
          </Box>
        )}

        {setupConnectedPages.length === 0 ? (
          <Box textAlign="center" py={4}>
            <Iconify icon="mdi:facebook" sx={{ fontSize: 64, color: '#1877F2', mb: 2 }} />
            <Typography variant="h6" gutterBottom>
              Chưa kết nối Facebook Page
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              Để bắt đầu sử dụng tính năng auto reply, bạn cần kết nối với Facebook Page của mình
            </Typography>
            <LoadingButton
              loading={loading || popupLoading}
              variant="contained"
              size="large"
              onClick={handleConnectFacebook}
              startIcon={<Iconify icon="mdi:facebook" />}
              sx={{
                backgroundColor: '#1877F2',
                '&:hover': { backgroundColor: '#166FE5' }
              }}
            >
              Kết nối Facebook
            </LoadingButton>
          </Box>
        ) : (
          <Box>
            <Typography variant="h6" gutterBottom>
              Chọn Facebook Page:
            </Typography>
            <List>
              {setupConnectedPages.map((page) => (
                <ListItem
                  key={page.page_id || page.id}
                  sx={{
                    border: 1,
                    borderColor: setupSelectedPage?.pageId === page.page_id ? 'primary.main' : 'divider',
                    borderRadius: 1,
                    mb: 1,
                    cursor: 'pointer',
                    backgroundColor: setupSelectedPage?.pageId === page.page_id ? 'primary.lighter' : 'transparent'
                  }}
                  onClick={() => !loading && handleSelectPage(page.page_id)}
                >
                  <ListItemIcon>
                    <Iconify
                      icon={setupSelectedPage?.pageId === page.page_id ? 'mdi:check-circle' : 'mdi:facebook'}
                      color={setupSelectedPage?.pageId === page.page_id ? 'primary' : '#1877F2'}
                    />
                  </ListItemIcon>
                  <ListItemText
                    primary={page.page_name}
                    secondary={
                      <Box>
                        <Typography variant="body2" color="text.secondary">
                          Page ID: {page.page_id}
                        </Typography>
                        {page.instagram_account_id && (
                          <Chip
                            icon={<Iconify icon="mdi:instagram" />}
                            label={`Instagram: ${page.instagram_username || 'Kết nối'}`}
                            size="small"
                            color="secondary"
                            sx={{ mt: 0.5 }}
                          />
                        )}
                        <Chip
                          label={page.is_active ? 'Hoạt động' : 'Không hoạt động'}
                          size="small"
                          color={page.is_active ? 'success' : 'default'}
                          sx={{ mt: 0.5, ml: page.instagram_account_id ? 1 : 0 }}
                        />
                        {page.auto_reply_config && (
                          <Chip
                            label="Auto Reply đã cấu hình"
                            size="small"
                            color="info"
                            sx={{ mt: 0.5, ml: 1 }}
                          />
                        )}
                      </Box>
                    }
                  />
                  {setupSelectedPage?.pageId === page.page_id && (
                    <Chip label="Đã chọn" color="primary" size="small" />
                  )}
                </ListItem>
              ))}
            </List>

            <Divider sx={{ my: 2 }} />

            <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
              <Button
                variant="outlined"
                onClick={handleConnectFacebook}
                startIcon={<Iconify icon="mdi:plus" />}
                disabled={loading || popupLoading}
              >
                Kết nối thêm Page
              </Button>

              {setupSelectedPage && (
                <Button variant="contained" onClick={handleNext}>
                  Tiếp tục cấu hình AI
                </Button>
              )}
            </Box>
          </Box>
        )}

        {/* Security Notice */}
        <Alert severity="success" sx={{ mt: 3 }}>
          <Typography variant="body2">
            <strong>🔒 Bảo mật:</strong> Chúng tôi chỉ sử dụng token để tự động trả lời tin nhắn. Không lưu trữ mật khẩu và có thể hủy kết nối bất cứ lúc nào.
          </Typography>
        </Alert>

        {/* Popup OAuth Notice */}
        <Alert severity="info" sx={{ mt: 2 }}>
          <Typography variant="body2">
            <strong>🔗 Popup OAuth:</strong> Sẽ mở cửa sổ popup để đăng nhập Facebook. Vui lòng cho phép popup từ trang này để tiếp tục.
          </Typography>
        </Alert>
      </Box>
    );
  };

  const renderAIConfigStep = () => {
    const { aiConfig = {} } = setupData;

    return (
      <Box>
        <Alert severity="info" sx={{ mb: 3 }}>
          <Typography variant="subtitle2" gutterBottom>
            Cấu hình AI Auto Reply
          </Typography>
          <Typography variant="body2">
            Thiết lập AI để tự động trả lời comment và tin nhắn từ khách hàng
          </Typography>
        </Alert>

        {/* Basic AI Settings */}
        <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
          Cài đặt cơ bản:
        </Typography>

        <Box sx={{ display: 'grid', gap: 2, mb: 3 }}>
          <TextField
            select
            fullWidth
            label="Giọng điệu trả lời"
            value={aiConfig.tone || 'friendly'}
            onChange={(e) => setSetupData(prev => ({
              ...prev,
              aiConfig: { ...prev.aiConfig, tone: e.target.value }
            }))}
          >
            <option value="friendly">Thân thiện</option>
            <option value="professional">Chuyên nghiệp</option>
            <option value="casual">Thoải mái</option>
            <option value="enthusiastic">Nhiệt tình</option>
          </TextField>

          <TextField
            select
            fullWidth
            label="Ngôn ngữ"
            value={aiConfig.language || 'vi'}
            onChange={(e) => setSetupData(prev => ({
              ...prev,
              aiConfig: { ...prev.aiConfig, language: e.target.value }
            }))}
          >
            <option value="vi">Tiếng Việt</option>
            <option value="en">English</option>
          </TextField>

          <TextField
            type="number"
            fullWidth
            label="Độ dài tối đa (ký tự)"
            value={aiConfig.maxLength || 500}
            onChange={(e) => setSetupData(prev => ({
              ...prev,
              aiConfig: { ...prev.aiConfig, maxLength: parseInt(e.target.value) }
            }))}
            inputProps={{ min: 50, max: 1000 }}
          />
        </Box>

        {/* Business Information */}
        <Typography variant="h6" gutterBottom>
          Thông tin doanh nghiệp:
        </Typography>

        <TextField
          fullWidth
          multiline
          rows={3}
          label="Thông tin doanh nghiệp"
          placeholder="VD: Chúng tôi là công ty chuyên bán quần áo thời trang..."
          value={aiConfig.businessInfo || ''}
          onChange={(e) => setSetupData(prev => ({
            ...prev,
            aiConfig: { ...prev.aiConfig, businessInfo: e.target.value }
          }))}
          sx={{ mb: 2 }}
        />

        <TextField
          fullWidth
          multiline
          rows={3}
          label="Sản phẩm/Dịch vụ"
          placeholder="VD: Quần áo nam nữ, phụ kiện thời trang, giày dép..."
          value={aiConfig.products || ''}
          onChange={(e) => setSetupData(prev => ({
            ...prev,
            aiConfig: { ...prev.aiConfig, products: e.target.value }
          }))}
          sx={{ mb: 2 }}
        />

        <TextField
          fullWidth
          multiline
          rows={2}
          label="Chính sách"
          placeholder="VD: Đổi trả trong 7 ngày, miễn phí vận chuyển từ 500k..."
          value={aiConfig.policies || ''}
          onChange={(e) => setSetupData(prev => ({
            ...prev,
            aiConfig: { ...prev.aiConfig, policies: e.target.value }
          }))}
          sx={{ mb: 3 }}
        />

        {/* AI Prompt */}
        <Typography variant="h6" gutterBottom>
          Hướng dẫn AI:
        </Typography>

        <TextField
          fullWidth
          multiline
          rows={4}
          label="Prompt hướng dẫn AI"
          placeholder="VD: Bạn là trợ lý bán hàng chuyên nghiệp. Hãy trả lời khách hàng một cách thân thiện và hữu ích..."
          value={aiConfig.prompt || ''}
          onChange={(e) => setSetupData(prev => ({
            ...prev,
            aiConfig: { ...prev.aiConfig, prompt: e.target.value }
          }))}
          sx={{ mb: 3 }}
        />

        <Box sx={{ mt: 3 }}>
          <LoadingButton
            loading={loading}
            variant="contained"
            onClick={handleSaveAIConfig}
            startIcon={<Iconify icon="mdi:content-save" />}
          >
            Lưu cấu hình AI
          </LoadingButton>
        </Box>
      </Box>
    );
  };

  const renderFeatureSettingsStep = () => {
    const { featureSettings } = setupData;

    return (
      <Box>
        <Alert severity="info" sx={{ mb: 3 }}>
          <Typography variant="subtitle2" gutterBottom>
            Thiết lập tính năng hoạt động
          </Typography>
          <Typography variant="body2">
            Cấu hình chi tiết các tính năng auto reply và tin nhắn riêng
          </Typography>
        </Alert>

        <List>
          <ListItem
            sx={{
              border: 1,
              borderColor: 'divider',
              borderRadius: 1,
              mb: 2,
              backgroundColor: 'background.paper'
            }}
          >
            <ListItemIcon>
              <Iconify icon="mdi:comment-outline" color="primary" />
            </ListItemIcon>
            <ListItemText
              primary="Auto Reply Facebook Comments"
              secondary="Tự động trả lời comment công khai trên Facebook posts"
            />
            <Button
              variant={featureSettings?.facebookComments ? 'contained' : 'outlined'}
              size="small"
              onClick={() => handleToggleFeature('facebookComments')}
            >
              {featureSettings?.facebookComments ? 'Đã bật' : 'Bật'}
            </Button>
          </ListItem>

          <ListItem
            sx={{
              border: 1,
              borderColor: 'divider',
              borderRadius: 1,
              mb: 2,
              backgroundColor: 'background.paper'
            }}
          >
            <ListItemIcon>
              <Iconify icon="mdi:instagram" color="primary" />
            </ListItemIcon>
            <ListItemText
              primary="Auto Reply Instagram Comments"
              secondary="Tự động trả lời comment công khai trên Instagram posts"
            />
            <Button
              variant={featureSettings?.instagramComments ? 'contained' : 'outlined'}
              size="small"
              onClick={() => handleToggleFeature('instagramComments')}
            >
              {featureSettings?.instagramComments ? 'Đã bật' : 'Bật'}
            </Button>
          </ListItem>

          <ListItem
            sx={{
              border: 1,
              borderColor: 'divider',
              borderRadius: 1,
              mb: 2,
              backgroundColor: 'background.paper'
            }}
          >
            <ListItemIcon>
              <Iconify icon="mdi:message" color="primary" />
            </ListItemIcon>
            <ListItemText
              primary="Auto Message Facebook Users"
              secondary="Tự động gửi tin nhắn riêng cho user khi họ comment (cần permission)"
            />
            <Button
              variant={featureSettings?.facebookMessages ? 'contained' : 'outlined'}
              size="small"
              onClick={() => handleToggleFeature('facebookMessages')}
            >
              {featureSettings?.facebookMessages ? 'Đã bật' : 'Bật'}
            </Button>
          </ListItem>

          <ListItem
            sx={{
              border: 1,
              borderColor: 'divider',
              borderRadius: 1,
              mb: 2,
              backgroundColor: 'background.paper'
            }}
          >
            <ListItemIcon>
              <Iconify icon="mdi:instagram" color="primary" />
            </ListItemIcon>
            <ListItemText
              primary="Auto Message Instagram Users"
              secondary="Tự động gửi tin nhắn riêng Instagram Direct cho user khi họ comment"
            />
            <Button
              variant={featureSettings?.instagramMessages ? 'contained' : 'outlined'}
              size="small"
              onClick={() => handleToggleFeature('instagramMessages')}
            >
              {featureSettings?.instagramMessages ? 'Đã bật' : 'Bật'}
            </Button>
          </ListItem>
        </List>

        <Alert severity="success" sx={{ mt: 2, mb: 3 }}>
          <Typography variant="body2">
            <strong>Gợi ý:</strong> Khuyến nghị bật tất cả tính năng để tối ưu trải nghiệm khách hàng. 
            Bạn có thể tắt/bật từng tính năng sau trong phần cấu hình.
          </Typography>
        </Alert>

        <Box sx={{ mt: 3 }}>
          <LoadingButton
            loading={loading}
            variant="contained"
            onClick={handleSaveFeatureSettings}
            startIcon={<Iconify icon="mdi:check" />}
          >
            Hoàn tất thiết lập
          </LoadingButton>
        </Box>
      </Box>
    );
  };

  const renderCompletionStep = () => {
    const { selectedPage: setupSelectedPage, featureSettings } = setupData;

    return (
      <Box textAlign="center">
        <Iconify icon="mdi:check-circle" sx={{ fontSize: 64, color: 'success.main', mb: 2 }} />

        <Typography variant="h5" gutterBottom>
          Kết nối thành công!
        </Typography>

        <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
          Facebook Page &quot;{setupSelectedPage?.pageName || setupSelectedPage?.page_name}&quot; đã được kết nối và AI auto reply đã sẵn sàng hoạt động.
        </Typography>

        <Alert severity="success" sx={{ mb: 3, textAlign: 'left' }}>
          <Typography variant="subtitle2" gutterBottom>
            Tính năng đã kích hoạt:
          </Typography>
          <Typography variant="body2" component="div">
            {featureSettings?.facebookComments && '✅ Auto reply Facebook Comments'}<br/>
            {featureSettings?.instagramComments && '✅ Auto reply Instagram Comments'}<br/>
            {featureSettings?.facebookMessages && '✅ Auto Message Facebook Users'}<br/>
            {featureSettings?.instagramMessages && '✅ Auto Message Instagram Users'}<br/>
            ✅ AI-powered responses<br/>
            ✅ Real-time monitoring<br/>
            ✅ Analytics tracking
          </Typography>
        </Alert>

        {setupSelectedPage?.instagramAccountId && (
          <Alert severity="info" sx={{ mb: 3, textAlign: 'left' }}>
            <Typography variant="subtitle2" gutterBottom>
              Instagram Business Account:
            </Typography>
            <Typography variant="body2">
              ✅ Instagram Account ID: {setupSelectedPage.instagramAccountId}<br/>
              ✅ Tự động trả lời comment và Direct Message Instagram đã được kích hoạt
            </Typography>
          </Alert>
        )}

        <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', mt: 3 }}>
          <Button
            variant="outlined"
            onClick={() => setActiveStep(2)}
            startIcon={<Iconify icon="mdi:settings" />}
          >
            Điều chỉnh cấu hình
          </Button>

          <Button
            variant="contained"
            size="large"
            onClick={() => onComplete?.()}
            startIcon={<Iconify icon="mdi:play" />}
          >
            Bắt đầu sử dụng
          </Button>
        </Box>
      </Box>
    );
  };

  if (loading && setupData.connectedPages.length === 0 && !setupData.selectedPage) {
    return (
      <Card>
        <CardContent sx={{ textAlign: 'center', py: 6 }}>
          <CircularProgress sx={{ mb: 2 }} />
          <Typography>Đang kiểm tra trạng thái kết nối...</Typography>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent sx={{ p: 4 }}>
        <Typography variant="h5" gutterBottom>
          Kết nối Facebook Integration
        </Typography>

        <Typography variant="body2" color="text.secondary" paragraph>
          Kết nối Facebook Page và cấu hình AI để tự động trả lời khách hàng
        </Typography>

        <Stepper activeStep={activeStep} orientation="vertical">
          {steps.map((step, index) => (
            <Step key={step.label}>
              <StepLabel>
                <Typography variant="h6">{step.label}</Typography>
                <Typography variant="body2" color="text.secondary">
                  {step.description}
                </Typography>
              </StepLabel>
              <StepContent>
                {index === 0 && renderFacebookConnectionStep()}
                {index === 1 && renderAIConfigStep()}
                {index === 2 && renderFeatureSettingsStep()}
                {index === 3 && renderCompletionStep()}
              </StepContent>
            </Step>
          ))}
        </Stepper>
      </CardContent>
    </Card>
  );
}