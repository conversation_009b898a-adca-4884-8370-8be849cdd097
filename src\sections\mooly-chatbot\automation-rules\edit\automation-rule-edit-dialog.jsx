'use client';

import { useState, useEffect } from 'react';
import { useForm, Controller } from 'react-hook-form';

import {
  <PERSON>,
  Tab,
  Ta<PERSON>,
  <PERSON>,
  <PERSON>ack,
  Alert,
  <PERSON>ton,
  Dialog,
  TextField,
  Typography,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress
} from '@mui/material';

import {
  getAvailableOffers,
  updateAutomationRule
} from 'src/actions/mooly-chatbot/automation-rules-service';

import { ScheduledMessageForm } from '../create/scheduled-message-form';
import { AIOfferSelectionForm } from '../create/ai-offer-selection-form';

// ----------------------------------------------------------------------

export function AutomationRuleEditDialog({ open, onClose, onSuccess, rule }) {
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [offers, setOffers] = useState([]);

  const { control, handleSubmit, reset, setValue, formState: { errors } } = useForm({
    defaultValues: {
      name: '',
      description: ''
    }
  });

  // Load offers when dialog opens
  useEffect(() => {
    if (open) {
      loadOffers();
    }
  }, [open]);

  // Set form values when rule changes
  useEffect(() => {
    if (rule && open) {
      // Set basic info
      setValue('name', rule.name || '');
      setValue('description', rule.description || '');

      // Set tab based on follow_up_type
      if (rule.follow_up_type === 'ai_offer_selection') {
        setActiveTab(0);
        setValue('customerIntent', rule.customer_intent || '');
        setValue('targetOffers', rule.target_offers || []);
      } else if (rule.follow_up_type === 'scheduled_message') {
        setActiveTab(1);
        setValue('followUpMessage', rule.follow_up_message || '');
        setValue('scheduleConfig', rule.schedule_config || {});
      }

      // Set customer filters
      setValue('customerFilters', rule.customer_filters || {});
    }
  }, [rule, open, setValue]);

  const loadOffers = async () => {
    try {
      const result = await getAvailableOffers();
      if (result.success) {
        setOffers(result.data || []);
      }
    } catch (err) {
      console.error('Error loading offers:', err);
    }
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
    setError(null);
  };

  const handleClose = () => {
    reset();
    setActiveTab(0);
    setError(null);
    onClose();
  };

  const onSubmit = async (data) => {
    try {
      setLoading(true);
      setError(null);

      // Prepare update data
      const updateData = {
        name: data.name,
        description: data.description,
        customerFilters: data.customerFilters || {}
      };

      // Add specific fields based on follow_up_type
      if (rule.follow_up_type === 'ai_offer_selection') {
        updateData.customerIntent = data.customerIntent;
        updateData.targetOffers = data.targetOffers || [];
      } else if (rule.follow_up_type === 'scheduled_message') {
        updateData.followUpMessage = data.followUpMessage;
        updateData.scheduleConfig = data.scheduleConfig || {};
      }

      const result = await updateAutomationRule(rule.id, updateData);

      if (result.success) {
        onSuccess();
        handleClose();
      } else {
        setError(result.error);
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  if (!rule) return null;

  const getTabLabel = () => {
    if (rule.follow_up_type === 'ai_offer_selection') {
      return 'AI Chọn Offer';
    }
    if (rule.follow_up_type === 'scheduled_message') {
      return 'Tin Nhắn Theo Lịch';
    }
    return 'Cấu hình';
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { height: '80vh' }
      }}
    >
      <DialogTitle>
        <Typography variant="h5">
          Chỉnh Sửa Automation Rule
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          {rule.name}
        </Typography>
      </DialogTitle>

      <DialogContent sx={{ p: 0 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={0} sx={{ px: 3 }}>
            <Tab label={getTabLabel()} />
          </Tabs>
        </Box>

        <Box sx={{ p: 3 }}>
          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {typeof error === 'string' ? error : JSON.stringify(error)}
            </Alert>
          )}

          <form onSubmit={handleSubmit(onSubmit)}>
            {/* Basic Information */}
            <Card sx={{ p: 3, mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                Thông Tin Cơ Bản
              </Typography>

              <Stack spacing={3}>
                <Controller
                  name="name"
                  control={control}
                  rules={{ required: 'Tên rule là bắt buộc' }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Tên Rule"
                      error={!!errors.name}
                      helperText={errors.name?.message}
                      fullWidth
                    />
                  )}
                />

                <Controller
                  name="description"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Mô Tả"
                      multiline
                      rows={2}
                      fullWidth
                    />
                  )}
                />
              </Stack>
            </Card>

            {/* Tab Content */}
            {rule.follow_up_type === 'ai_offer_selection' && (
              <AIOfferSelectionForm
                control={control}
                errors={errors}
                offers={offers}
                onOffersUpdate={loadOffers}
              />
            )}

            {rule.follow_up_type === 'scheduled_message' && (
              <ScheduledMessageForm
                control={control}
                errors={errors}
              />
            )}
          </form>
        </Box>
      </DialogContent>

      <DialogActions sx={{ p: 3, pt: 0 }}>
        <Button onClick={handleClose} disabled={loading}>
          Hủy
        </Button>
        <Button
          variant="contained"
          onClick={handleSubmit(onSubmit)}
          disabled={loading}
          startIcon={loading && <CircularProgress size={20} />}
        >
          {loading ? 'Đang cập nhật...' : 'Cập nhật'}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
