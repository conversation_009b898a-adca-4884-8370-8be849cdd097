/**
 * Instagram Business API OAuth Callback
 * Handles the OAuth callback and completes Instagram Business account connection
 * POPUP WINDOW COMPATIBLE - works with popup OAuth flow
 */

import { NextResponse } from 'next/server';
import { createClient } from 'src/utils/supabase/server';

// Instagram configuration - use direct env vars to avoid circular dependency
const INSTAGRAM_APP_ID = process.env.INSTAGRAM_APP_ID;
const INSTAGRAM_APP_SECRET = process.env.INSTAGRAM_APP_SECRET;
const INSTAGRAM_REDIRECT_URI = process.env.INSTAGRAM_REDIRECT_URI;

// In-memory cache to prevent code reuse (simple deduplication)
const usedCodes = new Set();
const CODE_EXPIRY_MS = 60 * 60 * 1000; // 1 hour - Instagram codes expire in 1 hour

// Clean expired codes periodically
setInterval(() => {
    usedCodes.clear();
}, CODE_EXPIRY_MS);

/**
 * GET /api/instagram-integration/callback
 * Handle Instagram OAuth callback and exchange code for access token
 * SUPPORTS POPUP WINDOW: Returns HTML page that communicates with parent window
 */
export async function GET(request) {
    console.log('🔄 Instagram OAuth Callback Hit');

    try {
        const { searchParams } = new URL(request.url);
        const code = searchParams.get('code');
        const state = searchParams.get('state');
        const error = searchParams.get('error');
        const errorDescription = searchParams.get('error_description');

        // Handle OAuth errors
        if (error) {
            console.error('❌ Instagram OAuth error:', { error, errorDescription });
            
            const errorMessage = errorDescription || 'Lỗi khi kết nối Instagram';
            
            // Return HTML page for popup communication
            return new NextResponse(
                generatePopupCallbackHtml({
                    success: false,
                    message: errorMessage
                }),
                { 
                    status: 200,
                    headers: { 'Content-Type': 'text/html' }
                }
            );
        }

        // Validate required parameters
        if (!code) {
            console.error('❌ Missing authorization code');
            
            return new NextResponse(
                generatePopupCallbackHtml({
                    success: false,
                    message: 'Thiếu mã xác thực từ Instagram'
                }),
                { 
                    status: 200,
                    headers: { 'Content-Type': 'text/html' }
                }
            );
        }

        // Prevent code reuse (deduplication)
        if (usedCodes.has(code)) {
            console.error('❌ Authorization code already used:', code.substring(0, 20) + '...');
            
            return new NextResponse(
                generatePopupCallbackHtml({
                    success: false,
                    message: 'Mã xác thực đã được sử dụng. Vui lòng thử kết nối lại.'
                }),
                { 
                    status: 200,
                    headers: { 'Content-Type': 'text/html' }
                }
            );
        }

        // Mark code as used immediately
        usedCodes.add(code);

        console.log('📋 Instagram OAuth Callback Parameters:', {
            hasCode: !!code,
            hasState: !!state,
            stateLength: state?.length
        });

        // Get authenticated user (SECURITY: User must be authenticated)
        const supabase = await createClient();
        const { data: { user }, error: userError } = await supabase.auth.getUser();

        if (userError || !user) {
            console.error('❌ User not authenticated:', userError);
            
            return new NextResponse(
                generatePopupCallbackHtml({
                    success: false,
                    message: 'Bạn cần đăng nhập để kết nối Instagram'
                }),
                { 
                    status: 200,
                    headers: { 'Content-Type': 'text/html' }
                }
            );
        }

        // Exchange authorization code for access token directly (avoiding SSL issues with internal fetch)
        const redirectUri = INSTAGRAM_REDIRECT_URI;
        
        if (!redirectUri) {
            console.error('❌ Instagram Redirect URI not configured');
            return new NextResponse(
                generatePopupCallbackHtml({
                    success: false,
                    message: 'Instagram Redirect URI not configured'
                }),
                { 
                    status: 200,
                    headers: { 'Content-Type': 'text/html' }
                }
            );
        }
        
        const exchangeResult = await exchangeInstagramToken(code, redirectUri, user.id, supabase);

        if (!exchangeResult.success) {
            console.error('❌ Instagram token exchange failed:', exchangeResult.error);
            const errorMessage = exchangeResult.error?.message || 'Lỗi khi kết nối Instagram';
            
            return new NextResponse(
                generatePopupCallbackHtml({
                    success: false,
                    message: errorMessage
                }),
                { 
                    status: 200,
                    headers: { 'Content-Type': 'text/html' }
                }
            );
        }

        console.log('✅ Instagram account connected successfully:', {
            username: exchangeResult.data.username,
            accountType: exchangeResult.data.accountType
        });

        // Return success HTML page for popup communication
        return new NextResponse(
            generatePopupCallbackHtml({
                success: true,
                message: `Kết nối Instagram thành công! (@${exchangeResult.data.username})`,
                data: {
                    platform: 'instagram',
                    username: exchangeResult.data.username,
                    accountType: exchangeResult.data.accountType || 'BUSINESS',
                    accountId: exchangeResult.data.accountId
                }
            }),
            { 
                status: 200,
                headers: { 'Content-Type': 'text/html' }
            }
        );

    } catch (error) {
        console.error('💥 Instagram OAuth callback error:', error);
        
        return new NextResponse(
            generatePopupCallbackHtml({
                success: false,
                message: 'Lỗi hệ thống khi kết nối Instagram'
            }),
            { 
                status: 200,
                headers: { 'Content-Type': 'text/html' }
            }
        );
    }
}

/**
 * Generate HTML page for popup window communication
 * This page sends the result back to the parent window and closes the popup
 */
function generatePopupCallbackHtml(result) {
    return `
<!DOCTYPE html>
<html>
<head>
    <title>Instagram Connection</title>
    <meta charset="utf-8">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background: linear-gradient(45deg, #E4405F 30%, #FCCC63 90%);
            color: white;
        }
        .container {
            text-align: center;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        .icon {
            font-size: 48px;
            margin-bottom: 1rem;
        }
        .message {
            font-size: 18px;
            margin-bottom: 1rem;
        }
        .loading {
            font-size: 14px;
            opacity: 0.8;
        }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">${result.success ? '✅' : '❌'}</div>
        <div class="message ${result.success ? 'success' : 'error'}">
            ${result.message}
        </div>
        <div class="loading">
            Đang đóng cửa sổ...
        </div>
    </div>

    <script>
        console.log('🔄 Instagram OAuth callback result:', ${JSON.stringify(result)});
        
        // Send result to parent window
        if (window.opener && window.opener.facebookAuthCallback) {
            try {
                window.opener.facebookAuthCallback(${JSON.stringify(result)});
                console.log('✅ Result sent to parent window');
            } catch (error) {
                console.error('❌ Error sending result to parent:', error);
            }
        } else {
            console.warn('⚠️ No parent window callback found');
        }
        
        // Close popup after short delay
        setTimeout(() => {
            try {
                window.close();
            } catch (error) {
                console.log('ℹ️ Could not close window automatically');
            }
        }, 2000);
    </script>
</body>
</html>`;
}

/**
 * Exchange Instagram authorization code for access token directly
 * Extracted from exchange-token route to avoid SSL issues with internal fetch
 */
async function exchangeInstagramToken(code, redirectUri, userId, supabase) {
    try {
        // Validate environment variables
        const appId = process.env.INSTAGRAM_APP_ID;
        const appSecret = process.env.INSTAGRAM_APP_SECRET;

        if (!appId || !appSecret) {
            console.error('❌ Missing Instagram app credentials');
            return {
                success: false,
                error: { message: 'Instagram app not configured properly' }
            };
        }

        console.log('🔑 Exchanging code for access token...');

        // Prepare form data for Instagram API (2025 requirements)
        const formData = new URLSearchParams({
            client_id: appId,
            client_secret: appSecret,
            grant_type: 'authorization_code',
            redirect_uri: redirectUri,
            code: code
        });

        console.log('📤 Request form data:', {
            endpoint: 'https://api.instagram.com/oauth/access_token',
            method: 'POST',
            contentType: 'application/x-www-form-urlencoded',
            bodyParams: {
                client_id: appId,
                grant_type: 'authorization_code',
                redirect_uri: redirectUri,
                code: code ? `${code.substring(0, 20)}...` : null,
                client_secret: appSecret ? '[HIDDEN]' : null
            }
        });

        // Exchange code for access token
        const tokenResponse = await fetch('https://api.instagram.com/oauth/access_token', {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: formData
        });

        const tokenData = await tokenResponse.json();

        console.log('📥 Instagram token response:', {
            status: tokenResponse.status,
            hasData: !!tokenData.data,
            hasDirectToken: !!tokenData.access_token,
            responseKeys: Object.keys(tokenData),
            dataLength: tokenData.data?.length || 0
        });

        if (tokenData.error) {
            console.error('❌ Instagram token exchange error:', tokenData.error);
            return {
                success: false,
                error: {
                    message: `Instagram API error: ${tokenData.error_message || tokenData.error}`,
                    code: tokenData.error_type
                }
            };
        }

        // FIX 2025: Instagram API trả về format mới với data array
        let accessToken, instagramUserId, permissions;
        
        if (tokenData.data && Array.isArray(tokenData.data) && tokenData.data.length > 0) {
            // NEW FORMAT 2025: { "data": [{ "access_token": "...", "user_id": "...", "permissions": "..." }] }
            const firstData = tokenData.data[0];
            accessToken = firstData.access_token;
            instagramUserId = firstData.user_id;
            permissions = firstData.permissions;
            console.log('✅ Using NEW Instagram API response format 2025');
        } else if (tokenData.access_token) {
            // OLD FORMAT: { "access_token": "...", "user_id": "..." }  
            accessToken = tokenData.access_token;
            instagramUserId = tokenData.user_id;
            console.log('⚠️ Using OLD Instagram API response format (deprecated)');
        } else {
            console.error('❌ No access token received from Instagram. Response:', tokenData);
            return {
                success: false,
                error: { message: 'No access token received from Instagram' }
            };
        }

        console.log('✅ Short-lived token received, exchanging for long-lived token...');

        // Exchange short-lived token for long-lived token
        const longLivedTokenResponse = await fetch(
            `https://graph.instagram.com/access_token?grant_type=ig_exchange_token&client_secret=${appSecret}&access_token=${accessToken}`
        );

        const longLivedTokenData = await longLivedTokenResponse.json();

        if (longLivedTokenData.error) {
            console.error('❌ Error getting long-lived token:', longLivedTokenData.error);
            return {
                success: false,
                error: {
                    message: `Failed to get long-lived token: ${longLivedTokenData.error.message}`,
                    code: longLivedTokenData.error.code
                }
            };
        }

        console.log('✅ Long-lived token received, fetching account info...');

        // Get Instagram account info
        const accountResponse = await fetch(
            `https://graph.instagram.com/me?fields=id,username,account_type,media_count&access_token=${longLivedTokenData.access_token}`
        );

        const accountData = await accountResponse.json();

        if (accountData.error) {
            console.error('❌ Error fetching Instagram account:', accountData.error);
            return {
                success: false,
                error: {
                    message: `Failed to fetch Instagram account: ${accountData.error.message}`,
                    code: accountData.error.code
                }
            };
        }

        // Save Instagram account to database
        const saveResult = await saveInstagramAccountToDatabase({
            accountId: accountData.id,
            username: accountData.username,
            accountType: accountData.account_type || 'BUSINESS',
            accessToken: longLivedTokenData.access_token,
            tokenExpiresAt: longLivedTokenData.expires_in 
                ? new Date(Date.now() + longLivedTokenData.expires_in * 1000).toISOString()
                : null,
            mediaCount: accountData.media_count || 0,
            isActive: true,
            connectedAt: new Date().toISOString(),
            lastSyncAt: new Date().toISOString()
        }, userId, supabase);

        if (!saveResult.success) {
            return {
                success: false,
                error: saveResult.error
            };
        }

        console.log(`✅ Successfully connected Instagram account: ${accountData.username}`);

        return {
            success: true,
            data: {
                account: saveResult.data,
                accountId: accountData.id,
                username: accountData.username,
                accountType: accountData.account_type
            }
        };

    } catch (error) {
        console.error('💥 Instagram token exchange error:', error);
        return {
            success: false,
            error: { 
                message: 'Internal server error during token exchange',
                details: error.message 
            }
        };
    }
}

/**
 * Helper function to save Instagram account to database
 * AUTO-DETECTS tenant_id from authenticated user
 */
async function saveInstagramAccountToDatabase(accountData, userId, supabase) {
    try {
        // Get user's tenant_id automatically from tenant_user_roles
        const { data: userTenant, error: tenantError } = await supabase
            .from('tenant_user_roles')
            .select('tenant_id')
            .eq('user_id', userId)
            .limit(1)
            .single();

        if (tenantError || !userTenant) {
            console.error('❌ Could not find tenant for user:', tenantError);
            return {
                success: false,
                error: { message: 'Could not find tenant for user' }
            };
        }

        const tenantId = userTenant.tenant_id;
        console.log('📊 Auto-detected tenant_id:', tenantId);

        // Check if account already exists
        const { data: existingAccount } = await supabase
            .from('instagram_accounts')
            .select('id')
            .eq('account_id', accountData.accountId)
            .eq('tenant_id', tenantId)
            .single();

        if (existingAccount) {
            // Update existing account
            const { data, error } = await supabase
                .from('instagram_accounts')
                .update({
                    username: accountData.username,
                    account_type: accountData.accountType,
                    access_token: accountData.accessToken,
                    token_expires_at: accountData.tokenExpiresAt,
                    media_count: accountData.mediaCount,
                    is_active: accountData.isActive,
                    last_sync_at: accountData.lastSyncAt,
                    updated_at: new Date().toISOString()
                })
                .eq('id', existingAccount.id)
                .select()
                .single();

            if (error) {
                console.error('❌ Error updating Instagram account:', error);
                throw error;
            }
            
            console.log('✅ Updated existing Instagram account');
            return { success: true, data };
        } else {
            // Create new account
            const { data, error } = await supabase
                .from('instagram_accounts')
                .insert({
                    tenant_id: tenantId,
                    account_id: accountData.accountId,
                    username: accountData.username,
                    account_type: accountData.accountType,
                    access_token: accountData.accessToken,
                    token_expires_at: accountData.tokenExpiresAt,
                    media_count: accountData.mediaCount,
                    is_active: accountData.isActive,
                    connected_at: accountData.connectedAt,
                    last_sync_at: accountData.lastSyncAt
                })
                .select()
                .single();

            if (error) {
                console.error('❌ Error creating Instagram account:', error);
                throw error;
            }

            console.log('✅ Created new Instagram account');
            return { success: true, data };
        }

    } catch (error) {
        console.error('💥 Database error saving Instagram account:', error);
        return {
            success: false,
            error: { 
                message: 'Failed to save Instagram account to database',
                details: error.message 
            }
        };
    }
}
