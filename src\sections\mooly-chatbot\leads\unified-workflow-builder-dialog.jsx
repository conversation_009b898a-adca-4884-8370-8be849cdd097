'use client';

import { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>,
  <PERSON>ack,
  <PERSON>ton,
  Dialog,
  TextField,
  Typography,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Chip,
  Grid,
  IconButton,
  FormControlLabel,
  Switch,
  Divider,
  Tooltip,
  CircularProgress,
  Menu,
  MenuItem,
  Fade,
  Paper,
} from '@mui/material';
import { LoadingButton } from '@mui/lab';
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';

import { Iconify } from 'src/components/iconify';
import { toast } from 'src/components/snackbar';

import { 
  useChatbotWorkflowStages,
  useWorkflowTemplates,
  updateChatbotWorkflow,
  validateAndOptimizeWorkflow,
  syncLeadsWithNewWorkflow,
  DEFAULT_WORKFLOW_STAGES,
  getDisplayColor,
  getStyleColor,
  isValidColor,
  normalizeStageColors,
} from 'src/actions/mooly-chatbot/unified-workflow-service';
import { Label } from 'src/components/label';

// =====================================================
// CONSTANTS
// =====================================================

const STAGE_COLORS = [
  '#e3f2fd', '#fff3e0', '#f3e5f5', '#e8f5e8', '#ffebee',
  '#fce4ec', '#e0f2f1', '#fff8e1', '#f1f8e9', '#e8eaf6',
  '#f3e5f5', '#e1f5fe', '#f9fbe7', '#fff3e0', '#fce4ec'
];

const STAGE_ICONS = [
  'solar:user-plus-bold',
  'solar:phone-bold', 
  'solar:star-bold',
  'solar:check-circle-bold',
  'solar:close-circle-bold',
  'solar:heart-bold',
  'solar:archive-bold',
  'solar:document-text-bold',
  'solar:calendar-bold',
  'solar:settings-bold'
];

// =====================================================
// COLOR PICKER COMPONENT
// =====================================================

function ColorPicker({ value, onChange, label = "Màu sắc" }) {
  const standardHexColors = ['#1976d2', '#dc004e', '#2e7d32', '#d32f2f', '#ed6c02', '#0288d1', '#757575'];
  const [showCustom, setShowCustom] = useState(
    value && value.startsWith('#') && !standardHexColors.includes(value)
  );
  const [customColor, setCustomColor] = useState(value || '#757575');

  const MUI_COLORS = [
    { name: 'primary', label: 'Primary', hex: '#1976d2' },
    { name: 'secondary', label: 'Secondary', hex: '#dc004e' },
    { name: 'success', label: 'Success', hex: '#2e7d32' },
    { name: 'error', label: 'Error', hex: '#d32f2f' },
    { name: 'warning', label: 'Warning', hex: '#ed6c02' },
    { name: 'info', label: 'Info', hex: '#0288d1' },
    { name: 'default', label: 'Default', hex: '#757575' },
  ];

  // Update customColor when value changes
  useEffect(() => {
    if (value && value.startsWith('#')) {
      setCustomColor(value);
    }
  }, [value]);

  const handleMuiColorSelect = (colorName) => {
    onChange(colorName);
    setShowCustom(false);
  };

  const handleCustomColorChange = (hexColor) => {
    setCustomColor(hexColor);
    onChange(hexColor);
    if (!showCustom) {
      setShowCustom(true);
    }
  };

  const currentDisplayColor = getDisplayColor(value);
  const currentStyleColor = getStyleColor(value);

  return (
    <Stack spacing={2}>
      <Typography variant="subtitle2">{label}</Typography>
      
      {/* MUI Colors */}
      <Box>
        <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: 'block' }}>
          Màu chuẩn
        </Typography>
        <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
          {MUI_COLORS.map((color) => {
            const isSelected = value === color.name || value === color.hex;
            return (
              <Tooltip key={color.name} title={color.label}>
                <Box
                  onClick={() => handleMuiColorSelect(color.name)}
                  sx={{
                    width: 32,
                    height: 32,
                    borderRadius: 1,
                    bgcolor: color.hex,
                    cursor: 'pointer',
                    border: isSelected ? '3px solid' : '1px solid',
                    borderColor: isSelected ? 'primary.main' : 'divider',
                    '&:hover': {
                      transform: 'scale(1.1)',
                      transition: 'transform 0.2s',
                    },
                  }}
                />
              </Tooltip>
            );
          })}
        </Stack>
      </Box>

      {/* Custom Color */}
      <Box>
        <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 1 }}>
          <Typography variant="caption" color="text.secondary">
            Màu tùy chỉnh
          </Typography>
          <Switch
            size="small"
            checked={showCustom}
            onChange={(e) => {
              setShowCustom(e.target.checked);
              if (!e.target.checked && value && value.startsWith('#')) {
                // Find closest MUI color when turning off custom
                const closestMuiColor = MUI_COLORS.find(c => c.hex === value);
                if (closestMuiColor) {
                  onChange(closestMuiColor.name);
                } else {
                  onChange('default');
                }
              }
            }}
          />
        </Stack>
        
        {showCustom && (
          <Stack direction="row" spacing={2} alignItems="center">
            <TextField
              type="color"
              value={customColor}
              onChange={(e) => handleCustomColorChange(e.target.value)}
              size="small"
              sx={{ width: 80 }}
            />
            <TextField
              fullWidth
              label="Hex Color"
              value={customColor}
              onChange={(e) => handleCustomColorChange(e.target.value)}
              size="small"
              placeholder="#757575"
              helperText="Nhập mã hex color (vd: #ff5722)"
            />
          </Stack>
        )}
      </Box>

      {/* Preview */}
      <Box>
        <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: 'block' }}>
          Xem trước
        </Typography>
        <Stack direction="row" spacing={2} alignItems="center">
          <Box
            sx={{
              width: 40,
              height: 40,
              borderRadius: 1,
              bgcolor: currentStyleColor,
              border: '1px solid',
              borderColor: 'divider',
            }}
          />
          <Stack>
            <Typography variant="body2">
              Display: <Label color={currentDisplayColor} size="small">Sample</Label>
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Value: {value} → Style: {currentStyleColor}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              MUI Color: {currentDisplayColor}
            </Typography>
          </Stack>
        </Stack>
      </Box>
    </Stack>
  );
}

// =====================================================
// STAGE EDITOR COMPONENT
// =====================================================

function StageEditor({ stage, onUpdate, onDelete, onAddAfter, canDelete = true, index, isDragging = false }) {
  const [isEditing, setIsEditing] = useState(false);
  const [editData, setEditData] = useState(stage);
  const [menuAnchor, setMenuAnchor] = useState(null);

  // Normalize stage colors
  const normalizedStage = normalizeStageColors(stage);

  const handleSave = () => {
    if (!editData.name?.trim()) {
      toast.error('Tên giai đoạn là bắt buộc');
      return;
    }

    if (!editData.id?.trim()) {
      toast.error('ID giai đoạn là bắt buộc');
      return;
    }

    // Validate ID format - linh hoạt hơn
    if (!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(editData.id)) {
      toast.error('ID chỉ được chứa chữ, số và dấu gạch dưới, bắt đầu bằng chữ');
      return;
    }

    // Validate color if provided
    if (editData.color && !isValidColor(editData.color)) {
      toast.error('Màu sắc không hợp lệ');
      return;
    }

    onUpdate(editData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditData(stage);
    setIsEditing(false);
  };

  const handleMenuOpen = (event) => {
    setMenuAnchor(event.currentTarget);
  };

  const handleMenuClose = () => {
    setMenuAnchor(null);
  };

  const handleAddAfter = () => {
    onAddAfter?.(index);
    handleMenuClose();
  };

  const handleToggleFinal = () => {
    setEditData(prev => ({ ...prev, isFinal: !prev.isFinal }));
    handleMenuClose();
  };

  return (
    <Card 
      sx={{ 
        p: 2, 
        mb: 2,
        border: '1px solid', 
        borderColor: isDragging ? 'primary.main' : 'divider',
        boxShadow: isDragging ? 3 : 1,
        transform: isDragging ? 'rotate(2deg)' : 'none',
        transition: 'all 0.2s ease',
        '&:hover': {
          borderColor: 'primary.light',
          boxShadow: 2,
        }
      }}
    >
      <Stack spacing={2}>
        <Stack direction="row" alignItems="center" justifyContent="space-between">
          <Stack direction="row" alignItems="center" spacing={2}>
            <Tooltip title="Drag để sắp xếp">
              <Box sx={{ cursor: 'grab', display: 'flex', alignItems: 'center' }}>
                <Iconify icon="solar:hamburger-menu-bold" width={16} sx={{ mr: 1, color: 'text.disabled' }} />
                <Chip 
                  label={index + 1} 
                  size="small" 
                  sx={{ 
                    bgcolor: normalizedStage.styleColor || STAGE_COLORS[index % STAGE_COLORS.length],
                    color: 'white',
                    fontWeight: 'bold'
                  }} 
                />
              </Box>
            </Tooltip>
            <Typography variant="h6">{stage.name}</Typography>
            {stage.isFinal && (
              <Chip label="Giai đoạn cuối" size="small" color="success" variant="outlined" />
            )}
          </Stack>
          
          <Stack direction="row" spacing={1}>
            <Tooltip title="Thêm actions">
              <IconButton size="small" onClick={handleMenuOpen}>
                <Iconify icon="solar:menu-dots-bold" />
              </IconButton>
            </Tooltip>
            
            <IconButton 
              size="small" 
              onClick={() => setIsEditing(true)}
              disabled={isEditing}
            >
              <Iconify icon="solar:pen-bold" />
            </IconButton>
            
            {canDelete && (
              <IconButton 
                size="small" 
                color="error" 
                onClick={() => onDelete(stage.id)}
                disabled={isEditing}
              >
                <Iconify icon="solar:trash-bin-trash-bold" />
              </IconButton>
            )}
          </Stack>

          {/* Action Menu */}
          <Menu
            anchorEl={menuAnchor}
            open={Boolean(menuAnchor)}
            onClose={handleMenuClose}
            TransitionComponent={Fade}
          >
            <MenuItem onClick={handleAddAfter}>
              <Iconify icon="mingcute:add-line" sx={{ mr: 1 }} />
              Thêm giai đoạn sau
            </MenuItem>
            <MenuItem onClick={handleToggleFinal}>
              <Iconify icon="solar:flag-bold" sx={{ mr: 1 }} />
              {stage.isFinal ? 'Bỏ đánh dấu Final' : 'Đánh dấu Final'}
            </MenuItem>
            <Divider />
            <MenuItem onClick={() => setIsEditing(true)}>
              <Iconify icon="solar:settings-bold" sx={{ mr: 1 }} />
              Cài đặt nâng cao
            </MenuItem>
          </Menu>
        </Stack>

        {isEditing ? (
          <Stack spacing={2}>
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  label="Tên giai đoạn"
                  value={editData.name || ''}
                  onChange={(e) => setEditData(prev => ({ ...prev, name: e.target.value }))}
                  size="small"
                />
              </Grid>
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  label="ID giai đoạn"
                  value={editData.id || ''}
                  onChange={(e) => setEditData(prev => ({ ...prev, id: e.target.value.toLowerCase() }))}
                  size="small"
                  helperText="Chỉ chữ thường, số, dấu gạch dưới"
                />
              </Grid>
            </Grid>

            <TextField
              fullWidth
              label="Mô tả"
              value={editData.description || ''}
              onChange={(e) => setEditData(prev => ({ ...prev, description: e.target.value }))}
              multiline
              rows={2}
              size="small"
            />

            <Grid container spacing={2}>
              <Grid item xs={4}>
                <ColorPicker
                  value={editData.color}
                  onChange={(color) => setEditData(prev => ({ ...prev, color: color }))}
                />
              </Grid>
              <Grid item xs={4}>
                <TextField
                  fullWidth
                  label="Thời gian (giờ)"
                  type="number"
                  value={editData.duration || ''}
                  onChange={(e) => setEditData(prev => ({ ...prev, duration: e.target.value ? parseInt(e.target.value) : null }))}
                  size="small"
                />
              </Grid>
              <Grid item xs={4}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={editData.isFinal || false}
                      onChange={(e) => setEditData(prev => ({ ...prev, isFinal: e.target.checked }))}
                    />
                  }
                  label="Giai đoạn cuối"
                />
              </Grid>
            </Grid>

            {/* Advanced Settings */}
            <Divider>
              <Typography variant="caption" color="text.secondary">
                Cài đặt nâng cao
              </Typography>
            </Divider>

            <Grid container spacing={2}>
              <Grid item xs={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={editData.restrictTransitions || false}
                      onChange={(e) => setEditData(prev => ({ ...prev, restrictTransitions: e.target.checked }))}
                    />
                  }
                  label="Hạn chế chuyển đổi"
                />
                <Typography variant="caption" color="text.secondary" display="block">
                  Chỉ cho phép chuyển sang giai đoạn cuối
                </Typography>
              </Grid>
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  label="Allowed Transitions (optional)"
                  value={editData.allowedTransitions?.join(', ') || ''}
                  onChange={(e) => {
                    const values = e.target.value.split(',').map(v => v.trim()).filter(v => v);
                    setEditData(prev => ({ ...prev, allowedTransitions: values.length > 0 ? values : undefined }));
                  }}
                  size="small"
                  placeholder="stage1, stage2, stage3"
                  helperText="Các stage ID có thể chuyển đến, phân cách bằng dấu phẩy"
                />
              </Grid>
            </Grid>

            <Stack direction="row" spacing={1} justifyContent="flex-end">
              <Button size="small" onClick={handleCancel}>
                Hủy
              </Button>
              <Button size="small" variant="contained" onClick={handleSave}>
                Lưu
              </Button>
            </Stack>
          </Stack>
        ) : (
          <Stack spacing={1}>
            <Typography variant="body2" color="text.secondary">
              ID: {stage.id} • {stage.description || 'Không có mô tả'}
            </Typography>
            {stage.duration && (
              <Typography variant="caption" color="text.secondary">
                Thời gian: {stage.duration} giờ
              </Typography>
            )}
          </Stack>
        )}
      </Stack>
    </Card>
  );
}

// =====================================================
// MAIN COMPONENT
// =====================================================

export default function UnifiedWorkflowBuilderDialog({ 
  open, 
  onClose, 
  chatbotId,
  onSuccess 
}) {
  const [stages, setStages] = useState([]);
  const [loading, setLoading] = useState(false);

  // Load current workflow stages
  const { stages: currentStages, loading: stagesLoading, mutate: mutateStages } = useChatbotWorkflowStages(chatbotId);

  useEffect(() => {
    if (open && currentStages) {
      setStages(currentStages);
    }
  }, [open, currentStages]);

  const handleAddStage = (afterIndex = null) => {
    const insertIndex = afterIndex !== null ? afterIndex + 1 : stages.length;
    const newStage = {
      id: `stage${Date.now()}`, // Fix: Bỏ underscore để pass validation
      name: `Giai đoạn ${insertIndex + 1}`,
      description: '',
      color: STAGE_COLORS[insertIndex % STAGE_COLORS.length],
      icon: STAGE_ICONS[insertIndex % STAGE_ICONS.length],
      order: insertIndex + 1,
      duration: null,
      isFinal: false,
    };

    setStages(prev => {
      const newStages = [...prev];
      newStages.splice(insertIndex, 0, newStage);
      // Reorder all stages
      return newStages.map((stage, index) => ({
        ...stage,
        order: index + 1,
        name: stage.name.includes('Giai đoạn') ? `Giai đoạn ${index + 1}` : stage.name
      }));
    });
  };

  const handleUpdateStage = (updatedStage) => {
    setStages(prev => prev.map(stage => 
      stage.id === updatedStage.id ? updatedStage : stage
    ));
  };

  const handleDeleteStage = (stageId) => {
    setStages(prev => {
      const filtered = prev.filter(stage => stage.id !== stageId);
      // Reorder after deletion
      return filtered.map((stage, index) => ({
        ...stage,
        order: index + 1
      }));
    });
  };

  const handleDragEnd = (result) => {
    if (!result.destination) return;

    const sourceIndex = result.source.index;
    const destinationIndex = result.destination.index;

    if (sourceIndex === destinationIndex) return;

    setStages(prev => {
      const newStages = [...prev];
      const [reorderedStage] = newStages.splice(sourceIndex, 1);
      newStages.splice(destinationIndex, 0, reorderedStage);
      
      // Update order numbers
      return newStages.map((stage, index) => ({
        ...stage,
        order: index + 1
      }));
    });
  };

  const handleSave = async () => {
    try {
      setLoading(true);

      const result = await updateChatbotWorkflow(chatbotId, {
        isUsingCustom: true,
        customStages: stages,
      });

      if (result.success) {
        toast.success('Cập nhật workflow thành công!');
        
        // Invalidate SWR cache để UI cập nhật ngay lập tức
        mutateStages();
        
        onSuccess?.();
        onClose();
      } else {
        toast.error(result.error?.message || 'Không thể cập nhật workflow');
      }
    } catch (error) {
      console.error('Error saving workflow:', error);
      toast.error('Có lỗi xảy ra khi lưu workflow');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { height: '80vh' },
      }}
    >
      <DialogTitle>
        <Stack direction="row" alignItems="center" justifyContent="space-between">
          <Stack>
            <Typography variant="h6">Thiết kế Workflow</Typography>
            <Typography variant="body2" color="text.secondary">
              {chatbotId ? `Workflow cho Chatbot ${chatbotId}` : 'Workflow Global'} • {stages.length} giai đoạn
            </Typography>
          </Stack>
          <IconButton onClick={onClose}>
            <Iconify icon="mingcute:close-line" />
          </IconButton>
        </Stack>
      </DialogTitle>

      <DialogContent dividers>
        <Stack spacing={3}>
          <Alert severity="info" variant="outlined">
            <Typography variant="body2">
              <strong>💡 Hướng dẫn sử dụng:</strong>
            </Typography>
                         <Typography variant="body2" component="div">
               • <strong>Drag & Drop:</strong> Kéo icon hamburger để sắp xếp giai đoạn<br/>
               • <strong>Thêm giai đoạn:</strong> Click menu &ldquo;•••&rdquo; → &ldquo;Thêm giai đoạn sau&rdquo;<br/>
               • <strong>Transition linh hoạt:</strong> Mặc định cho phép chuyển tự do giữa các giai đoạn<br/>
               • <strong>Giai đoạn cuối:</strong> Đánh dấu để xác định điểm kết thúc workflow
             </Typography>
          </Alert>

          {stagesLoading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          ) : (
            <Stack spacing={2}>
              <DragDropContext onDragEnd={handleDragEnd}>
                <Droppable droppableId="stages">
                  {(droppableProvided) => (
                    <div {...droppableProvided.droppableProps} ref={droppableProvided.innerRef}>
                      {stages.map((stage, index) => (
                        <Draggable key={stage.id} draggableId={stage.id} index={index}>
                          {(draggableProvided, snapshot) => (
                            <div
                              ref={draggableProvided.innerRef}
                              {...draggableProvided.draggableProps}
                              {...draggableProvided.dragHandleProps}
                            >
                              <StageEditor
                                stage={stage}
                                index={index}
                                isDragging={snapshot.isDragging}
                                onUpdate={handleUpdateStage}
                                onDelete={handleDeleteStage}
                                onAddAfter={handleAddStage}
                                canDelete={stages.length > 1}
                              />
                            </div>
                          )}
                        </Draggable>
                      ))}
                      {droppableProvided.placeholder}
                    </div>
                  )}
                </Droppable>
              </DragDropContext>

              <Button
                variant="outlined"
                startIcon={<Iconify icon="mingcute:add-line" />}
                onClick={() => handleAddStage()}
                sx={{ alignSelf: 'flex-start' }}
              >
                Thêm Giai Đoạn Cuối
              </Button>
            </Stack>
          )}
        </Stack>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose}>
          Hủy
        </Button>
        <LoadingButton
          variant="contained"
          onClick={handleSave}
          loading={loading}
          disabled={stages.length === 0}
        >
          Lưu Workflow
        </LoadingButton>
      </DialogActions>
    </Dialog>
  );
} 