'use client';

import { useEffect, useRef } from 'react';
import { cleanupTemporaryImages } from 'src/actions/mooly-chatbot/message-image-service';

/**
 * Hook để tự động cleanup temporary images khi component unmount
 * hoặc khi có thay đổi trong danh sách images
 */
export function useMessageImageCleanup() {
  const tempImagesRef = useRef([]);

  // Track temporary images
  const trackTemporaryImages = (images) => {
    if (!images || !Array.isArray(images)) return;
    
    const tempImages = images.filter(img => img.isTemporary);
    tempImagesRef.current = tempImages;
  };

  // Cleanup specific temporary images
  const cleanupImages = async (images) => {
    if (!images || images.length === 0) return;
    
    const tempImages = images.filter(img => img.isTemporary);
    if (tempImages.length > 0) {
      try {
        await cleanupTemporaryImages(tempImages);
        console.log('Cleaned up temporary images:', tempImages.length);
      } catch (error) {
        console.error('Error cleaning up temporary images:', error);
      }
    }
  };

  // Cleanup all tracked temporary images
  const cleanupAllTracked = async () => {
    if (tempImagesRef.current.length > 0) {
      await cleanupImages(tempImagesRef.current);
      tempImagesRef.current = [];
    }
  };

  // Auto cleanup on unmount
  useEffect(() => {
    return () => {
      // Cleanup when component unmounts
      if (tempImagesRef.current.length > 0) {
        cleanupTemporaryImages(tempImagesRef.current).catch(error => {
          console.error('Error cleaning up temporary images on unmount:', error);
        });
      }
    };
  }, []);

  return {
    trackTemporaryImages,
    cleanupImages,
    cleanupAllTracked
  };
}

/**
 * Hook để quản lý temporary images trong form
 * Tự động cleanup khi form reset hoặc component unmount
 */
export function useFormImageCleanup(images) {
  const { trackTemporaryImages, cleanupAllTracked } = useMessageImageCleanup();
  const previousImagesRef = useRef([]);

  // Track images changes
  useEffect(() => {
    if (images) {
      trackTemporaryImages(images);
      
      // Cleanup images that were removed
      const removedImages = previousImagesRef.current.filter(
        prevImg => !images.some(img => img.id === prevImg.id)
      );
      
      if (removedImages.length > 0) {
        const tempRemovedImages = removedImages.filter(img => img.isTemporary);
        if (tempRemovedImages.length > 0) {
          cleanupTemporaryImages(tempRemovedImages).catch(error => {
            console.error('Error cleaning up removed temporary images:', error);
          });
        }
      }
      
      previousImagesRef.current = images;
    }
  }, [images, trackTemporaryImages]);

  // Manual cleanup function
  const resetAndCleanup = async () => {
    await cleanupAllTracked();
    previousImagesRef.current = [];
  };

  return {
    resetAndCleanup
  };
}
