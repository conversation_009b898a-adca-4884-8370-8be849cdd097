'use client';

import { useState } from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';

import {
  <PERSON>,
  <PERSON>ack,
  Alert,
  Button,
  Dialog,
  Select,
  MenuItem,
  TextField,
  Typography,
  InputLabel,
  FormControl,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress
} from '@mui/material';

import { createPromotion } from 'src/actions/mooly-chatbot/promotion-service';

// ----------------------------------------------------------------------

export function QuickOfferCreateDialog({ open, onClose, onSuccess }) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const { control, handleSubmit, reset, formState: { errors } } = useForm({
    defaultValues: {
      name: '',
      code: '',
      description: '',
      valueType: 'percentage',
      value: '',
      minPurchaseAmount: '',
      startDate: '',
      endDate: '',
      usageLimit: ''
    }
  });

  const handleClose = () => {
    reset();
    setError(null);
    onClose();
  };

  const onSubmit = async (data) => {
    try {
      setLoading(true);
      setError(null);

      // Prepare promotion data
      const promotionData = {
        name: data.name,
        code: data.code.toUpperCase(),
        description: data.description,
        type: 'coupon',
        value_type: data.valueType,
        value: Number(data.value),
        min_purchase_amount: data.minPurchaseAmount ? Number(data.minPurchaseAmount) : null,
        max_discount_amount: null,
        usage_limit: data.usageLimit ? Number(data.usageLimit) : null,
        usage_count: 0,
        is_active: true,
        start_date: data.startDate ? new Date(data.startDate).toISOString() : new Date().toISOString(),
        end_date: data.endDate ? new Date(data.endDate).toISOString() : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
        applies_to: 'all',
        eligible_product_ids: [],
        eligible_category_ids: []
      };

      const result = await createPromotion(promotionData);

      if (result.success) {
        onSuccess(result.data);
        handleClose();
      } else {
        setError(result.error);
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle>
        <Typography variant="h6">
          Tạo Offer Nhanh
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          Tạo offer đơn giản để sử dụng trong automation rule
        </Typography>
      </DialogTitle>

      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {typeof error === 'string' ? error : JSON.stringify(error)}
          </Alert>
        )}

        <form onSubmit={handleSubmit(onSubmit)}>
          <Stack spacing={3} sx={{ mt: 1 }}>
            {/* Basic Info */}
            <Card sx={{ p: 3 }}>
              <Typography variant="subtitle2" gutterBottom>
                Thông Tin Cơ Bản
              </Typography>
              
              <Stack spacing={2}>
                <Controller
                  name="name"
                  control={control}
                  rules={{ required: 'Tên offer là bắt buộc' }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Tên Offer"
                      placeholder="VD: Giảm giá mùa hè"
                      error={!!errors.name}
                      helperText={errors.name?.message}
                      fullWidth
                      size="small"
                    />
                  )}
                />

                <Controller
                  name="code"
                  control={control}
                  rules={{ 
                    required: 'Mã offer là bắt buộc',
                    pattern: {
                      value: /^[A-Z0-9_-]+$/,
                      message: 'Mã chỉ được chứa chữ hoa, số, gạch dưới và gạch ngang'
                    }
                  }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Mã Offer"
                      placeholder="VD: SUMMER2024"
                      error={!!errors.code}
                      helperText={errors.code?.message}
                      fullWidth
                      size="small"
                      onChange={(e) => field.onChange(e.target.value.toUpperCase())}
                    />
                  )}
                />

                <Controller
                  name="description"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Mô Tả"
                      placeholder="Mô tả ngắn về offer"
                      multiline
                      rows={2}
                      fullWidth
                      size="small"
                    />
                  )}
                />
              </Stack>
            </Card>

            {/* Discount Value */}
            <Card sx={{ p: 3 }}>
              <Typography variant="subtitle2" gutterBottom>
                Giá Trị Giảm Giá
              </Typography>
              
              <Stack spacing={2}>
                <Controller
                  name="valueType"
                  control={control}
                  render={({ field }) => (
                    <FormControl fullWidth size="small">
                      <InputLabel>Loại Giảm Giá</InputLabel>
                      <Select {...field} label="Loại Giảm Giá">
                        <MenuItem value="percentage">Phần trăm (%)</MenuItem>
                        <MenuItem value="fixed_amount">Số tiền cố định (đ)</MenuItem>
                      </Select>
                    </FormControl>
                  )}
                />

                <Controller
                  name="value"
                  control={control}
                  rules={{ 
                    required: 'Giá trị giảm giá là bắt buộc',
                    min: { value: 1, message: 'Giá trị phải lớn hơn 0' }
                  }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Giá Trị"
                      type="number"
                      placeholder="10"
                      error={!!errors.value}
                      helperText={errors.value?.message}
                      fullWidth
                      size="small"
                    />
                  )}
                />

                <Controller
                  name="minPurchaseAmount"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Giá Trị Đơn Hàng Tối Thiểu"
                      type="number"
                      placeholder="100000"
                      InputProps={{
                        endAdornment: 'đ'
                      }}
                      fullWidth
                      size="small"
                    />
                  )}
                />
              </Stack>
            </Card>

            {/* Usage Limit */}
            <Card sx={{ p: 3 }}>
              <Typography variant="subtitle2" gutterBottom>
                Giới Hạn Sử Dụng
              </Typography>
              
              <Controller
                name="usageLimit"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Số Lần Sử Dụng Tối Đa"
                    type="number"
                    placeholder="100"
                    helperText="Để trống nếu không giới hạn"
                    fullWidth
                    size="small"
                  />
                )}
              />
            </Card>
          </Stack>
        </form>
      </DialogContent>

      <DialogActions sx={{ p: 3, pt: 0 }}>
        <Button onClick={handleClose} disabled={loading}>
          Hủy
        </Button>
        <Button
          variant="contained"
          onClick={handleSubmit(onSubmit)}
          disabled={loading}
          startIcon={loading && <CircularProgress size={20} />}
        >
          {loading ? 'Đang tạo...' : 'Tạo Offer'}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
