/**
 * Simple in-memory rate limiter for credit operations
 * In production, this should use Redis or similar distributed cache
 */

class RateLimiter {
  constructor() {
    this.requests = new Map();
    this.cleanupInterval = setInterval(() => this.cleanup(), 60000); // Cleanup every minute
  }

  /**
   * Check if request is allowed based on rate limiting rules
   * @param {string} key - Unique identifier (e.g., user ID + IP)
   * @param {number} maxRequests - Maximum requests allowed
   * @param {number} windowMs - Time window in milliseconds
   * @returns {boolean} - Whether request is allowed
   */
  isAllowed(key, maxRequests = 10, windowMs = 60000) {
    const now = Date.now();
    const windowStart = now - windowMs;

    if (!this.requests.has(key)) {
      this.requests.set(key, []);
    }

    const userRequests = this.requests.get(key);
    
    // Remove old requests outside the window
    const validRequests = userRequests.filter(timestamp => timestamp > windowStart);
    
    // Check if under limit
    if (validRequests.length >= maxRequests) {
      return false;
    }

    // Add current request
    validRequests.push(now);
    this.requests.set(key, validRequests);
    
    return true;
  }

  /**
   * Get remaining requests for a key
   * @param {string} key - Unique identifier
   * @param {number} maxRequests - Maximum requests allowed
   * @param {number} windowMs - Time window in milliseconds
   * @returns {number} - Remaining requests
   */
  getRemaining(key, maxRequests = 10, windowMs = 60000) {
    const now = Date.now();
    const windowStart = now - windowMs;

    if (!this.requests.has(key)) {
      return maxRequests;
    }

    const userRequests = this.requests.get(key);
    const validRequests = userRequests.filter(timestamp => timestamp > windowStart);
    
    return Math.max(0, maxRequests - validRequests.length);
  }

  /**
   * Cleanup old entries
   */
  cleanup() {
    const now = Date.now();
    const maxAge = 3600000; // 1 hour

    for (const [key, requests] of this.requests.entries()) {
      const validRequests = requests.filter(timestamp => timestamp > now - maxAge);
      
      if (validRequests.length === 0) {
        this.requests.delete(key);
      } else {
        this.requests.set(key, validRequests);
      }
    }
  }

  /**
   * Clear all rate limiting data
   */
  clear() {
    this.requests.clear();
  }

  /**
   * Destroy the rate limiter and cleanup interval
   */
  destroy() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.clear();
  }
}

// Create singleton instance
const rateLimiter = new RateLimiter();

/**
 * Rate limiting middleware for credit operations
 * @param {string} operation - Type of operation (e.g., 'credit_usage', 'credit_balance')
 * @param {number} maxRequests - Maximum requests per window
 * @param {number} windowMs - Time window in milliseconds
 * @returns {Function} - Middleware function
 */
export function createRateLimitMiddleware(operation = 'default', maxRequests = 10, windowMs = 60000) {
  return (req, res, next) => {
    try {
      // Create unique key based on user, IP, and operation
      const userId = req.user?.id || 'anonymous';
      const ip = req.headers['x-forwarded-for'] || req.connection.remoteAddress || 'unknown';
      const key = `${operation}:${userId}:${ip}`;

      if (!rateLimiter.isAllowed(key, maxRequests, windowMs)) {
        const remaining = rateLimiter.getRemaining(key, maxRequests, windowMs);
        const resetTime = new Date(Date.now() + windowMs);

        return res.status(429).json({
          success: false,
          error: 'Too many requests',
          rateLimit: {
            limit: maxRequests,
            remaining,
            resetTime: resetTime.toISOString(),
            retryAfter: Math.ceil(windowMs / 1000),
          },
        });
      }

      // Add rate limit headers
      const remaining = rateLimiter.getRemaining(key, maxRequests, windowMs);
      res.setHeader('X-RateLimit-Limit', maxRequests);
      res.setHeader('X-RateLimit-Remaining', remaining);
      res.setHeader('X-RateLimit-Reset', new Date(Date.now() + windowMs).toISOString());

      next();
    } catch (error) {
      console.error('Rate limiting error:', error);
      // Don't block requests if rate limiter fails
      next();
    }
  };
}

/**
 * Check rate limit for Next.js API routes
 * @param {Request} request - Next.js request object
 * @param {string} userId - User ID
 * @param {string} operation - Operation type
 * @param {number} maxRequests - Maximum requests
 * @param {number} windowMs - Time window
 * @returns {Object} - Rate limit result
 */
export function checkRateLimit(request, userId, operation = 'default', maxRequests = 10, windowMs = 60000) {
  try {
    const ip = request.headers.get('x-forwarded-for') || 'unknown';
    const key = `${operation}:${userId}:${ip}`;

    const allowed = rateLimiter.isAllowed(key, maxRequests, windowMs);
    const remaining = rateLimiter.getRemaining(key, maxRequests, windowMs);

    return {
      allowed,
      remaining,
      limit: maxRequests,
      resetTime: new Date(Date.now() + windowMs).toISOString(),
      retryAfter: Math.ceil(windowMs / 1000),
    };
  } catch (error) {
    console.error('Rate limit check error:', error);
    // Allow request if rate limiter fails
    return {
      allowed: true,
      remaining: maxRequests,
      limit: maxRequests,
      resetTime: new Date(Date.now() + windowMs).toISOString(),
      retryAfter: 0,
    };
  }
}

export default rateLimiter;
