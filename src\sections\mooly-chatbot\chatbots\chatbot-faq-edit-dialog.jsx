'use client';

import { useState, useEffect } from 'react';

import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import Typography from '@mui/material/Typography';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import IconButton from '@mui/material/IconButton';
import Divider from '@mui/material/Divider';
import CircularProgress from '@mui/material/CircularProgress';
import TextField from '@mui/material/TextField';
import Stack from '@mui/material/Stack';

import { updateChatbotFaq } from 'src/actions/mooly-chatbot/chatbot-faq-service';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';

import { useAuthContext } from 'src/auth/hooks';

// ----------------------------------------------------------------------

export default function ChatbotFaqEditDialog({ open, onClose, chatbot, faq, onSuccess }) {
  const { user } = useAuthContext();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    topic: '',
    content: '',
  });

  // Load dữ liệu FAQ khi dialog mở
  useEffect(() => {
    if (open && faq) {
      setFormData({
        topic: faq.topic || '',
        content: faq.content || '',
      });
    }
  }, [open, faq]);

  // Reset form khi dialog đóng
  const handleClose = () => {
    setFormData({
      topic: '',
      content: '',
    });
    onClose();
  };

  // Cập nhật form data
  const handleInputChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Xử lý submit form
  const handleSubmit = async () => {
    try {
      setIsSubmitting(true);

      if (!chatbot?.id) {
        toast.error('Không tìm thấy thông tin chatbot');
        return;
      }

      if (!faq?.id) {
        toast.error('Không tìm thấy thông tin FAQ');
        return;
      }

      if (!user?.app_metadata?.tenant_id) {
        toast.error('Không tìm thấy thông tin tenant');
        return;
      }

      // Validate dữ liệu
      const cleanedData = {
        topic: formData.topic.trim(),
        content: formData.content.trim(),
      };

      if (!cleanedData.topic || !cleanedData.content) {
        toast.error('Vui lòng nhập đầy đủ chủ đề và nội dung FAQ');
        return;
      }

      // Cập nhật FAQ
      const result = await updateChatbotFaq(
        faq.id,
        cleanedData,
        user.app_metadata.tenant_id,
        chatbot.id
      );

      if (result.success) {
        toast.success('Đã cập nhật FAQ thành công');
        onSuccess?.();
        handleClose();
      } else {
        toast.error('Có lỗi xảy ra khi cập nhật FAQ');
      }
    } catch (error) {
      console.error('Error updating FAQ:', error);
      toast.error(error.message || 'Có lỗi xảy ra khi cập nhật FAQ');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog 
      open={open} 
      onClose={handleClose} 
      maxWidth="md" 
      fullWidth
      PaperProps={{
        sx: { minHeight: 400 },
      }}
    >
      <DialogTitle>
        <Stack direction="row" alignItems="center" justifyContent="space-between">
          <Typography variant="h6">Chỉnh sửa FAQ</Typography>
          <IconButton onClick={handleClose}>
            <Iconify icon="mingcute:close-line" />
          </IconButton>
        </Stack>
      </DialogTitle>

      <Divider />

      <DialogContent>
        <Stack spacing={3} sx={{ pt: 1 }}>
          <Typography variant="subtitle2" sx={{ color: 'text.secondary' }}>
            Chỉnh sửa thông tin câu hỏi thường gặp
          </Typography>

          <TextField
            label="Chủ đề / Câu hỏi"
            value={formData.topic}
            onChange={(e) => handleInputChange('topic', e.target.value)}
            fullWidth
            required
            multiline
            rows={2}
            placeholder="Ví dụ: Thông tin về giao hàng..."
          />

          <TextField
            label="Nội dung trả lời"
            value={formData.content}
            onChange={(e) => handleInputChange('content', e.target.value)}
            fullWidth
            required
            multiline
            rows={6}
            placeholder="Nhập nội dung trả lời chi tiết..."
          />
        </Stack>
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose} disabled={isSubmitting}>
          Hủy
        </Button>
        <Button
          variant="contained"
          onClick={handleSubmit}
          disabled={isSubmitting || !formData.topic.trim() || !formData.content.trim()}
          startIcon={isSubmitting ? <CircularProgress size={20} /> : null}
        >
          Cập nhật
        </Button>
      </DialogActions>
    </Dialog>
  );
} 