import { NextResponse } from 'next/server';

import { withTenantAuth, validateTenantId } from 'src/utils/server-auth';
import storageService from 'src/actions/mooly-chatbot/storage-service';

/**
 * API route để lấy thông tin chi tiết sản phẩm
 * @param {Request} request - <PERSON><PERSON><PERSON> cầu HTTP
 * @param {Object} params - Route parameters
 * @returns {Promise<NextResponse>} - Phản hồi HTTP
 */
export const GET = withTenantAuth(async (request, { tenantId, userId }, { params }) => {
  try {
    const { id } = await params;

    if (!id) {
      return NextResponse.json({ error: 'Product ID is required' }, { status: 400 });
    }

    // Lấy thông tin sản phẩm với RLS protection
    const { data, error } = await request.supabase
      .from('products')
      .select(`
        *,
        product_variants (*)
      `)
      .eq('id', id)
      .eq('tenant_id', tenantId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Product not found' }, { status: 404 });
      }
      console.error('Error fetching product:', error);
      return NextResponse.json(
        { error: 'Failed to fetch product', details: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data,
    });
  } catch (error) {
    console.error('Error in product GET route:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
});

/**
 * So sánh hình ảnh để xác định những hình cần xóa
 * @param {Array} defaultImages - Danh sách hình ảnh ban đầu
 * @param {Array} currentImages - Danh sách hình ảnh hiện tại
 * @returns {Object} - Kết quả so sánh
 */
function compareImages(defaultImages = [], currentImages = []) {
  // Đảm bảo defaultImages và currentImages là mảng
  const safeDefaultImages = Array.isArray(defaultImages) ? defaultImages : [];
  const safeCurrentImages = Array.isArray(currentImages) ? currentImages : [];

  // Xác định các hình cần xóa (có trong defaultImages nhưng không còn trong currentImages)
  const imagesToDelete = safeDefaultImages.filter(
    (defaultImg) => {
      // Chỉ xử lý các URL string hợp lệ
      if (typeof defaultImg !== 'string' || !defaultImg.trim()) {
        return false;
      }

      // Kiểm tra xem URL này có còn trong currentImages không
      return !safeCurrentImages.some((currentImg) => {
        // Nếu currentImg là string (URL), so sánh trực tiếp
        if (typeof currentImg === 'string') {
          return currentImg === defaultImg;
        }
        // Nếu currentImg là object, nó là file mới, không phải URL cũ
        return false;
      });
    }
  );

  return {
    toDelete: imagesToDelete,
  };
}

/**
 * API route để cập nhật sản phẩm
 * @param {Request} request - Yêu cầu HTTP
 * @param {Object} params - Route parameters
 * @returns {Promise<NextResponse>} - Phản hồi HTTP
 */
export const PUT = withTenantAuth(async (request, { tenantId, userId }, { params }) => {
  try {
    const { id } = await params;

    if (!id) {
      return NextResponse.json({ error: 'Product ID is required' }, { status: 400 });
    }

    // Validate request body và kiểm tra tenant_id
    const validation = await validateTenantId(request, tenantId);
    if (!validation.valid) {
      return NextResponse.json({ error: validation.error }, { status: validation.status });
    }

    const updateData = validation.body;

    // Kiểm tra sản phẩm có tồn tại và thuộc tenant không, lấy thêm images để so sánh
    const { data: existingProduct, error: checkError } = await request.supabase
      .from('products')
      .select('id, sku, images, avatar')
      .eq('id', id)
      .eq('tenant_id', tenantId)
      .single();

    if (checkError) {
      if (checkError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Product not found' }, { status: 404 });
      }
      console.error('Error checking product:', checkError);
      return NextResponse.json(
        { error: 'Failed to verify product', details: checkError.message },
        { status: 500 }
      );
    }

    // Kiểm tra SKU unique nếu có thay đổi
    if (updateData.sku && updateData.sku !== existingProduct.sku) {
      const { data: duplicateProduct } = await request.supabase
        .from('products')
        .select('id')
        .eq('tenant_id', tenantId)
        .eq('sku', updateData.sku)
        .neq('id', id)
        .single();

      if (duplicateProduct) {
        return NextResponse.json(
          { error: 'SKU đã tồn tại trong hệ thống' },
          { status: 400 }
        );
      }
    }

    // Xử lý xóa hình ảnh không còn sử dụng
    const imagesToDelete = [];

    // So sánh hình ảnh nếu có thay đổi
    if (updateData.images && existingProduct.images) {
      const imageChanges = compareImages(existingProduct.images, updateData.images);
      if (imageChanges.toDelete && imageChanges.toDelete.length > 0) {
        imagesToDelete.push(...imageChanges.toDelete);
      }
    }

    // So sánh avatar nếu có thay đổi
    if (updateData.avatar !== existingProduct.avatar && existingProduct.avatar) {
      // Kiểm tra xem avatar cũ có còn được sử dụng trong danh sách hình ảnh không
      const avatarStillUsed = updateData.images && updateData.images.some(img =>
        typeof img === 'string' && img === existingProduct.avatar
      );

      if (!avatarStillUsed && typeof existingProduct.avatar === 'string' && existingProduct.avatar.trim()) {
        imagesToDelete.push(existingProduct.avatar);
      }
    }

    // Thêm thông tin cập nhật
    updateData.updated_at = new Date().toISOString();
    updateData.updated_by = userId;

    // Cập nhật sản phẩm
    const { data, error } = await request.supabase
      .from('products')
      .update(updateData)
      .eq('id', id)
      .eq('tenant_id', tenantId)
      .select()
      .single();

    if (error) {
      console.error('Error updating product:', error);
      return NextResponse.json(
        { error: 'Failed to update product', details: error.message },
        { status: 500 }
      );
    }

    // Xóa hình ảnh không còn sử dụng sau khi cập nhật thành công (không blocking)
    if (imagesToDelete.length > 0) {
      try {
        console.log('Deleting unused images:', imagesToDelete);
        const deleteResult = await storageService.deleteFilesWithPublicUrl('public', imagesToDelete);

        if (deleteResult.success) {
          console.log('Successfully deleted unused images:', imagesToDelete);
        } else {
          console.warn('Failed to delete some images:', deleteResult.error);
        }
      } catch (storageError) {
        console.error('Error deleting unused images:', storageError);
        // Không fail vì storage error không ảnh hưởng đến database update
      }
    }

    return NextResponse.json({
      success: true,
      data,
      message: 'Cập nhật sản phẩm thành công',
    });
  } catch (error) {
    console.error('Error in product PUT route:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
});

/**
 * API route để xóa sản phẩm
 * @param {Request} request - Yêu cầu HTTP
 * @param {Object} params - Route parameters
 * @returns {Promise<NextResponse>} - Phản hồi HTTP
 */
export const DELETE = withTenantAuth(async (request, { tenantId, userId }, { params }) => {
  try {
    const { id } = await params;

    if (!id) {
      return NextResponse.json({ error: 'Product ID is required' }, { status: 400 });
    }

    // Kiểm tra sản phẩm có tồn tại và thuộc tenant không
    const { data: existingProduct, error: checkError } = await request.supabase
      .from('products')
      .select('id, name, sku, images')
      .eq('id', id)
      .eq('tenant_id', tenantId)
      .single();

    if (checkError) {
      if (checkError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Product not found' }, { status: 404 });
      }
      console.error('Error checking product:', checkError);
      return NextResponse.json(
        { error: 'Failed to verify product', details: checkError.message },
        { status: 500 }
      );
    }

    try {
      // 1. Xóa product variants trước
      const { error: variantError } = await request.supabase
        .from('product_variants')
        .delete()
        .eq('product_id', id)
        .eq('tenant_id', tenantId);

      if (variantError) {
        console.error('Error deleting variants:', variantError);
        return NextResponse.json(
          { error: 'Failed to delete product variants', details: variantError.message },
          { status: 500 }
        );
      }

      // 2. Xóa inventory transactions
      const { error: inventoryError } = await request.supabase
        .from('inventory_transactions')
        .delete()
        .eq('product_id', id)
        .eq('tenant_id', tenantId);

      if (inventoryError) {
        console.error('Error deleting inventory:', inventoryError);
        // Không fail vì inventory có thể không tồn tại
      }

      // 3. Xóa sản phẩm chính
      const { error: deleteError } = await request.supabase
        .from('products')
        .delete()
        .eq('id', id)
        .eq('tenant_id', tenantId);

      if (deleteError) {
        console.error('Error deleting product:', deleteError);
        return NextResponse.json(
          { error: 'Failed to delete product', details: deleteError.message },
          { status: 500 }
        );
      }

      // 4. Xóa hình ảnh từ storage (không blocking)
      if (existingProduct.images && existingProduct.images.length > 0) {
        try {
          console.log('Deleting product images:', existingProduct.images);
          const deleteResult = await storageService.deleteFilesWithPublicUrl('public', existingProduct.images);

          if (deleteResult.success) {
            console.log('Successfully deleted product images:', existingProduct.images);
          } else {
            console.warn('Failed to delete some product images:', deleteResult.error);
          }
        } catch (storageError) {
          console.error('Error deleting images:', storageError);
          // Không fail vì storage error không ảnh hưởng đến database
        }
      }

      // 5. Xóa khỏi Weaviate (không blocking)
      try {
        const BACKEND_API_URL = process.env.BACKEND_API_URL || 'http://localhost:3000';
        await fetch(`${BACKEND_API_URL}/api/weaviate/products/delete-by-id`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            product_id: id,
            tenant_id: tenantId
          }),
        });
      } catch (weaviateError) {
        console.error('Error deleting from Weaviate:', weaviateError);
        // Không fail vì Weaviate error không ảnh hưởng đến database
      }

      return NextResponse.json({
        success: true,
        message: 'Xóa sản phẩm thành công',
        data: {
          id,
          name: existingProduct.name,
          sku: existingProduct.sku
        }
      });

    } catch (error) {
      console.error('Error during product deletion:', error);
      return NextResponse.json(
        { error: 'Failed to delete product', details: error.message },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error in product DELETE route:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
});
