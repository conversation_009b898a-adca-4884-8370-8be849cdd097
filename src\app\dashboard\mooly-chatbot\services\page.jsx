'use client';

import { <PERSON>, <PERSON>, Grid, <PERSON>ack, But<PERSON>, Container, Typography } from '@mui/material';

import { DashboardContent } from 'src/layouts/dashboard';

import { Iconify } from 'src/components/iconify';
import { CustomBreadcrumbs } from 'src/components/custom-breadcrumbs';

// ----------------------------------------------------------------------

export default function ServicesPage() {
  const statsCards = [
    {
      title: 'Tổng dịch vụ',
      value: '0',
      subtitle: 'Dịch vụ đang hoạt động',
      icon: 'eva:briefcase-fill',
      color: 'primary',
    },
    {
      title: 'Lịch hẹn hôm nay',
      value: '0',
      subtitle: 'Cuộc hẹn được đặt',
      icon: 'eva:calendar-fill',
      color: 'success',
    },
    {
      title: '<PERSON><PERSON><PERSON> thu tháng',
      value: '0 ₫',
      subtitle: 'Từ dịch vụ',
      icon: 'eva:trending-up-fill',
      color: 'warning',
    },
  ];

  return (
    <DashboardContent>
      <Container maxWidth="xl">
        <CustomBreadcrumbs
          heading="Quản lý dịch vụ"
          subheading="Tạo và quản lý các dịch vụ của doanh nghiệp"
          links={[
            { name: 'Dashboard', href: '/dashboard' },
            { name: 'Dịch vụ' },
          ]}
          sx={{ mb: 3 }}
        />

        <Grid container spacing={3} sx={{ mb: 3 }}>
          {statsCards.map((card, index) => (
            <Grid item size={{xs: 12, md: 4}} key={index}>
              <Card sx={{ p: 3 }}>
                <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 2 }}>
                  <Typography variant="h6">{card.title}</Typography>
                  <Box
                    sx={{
                      width: 40,
                      height: 40,
                      borderRadius: 1.5,
                      bgcolor: `${card.color}.lighter`,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <Iconify icon={card.icon} width={20} sx={{ color: `${card.color}.main` }} />
                  </Box>
                </Stack>
                <Typography variant="h3" sx={{ mb: 0.5 }}>
                  {card.value}
                </Typography>
                <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                  {card.subtitle}
                </Typography>
              </Card>
            </Grid>
          ))}
        </Grid>

        <Card>
          <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ p: 3, borderBottom: 1, borderColor: 'divider' }}>
            <Typography variant="h6">Danh sách dịch vụ</Typography>
            <Button variant="contained" startIcon={<Iconify icon="eva:plus-fill" />}>
              Thêm dịch vụ mới
            </Button>
          </Stack>

          <Box sx={{ p: 6 }}>
            <Stack alignItems="center" spacing={3}>
              <Box
                sx={{
                  width: 96,
                  height: 96,
                  borderRadius: '50%',
                  bgcolor: 'grey.100',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <Iconify icon="eva:briefcase-fill" width={48} sx={{ color: 'text.disabled' }} />
              </Box>

              <Typography variant="h6">Chưa có dịch vụ nào</Typography>

              <Typography variant="body1" sx={{ textAlign: 'center', maxWidth: 480, color: 'text.secondary' }}>
                Bắt đầu tạo dịch vụ đầu tiên để khách hàng có thể đặt lịch hẹn với doanh nghiệp của bạn.
              </Typography>

              <Button variant="contained" size="large">
                Tạo dịch vụ đầu tiên
              </Button>
            </Stack>
          </Box>
        </Card>
      </Container>
    </DashboardContent>
  );
}
