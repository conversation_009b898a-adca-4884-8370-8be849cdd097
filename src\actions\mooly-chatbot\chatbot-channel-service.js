'use client';

import { useRef, useMemo, useState, useEffect, useCallback } from 'react';

import { fetchData, createData, updateData, deleteData } from './supabase-utils';

// Tên bảng trong Supabase
const TABLE_NAME = 'chatbot_channels';

/**
 * L<PERSON>y danh sách kênh chatbot với các tùy chọn lọc
 * @param {Object} options - <PERSON><PERSON><PERSON> tùy chọn truy vấn
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getChatbotChannels(options = {}) {
  return fetchData(TABLE_NAME, options);
}

/**
 * L<PERSON>y thông tin kênh chatbot theo ID
 * @param {string} channelId - ID của kênh
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getChatbotChannelById(channelId) {
  if (!channelId) return { success: false, error: 'Channel ID is required', data: null };

  return fetchData(TABLE_NAME, {
    filters: { id: channelId },
    single: true,
  });
}

/**
 * <PERSON><PERSON><PERSON> thông tin kênh chatbot theo inboxId
 * @param {string} inboxId - ID của inbox
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getChatbotChannelByInboxId(inboxId) {
  if (!inboxId) return { success: false, error: 'Inbox ID is required', data: null };

  return fetchData(TABLE_NAME, {
    filters: { inbox_id: inboxId },
    single: true,
  });
}

/**
 * Tạo kênh chatbot mới
 * @param {Object} channelData - Dữ liệu kênh
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function createChatbotChannel(channelData) {
  console.log(channelData);
  return createData(TABLE_NAME, channelData);
}

/**
 * Cập nhật kênh chatbot
 * @param {string} channelId - ID của kênh
 * @param {Object} channelData - Dữ liệu cập nhật
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function updateChatbotChannel(channelId, channelData) {
  if (!channelId) return { success: false, error: 'Channel ID is required', data: null };

  return updateData(TABLE_NAME, channelData, { id: channelId });
}

/**
 * Xóa kênh chatbot
 * @param {string} channelId - ID của kênh
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function deleteChatbotChannel(channelId) {
  if (!channelId) return { success: false, error: 'Channel ID is required', data: null };

  return deleteData(TABLE_NAME, { id: channelId });
}

/**
 * Hook để lấy danh sách kênh chatbot
 * @param {Object} options - Các tùy chọn truy vấn
 * @returns {Object} - Kết quả và các hàm tương tác
 */
export function useChatbotChannels(options = {}) {
  const [channels, setChannels] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // Lấy dữ liệu kênh với các tùy chọn ổn định
  const channelOptions = useMemo(() => ({ ...options }), [options]);

  // Sử dụng ref để theo dõi nếu component đã unmount
  const isMounted = useRef(true);

  const fetchChannels = useCallback(async () => {
    // Tránh gọi API nhiều lần liên tiếp
    if (isLoading) return;

    setIsLoading(true);
    try {
      const result = await getChatbotChannels(channelOptions);
      // Kiểm tra nếu component vẫn mounted trước khi cập nhật state
      if (isMounted.current) {
        if (result.success) {
          setChannels(result.data || []);
        } else {
          setError(result.error);
        }
      }
    } catch (err) {
      if (isMounted.current) {
        setError(err.message || 'Failed to fetch chatbot channels');
      }
    } finally {
      if (isMounted.current) {
        setIsLoading(false);
      }
    }
  }, [channelOptions, isLoading]);

  // Tải dữ liệu khi component được mount hoặc options thay đổi
  useEffect(() => {
    // Đánh dấu component đã mount
    isMounted.current = true;

    // Chỉ gọi API khi không đang loading
    if (!isLoading) {
      fetchChannels();
    }

    // Cleanup function để đánh dấu component đã unmount
    return () => {
      isMounted.current = false;
    };
  }, [fetchChannels, isLoading]);

  // Hàm để làm mới dữ liệu
  const mutate = useCallback(() => {
    // Chỉ gọi API khi không đang loading
    if (!isLoading) {
      fetchChannels();
    }
  }, [fetchChannels, isLoading]);

  return {
    channels,
    isLoading,
    error,
    mutate,
  };
}

/**
 * Hook để lấy thông tin kênh chatbot theo ID
 * @param {string} channelId - ID của kênh
 * @returns {Object} - Kết quả và các hàm tương tác
 */
export function useChatbotChannel(channelId) {
  const [channel, setChannel] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // Sử dụng useMemo để tránh re-render không cần thiết
  const id = useMemo(() => channelId, [channelId]);

  // Sử dụng ref để theo dõi nếu component đã unmount
  const isMounted = useRef(true);

  const fetchChannel = useCallback(async () => {
    // Tránh gọi API nhiều lần liên tiếp
    if (isLoading) return;

    if (!id) {
      setChannel(null);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    try {
      const result = await getChatbotChannelById(id);
      // Kiểm tra nếu component vẫn mounted trước khi cập nhật state
      if (isMounted.current) {
        if (result.success) {
          setChannel(result.data);
        } else {
          setError(result.error);
        }
      }
    } catch (err) {
      if (isMounted.current) {
        setError(err.message || 'Failed to fetch chatbot channel');
      }
    } finally {
      if (isMounted.current) {
        setIsLoading(false);
      }
    }
  }, [id, isLoading]);

  // Tải dữ liệu khi component được mount hoặc id thay đổi
  useEffect(() => {
    // Đánh dấu component đã mount
    isMounted.current = true;

    // Chỉ gọi API khi không đang loading
    if (!isLoading) {
      fetchChannel();
    }

    // Cleanup function để đánh dấu component đã unmount
    return () => {
      isMounted.current = false;
    };
  }, [fetchChannel, isLoading]);

  // Hàm để làm mới dữ liệu
  const mutate = useCallback(() => {
    // Chỉ gọi API khi không đang loading
    if (!isLoading) {
      fetchChannel();
    }
  }, [fetchChannel, isLoading]);

  return {
    channel,
    isLoading,
    error,
    mutate,
  };
}

/**
 * Hook để quản lý các thao tác với kênh chatbot
 * @returns {Object} - Các hàm tương tác và trạng thái
 */
export function useChatbotChannelMutations() {
  const [isCreating, setIsCreating] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState(null);

  // Hàm tạo kênh chatbot
  const createChannel = useCallback(async (channelData) => {
    setIsCreating(true);
    setError(null);
    try {
      const result = await createChatbotChannel(channelData);
      if (!result.success) {
        setError(result.error);
      }
      return result;
    } catch (err) {
      setError(err.message || 'Failed to create chatbot channel');
      return { success: false, error: err.message, data: null };
    } finally {
      setIsCreating(false);
    }
  }, []);

  // Hàm cập nhật kênh chatbot
  const updateChannel = useCallback(async (channelId, channelData) => {
    setIsUpdating(true);
    setError(null);
    try {
      const result = await updateChatbotChannel(channelId, channelData);
      if (!result.success) {
        setError(result.error);
      }
      return result;
    } catch (err) {
      setError(err.message || 'Failed to update chatbot channel');
      return { success: false, error: err.message, data: null };
    } finally {
      setIsUpdating(false);
    }
  }, []);

  // Hàm xóa kênh chatbot
  const deleteChannel = useCallback(async (channelId) => {
    setIsDeleting(true);
    setError(null);
    try {
      const result = await deleteChatbotChannel(channelId);
      if (!result.success) {
        setError(result.error);
      }
      return result;
    } catch (err) {
      setError(err.message || 'Failed to delete chatbot channel');
      return { success: false, error: err.message, data: null };
    } finally {
      setIsDeleting(false);
    }
  }, []);

  return {
    createChannel,
    updateChannel,
    deleteChannel,
    isCreating,
    isUpdating,
    isDeleting,
    error,
  };
}
