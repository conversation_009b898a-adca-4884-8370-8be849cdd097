# Supabase Admin Client Setup

## <PERSON><PERSON> tả

Hệ thống admin client đư<PERSON><PERSON> sử dụng cho các tác vụ cần quyền cao như:
- Facebook webhooks 
- Cron jobs
- Background tasks
- System automation

Admin client sử dụng **service role key** để bypass RLS (Row Level Security) và có quyền truy cập đầy đủ vào database.

## Cấu hình Environment Variables

### 1. Thêm Service Role Key

Trong file `.env.local`:

```bash
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key

# Service Role Key cho admin operations
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

### 2. Lấy Service Role Key từ Supabase Dashboard

1. Truy cập [Supabase Dashboard](https://app.supabase.com)
2. Chọn project của bạn
3. Vào **Settings** > **API**
4. Copy **service_role secret** key

⚠️ **QUAN TRỌNG**: Service role key c<PERSON> quyền bypass mọi RLS policy. Không bao giờ expose key này ở client-side!

## Sử dụng trong Code

### Import Admin Client

```javascript
import { createAdminClient } from 'src/utils/supabase/server';
```

### Tạo Admin Client

```javascript
// Trong API routes hoặc server-side functions
const supabase = createAdminClient();

// Có thể truy cập mọi table mà không bị RLS block
const { data, error } = await supabase
  .from('any_table')
  .select('*')
  .eq('condition', 'value');
```

### So sánh với Regular Client

```javascript
// Regular client (có RLS)
import { createClient } from 'src/utils/supabase/server';
const supabase = await createClient(); // Cần user authentication

// Admin client (bypass RLS)  
import { createAdminClient } from 'src/utils/supabase/server';
const supabase = createAdminClient(); // Không cần authentication
```

## Use Cases

### 1. Facebook Webhooks
```javascript
// src/app/api/facebook-webhooks/route.js
export async function POST(request) {
  const supabase = createAdminClient();
  
  // Có thể access facebook_accounts, facebook_activity_logs
  // mà không cần user login
}
```

### 2. Background Jobs
```javascript
// src/app/api/cron/cleanup/route.js
export async function GET() {
  const supabase = createAdminClient();
  
  // Cleanup expired data
  await supabase
    .from('expired_sessions')
    .delete()
    .lt('expires_at', new Date().toISOString());
}
```

### 3. System Automation
```javascript
// src/app/api/automation/route.js
export async function POST() {
  const supabase = createAdminClient();
  
  // Bulk operations, migrations, etc.
}
```

## Security Best Practices

1. **Chỉ sử dụng server-side**: Không bao giờ dùng admin client ở client components
2. **Validate inputs**: Luôn validate dữ liệu đầu vào khi sử dụng admin client
3. **Logging**: Log tất cả admin operations để audit
4. **Environment protection**: Đảm bảo service role key không leak ra production logs

## Database Schema Requirements

Admin client sẽ access các table sau trong Facebook integration:

```sql
-- Facebook accounts storage
CREATE TABLE facebook_accounts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID REFERENCES tenants(id),
  page_id VARCHAR NOT NULL,
  page_name VARCHAR,
  user_access_token TEXT,
  page_access_token TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Activity logging
CREATE TABLE facebook_activity_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  page_id VARCHAR NOT NULL,
  activity VARCHAR NOT NULL,
  metadata JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

## Troubleshooting

### Error: "SUPABASE_SERVICE_ROLE_KEY is required"
- Kiểm tra file `.env.local` có chứa `SUPABASE_SERVICE_ROLE_KEY`
- Restart development server sau khi thêm env variable

### Error: "Invalid API key"
- Đảm bảo service role key được copy chính xác từ Supabase dashboard
- Không nhầm lẫn với anon key hoặc public key

### RLS vẫn block requests
- Kiểm tra có sử dụng `createAdminClient()` thay vì `createClient()`
- Verify service role key có quyền bypass RLS trong Supabase dashboard 