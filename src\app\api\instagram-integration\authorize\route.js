import { NextResponse } from 'next/server';

// Instagram OAuth configuration
const INSTAGRAM_APP_ID = process.env.INSTAGRAM_APP_ID;
const INSTAGRAM_REDIRECT_URI = process.env.INSTAGRAM_REDIRECT_URI;

// Instagram Business API scopes (2025 updated)
const INSTAGRAM_SCOPES = [
    'instagram_business_basic',
    'instagram_business_manage_messages',
    'instagram_business_manage_comments',
    'instagram_business_content_publish'
].join(',');

/**
 * GET /api/instagram-integration/authorize
 * Initiate Instagram OAuth flow - redirect to Instagram authorization
 * POPUP WINDOW COMPATIBLE - works with popup OAuth flow
 */
export async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const returnUrl = searchParams.get('returnUrl') || '/dashboard/mooly-chatbot/social-media-integration';

        // Validate required environment variables
        if (!INSTAGRAM_APP_ID) {
            return NextResponse.json(
                { error: 'Instagram App ID not configured' },
                { status: 500 }
            );
        }

        if (!INSTAGRAM_REDIRECT_URI) {
            return NextResponse.json(
                { error: 'Instagram Redirect URI not configured. Please set INSTAGRAM_REDIRECT_URI environment variable.' },
                { status: 500 }
            );
        }

        // Use configured redirect URI instead of dynamic origin
        const redirectUri = INSTAGRAM_REDIRECT_URI;

        // Build Instagram OAuth URL (Business Login 2025 - use www.instagram.com for authorization)
        const authUrl = new URL('https://www.instagram.com/oauth/authorize');
        authUrl.searchParams.set('client_id', INSTAGRAM_APP_ID);
        authUrl.searchParams.set('redirect_uri', redirectUri);
        authUrl.searchParams.set('response_type', 'code');
        authUrl.searchParams.set('scope', INSTAGRAM_SCOPES);
        
        // Add state parameter for security and return URL tracking
        const state = Buffer.from(JSON.stringify({ 
            returnUrl,
            timestamp: Date.now(),
            isPopup: true // Indicate this is popup flow
        })).toString('base64');
        authUrl.searchParams.set('state', state);

        console.log('🚀 Instagram OAuth redirect (popup flow):', {
            authUrl: authUrl.toString(),
            scopes: INSTAGRAM_SCOPES,
            redirectUri,
            isPopup: true
        });

        // Redirect to Instagram OAuth
        return NextResponse.redirect(authUrl.toString());

    } catch (error) {
        console.error('❌ Instagram OAuth initiation error:', error);
        
        return NextResponse.json(
            { 
                error: 'Failed to initiate Instagram OAuth',
                message: error.message
            },
            { status: 500 }
        );
    }
} 