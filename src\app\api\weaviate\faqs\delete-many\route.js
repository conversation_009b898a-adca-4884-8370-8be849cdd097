import { NextResponse } from 'next/server';

/**
 * API route để xóa nhiều FAQs cùng lúc trong Weaviate
 * @param {Request} request - <PERSON><PERSON><PERSON> cầu HTTP
 * @returns {Promise<NextResponse>} - <PERSON><PERSON><PERSON> hồi HTTP
 */
export async function DELETE(request) {
  try {
    const { supabase_ids, tenant_id } = await request.json();

    // Kiểm tra dữ liệu đầu vào
    if (!supabase_ids || !Array.isArray(supabase_ids) || supabase_ids.length === 0) {
      return NextResponse.json({ message: 'No supabase IDs to delete' }, { status: 200 });
    }

    if (!tenant_id) {
      return NextResponse.json(
        { error: 'tenant_id là bắt buộc' },
        { status: 400 }
      );
    }

    // Lấy URL Backend từ biến môi trường
    const BACKEND_API_URL = process.env.BACKEND_API_URL || 'http://localhost:3000';

    // Gọi API Weaviate Backend
    const response = await fetch(`${BACKEND_API_URL}/api/weaviate/faqs/delete-many`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        supabase_ids,
        tenant_id,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      return NextResponse.json(
        { error: errorData.message || 'Failed to delete FAQs from Weaviate' },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in FAQs delete-many API route:', error);
    return NextResponse.json({ error: error.message || 'Internal server error' }, { status: 500 });
  }
} 