'use client';

import { useState } from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

import Box from '@mui/material/Box';
import IconButton from '@mui/material/IconButton';
import Tooltip from '@mui/material/Tooltip';
import Paper from '@mui/material/Paper';
import Fade from '@mui/material/Fade';
import { alpha } from '@mui/material/styles';

import { Iconify } from 'src/components/iconify';

import LeadsKanbanColumn from './leads-kanban-column';

// =====================================================
// COMPONENT
// =====================================================

export default function SortableKanbanColumn({
  column,
  tasks,
  disabled,
  onAddStageAfter,
  onEditStage,
  onDeleteStage,
  children,
  ...other
}) {
  const [isHovered, setIsHovered] = useState(false);
  const [isAddHovered, setIsAddHovered] = useState(false);

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: column.id,
    data: {
      type: 'Column',
      column,
    },
    disabled: disabled || column.id === 'uncategorized', // Không cho drag uncategorized column
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const canAddStage = column.id !== 'uncategorized'; // Không cho add stage sau uncategorized
  const canDragColumn = !disabled && column.id !== 'uncategorized';

  return (
    <Box
      ref={setNodeRef}
      style={style}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      sx={{
        position: 'relative',
        opacity: isDragging ? 0.4 : 1,
        transform: isDragging ? 'rotate(5deg)' : 'none',
        transition: 'all 0.2s ease-in-out',
        '&:hover': {
          '& .add-stage-button': {
            opacity: canAddStage ? 1 : 0,
          },
          '& .stage-management-buttons': {
            opacity: 1,
          },
          transform: isDragging ? 'rotate(5deg)' : 'translateY(-2px)',
          boxShadow: isDragging ? 'none' : '0 8px 24px rgba(0,0,0,0.12)',
        },
      }}
    >
      {/* Main Kanban Column */}
      <LeadsKanbanColumn
        column={column}
        tasks={tasks}
        dragAttributes={canDragColumn ? attributes : {}}
        dragListeners={canDragColumn ? listeners : {}}
        {...other}
      >
        {children}
      </LeadsKanbanColumn>

      {/* Add Stage Button - hiện khi hover */}
      {canAddStage && (
        <Fade in={isHovered}>
          <Box
            className="add-stage-button"
            sx={{
              position: 'absolute',
              top: '50%',
              right: -20,
              transform: 'translateY(-50%)',
              zIndex: 10,
              opacity: 0,
              transition: 'opacity 0.2s ease-in-out',
            }}
          >
            <Tooltip title="Thêm giai đoạn mới phía sau" placement="top">
              <Paper
                elevation={isAddHovered ? 12 : 6}
                onMouseEnter={() => setIsAddHovered(true)}
                onMouseLeave={() => setIsAddHovered(false)}
                sx={{
                  borderRadius: '50%',
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                  backgroundColor: 'primary.main',
                  border: '2px solid',
                  borderColor: 'primary.light',
                  '&:hover': {
                    backgroundColor: 'primary.dark',
                    borderColor: 'primary.main',
                    transform: 'scale(1.15) rotate(90deg)',
                  },
                }}
              >
                <IconButton
                  size="small"
                  onClick={() => onAddStageAfter?.(column.id)}
                  sx={{
                    color: 'primary.contrastText',
                    padding: 1,
                    '&:hover': {
                      backgroundColor: 'transparent',
                    },
                  }}
                >
                  <Iconify icon="solar:add-circle-bold" width={20} />
                </IconButton>
              </Paper>
            </Tooltip>
          </Box>
        </Fade>
      )}

      {/* Stage Management Buttons - hiện khi hover column header */}
      {column.id !== 'uncategorized' && (
        <Fade in={isHovered}>
          <Box
            sx={{
              position: 'absolute',
              top: 8,
              right: 8,
              zIndex: 10,
              opacity: isHovered ? 1 : 0,
              transition: 'all 0.2s ease-in-out',
              display: 'flex',
              gap: 0.5,
              transform: isHovered ? 'translateY(0)' : 'translateY(-4px)',
            }}
            className="stage-management-buttons"
          >
            {/* Edit Stage Button */}
            <Tooltip title="Chỉnh sửa giai đoạn">
              <IconButton
                size="small"
                onClick={() => onEditStage?.(column)}
                sx={{
                  backgroundColor: alpha('#fff', 0.9),
                  '&:hover': {
                    backgroundColor: alpha('#fff', 1),
                  },
                  width: 28,
                  height: 28,
                }}
              >
                <Iconify icon="solar:pen-bold" width={14} />
              </IconButton>
            </Tooltip>

            {/* Delete Stage Button */}
            <Tooltip title="Xóa giai đoạn">
              <IconButton
                size="small"
                onClick={() => onDeleteStage?.(column)}
                sx={{
                  backgroundColor: alpha('#ff5722', 0.1),
                  color: 'error.main',
                  '&:hover': {
                    backgroundColor: alpha('#ff5722', 0.2),
                  },
                  width: 28,
                  height: 28,
                }}
              >
                <Iconify icon="solar:trash-bin-minimalistic-bold" width={14} />
              </IconButton>
            </Tooltip>

            {/* Drag Handle */}
            {canDragColumn && (
              <Tooltip title="Kéo để sắp xếp lại thứ tự giai đoạn">
                <IconButton
                  size="small"
                  {...attributes}
                  {...listeners}
                  sx={{
                    backgroundColor: alpha('#2196f3', 0.15),
                    color: 'primary.main',
                    cursor: 'grab',
                    border: '1px solid',
                    borderColor: alpha('#2196f3', 0.3),
                    '&:hover': {
                      backgroundColor: alpha('#2196f3', 0.25),
                      borderColor: 'primary.main',
                      transform: 'scale(1.05)',
                    },
                    '&:active': {
                      cursor: 'grabbing',
                      transform: 'scale(0.95)',
                    },
                    width: 28,
                    height: 28,
                    transition: 'all 0.2s ease-in-out',
                  }}
                >
                  <Iconify icon="solar:sort-vertical-bold" width={14} />
                </IconButton>
              </Tooltip>
            )}
          </Box>
        </Fade>
      )}

      {/* Drag Overlay Effect */}
      {isDragging && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: alpha('#2196f3', 0.1),
            border: '2px dashed',
            borderColor: 'primary.main',
            borderRadius: 'var(--column-radius)',
            pointerEvents: 'none',
            zIndex: 5,
          }}
        />
      )}
    </Box>
  );
} 