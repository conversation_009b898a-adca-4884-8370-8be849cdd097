'use client';

import { useState, useEffect, useCallback } from 'react';

import { checkSKUExists } from 'src/actions/mooly-chatbot/product-api';

/**
 * Hook để validate SKU real-time
 * @param {string} sku - SKU cần validate
 * @param {string} excludeId - ID sản phẩm cần lo<PERSON> trừ (cho tr<PERSON>ờ<PERSON> hợp edit)
 * @param {number} debounceMs - Thời gian debounce (ms)
 * @returns {Object} - { isValidating, isValid, error }
 */
export function useSKUValidation(sku, excludeId = null, debounceMs = 500) {
  const [isValidating, setIsValidating] = useState(false);
  const [isValid, setIsValid] = useState(true);
  const [error, setError] = useState(null);

  const validateSKU = useCallback(async (skuToValidate) => {
    if (!skuToValidate || skuToValidate.trim() === '') {
      setIsValid(true); // Cho phép SKU trống để tự động tạo
      setError(null);
      return;
    }

    setIsValidating(true);
    setError(null);

    try {
      const exists = await checkSKUExists(skuToValidate, excludeId);

      if (exists) {
        setIsValid(false);
        setError('SKU đã tồn tại. Vui lòng sử dụng SKU khác.');
      } else {
        setIsValid(true);
        setError(null);
      }
    } catch (err) {
      console.error('Error validating SKU:', err);
      setIsValid(false);
      setError('Không thể kiểm tra SKU. Vui lòng thử lại.');
    } finally {
      setIsValidating(false);
    }
  }, [excludeId]);

  useEffect(() => {
    // Nếu sku là empty string, không validate (dùng để tắt validation)
    if (sku === '') {
      setIsValidating(false);
      setIsValid(true);
      setError(null);
      return;
    }

    if (!sku) {
      setIsValid(true); // Cho phép SKU trống để tự động tạo
      setError(null);
      return;
    }

    const timer = setTimeout(() => {
      validateSKU(sku);
    }, debounceMs);

    return () => clearTimeout(timer);
  }, [sku, validateSKU, debounceMs]);

  return {
    isValidating,
    isValid,
    error,
    validateSKU: () => validateSKU(sku)
  };
}

/**
 * Hook để tạo SKU tự động và validate
 * @param {string} productName - Tên sản phẩm
 * @param {string} excludeId - ID sản phẩm cần loại trừ
 * @returns {Object} - { generateSKU, isValidating, isValid, error }
 */
export function useAutoSKU(productName, excludeId = null) {
  const [generatedSKU, setGeneratedSKU] = useState('');

  const generateSKU = useCallback((name = productName) => {
    if (!name) return '';

    // Tạo slug từ tên sản phẩm
    const baseSlug = name
      .toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .replace(/[^\w\s-]/g, ' ')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-+|-+$/g, '');

    // Thêm timestamp để đảm bảo unique
    const timestamp = Date.now().toString().slice(-6);

    // Tạo SKU: SLUG-TIMESTAMP (viết hoa)
    const newSKU = `${baseSlug}-${timestamp}`.toUpperCase();
    setGeneratedSKU(newSKU);
    return newSKU;
  }, [productName]);

  const validation = useSKUValidation(generatedSKU, excludeId);

  return {
    generatedSKU,
    generateSKU,
    ...validation
  };
}
