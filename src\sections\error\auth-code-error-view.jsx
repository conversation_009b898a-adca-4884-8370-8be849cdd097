'use client';

import { useSearchParams } from 'next/navigation';

import Box from '@mui/material/Box';
import Alert from '@mui/material/Alert';
import Button from '@mui/material/Button';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';

import { paths } from 'src/routes/paths';
import { RouterLink } from 'src/routes/components';

import { SimpleLayout } from 'src/layouts/simple';

// ----------------------------------------------------------------------

export function AuthCodeErrorView() {
  const searchParams = useSearchParams();
  const error = searchParams.get('error');
  const errorDescription = searchParams.get('error_description');

  // Tạo thông báo lỗi dựa trên error code
  const getErrorInfo = () => {
    switch (error) {
      case 'access_denied':
        return {
          title: '<PERSON><PERSON><PERSON> cập bị từ chối',
          message: '<PERSON><PERSON><PERSON> đã từ chối cấp quyền truy cập. Vui lòng thử lại và cho phép quyền truy cập.',
          severity: 'warning',
        };
      case 'exchange_failed':
        return {
          title: 'Lỗi xác thực',
          message: 'Không thể xác thực với Google. Vui lòng thử lại.',
          severity: 'error',
        };
      case 'user_setup_failed':
        return {
          title: 'Lỗi thiết lập tài khoản',
          message: 'Không thể thiết lập tài khoản của bạn. Vui lòng thử lại sau.',
          severity: 'error',
        };
      case 'account_inactive':
        return {
          title: 'Tài khoản bị khóa',
          message: 'Tài khoản của bạn đã bị tạm khóa. Vui lòng liên hệ hỗ trợ.',
          severity: 'error',
        };
      case 'callback_failed':
        return {
          title: 'Lỗi callback',
          message: 'Có lỗi xảy ra trong quá trình xử lý đăng nhập. Vui lòng thử lại.',
          severity: 'error',
        };
      case 'no_code':
        return {
          title: 'Thiếu mã xác thực',
          message: 'Không nhận được mã xác thực từ Google. Vui lòng thử lại.',
          severity: 'warning',
        };
      default:
        return {
          title: 'Lỗi xác thực',
          message: 'Đã xảy ra lỗi trong quá trình xác thực. Vui lòng thử lại.',
          severity: 'error',
        };
    }
  };

  const errorInfo = getErrorInfo();

  return (
    <SimpleLayout content={{ compact: true }}>
      <Container>
        <Box
          sx={{
            py: 12,
            maxWidth: 480,
            mx: 'auto',
            display: 'flex',
            minHeight: '100vh',
            textAlign: 'center',
            alignItems: 'center',
            flexDirection: 'column',
            justifyContent: 'center',
          }}
        >
          <Typography variant="h3" sx={{ mb: 3 }}>
            {errorInfo.title}
          </Typography>

          <Typography sx={{ color: 'text.secondary', mb: 3 }}>
            {errorDescription || errorInfo.message}
          </Typography>

          {error && (
            <Alert severity={errorInfo.severity} sx={{ mb: 3, width: '100%' }}>
              <Typography variant="body2">
                <strong>Mã lỗi:</strong> {error}
              </Typography>
              {errorDescription && (
                <Typography variant="body2" sx={{ mt: 1 }}>
                  <strong>Chi tiết:</strong> {errorDescription}
                </Typography>
              )}
            </Alert>
          )}

          <Box sx={{ display: 'flex', gap: 2, flexDirection: { xs: 'column', sm: 'row' } }}>
            <Button
              component={RouterLink}
              href={paths.auth.supabase.signIn}
              size="large"
              variant="contained"
            >
              Thử lại đăng nhập
            </Button>

            <Button
              component={RouterLink}
              href={paths.auth.supabase.signUp}
              size="large"
              variant="outlined"
            >
              Đăng ký tài khoản
            </Button>
          </Box>

          {error === 'account_inactive' && (
            <Typography variant="body2" sx={{ mt: 3, color: 'text.secondary' }}>
              Cần hỗ trợ? Liên hệ: <EMAIL>
            </Typography>
          )}
        </Box>
      </Container>
    </SimpleLayout>
  );
}
