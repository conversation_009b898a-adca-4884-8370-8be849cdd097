import { NextResponse } from 'next/server';

import { withTenantAuth, validateTenantId } from 'src/utils/server-auth';
import storageService from 'src/actions/mooly-chatbot/storage-service';

/**
 * API route để xóa nhiều sản phẩm cùng lúc
 * @param {Request} request - <PERSON><PERSON>u cầu HTTP
 * @returns {Promise<NextResponse>} - Phản hồi HTTP
 */
export const DELETE = withTenantAuth(async (request, { tenantId, userId }) => {
  try {
    // Validate request body và kiểm tra tenant_id
    const validation = await validateTenantId(request, tenantId);
    if (!validation.valid) {
      return NextResponse.json({ error: validation.error }, { status: validation.status });
    }

    const { product_ids } = validation.body;

    // Kiểm tra dữ liệu đầu vào
    if (!product_ids || !Array.isArray(product_ids) || product_ids.length === 0) {
      return NextResponse.json(
        { error: 'Missing or invalid product_ids array' },
        { status: 400 }
      );
    }

    // Giới hạn số lượng sản phẩm có thể xóa cùng lúc
    if (product_ids.length > 100) {
      return NextResponse.json(
        { error: 'Cannot delete more than 100 products at once' },
        { status: 400 }
      );
    }

    // Kiểm tra tất cả sản phẩm có thuộc tenant hiện tại không
    const { data: existingProducts, error: checkError } = await request.supabase
      .from('products')
      .select('id, name, sku, images')
      .eq('tenant_id', tenantId)
      .in('id', product_ids);

    if (checkError) {
      console.error('Error checking products:', checkError);
      return NextResponse.json(
        { error: 'Failed to verify products', details: checkError.message },
        { status: 500 }
      );
    }

    // Kiểm tra xem có sản phẩm nào không tồn tại hoặc không thuộc tenant
    const foundIds = existingProducts.map(p => p.id);
    const notFoundIds = product_ids.filter(id => !foundIds.includes(id));

    if (notFoundIds.length > 0) {
      return NextResponse.json(
        { 
          error: 'Some products not found or access denied',
          not_found_ids: notFoundIds
        },
        { status: 404 }
      );
    }

    // Bắt đầu transaction để xóa sản phẩm và các dữ liệu liên quan
    const results = {
      success: [],
      failed: [],
      total: product_ids.length
    };

    // Xóa từng sản phẩm một để đảm bảo RLS và xử lý lỗi riêng biệt
    for (const productId of product_ids) {
      try {
        const product = existingProducts.find(p => p.id === productId);
        
        // 1. Xóa product variants trước
        const { error: variantError } = await request.supabase
          .from('product_variants')
          .delete()
          .eq('product_id', productId)
          .eq('tenant_id', tenantId);

        if (variantError) {
          console.error(`Error deleting variants for product ${productId}:`, variantError);
          results.failed.push({
            id: productId,
            name: product?.name,
            error: 'Failed to delete product variants'
          });
          continue;
        }

        // 2. Xóa inventory transactions
        const { error: inventoryError } = await request.supabase
          .from('inventory_transactions')
          .delete()
          .eq('product_id', productId)
          .eq('tenant_id', tenantId);

        if (inventoryError) {
          console.error(`Error deleting inventory for product ${productId}:`, inventoryError);
          // Không fail vì inventory có thể không tồn tại
        }

        // 3. Xóa sản phẩm chính
        const { error: deleteError } = await request.supabase
          .from('products')
          .delete()
          .eq('id', productId)
          .eq('tenant_id', tenantId);

        if (deleteError) {
          console.error(`Error deleting product ${productId}:`, deleteError);
          results.failed.push({
            id: productId,
            name: product?.name,
            error: deleteError.message
          });
          continue;
        }

        results.success.push({
          id: productId,
          name: product?.name,
          sku: product?.sku
        });

        // 4. Xóa hình ảnh từ storage (không blocking)
        if (product?.images && product.images.length > 0) {
          try {
            console.log(`Deleting images for product ${productId}:`, product.images);
            const deleteResult = await storageService.deleteFilesWithPublicUrl('public', product.images);

            if (deleteResult.success) {
              console.log(`Successfully deleted images for product ${productId}:`, product.images);
            } else {
              console.warn(`Failed to delete some images for product ${productId}:`, deleteResult.error);
            }
          } catch (storageError) {
            console.error(`Error deleting images for product ${productId}:`, storageError);
            // Không fail vì storage error không ảnh hưởng đến database
          }
        }

        // 5. Xóa khỏi Weaviate (không blocking)
        try {
          const BACKEND_API_URL = process.env.BACKEND_API_URL || 'http://localhost:3000';
          await fetch(`${BACKEND_API_URL}/api/weaviate/products/delete-by-id`, {
            method: 'DELETE',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              product_id: productId,
              tenant_id: tenantId
            }),
          });
        } catch (weaviateError) {
          console.error(`Error deleting from Weaviate for product ${productId}:`, weaviateError);
          // Không fail vì Weaviate error không ảnh hưởng đến database
        }

      } catch (error) {
        console.error(`Unexpected error deleting product ${productId}:`, error);
        results.failed.push({
          id: productId,
          name: existingProducts.find(p => p.id === productId)?.name,
          error: error.message
        });
      }
    }

    // Trả về kết quả
    const response = {
      success: true,
      message: `Đã xử lý ${results.total} sản phẩm`,
      results: {
        total: results.total,
        success_count: results.success.length,
        failed_count: results.failed.length,
        success: results.success,
        failed: results.failed
      }
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Error in bulk delete route:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
});
