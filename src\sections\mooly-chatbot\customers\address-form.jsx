'use client';

import PropTypes from 'prop-types';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMemo, useEffect, useCallback } from 'react';
import { useForm, Controller } from 'react-hook-form';

import Card from '@mui/material/Card';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import Checkbox from '@mui/material/Checkbox';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import LoadingButton from '@mui/lab/LoadingButton';
import DialogTitle from '@mui/material/DialogTitle';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import FormControlLabel from '@mui/material/FormControlLabel';

import { toast } from 'src/components/snackbar';
import { Form } from 'src/components/hook-form';

import { AddressSchema, defaultAddressValues } from './customer-schema';

export function AddressForm({ open, onClose, address, customerId, onSubmit, isSubmitting }) {
  const defaultValues = useMemo(() => {
    if (address) {
      return {
        ...defaultAddressValues,
        ...address,
        customerId: customerId || address.customerId,
      };
    }
    return {
      ...defaultAddressValues,
      customerId,
    };
  }, [address, customerId]);

  const methods = useForm({
    resolver: zodResolver(AddressSchema),
    defaultValues,
  });

  const {
    reset,
    control,
    handleSubmit,
    formState: { isSubmitting: formIsSubmitting },
  } = methods;

  useEffect(() => {
    if (open) {
      reset(defaultValues);
    }
  }, [open, reset, defaultValues]);

  const onSubmitForm = handleSubmit(async (data) => {
    try {
      console.log('Address form data:', data);

      // Validation bổ sung
      if (!data.address || data.address.trim() === '') {
        toast.error('❌ Địa chỉ là bắt buộc');
        return;
      }

      if (!data.customerId) {
        toast.error('❌ Thiếu thông tin khách hàng');
        return;
      }

      // Làm sạch dữ liệu
      const cleanData = {
        ...data,
        fullName: data.fullName || '',
        phone: data.phone || '',
        address: data.address.trim(),
        province: data.province || '',
        district: data.district || '',
        ward: data.ward || '',
        notes: data.notes || '',
        isDefault: data.isDefault || false,
      };

      console.log('Clean address data:', cleanData);

      const result = await onSubmit(cleanData);

      // Xử lý lỗi từ API
      if (!result?.success && result?.error) {
        console.error('Address API Error:', result.error);

        // Xử lý lỗi validation từ server
        if (result.error.field) {
          methods.setError(result.error.field, {
            type: 'manual',
            message: result.error.message || 'Dữ liệu không hợp lệ',
          });
          toast.error(`❌ ${result.error.message || 'Dữ liệu không hợp lệ'}`);
          return;
        }

        // Xử lý các lỗi khác
        const errorMessage = result.error.message || 'Có lỗi xảy ra khi xử lý địa chỉ';
        toast.error(`❌ ${errorMessage}`);
        return;
      }

      // Nếu thành công
      reset();
      onClose();
      
      const successMessage = address 
        ? '✅ Cập nhật địa chỉ thành công!' 
        : '✅ Thêm địa chỉ thành công!';
      toast.success(successMessage);
      
    } catch (error) {
      console.error('Address form submission error:', error);
      const errorMessage = error.message || 'Có lỗi không xác định xảy ra';
      toast.error(`❌ ${errorMessage}`);
    }
  });

  return (
    <Dialog
      fullWidth
      maxWidth="sm"
      open={open}
      onClose={onClose}
      slotProps={{
        paper: {
          sx: { maxHeight: '90vh' },
        },
      }}
    >
      <DialogTitle>
        {address ? 'Chỉnh sửa địa chỉ' : 'Thêm địa chỉ mới'}
      </DialogTitle>

      <DialogContent dividers sx={{ pt: 1, pb: 0, border: 'none' }}>
        <Form methods={methods} onSubmit={onSubmitForm}>
          <Card sx={{ p: 3 }}>
            <Stack spacing={3}>
              <Grid container spacing={2}>
                <Grid size={{ xs: 12, sm: 6 }}>
                  <Controller
                    name="fullName"
                    control={control}
                    render={({ field, fieldState: { error } }) => (
                      <TextField
                        {...field}
                        fullWidth
                        label="Tên người nhận"
                        placeholder="Để trống nếu giống với khách hàng"
                        error={!!error}
                        helperText={error?.message}
                      />
                    )}
                  />
                </Grid>

                <Grid size={{ xs: 12, sm: 6 }}>
                  <Controller
                    name="phone"
                    control={control}
                    render={({ field, fieldState: { error } }) => (
                      <TextField
                        {...field}
                        fullWidth
                        label="Số điện thoại"
                        placeholder="Để trống nếu giống với khách hàng"
                        error={!!error}
                        helperText={error?.message}
                      />
                    )}
                  />
                </Grid>

                <Grid size={{ xs: 12 }}>
                  <Controller
                    name="address"
                    control={control}
                    render={({ field, fieldState: { error } }) => (
                      <TextField
                        {...field}
                        fullWidth
                        label="Địa chỉ chi tiết *"
                        placeholder="Số nhà, tên đường..."
                        error={!!error}
                        helperText={error?.message}
                      />
                    )}
                  />
                </Grid>

                <Grid size={{ xs: 12, sm: 4 }}>
                  <Controller
                    name="province"
                    control={control}
                    render={({ field }) => (
                      <TextField {...field} fullWidth label="Tỉnh/Thành phố" />
                    )}
                  />
                </Grid>

                <Grid size={{ xs: 12, sm: 4 }}>
                  <Controller
                    name="district"
                    control={control}
                    render={({ field }) => (
                      <TextField {...field} fullWidth label="Quận/Huyện" />
                    )}
                  />
                </Grid>

                <Grid size={{ xs: 12, sm: 4 }}>
                  <Controller
                    name="ward"
                    control={control}
                    render={({ field }) => (
                      <TextField {...field} fullWidth label="Phường/Xã" />
                    )}
                  />
                </Grid>

                <Grid size={{ xs: 12 }}>
                  <Controller
                    name="notes"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        fullWidth
                        label="Ghi chú"
                        multiline
                        rows={2}
                        placeholder="Ghi chú thêm về địa chỉ..."
                      />
                    )}
                  />
                </Grid>

                <Grid size={{ xs: 12 }}>
                  <FormControlLabel
                    control={
                      <Controller
                        name="isDefault"
                        control={control}
                        render={({ field: { value, ...field } }) => (
                          <Checkbox {...field} checked={value} />
                        )}
                      />
                    }
                    label="Đặt làm địa chỉ giao hàng mặc định"
                  />
                </Grid>
              </Grid>
            </Stack>
          </Card>
        </Form>
      </DialogContent>

      <DialogActions>
        <Button variant="outlined" color="inherit" onClick={onClose}>
          Hủy
        </Button>

        <LoadingButton
          type="submit"
          variant="contained"
          loading={isSubmitting || formIsSubmitting}
          onClick={onSubmitForm}
        >
          {address ? 'Cập nhật' : 'Thêm mới'}
        </LoadingButton>
      </DialogActions>
    </Dialog>
  );
}

AddressForm.propTypes = {
  address: PropTypes.object,
  customerId: PropTypes.string,
  isSubmitting: PropTypes.bool,
  onClose: PropTypes.func,
  onSubmit: PropTypes.func,
  open: PropTypes.bool,
};
