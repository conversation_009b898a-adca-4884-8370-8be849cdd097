'use client';

import { useState, useEffect } from 'react';

import {
  <PERSON>,
  Tab,
  Tabs,
  <PERSON><PERSON>,
  Dialog,
  Button,
  TextField,
  Typography,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Card,
  Chip,
  Grid,
} from '@mui/material';
import { LoadingButton } from '@mui/lab';

import { Iconify } from 'src/components/iconify';
import { toast } from 'src/components/snackbar';

// ----------------------------------------------------------------------

// Workflow templates
const workflowTemplates = {
  sales: {
    name: '<PERSON>uy trình <PERSON> hàng',
    description: 'Quy trình chuẩn cho việc chuyển đổi leads thành khách hàng',
    workflow: {
      stages: [
        {
          id: 'new',
          name: 'Lead mới',
          description: 'Lead vừa được tạo',
          actions: ['contact', 'qualify'],
          autoActions: [],
          duration: 24,
        },
        {
          id: 'contacted',
          name: '<PERSON><PERSON> liên hệ',
          description: 'Đã có liên hệ ban đầu',
          actions: ['follow_up', 'qualify', 'reject'],
          autoActions: ['schedule_follow_up'],
          duration: 72,
        },
        {
          id: 'qualified',
          name: 'Lead tiềm năng',
          description: 'Lead có tiềm năng mua hàng',
          actions: ['propose', 'nurture', 'convert'],
          autoActions: ['assign_sales_rep'],
          duration: 168,
        },
        {
          id: 'converted',
          name: 'Đã chuyển đổi',
          description: 'Lead đã thành khách hàng',
          actions: ['upsell', 'referral'],
          autoActions: ['add_to_crm'],
          duration: null,
        },
      ],
      notifications: {
        email: true,
        sms: false,
        slack: true,
      },
      scoring: {
        enabled: true,
        factors: [
          { field: 'company', weight: 0.3, description: 'Có công ty' },
          { field: 'email', weight: 0.2, description: 'Có email' },
          { field: 'phone', weight: 0.2, description: 'Có số điện thoại' },
          { field: 'source_quality', weight: 0.3, description: 'Chất lượng nguồn' },
        ],
      },
    },
  },
  support: {
    name: 'Quy trình Hỗ trợ',
    description: 'Quy trình xử lý yêu cầu hỗ trợ khách hàng',
    workflow: {
      stages: [
        {
          id: 'new',
          name: 'Yêu cầu mới',
          description: 'Yêu cầu hỗ trợ vừa được tạo',
          actions: ['acknowledge', 'categorize'],
          autoActions: ['send_auto_reply'],
          duration: 2,
        },
        {
          id: 'in_progress',
          name: 'Đang xử lý',
          description: 'Đang xử lý yêu cầu',
          actions: ['update', 'escalate', 'resolve'],
          autoActions: ['notify_customer'],
          duration: 24,
        },
        {
          id: 'resolved',
          name: 'Đã giải quyết',
          description: 'Yêu cầu đã được giải quyết',
          actions: ['follow_up', 'close'],
          autoActions: ['satisfaction_survey'],
          duration: 72,
        },
      ],
      notifications: {
        email: true,
        sms: true,
        slack: true,
      },
      scoring: {
        enabled: false,
      },
    },
  },
  custom: {
    name: 'Quy trình Tùy chỉnh',
    description: 'Tạo quy trình riêng cho doanh nghiệp',
    workflow: {
      stages: [],
      notifications: {
        email: false,
        sms: false,
        slack: false,
      },
      scoring: {
        enabled: false,
      },
    },
  },
};

// ----------------------------------------------------------------------

export default function LeadWorkflowDialog({ open, onClose }) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [currentTab, setCurrentTab] = useState(0);
  const [selectedTemplate, setSelectedTemplate] = useState('sales');
  const [workflowConfig, setWorkflowConfig] = useState('');

  useEffect(() => {
    if (open) {
      // Load current workflow config
      const template = workflowTemplates[selectedTemplate];
      setWorkflowConfig(JSON.stringify(template.workflow, null, 2));
    }
  }, [open, selectedTemplate]);

  const handleTemplateSelect = (templateKey) => {
    setSelectedTemplate(templateKey);
    const template = workflowTemplates[templateKey];
    setWorkflowConfig(JSON.stringify(template.workflow, null, 2));
  };

  const handleSave = async () => {
    try {
      setLoading(true);
      setError(null);

      // Validate JSON
      JSON.parse(workflowConfig);

      // Save workflow config (you would implement this API call)
      // const result = await saveWorkflowConfig(workflowConfig);

      toast.success('Lưu cấu hình workflow thành công!');
      onClose();
    } catch (err) {
      if (err instanceof SyntaxError) {
        setError('JSON không hợp lệ. Vui lòng kiểm tra lại cú pháp.');
      } else {
        setError(err.message);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      setError(null);
      onClose();
    }
  };

  const renderTemplateCard = (key, template) => (
    <Card
      key={key}
      sx={{
        p: 2,
        cursor: 'pointer',
        border: '2px solid',
        borderColor: selectedTemplate === key ? 'primary.main' : 'transparent',
        '&:hover': {
          borderColor: selectedTemplate === key ? 'primary.main' : 'primary.light',
        },
      }}
      onClick={() => handleTemplateSelect(key)}
    >
      <Stack spacing={1}>
        <Stack direction="row" alignItems="center" justifyContent="space-between">
          <Typography variant="subtitle1">{template.name}</Typography>
          {selectedTemplate === key && (
            <Chip label="Đã chọn" color="primary" size="small" />
          )}
        </Stack>
        <Typography variant="body2" sx={{ color: 'text.secondary' }}>
          {template.description}
        </Typography>
        <Typography variant="caption" sx={{ color: 'text.disabled' }}>
          {template.workflow.stages?.length || 0} giai đoạn
        </Typography>
      </Stack>
    </Card>
  );

  return (
    <Dialog 
      open={open} 
      onClose={handleClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: { height: '80vh' }
      }}
    >
      <DialogTitle>
        <Stack direction="row" alignItems="center" spacing={2}>
          <Iconify icon="solar:settings-bold" width={24} />
          <Typography variant="h6">Cấu hình Workflow JSON</Typography>
        </Stack>
      </DialogTitle>

      <DialogContent>
        <Box sx={{ width: '100%' }}>
          <Tabs value={currentTab} onChange={(e, newValue) => setCurrentTab(newValue)}>
            <Tab label="Chọn Template" />
            <Tab label="Cấu hình JSON" />
            <Tab label="Tài liệu" />
          </Tabs>

          <Box sx={{ mt: 3 }}>
            {/* Tab 1: Template Selection */}
            {currentTab === 0 && (
              <Stack spacing={3}>
                <Typography variant="body1">
                  Chọn template workflow phù hợp với quy trình kinh doanh của bạn:
                </Typography>

                <Grid container spacing={2}>
                  {Object.entries(workflowTemplates).map(([key, template]) =>
                    renderTemplateCard(key, template)
                  )}
                </Grid>

                <Alert severity="info">
                  <Typography variant="body2">
                    Bạn có thể tùy chỉnh workflow sau khi chọn template ở tab &quot;Cấu hình JSON&quot;
                  </Typography>
                </Alert>
              </Stack>
            )}

            {/* Tab 2: JSON Configuration */}
            {currentTab === 1 && (
              <Stack spacing={3}>
                {error && (
                  <Alert severity="error" onClose={() => setError(null)}>
                    {error}
                  </Alert>
                )}

                <Box>
                  <Typography variant="subtitle1" gutterBottom>
                    Cấu hình Workflow (JSON)
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                    Chỉnh sửa cấu hình workflow bằng JSON. Hệ thống sẽ tự động áp dụng quy trình này cho tất cả leads.
                  </Typography>
                </Box>

                <TextField
                  multiline
                  rows={20}
                  fullWidth
                  value={workflowConfig}
                  onChange={(e) => setWorkflowConfig(e.target.value)}
                  variant="outlined"
                  sx={{
                    '& .MuiInputBase-input': {
                      fontFamily: 'monospace',
                      fontSize: '0.875rem',
                    },
                  }}
                />
              </Stack>
            )}

            {/* Tab 3: Documentation */}
            {currentTab === 2 && (
              <Stack spacing={3}>
                <Typography variant="h6">Tài liệu Workflow Configuration</Typography>

                <Card sx={{ p: 3 }}>
                  <Typography variant="subtitle1" gutterBottom>
                    Cấu trúc JSON
                  </Typography>
                  <Box
                    component="pre"
                    sx={{
                      p: 2,
                      bgcolor: 'grey.100',
                      borderRadius: 1,
                      overflow: 'auto',
                      fontSize: '0.875rem',
                      fontFamily: 'monospace',
                    }}
                  >
{`{
  "stages": [
    {
      "id": "stage_id",
      "name": "Tên giai đoạn",
      "description": "Mô tả giai đoạn",
      "actions": ["action1", "action2"],
      "autoActions": ["auto_action1"],
      "duration": 24 // giờ
    }
  ],
  "notifications": {
    "email": true,
    "sms": false,
    "slack": true
  },
  "scoring": {
    "enabled": true,
    "factors": [
      {
        "field": "field_name",
        "weight": 0.3,
        "description": "Mô tả"
      }
    ]
  }
}`}
                  </Box>
                </Card>

                <Card sx={{ p: 3 }}>
                  <Typography variant="subtitle1" gutterBottom>
                    Các Actions có sẵn
                  </Typography>
                  <Grid container spacing={2}>
                    {[
                      'contact', 'qualify', 'follow_up', 'reject', 'propose', 
                      'nurture', 'convert', 'upsell', 'referral'
                    ].map((action) => (
                      <Grid item key={action}>
                        <Chip label={action} variant="outlined" />
                      </Grid>
                    ))}
                  </Grid>
                </Card>

                <Card sx={{ p: 3 }}>
                  <Typography variant="subtitle1" gutterBottom>
                    Auto Actions
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                    Các hành động được thực hiện tự động khi lead chuyển sang giai đoạn mới:
                  </Typography>
                  <Box component="ul" sx={{ mt: 1 }}>
                    <li>schedule_follow_up: Tự động đặt lịch follow-up</li>
                    <li>assign_sales_rep: Phân công nhân viên bán hàng</li>
                    <li>add_to_crm: Thêm vào hệ thống CRM</li>
                    <li>send_auto_reply: Gửi email tự động</li>
                    <li>notify_customer: Thông báo cho khách hàng</li>
                    <li>satisfaction_survey: Gửi khảo sát hài lòng</li>
                  </Box>
                </Card>
              </Stack>
            )}
          </Box>
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose} disabled={loading}>
          Hủy bỏ
        </Button>
        <LoadingButton
          variant="contained"
          loading={loading}
          onClick={handleSave}
          startIcon={<Iconify icon="solar:check-bold" />}
        >
          Lưu Workflow
        </LoadingButton>
      </DialogActions>
    </Dialog>
  );
} 