'use client';

/**
 * Comprehensive session cleanup utilities for Supabase multi-tenant authentication
 */

/**
 * Clear all Supabase authentication data from browser storage
 * Ensures complete session cleanup for multi-tenant isolation
 */
export function clearSupabaseSession() {
  if (typeof window === 'undefined') return;

  console.log('🧹 Starting comprehensive session cleanup...');

  // Step 1: Clear localStorage
  const authKeys = [
    'supabase.auth.token',
    'sb-access-token',
    'sb-refresh-token', 
    'supabase-auth-token',
    'supabase.auth.session',
    'supabase.session',
  ];

  authKeys.forEach(key => {
    localStorage.removeItem(key);
    sessionStorage.removeItem(key);
  });

  // Clear all keys that start with supabase
  Object.keys(localStorage).forEach(key => {
    if (key.startsWith('supabase') || key.startsWith('sb-')) {
      localStorage.removeItem(key);
    }
  });

  Object.keys(sessionStorage).forEach(key => {
    if (key.startsWith('supabase') || key.startsWith('sb-')) {
      sessionStorage.removeItem(key);
    }
  });

  // Step 2: Clear tenant-specific data for multi-tenant isolation
  const tenantKeys = [
    'tenant',
    'chatbot',
    'mooly',
    'user_tenant',
    'current_tenant',
  ];

  Object.keys(localStorage).forEach(key => {
    const lowerKey = key.toLowerCase();
    if (tenantKeys.some(tenantKey => lowerKey.includes(tenantKey))) {
      localStorage.removeItem(key);
    }
  });

  // Step 3: Clear cookies
  clearSupabaseCookies();

  console.log('✅ Session cleanup completed');
}

/**
 * Clear all Supabase authentication cookies
 * Handles different domain configurations
 */
export function clearSupabaseCookies() {
  if (typeof document === 'undefined') return;

  const authCookieNames = [
    'sb-access-token',
    'sb-refresh-token',
    'supabase-auth-token', 
    'supabase.auth.token',
    'supabase-session',
    'supabase.session',
  ];

  const hostname = window.location.hostname;
  const domain = hostname.split('.').slice(-2).join('.');

  authCookieNames.forEach(cookieName => {
    // Clear for current path
    document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
    
    // Clear for current domain
    document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=${hostname};`;
    
    // Clear for parent domain
    document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.${domain};`;
    
    // Clear for root domain
    document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.${domain.split('.')[1]};`;
  });
}

/**
 * Validate if current session is valid and belongs to correct tenant
 * @param {Object} supabase - Supabase client instance
 * @param {string} expectedTenantId - Expected tenant ID for validation
 * @returns {Promise<boolean>} - True if session is valid and matches tenant
 */
export async function validateSessionTenant(supabase, expectedTenantId) {
  try {
    const { data: { session }, error } = await supabase.auth.getSession();
    
    if (error || !session) {
      console.warn('⚠️ No valid session found');
      return false;
    }

    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      console.warn('⚠️ No valid user found');
      return false;
    }

    // If no expected tenant ID, just validate session exists
    if (!expectedTenantId) {
      return true;
    }

    // Get user's tenant from database
    const { data: userData, error: tenantError } = await supabase
      .from('users')
      .select('tenant_id')
      .eq('id', user.id)
      .single();

    if (tenantError || !userData?.tenant_id) {
      console.warn('⚠️ No tenant found for user');
      return false;
    }

    const isValidTenant = userData.tenant_id === expectedTenantId;
    
    if (!isValidTenant) {
      console.warn('🚫 Session tenant mismatch:', {
        expected: expectedTenantId,
        actual: userData.tenant_id,
      });
    }

    return isValidTenant;
  } catch (error) {
    console.error('💥 Error validating session tenant:', error);
    return false;
  }
}

/**
 * Force clear all browser data related to authentication
 * Nuclear option for complete cleanup
 */
export function forceAuthCleanup() {
  if (typeof window === 'undefined') return;

  console.log('🚨 Force authentication cleanup initiated...');

  try {
    // Clear all storage
    localStorage.clear();
    sessionStorage.clear();

    // Clear all cookies
    document.cookie.split(";").forEach((c) => {
      document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
    });

    // Clear any cached data
    if (window.caches) {
      window.caches.keys().then(cacheNames => {
        cacheNames.forEach(cacheName => {
          window.caches.delete(cacheName);
        });
      });
    }

    console.log('✅ Force cleanup completed');
  } catch (error) {
    console.error('💥 Error during force cleanup:', error);
  }
}

/**
 * Check if user should be signed out due to session issues
 * @param {Object} supabase - Supabase client instance
 * @returns {Promise<boolean>} - True if user should be signed out
 */
export async function shouldSignOut(supabase) {
  try {
    const { data: { session }, error } = await supabase.auth.getSession();
    
    // If there's an auth error, sign out
    if (error) {
      console.warn('⚠️ Auth error detected:', error.message);
      return true;
    }

    // If no session, sign out
    if (!session) {
      console.warn('⚠️ No session found');
      return true;
    }

    // Check if session is expired
    const now = Math.floor(Date.now() / 1000);
    if (session.expires_at && session.expires_at < now) {
      console.warn('⚠️ Session expired');
      return true;
    }

    // Validate user still exists
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      console.warn('⚠️ User validation failed');
      return true;
    }

    return false;
  } catch (error) {
    console.error('💥 Error checking if should sign out:', error);
    return true; // Sign out on error to be safe
  }
}
