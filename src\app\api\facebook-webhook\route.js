import { NextRequest, NextResponse } from 'next/server';
import { processWebhookEvent, verifyWebhookSignature } from '../../../actions/mooly-chatbot/facebook-integration-service.js';

const WEBHOOK_VERIFY_TOKEN = process.env.FACEBOOK_WEBHOOK_VERIFY_TOKEN;
const APP_SECRET = process.env.FACEBOOK_APP_SECRET;

/**
 * GET - Webhook verification for Facebook
 */
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const mode = searchParams.get('hub.mode');
    const token = searchParams.get('hub.verify_token');
    const challenge = searchParams.get('hub.challenge');

    console.log('📞 Facebook webhook verification:', { mode, token, challenge });

    // Verify the webhook
    if (mode === 'subscribe' && token === WEBHOOK_VERIFY_TOKEN) {
      console.log('✅ Facebook webhook verified successfully');
      return new NextResponse(challenge, { status: 200 });
    }

    console.log('❌ Facebook webhook verification failed');
    return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
  } catch (error) {
    console.error('❌ Error in Facebook webhook verification:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

/**
 * POST - Process Facebook webhook events
 */
export async function POST(request) {
  try {
    const body = await request.text();
    const signature = request.headers.get('x-hub-signature');

    console.log('📨 Received Facebook webhook event:', {
      hasSignature: !!signature,
      bodyLength: body?.length || 0
    });

    // Verify webhook signature
    if (!signature || !verifyWebhookSignature(body, signature, APP_SECRET)) {
      console.log('❌ Invalid webhook signature');
      return NextResponse.json({ error: 'Invalid signature' }, { status: 403 });
    }

    const eventData = JSON.parse(body);
    console.log('🔍 Parsed webhook data:', {
      object: eventData.object,
      entryCount: eventData.entry?.length || 0
    });

    // Process the webhook event
    const result = await processWebhookEvent(eventData);

    if (result.success) {
      console.log('✅ Facebook webhook processed successfully');
      return NextResponse.json({ success: true }, { status: 200 });
    } else {
      console.log('⚠️ Facebook webhook processing failed:', result.error);
      return NextResponse.json({ error: 'Processing failed' }, { status: 500 });
    }
  } catch (error) {
    console.error('❌ Error processing Facebook webhook:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

/**
 * OPTIONS - Handle CORS preflight
 */
export async function OPTIONS(request) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, x-hub-signature',
    },
  });
} 