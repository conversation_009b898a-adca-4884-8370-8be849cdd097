/**
 * Social Media Connection Dialog
 * Dialog để kết nối social media accounts
 */

import { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Stack,
  Typography,
  Card,
  CardContent,
  Avatar,
  Box,
  Alert
} from '@mui/material';
import { LoadingButton } from '@mui/lab';

import { Iconify } from 'src/components/iconify';
import { useAuthContext } from 'src/auth/hooks';
import { 
  PLATFORMS, 
  PLATFORM_COLORS, 
  PLATFORM_ICONS 
} from 'src/actions/mooly-chatbot/social-media-integration/social-media-constants';
import { getOAuthUrl } from 'src/actions/mooly-chatbot/social-media-integration';

export function SocialMediaConnectionDialog({ 
  open, 
  onClose, 
  onSuccess, 
  selectedPlatform = null 
}) {
  const [connecting, setConnecting] = useState(false);
  const [error, setError] = useState(null);

  // Get authenticated user context
  const { user } = useAuthContext();
  const tenantId = user?.user_metadata?.tenant_id || user?.app_metadata?.tenant_id;

  const platforms = selectedPlatform 
    ? [{ key: selectedPlatform, name: selectedPlatform.charAt(0).toUpperCase() + selectedPlatform.slice(1) }]
    : [
        { key: PLATFORMS.FACEBOOK, name: 'Facebook' },
        { key: PLATFORMS.INSTAGRAM, name: 'Instagram' }
      ];

  // Validate tenant ID
  useEffect(() => {
    if (!tenantId) {
      setError('Không thể xác định tenant ID. Vui lòng đăng nhập lại.');
    } else {
      setError(null);
    }
  }, [tenantId]);

  const handleConnect = async (platform) => {
    try {
      setConnecting(true);
      setError(null);

      if (!tenantId) {
        throw new Error('Tenant ID không được xác định. Vui lòng đăng nhập lại.');
      }

      // Get current URL to return after OAuth
      const returnUrl = window.location.href.split('?')[0]; // Remove query params
      const oauthUrl = getOAuthUrl(platform, tenantId, returnUrl);
      
      console.log('🔗 Starting OAuth flow:', { platform, tenantId, returnUrl });
      
      // Redirect to OAuth
      window.location.href = oauthUrl;
    } catch (err) {
      console.error('Error starting OAuth:', err);
      setError(err.message || 'Không thể kết nối. Vui lòng thử lại.');
      setConnecting(false);
    }
  };

  const renderPlatformCard = (platform) => (
    <Card 
      key={platform.key}
      sx={{ 
        cursor: connecting || !tenantId ? 'not-allowed' : 'pointer',
        transition: 'all 0.2s',
        opacity: connecting || !tenantId ? 0.6 : 1,
        '&:hover': connecting || !tenantId ? {} : {
          transform: 'translateY(-2px)',
          boxShadow: 2
        }
      }}
      onClick={() => {
        if (!connecting && tenantId) {
          handleConnect(platform.key);
        }
      }}
    >
      <CardContent sx={{ textAlign: 'center', py: 3 }}>
        <Avatar
          sx={{
            width: 64,
            height: 64,
            mx: 'auto',
            mb: 2,
            bgcolor: PLATFORM_COLORS[platform.key]
          }}
        >
          <Iconify 
            icon={PLATFORM_ICONS[platform.key]} 
            sx={{ fontSize: 32, color: 'white' }}
          />
        </Avatar>
        
        <Typography variant="h6" gutterBottom>
          Kết nối {platform.name}
        </Typography>
        
        <Typography variant="body2" color="text.secondary">
          {platform.key === PLATFORMS.FACEBOOK && 'Kết nối Facebook Page để tự động trả lời comments và messages'}
          {platform.key === PLATFORMS.INSTAGRAM && 'Kết nối Instagram Business để quản lý comments và direct messages'}
        </Typography>

        {connecting && (
          <Box sx={{ mt: 2 }}>
            <Typography variant="caption" color="primary">
              Đang chuyển hướng...
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  );

  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle>
        <Stack direction="row" alignItems="center" spacing={2}>
          <Iconify icon="mdi:link-plus" />
          <Typography variant="h6">
            Kết nối Social Media
          </Typography>
        </Stack>
      </DialogTitle>

      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {!tenantId && (
          <Alert severity="warning" sx={{ mb: 3 }}>
            <Typography variant="body2">
              Không thể xác định thông tin tài khoản. Vui lòng đăng nhập lại để tiếp tục.
            </Typography>
          </Alert>
        )}

        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Chọn platform bạn muốn kết nối để bắt đầu tự động trả lời khách hàng
        </Typography>

        <Stack spacing={2}>
          {platforms.map(renderPlatformCard)}
        </Stack>

        <Box sx={{ mt: 3, p: 2, bgcolor: 'background.neutral', borderRadius: 1 }}>
          <Typography variant="caption" color="text.secondary">
            <strong>Lưu ý:</strong> Bạn cần có quyền quản trị viên của Facebook Page hoặc Instagram Business Account để kết nối thành công.
          </Typography>
        </Box>

        {/* Connection Requirements */}
        <Box sx={{ mt: 2 }}>
          <Typography variant="subtitle2" gutterBottom>
            Yêu cầu kết nối:
          </Typography>
          <Typography variant="body2" color="text.secondary">
            • Facebook: Quyền Admin hoặc Editor của Facebook Page<br/>
            • Instagram: Instagram Business Account liên kết với Facebook Page<br/>
            • Các quyền cần thiết: Quản lý bài viết, tin nhắn, và comments
          </Typography>
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} disabled={connecting}>
          Hủy
        </Button>
        {connecting && (
          <LoadingButton loading variant="contained" disabled>
            Đang kết nối...
          </LoadingButton>
        )}
      </DialogActions>
    </Dialog>
  );
}
