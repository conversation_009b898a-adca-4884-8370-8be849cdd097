import { NextResponse } from 'next/server';
import { createClient } from 'src/utils/supabase/server';
import { FACEBOOK_CONFIG } from 'src/lib/facebook-config';

/**
 * Exchange Facebook authorization code for access token
 * This route handles the server-side token exchange to keep app secret secure
 */
export async function POST(request) {
    console.log('🔄 Facebook Token Exchange API hit');

    try {
        // Parse request body
        const { code, redirectUri } = await request.json();

        if (!code) {
            return NextResponse.json({
                success: false,
                error: { message: 'Authorization code is required' }
            }, { status: 400 });
        }

        // Validate environment variables
        const appId = process.env.NEXT_PUBLIC_FACEBOOK_APP_ID;
        const appSecret = process.env.FACEBOOK_APP_SECRET;

        if (!appId || !appSecret) {
            console.error('❌ Missing Facebook app credentials');
            return NextResponse.json({
                success: false,
                error: { message: 'Facebook app not configured properly' }
            }, { status: 500 });
        }

        // Get authenticated user
        const supabase = await createClient();
        const { data: { user }, error: userError } = await supabase.auth.getUser();

        if (userError || !user) {
            return NextResponse.json({
                success: false,
                error: { message: 'User not authenticated' }
            }, { status: 401 });
        }

        console.log('🔑 Exchanging code for access token...');

        // Exchange code for access token using centralized config
        const tokenResponse = await fetch(FACEBOOK_CONFIG.getApiUrl(FACEBOOK_CONFIG.ENDPOINTS.OAUTH_ACCESS_TOKEN), {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: new URLSearchParams({
                client_id: FACEBOOK_CONFIG.APP_ID,
                client_secret: FACEBOOK_CONFIG.APP_SECRET,
                redirect_uri: redirectUri,
                code: code
            })
        });

        const tokenData = await tokenResponse.json();

        if (tokenData.error) {
            console.error('❌ Facebook token exchange error:', tokenData.error);
            return NextResponse.json({
                success: false,
                error: {
                    message: `Facebook API error: ${tokenData.error.message}`,
                    code: tokenData.error.code
                }
            }, { status: 400 });
        }

        if (!tokenData.access_token) {
            console.error('❌ No access token received from Facebook');
            return NextResponse.json({
                success: false,
                error: { message: 'No access token received from Facebook' }
            }, { status: 400 });
        }

        console.log('✅ Access token received, fetching user pages...');

        // Get user's Facebook pages
        const pagesResponse = await fetch(
            `https://graph.facebook.com/v19.0/me/accounts?access_token=${tokenData.access_token}&fields=name,id,access_token,instagram_business_account{id,name,username}`
        );

        const pagesData = await pagesResponse.json();

        if (pagesData.error) {
            console.error('❌ Error fetching Facebook pages:', pagesData.error);
            return NextResponse.json({
                success: false,
                error: {
                    message: `Failed to fetch Facebook pages: ${pagesData.error.message}`,
                    code: pagesData.error.code
                }
            }, { status: 400 });
        }

        if (!pagesData.data || pagesData.data.length === 0) {
            return NextResponse.json({
                success: false,
                error: { 
                    message: 'Không tìm thấy Facebook Page nào. Vui lòng đảm bảo bạn là admin của ít nhất một Page.' 
                }
            }, { status: 400 });
        }

        // Process and save Facebook accounts
        const accountsToSave = [];

        for (const page of pagesData.data) {
            const accountData = {
                pageId: page.id,
                pageName: page.name,
                pageAccessToken: page.access_token,
                userAccessToken: tokenData.access_token,
                tokenExpiresAt: tokenData.expires_in 
                    ? new Date(Date.now() + tokenData.expires_in * 1000).toISOString()
                    : null,
                instagramAccountId: page.instagram_business_account?.id || null,
                instagramUsername: page.instagram_business_account?.username || null,
                isActive: true,
                connectedAt: new Date().toISOString(),
                lastSyncAt: new Date().toISOString()
            };

            // Save to database via service
            const saveResult = await saveFacebookAccountToDatabase(accountData, user.id, supabase);
            if (saveResult.success) {
                accountsToSave.push(saveResult.data);
            } else {
                console.error(`❌ Failed to save page ${page.name}:`, saveResult.error);
            }
        }

        console.log(`✅ Successfully saved ${accountsToSave.length} Facebook accounts`);

        return NextResponse.json({
            success: true,
            data: {
                accountsConnected: accountsToSave.length,
                totalPages: pagesData.data.length,
                accounts: accountsToSave
            }
        });

    } catch (error) {
        console.error('💥 Token exchange error:', error);
        return NextResponse.json({
            success: false,
            error: { 
                message: 'Internal server error during token exchange',
                details: error.message 
            }
        }, { status: 500 });
    }
}

/**
 * Helper function to save Facebook account to database
 */
async function saveFacebookAccountToDatabase(accountData, userId, supabase) {
    try {
        // Check if account already exists
        const { data: existingAccount } = await supabase
            .from('facebook_accounts')
            .select('id')
            .eq('page_id', accountData.pageId)
            .single();

        if (existingAccount) {
            // Update existing account
            const { data, error } = await supabase
                .from('facebook_accounts')
                .update({
                    page_name: accountData.pageName,
                    page_access_token: accountData.pageAccessToken,
                    user_access_token: accountData.userAccessToken,
                    token_expires_at: accountData.tokenExpiresAt,
                    instagram_account_id: accountData.instagramAccountId,
                    instagram_username: accountData.instagramUsername,
                    is_active: accountData.isActive,
                    last_sync_at: accountData.lastSyncAt,
                    updated_at: new Date().toISOString()
                })
                .eq('id', existingAccount.id)
                .select()
                .single();

            if (error) throw error;
            return { success: true, data };
        } else {
            // Create new account
            const { data, error } = await supabase
                .from('facebook_accounts')
                .insert({
                    page_id: accountData.pageId,
                    page_name: accountData.pageName,
                    page_access_token: accountData.pageAccessToken,
                    user_access_token: accountData.userAccessToken,
                    token_expires_at: accountData.tokenExpiresAt,
                    instagram_account_id: accountData.instagramAccountId,
                    instagram_username: accountData.instagramUsername,
                    is_active: accountData.isActive,
                    connected_at: accountData.connectedAt,
                    last_sync_at: accountData.lastSyncAt
                })
                .select()
                .single();

            if (error) throw error;
            return { success: true, data };
        }
    } catch (error) {
        console.error('Database save error:', error);
        return { 
            success: false, 
            error: { message: error.message } 
        };
    }
} 