'use client';

import { z as zod } from 'zod';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useBoolean } from 'minimal-shared/hooks';
import { zodResolver } from '@hookform/resolvers/zod';

import Box from '@mui/material/Box';
import Link from '@mui/material/Link';
import Alert from '@mui/material/Alert';
import Button from '@mui/material/Button';
import Divider from '@mui/material/Divider';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import InputAdornment from '@mui/material/InputAdornment';

import { paths } from 'src/routes/paths';
import { useRouter } from 'src/routes/hooks';
import { RouterLink } from 'src/routes/components';

import { getCurrentTenantInfo } from 'src/actions/mooly-chatbot/tenant-middleware';

import { Iconify } from 'src/components/iconify';
import { Form, Field } from 'src/components/hook-form';

import { useAuthContext } from '../../hooks';
import { getErrorMessage } from '../../utils';
import { FormHead } from '../../components/form-head';
import { signInWithPassword } from '../../context/supabase';
import { GoogleSignInButton } from '../../components/google-sign-in-button';

// ----------------------------------------------------------------------

export const SignInSchema = zod.object({
  email: zod
    .string()
    .min(1, { message: 'Email là bắt buộc!' })
    .email({ message: 'Email phải là địa chỉ email hợp lệ!' }),
  password: zod
    .string()
    .min(1, { message: 'Mật khẩu là bắt buộc!' })
    .min(6, { message: 'Mật khẩu phải có ít nhất 6 ký tự!' }),
});

// ----------------------------------------------------------------------

export function SupabaseSignInView() {
  const router = useRouter();

  const showPassword = useBoolean();

  const { checkUserSession } = useAuthContext();

  const [errorMessage, setErrorMessage] = useState('');

  const defaultValues = {
    email: '',
    password: '',
  };

  const methods = useForm({
    resolver: zodResolver(SignInSchema),
    defaultValues,
  });

  const {
    handleSubmit,
    formState: { isSubmitting },
  } = methods;

  const onSubmit = handleSubmit(async (data) => {
    try {
      setErrorMessage(''); // Clear previous errors

      // Đăng nhập với Supabase
      await signInWithPassword({ email: data.email, password: data.password });

      // Kiểm tra session và tenant
      await checkUserSession?.();

      // Validate tenant info sau khi đăng nhập (với retry)
      let tenantInfo = null;
      let retryCount = 0;
      const maxRetries = 3;

      while (!tenantInfo && retryCount < maxRetries) {
        try {
          tenantInfo = await getCurrentTenantInfo();
          if (!tenantInfo) {
            retryCount++;
            console.warn(`⚠️ Retry ${retryCount}/${maxRetries} - No tenant info found`);
            if (retryCount < maxRetries) {
              await new Promise(resolve => setTimeout(resolve, 1000));
            }
          }
        } catch (error) {
          console.error('Error getting tenant info:', error);
          retryCount++;
          if (retryCount < maxRetries) {
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        }
      }

      if (!tenantInfo) {
        console.warn('⚠️ No tenant info after retries, but allowing login');
        // Vẫn cho phép đăng nhập, có thể là user mới
        router.refresh();
        return;
      }

      if (!tenantInfo.is_active) {
        throw new Error('Tài khoản của bạn đã bị tạm khóa. Vui lòng liên hệ hỗ trợ.');
      }

      console.log('✅ Sign in successful for tenant:', tenantInfo.name);
      router.refresh();
    } catch (error) {
      console.error('💥 Sign in error:', error);
      const feedbackMessage = getErrorMessage(error);
      setErrorMessage(feedbackMessage);
    }
  });

  const renderForm = () => (
    <Box sx={{ gap: 3, display: 'flex', flexDirection: 'column' }}>
      <Field.Text name="email" label="Địa chỉ email" slotProps={{ inputLabel: { shrink: true } }} />

      <Box sx={{ gap: 1.5, display: 'flex', flexDirection: 'column' }}>
        <Link
          component={RouterLink}
          href={paths.auth.supabase.resetPassword}
          variant="body2"
          color="inherit"
          sx={{ alignSelf: 'flex-end' }}
        >
          Quên mật khẩu?
        </Link>

        <Field.Text
          name="password"
          label="Mật khẩu"
          placeholder="Tối thiểu 6 ký tự"
          type={showPassword.value ? 'text' : 'password'}
          slotProps={{
            inputLabel: { shrink: true },
            input: {
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton onClick={showPassword.onToggle} edge="end">
                    <Iconify
                      icon={showPassword.value ? 'solar:eye-bold' : 'solar:eye-closed-bold'}
                    />
                  </IconButton>
                </InputAdornment>
              ),
            },
          }}
        />
      </Box>

      <Button
        fullWidth
        color="inherit"
        size="large"
        type="submit"
        variant="contained"
        loading={isSubmitting}
        loadingIndicator="Đang đăng nhập..."
      >
        Đăng nhập
      </Button>
    </Box>
  );

  return (
    <>
      <FormHead
        title="Đăng nhập vào tài khoản của bạn"
        description={
          <>
            {`Chưa có tài khoản? `}
            <Link component={RouterLink} href={paths.auth.supabase.signUp} variant="subtitle2">
              Đăng ký ngay
            </Link>
          </>
        }
        sx={{ textAlign: { xs: 'center', md: 'left' } }}
      />

      {!!errorMessage && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {errorMessage}
        </Alert>
      )}

      {/* Google Sign In Button */}
      <GoogleSignInButton
        onError={(error) => {
          const feedbackMessage = getErrorMessage(error);
          setErrorMessage(feedbackMessage);
        }}
        sx={{ mb: 3 }}
      />

      {/* Divider */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Divider sx={{ flexGrow: 1 }} />
        <Typography variant="body2" sx={{ mx: 2, color: 'text.secondary' }}>
          hoặc
        </Typography>
        <Divider sx={{ flexGrow: 1 }} />
      </Box>

      <Form methods={methods} onSubmit={onSubmit}>
        {renderForm()}
      </Form>
    </>
  );
}
