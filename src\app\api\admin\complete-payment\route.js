import { NextResponse } from 'next/server';

import { createClient } from 'src/utils/supabase/server';

/**
 * Admin API endpoint để complete payment và add credits
 * Chỉ được gọi từ server-side với service role
 * @param {Request} request - Request object
 * @returns {Promise<Response>} - Response object
 */
export const POST = async (request) => {
  try {
    // Lấy dữ liệu từ request body
    const { paymentId, paymentData, status } = await request.json();

    // Validation đầu vào
    if (!paymentId || !status) {
      return NextResponse.json(
        { success: false, error: 'Payment ID and status are required' },
        { status: 400 }
      );
    }

    // Tạo Supabase client với service role
    const supabase = createClient();

    // Lấy thông tin payment
    const { data: payment, error: paymentError } = await supabase
      .from('credit_payments')
      .select('*')
      .eq('id', paymentId)
      .single();

    if (paymentError || !payment) {
      return NextResponse.json(
        { success: false, error: 'Payment not found' },
        { status: 404 }
      );
    }

    // Nếu payment đã completed, không làm gì cả
    if (payment.payment_status === 'completed') {
      return NextResponse.json({
        success: true,
        message: 'Payment already completed',
        data: payment,
      });
    }

    // Cập nhật payment status
    const { error: updateError } = await supabase
      .from('credit_payments')
      .update({
        payment_status: status,
        payment_data: paymentData || payment.payment_data,
        updated_at: new Date().toISOString(),
      })
      .eq('id', paymentId);

    if (updateError) {
      return NextResponse.json(
        { success: false, error: 'Failed to update payment status' },
        { status: 500 }
      );
    }

    // Nếu payment thành công, add credits
    if (status === 'completed' && !payment.transaction_id) {
      const { data: creditResult, error: creditError } = await supabase.rpc(
        'admin_add_tenant_credit',
        {
          p_tenant_id: payment.tenant_id,
          p_amount: payment.credit_amount,
          p_description: `Mua ${payment.credit_amount} credit`,
          p_reference_type: 'payment',
          p_reference_id: payment.id,
          p_created_by: payment.created_by,
        }
      );

      if (creditError || !creditResult?.success) {
        console.error('Failed to add credits:', creditError || creditResult?.error);
        return NextResponse.json(
          { success: false, error: 'Failed to add credits to account' },
          { status: 500 }
        );
      }

      // Cập nhật transaction_id trong payment
      await supabase
        .from('credit_payments')
        .update({
          transaction_id: creditResult.transaction_id,
        })
        .eq('id', paymentId);

      return NextResponse.json({
        success: true,
        message: 'Payment completed and credits added successfully',
        data: {
          payment_id: paymentId,
          transaction_id: creditResult.transaction_id,
          credits_added: payment.credit_amount,
          new_balance: creditResult.new_balance,
        },
      });
    }

    return NextResponse.json({
      success: true,
      message: 'Payment status updated successfully',
      data: {
        payment_id: paymentId,
        status,
      },
    });

  } catch (error) {
    console.error('Error completing payment:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
};
