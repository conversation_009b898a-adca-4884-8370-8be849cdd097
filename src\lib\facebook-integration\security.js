import crypto from 'crypto';

/**
 * Facebook Integration Security Utilities
 * Handles webhook verification, rate limiting, and security checks
 */

// Rate limiting store (in production, use Redis or similar)
const rateLimitStore = new Map();

// Verify Facebook webhook signature
export function verifyWebhookSignature(payload, signature, appSecret) {
  if (!signature || !appSecret) {
    return false;
  }
  
  try {
    const expectedSignature = crypto
      .createHmac('sha256', appSecret)
      .update(payload, 'utf8')
      .digest('hex');
    
    const signatureHash = signature.replace('sha256=', '');
    
    return crypto.timingSafeEqual(
      Buffer.from(expectedSignature, 'hex'),
      Buffer.from(signatureHash, 'hex')
    );
  } catch (error) {
    console.error('💥 Error verifying webhook signature:', error);
    return false;
  }
}

// Rate limiting for API calls
export function checkRateLimit(identifier, maxRequests = 100, windowMs = 60 * 60 * 1000) {
  const now = Date.now();
  const windowStart = now - windowMs;
  
  // Clean old entries
  for (const [key, data] of rateLimitStore.entries()) {
    if (data.resetTime < now) {
      rateLimitStore.delete(key);
    }
  }
  
  // Get current rate limit data
  let rateLimitData = rateLimitStore.get(identifier);
  
  if (!rateLimitData || rateLimitData.resetTime < now) {
    // Create new rate limit window
    rateLimitData = {
      count: 0,
      resetTime: now + windowMs,
      requests: []
    };
  }
  
  // Remove old requests from current window
  rateLimitData.requests = rateLimitData.requests.filter(time => time > windowStart);
  
  // Check if limit exceeded
  if (rateLimitData.requests.length >= maxRequests) {
    return {
      allowed: false,
      remaining: 0,
      resetTime: rateLimitData.resetTime,
      retryAfter: Math.ceil((rateLimitData.resetTime - now) / 1000)
    };
  }
  
  // Add current request
  rateLimitData.requests.push(now);
  rateLimitData.count = rateLimitData.requests.length;
  
  // Update store
  rateLimitStore.set(identifier, rateLimitData);
  
  return {
    allowed: true,
    remaining: maxRequests - rateLimitData.count,
    resetTime: rateLimitData.resetTime,
    retryAfter: 0
  };
}

// Validate Facebook webhook event
export function validateWebhookEvent(event) {
  if (!event || typeof event !== 'object') {
    return { valid: false, error: 'Invalid event format' };
  }
  
  // Check required fields
  if (!event.object) {
    return { valid: false, error: 'Missing object field' };
  }
  
  if (!event.entry || !Array.isArray(event.entry)) {
    return { valid: false, error: 'Missing or invalid entry field' };
  }
  
  // Validate each entry
  for (const entry of event.entry) {
    if (!entry.id) {
      return { valid: false, error: 'Entry missing ID' };
    }
    
    if (!entry.time) {
      return { valid: false, error: 'Entry missing timestamp' };
    }
    
    // Check if event is too old (older than 5 minutes)
    const eventTime = entry.time * 1000; // Convert to milliseconds
    const now = Date.now();
    const maxAge = 5 * 60 * 1000; // 5 minutes
    
    if (now - eventTime > maxAge) {
      return { valid: false, error: 'Event too old' };
    }
  }
  
  return { valid: true };
}

// Sanitize webhook data to prevent injection attacks
export function sanitizeWebhookData(data) {
  if (typeof data !== 'object' || data === null) {
    return data;
  }
  
  if (Array.isArray(data)) {
    return data.map(item => sanitizeWebhookData(item));
  }
  
  const sanitized = {};
  
  for (const [key, value] of Object.entries(data)) {
    // Skip potentially dangerous keys
    if (key.startsWith('__') || key.includes('script') || key.includes('eval')) {
      continue;
    }
    
    if (typeof value === 'string') {
      // Basic XSS prevention
      sanitized[key] = value
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        .replace(/javascript:/gi, '')
        .replace(/on\w+\s*=/gi, '');
    } else if (typeof value === 'object') {
      sanitized[key] = sanitizeWebhookData(value);
    } else {
      sanitized[key] = value;
    }
  }
  
  return sanitized;
}

// Generate secure tokens
export function generateSecureToken(length = 32) {
  return crypto.randomBytes(length).toString('hex');
}

// Validate Facebook access token format
export function validateAccessToken(token) {
  if (!token || typeof token !== 'string') {
    return false;
  }
  
  // Basic format validation for Facebook access tokens
  // Facebook tokens are typically alphanumeric with some special characters
  const tokenRegex = /^[A-Za-z0-9_-]+$/;
  
  return tokenRegex.test(token) && token.length > 10;
}

// Check if IP is from Facebook's webhook servers
export function isValidFacebookIP(ip) {
  // Facebook webhook IP ranges (these should be updated regularly)
  const facebookIPRanges = [
    '************/24',
    '************/24',
    '************/24',
    '************/24',
    '************/24',
    '************/24',
    '***********/24',
    '***********/24',
    '***********/24',
    '***********/24',
    '***********/24',
    '***********/24',
    '***********/24',
    '***********/24',
    '***********/24',
    '***********/24',
    '***********/24',
    '***********/24',
    '***********/24',
    '***********/24',
    '***********/24'
  ];
  
  // For development, allow localhost
  if (process.env.NODE_ENV === 'development' && 
      (ip === '127.0.0.1' || ip === '::1' || ip === 'localhost')) {
    return true;
  }
  
  // In production, you should implement proper CIDR checking
  // For now, we'll skip IP validation but log the IP
  console.log('📍 Webhook received from IP:', ip);
  
  return true; // Allow all IPs for now, but log them
}

// Encrypt sensitive data before storing
export function encryptToken(token, key) {
  if (!key) {
    throw new Error('Encryption key required');
  }
  
  const algorithm = 'aes-256-gcm';
  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipher(algorithm, key);
  
  let encrypted = cipher.update(token, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  
  const authTag = cipher.getAuthTag();
  
  return {
    encrypted,
    iv: iv.toString('hex'),
    authTag: authTag.toString('hex')
  };
}

// Decrypt sensitive data
export function decryptToken(encryptedData, key) {
  if (!key) {
    throw new Error('Decryption key required');
  }
  
  const algorithm = 'aes-256-gcm';
  const decipher = crypto.createDecipher(algorithm, key);
  
  decipher.setAuthTag(Buffer.from(encryptedData.authTag, 'hex'));
  
  let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  
  return decrypted;
}
