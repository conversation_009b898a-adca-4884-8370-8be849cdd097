'use client';

import { Box, Grid, Paper, Avatar, Divider, Container, Typography } from '@mui/material';

import { useAuthContext } from 'src/auth/hooks';

export default function ProfilePage() {
  const { user } = useAuthContext();

  if (!user) {
    return (
      <Container>
        <Typography variant="h4" sx={{ mb: 3 }}>
          Thông tin người dùng
        </Typography>
        <Paper sx={{ p: 3 }}>
          <Typography>Đang tải thông tin...</Typography>
        </Paper>
      </Container>
    );
  }

  return (
    <Container>
      <Typography variant="h4" sx={{ mb: 3 }}>
        Thông tin người dùng
      </Typography>
      <Paper sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <Avatar
            src={user.photoURL || ''}
            alt={user.displayName}
            sx={{ width: 80, height: 80, mr: 2 }}
          />
          <Box>
            <Typography variant="h5">{user.displayName}</Typography>
            <Typography variant="body2" color="text.secondary">
              {user.email}
            </Typography>
          </Box>
        </Box>

        <Divider sx={{ my: 3 }} />

        <Grid container spacing={2}>
          <Grid item size={{ xs: 12, md: 6 }}>
            <Typography variant="subtitle1" fontWeight="bold">
              ID người dùng
            </Typography>
            <Typography variant="body2">{user.id}</Typography>
          </Grid>
          <Grid item size={{ xs: 12, md: 6 }}>
            <Typography variant="subtitle1" fontWeight="bold">
              Vai trò
            </Typography>
            <Typography variant="body2">{user.role}</Typography>
          </Grid>
        </Grid>
      </Paper>
    </Container>
  );
}
