'use client';

import { CONFIG } from 'src/global-config';

/**
 * Service để quản lý invite user thông qua Admin API
 * Sử dụng TOKEN_ACCESS_API để xác thực
 */
export class InviteUserService {
  constructor() {
    this.baseUrl = CONFIG.serverUrl || '';
    this.endpoint = '/api/admin/invite-user';
  }

  /**
   * Invite user mới vào hệ thống
   * @param {Object} userData - Thông tin user
   * @param {string} userData.email - Email của user
   * @param {string} userData.name - Tên của user
   * @param {string} userData.role - Role của user (Owner, Administrator, Developer, Read-Only)
   * @param {string} tokenAccessApi - TOKEN_ACCESS_API để xác thực
   * @returns {Promise<Object>} - Kết quả invite
   */
  async inviteUser(userData, tokenAccessApi) {
    try {
      if (!tokenAccessApi) {
        throw new Error('TOKEN_ACCESS_API is required');
      }

      const { email, name, role = 'Developer' } = userData;

      if (!email || !name) {
        throw new Error('Email and name are required');
      }

      const response = await fetch(`${this.baseUrl}${this.endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${tokenAccessApi}`,
        },
        body: JSON.stringify({
          email,
          name,
          role,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || `HTTP error! status: ${response.status}`);
      }

      return {
        success: true,
        data: data.data,
        message: data.message,
      };
    } catch (error) {
      console.error('❌ Invite user error:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Kiểm tra trạng thái API
   * @returns {Promise<Object>} - Thông tin API status
   */
  async getApiStatus() {
    try {
      const response = await fetch(`${this.baseUrl}${this.endpoint}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || `HTTP error! status: ${response.status}`);
      }

      return {
        success: true,
        data,
      };
    } catch (error) {
      console.error('❌ API status check error:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Validate email format
   * @param {string} email - Email để validate
   * @returns {boolean} - true nếu email hợp lệ
   */
  validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate role
   * @param {string} role - Role để validate
   * @returns {boolean} - true nếu role hợp lệ
   */
  validateRole(role) {
    const validRoles = ['Owner', 'Administrator', 'Developer', 'Read-Only'];
    return validRoles.includes(role);
  }

  /**
   * Get available roles
   * @returns {Array<string>} - Danh sách roles có sẵn
   */
  getAvailableRoles() {
    return ['Owner', 'Administrator', 'Developer', 'Read-Only'];
  }
}

/**
 * Hook để sử dụng InviteUserService
 * @returns {InviteUserService} - Instance của InviteUserService
 */
export function useInviteUserService() {
  return new InviteUserService();
}

/**
 * Utility function để invite user nhanh
 * @param {Object} userData - Thông tin user
 * @param {string} tokenAccessApi - TOKEN_ACCESS_API
 * @returns {Promise<Object>} - Kết quả invite
 */
export async function inviteUser(userData, tokenAccessApi) {
  const service = new InviteUserService();
  return await service.inviteUser(userData, tokenAccessApi);
}

/**
 * Utility function để kiểm tra API status
 * @returns {Promise<Object>} - API status
 */
export async function checkInviteApiStatus() {
  const service = new InviteUserService();
  return await service.getApiStatus();
}
