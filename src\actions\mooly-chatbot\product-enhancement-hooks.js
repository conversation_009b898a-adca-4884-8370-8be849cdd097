'use client';

/**
 * React Hooks for Product Enhancement Service
 * Cung cấp các hooks để sử dụng Product Enhancement Service trong React components
 */

import { useMemo, useState, useCallback } from 'react';

import {
  getProductBundles,
  // Bulk operations
  bulkUpdateProducts,
  bulkDeleteProducts,
  getRelatedProducts,

  // Bundle functions
  createProductBundle,
  updateProductBundle,

  deleteProductBundle,
  // Analytics
  getProductAnalytics,
  getLowStockProducts,
  // Advanced search
  advancedProductSearch,
  // Import/Export
  importProductsFromCSV,

  getBestSellingProducts,
  bulkUpdateProductStatus,

  bulkUpdateProductPrices,
  // Business type functions
  getProductsByBusinessType,
  bulkUpdateProductCategory,

  filterProductFieldsByBusinessType
} from './product-enhancement-service';

/**
 * 🎁 BUNDLE MANAGEMENT HOOKS
 */

/**
 * Hook để quản lý product bundles
 * @returns {Object} - Bundle management functions và state
 */
export function useProductBundles() {
  const [bundles, setBundles] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // Fetch bundles
  const fetchBundles = useCallback(async (options = {}) => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await getProductBundles(options);

      if (result.success) {
        setBundles(result.data || []);
      } else {
        setError(result.error);
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Create bundle
  const createBundle = useCallback(async (bundleData, bundleItems) => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await createProductBundle(bundleData, bundleItems);

      if (result.success) {
        // Refresh bundles list
        await fetchBundles();
        return result;
      } else {
        setError(result.error);
        return result;
      }
    } catch (err) {
      const errorMessage = err.message;
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  }, [fetchBundles]);

  // Update bundle
  const updateBundle = useCallback(async (bundleId, bundleData, bundleItems = null) => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await updateProductBundle(bundleId, bundleData, bundleItems);

      if (result.success) {
        // Refresh bundles list
        await fetchBundles();
        return result;
      } else {
        setError(result.error);
        return result;
      }
    } catch (err) {
      const errorMessage = err.message;
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  }, [fetchBundles]);

  // Delete bundle
  const deleteBundle = useCallback(async (bundleId) => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await deleteProductBundle(bundleId);

      if (result.success) {
        // Refresh bundles list
        await fetchBundles();
        return result;
      } else {
        setError(result.error);
        return result;
      }
    } catch (err) {
      const errorMessage = err.message;
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  }, [fetchBundles]);

  const memoizedValue = useMemo(
    () => ({
      bundles,
      isLoading,
      error,
      fetchBundles,
      createBundle,
      updateBundle,
      deleteBundle,
      isEmpty: !isLoading && (!bundles || bundles.length === 0)
    }),
    [bundles, isLoading, error, fetchBundles, createBundle, updateBundle, deleteBundle]
  );

  return memoizedValue;
}

/**
 * 🏢 BUSINESS TYPE HOOKS
 */

/**
 * Hook để lấy sản phẩm theo business type
 * @param {string} businessType - Loại hình kinh doanh
 * @param {Object} options - Tùy chọn truy vấn
 * @returns {Object} - Products và functions
 */
export function useProductsByBusinessType(businessType, options = {}) {
  const [products, setProducts] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchProducts = useCallback(async () => {
    if (!businessType) return;

    setIsLoading(true);
    setError(null);

    try {
      const result = await getProductsByBusinessType(businessType, options);

      if (result.success) {
        setProducts(result.data || []);
      } else {
        setError(result.error);
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  }, [businessType, options]);

  // Filter product fields by business type
  const filterProductFields = useCallback((productData) => filterProductFieldsByBusinessType(productData, businessType), [businessType]);

  const memoizedValue = useMemo(
    () => ({
      products,
      isLoading,
      error,
      fetchProducts,
      filterProductFields,
      isEmpty: !isLoading && (!products || products.length === 0)
    }),
    [products, isLoading, error, fetchProducts, filterProductFields]
  );

  return memoizedValue;
}

/**
 * 📦 BULK OPERATIONS HOOKS
 */

/**
 * Hook để thực hiện bulk operations
 * @returns {Object} - Bulk operation functions
 */
export function useBulkOperations() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // Bulk update products
  const bulkUpdate = useCallback(async (productIds, updateData) => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await bulkUpdateProducts(productIds, updateData);

      if (!result.success) {
        setError(result.error);
      }

      return result;
    } catch (err) {
      const errorMessage = err.message;
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Bulk delete products
  const bulkDelete = useCallback(async (productIds) => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await bulkDeleteProducts(productIds);

      if (!result.success) {
        setError(result.error);
      }

      return result;
    } catch (err) {
      const errorMessage = err.message;
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Bulk update status
  const bulkUpdateStatus = useCallback(async (productIds, isActive) => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await bulkUpdateProductStatus(productIds, isActive);

      if (!result.success) {
        setError(result.error);
      }

      return result;
    } catch (err) {
      const errorMessage = err.message;
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Bulk update prices
  const bulkUpdatePrices = useCallback(async (productIds, priceData) => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await bulkUpdateProductPrices(productIds, priceData);

      if (!result.success) {
        setError(result.error);
      }

      return result;
    } catch (err) {
      const errorMessage = err.message;
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Bulk update category
  const bulkUpdateCategory = useCallback(async (productIds, categoryId) => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await bulkUpdateProductCategory(productIds, categoryId);

      if (!result.success) {
        setError(result.error);
      }

      return result;
    } catch (err) {
      const errorMessage = err.message;
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  }, []);

  const memoizedValue = useMemo(
    () => ({
      isLoading,
      error,
      bulkUpdate,
      bulkDelete,
      bulkUpdateStatus,
      bulkUpdatePrices,
      bulkUpdateCategory
    }),
    [isLoading, error, bulkUpdate, bulkDelete, bulkUpdateStatus, bulkUpdatePrices, bulkUpdateCategory]
  );

  return memoizedValue;
}

/**
 * 🔍 ADVANCED SEARCH HOOKS
 */

/**
 * Hook để tìm kiếm sản phẩm nâng cao
 * @returns {Object} - Search functions và state
 */
export function useAdvancedProductSearch() {
  const [searchResults, setSearchResults] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [totalCount, setTotalCount] = useState(0);

  const search = useCallback(async (searchParams) => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await advancedProductSearch(searchParams);

      if (result.success) {
        setSearchResults(result.data || []);
        setTotalCount(result.totalCount || 0);
      } else {
        setError(result.error);
        setSearchResults([]);
        setTotalCount(0);
      }

      return result;
    } catch (err) {
      const errorMessage = err.message;
      setError(errorMessage);
      setSearchResults([]);
      setTotalCount(0);
      return { success: false, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  }, []);

  const clearResults = useCallback(() => {
    setSearchResults([]);
    setTotalCount(0);
    setError(null);
  }, []);

  const memoizedValue = useMemo(
    () => ({
      searchResults,
      isLoading,
      error,
      totalCount,
      search,
      clearResults,
      isEmpty: !isLoading && (!searchResults || searchResults.length === 0)
    }),
    [searchResults, isLoading, error, totalCount, search, clearResults]
  );

  return memoizedValue;
}

/**
 * Hook để lấy sản phẩm liên quan
 * @param {string} productId - ID sản phẩm gốc
 * @param {Object} options - Tùy chọn
 * @returns {Object} - Related products và functions
 */
export function useRelatedProducts(productId, options = {}) {
  const [relatedProducts, setRelatedProducts] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchRelatedProducts = useCallback(async () => {
    if (!productId) return;

    setIsLoading(true);
    setError(null);

    try {
      const result = await getRelatedProducts(productId, options);

      if (result.success) {
        setRelatedProducts(result.data || []);
      } else {
        setError(result.error);
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  }, [productId, options]);

  const memoizedValue = useMemo(
    () => ({
      relatedProducts,
      isLoading,
      error,
      fetchRelatedProducts,
      isEmpty: !isLoading && (!relatedProducts || relatedProducts.length === 0)
    }),
    [relatedProducts, isLoading, error, fetchRelatedProducts]
  );

  return memoizedValue;
}

/**
 * 📊 ANALYTICS HOOKS
 */

/**
 * Hook để lấy thống kê sản phẩm
 * @param {Object} options - Tùy chọn thống kê
 * @returns {Object} - Analytics data và functions
 */
export function useProductAnalytics(options = {}) {
  const [analytics, setAnalytics] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchAnalytics = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await getProductAnalytics(options);

      if (result.success) {
        setAnalytics(result.data);
      } else {
        setError(result.error);
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  }, [options]);

  const memoizedValue = useMemo(
    () => ({
      analytics,
      isLoading,
      error,
      fetchAnalytics,
      isEmpty: !isLoading && !analytics
    }),
    [analytics, isLoading, error, fetchAnalytics]
  );

  return memoizedValue;
}

/**
 * Hook để lấy sản phẩm bán chạy
 * @param {Object} options - Tùy chọn
 * @returns {Object} - Best selling products và functions
 */
export function useBestSellingProducts(options = {}) {
  const [bestSellingProducts, setBestSellingProducts] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchBestSellingProducts = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await getBestSellingProducts(options);

      if (result.success) {
        setBestSellingProducts(result.data || []);
      } else {
        setError(result.error);
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  }, [options]);

  const memoizedValue = useMemo(
    () => ({
      bestSellingProducts,
      isLoading,
      error,
      fetchBestSellingProducts,
      isEmpty: !isLoading && (!bestSellingProducts || bestSellingProducts.length === 0)
    }),
    [bestSellingProducts, isLoading, error, fetchBestSellingProducts]
  );

  return memoizedValue;
}

/**
 * Hook để lấy sản phẩm sắp hết hàng
 * @param {Object} options - Tùy chọn
 * @returns {Object} - Low stock products và functions
 */
export function useLowStockProducts(options = {}) {
  const [lowStockProducts, setLowStockProducts] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchLowStockProducts = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await getLowStockProducts(options);

      if (result.success) {
        setLowStockProducts(result.data || []);
      } else {
        setError(result.error);
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  }, [options]);

  const memoizedValue = useMemo(
    () => ({
      lowStockProducts,
      isLoading,
      error,
      fetchLowStockProducts,
      isEmpty: !isLoading && (!lowStockProducts || lowStockProducts.length === 0)
    }),
    [lowStockProducts, isLoading, error, fetchLowStockProducts]
  );

  return memoizedValue;
}

/**
 * 🔄 IMPORT/EXPORT HOOKS
 */

/**
 * Hook để import sản phẩm từ CSV
 * @returns {Object} - Import functions và state
 */
export function useProductImport() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [importResult, setImportResult] = useState(null);

  const importFromCSV = useCallback(async (csvData, options = {}) => {
    setIsLoading(true);
    setError(null);
    setImportResult(null);

    try {
      const result = await importProductsFromCSV(csvData, options);

      if (result.success) {
        setImportResult(result.data);
      } else {
        setError(result.error);
      }

      return result;
    } catch (err) {
      const errorMessage = err.message;
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  }, []);

  const validateCSV = useCallback(async (csvData, options = {}) => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await importProductsFromCSV(csvData, {
        ...options,
        validateOnly: true
      });

      if (result.success) {
        setImportResult(result.data);
      } else {
        setError(result.error);
      }

      return result;
    } catch (err) {
      const errorMessage = err.message;
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  }, []);

  const clearImportResult = useCallback(() => {
    setImportResult(null);
    setError(null);
  }, []);

  const memoizedValue = useMemo(
    () => ({
      isLoading,
      error,
      importResult,
      importFromCSV,
      validateCSV,
      clearImportResult
    }),
    [isLoading, error, importResult, importFromCSV, validateCSV, clearImportResult]
  );

  return memoizedValue;
}
