/**
 * URL Helper Utilities
 * Tối ưu hóa cấu hình URL cho development và production
 */

/**
 * Lấy base URL của ứng dụng dựa trên môi trường
 * @returns {string} Base URL
 */
export function getBaseURL() {
  // Trong development, sử dụng localhost với port 3032
  if (process.env.NODE_ENV === 'development') {
    return 'http://localhost:3032';
  }

  // Trong production, ưu tiên các biến môi trường theo thứ tự
  return (
    process.env.NEXT_PUBLIC_PUBLIC_SITE_URL ||
    process.env.NEXTAUTH_URL ||
    process.env.NEXT_PUBLIC_VERCEL_URL ||
    'http://localhost:3032'
  );
}

/**
 * Tạo redirect URL cho Supabase auth
 * @param {string} path - Đường dẫn redirect (mặc định: /dashboard/mooly-chatbot/chatbots)
 * @returns {string} Full redirect URL
 */
export function getAuthRedirectURL(path = '/dashboard/mooly-chatbot/chatbots') {
  const baseURL = getBaseURL();
  
  // Đ<PERSON>m bảo path bắt đầu bằng /
  const normalizedPath = path.startsWith('/') ? path : `/${path}`;
  
  return `${baseURL}${normalizedPath}`;
}

/**
 * Tạo callback URL cho OAuth providers
 * @param {string} provider - Provider name (facebook, instagram, google, etc.)
 * @returns {string} Full callback URL
 */
export function getOAuthCallbackURL(provider = 'auth') {
  const baseURL = getBaseURL();
  
  switch (provider) {
    case 'facebook':
      return `${baseURL}/api/facebook-integration/popup-callback`;
    case 'instagram':
      return `${baseURL}/api/instagram-integration/callback`;
    case 'google':
    case 'supabase':
    default:
      return `${baseURL}/auth/callback`;
  }
}

/**
 * Kiểm tra xem có phải môi trường development không
 * @returns {boolean}
 */
export function isDevelopment() {
  return process.env.NODE_ENV === 'development';
}

/**
 * Kiểm tra xem có phải môi trường production không
 * @returns {boolean}
 */
export function isProduction() {
  return process.env.NODE_ENV === 'production';
}

/**
 * Lấy port hiện tại từ URL hoặc environment
 * @returns {number} Port number
 */
export function getCurrentPort() {
  if (typeof window !== 'undefined') {
    return parseInt(window.location.port) || 3032;
  }
  
  return parseInt(process.env.PORT) || 3032;
}

/**
 * Tạo URL với port cụ thể cho development
 * @param {string} path - Đường dẫn
 * @param {number} port - Port number (mặc định: 3032)
 * @returns {string} Full URL
 */
export function getDevURL(path = '', port = 3032) {
  const normalizedPath = path.startsWith('/') ? path : `/${path}`;
  return `http://localhost:${port}${normalizedPath}`;
}

/**
 * Validate và normalize URL
 * @param {string} url - URL cần validate
 * @returns {string} Normalized URL
 */
export function normalizeURL(url) {
  if (!url) return getBaseURL();
  
  try {
    const urlObj = new URL(url);
    return urlObj.toString();
  } catch (error) {
    console.warn('Invalid URL provided, using base URL:', error);
    return getBaseURL();
  }
}
