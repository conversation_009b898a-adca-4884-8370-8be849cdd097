import { useState, useEffect, useCallback, useMemo } from 'react';
import {
  getFacebookAccounts,
  getFacebookAccountById,
  getAutoReplyConfig,
  getSelectedPageForChatbot,
  getFacebookActivityLogs
} from './facebook-api';

/**
 * Hook để quản lý danh sách Facebook accounts
 * @param {Object} options - <PERSON><PERSON><PERSON>
 * @returns {Object} - State và functions
 */
export function useFacebookAccounts(options = {}) {
  const [accounts, setAccounts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [lastFetch, setLastFetch] = useState(null);

  // Memoize options để tránh re-render không cần thiết
  const memoizedOptions = useMemo(() => ({
    includeConfig: false, // Default to basic data only
    includeActivity: false,
    ...options
  }), [JSON.stringify(options)]);

  const fetchAccounts = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
 
      console.log('🔄 Fetching Facebook accounts from API...');
      const result = await getFacebookAccounts(memoizedOptions);
 
      if (result.success) {
        setAccounts(result.data || []);
        setLastFetch(Date.now());
        console.log('✅ Facebook accounts fetched successfully:', result.data?.length || 0);
      } else {
        setError(result.error);
        setAccounts([]);
        console.error('❌ Failed to fetch Facebook accounts:', result.error);
      }
    } catch (err) {
      console.error('💥 Error fetching Facebook accounts:', err);
      setError(err);
      setAccounts([]);
    } finally {
      setLoading(false);
    }
  }, [memoizedOptions]);

  // Chỉ fetch một lần khi mount hoặc khi options thay đổi
  useEffect(() => {
    fetchAccounts();
  }, [fetchAccounts]);

  // Refetch function
  const refetch = useCallback(async () => {
    await fetchAccounts();
  }, [fetchAccounts]);

  return {
    accounts,
    loading,
    error,
    refetch,
    lastFetch: lastFetch ? new Date(lastFetch) : null
  };
}

/**
 * Hook để quản lý một Facebook account
 * @param {string} accountId - Account ID
 * @returns {Object} - State và functions
 */
export function useFacebookAccount(accountId) {
  const [account, setAccount] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchAccount = useCallback(async () => {
    if (!accountId) {
      setAccount(null);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const result = await getFacebookAccountById(accountId);

      if (result.success) {
        setAccount(result.data);
      } else {
        setError(result.error);
        setAccount(null);
      }
    } catch (err) {
      console.error('Error fetching Facebook account:', err);
      setError(err);
      setAccount(null);
    } finally {
      setLoading(false);
    }
  }, [accountId]);

  useEffect(() => {
    fetchAccount();
  }, [fetchAccount]);

  // Refetch function không phụ thuộc vào fetchAccount để tránh loop
  const refetch = useCallback(async () => {
    if (!accountId) {
      setAccount(null);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const result = await getFacebookAccountById(accountId);

      if (result.success) {
        setAccount(result.data);
      } else {
        setError(result.error);
        setAccount(null);
      }
    } catch (err) {
      console.error('Error refetching Facebook account:', err);
      setError(err);
      setAccount(null);
    } finally {
      setLoading(false);
    }
  }, [accountId]);

  return {
    account,
    loading,
    error,
    refetch
  };
}

/**
 * Hook để quản lý cấu hình auto reply
 * @param {string} pageId - Page ID
 * @returns {Object} - State và functions
 */
export function useAutoReplyConfig(pageId) {
  const [config, setConfig] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchConfig = useCallback(async () => {
    if (!pageId) {
      setConfig(null);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const result = await getAutoReplyConfig(pageId);

      if (result.success) {
        setConfig(result.data);
      } else {
        // If no config found, set default config
        if (result.error?.code === 'PGRST116') {
          setConfig(null);
        } else {
          setError(result.error);
        }
      }
    } catch (err) {
      console.error('Error fetching auto reply config:', err);
      setError(err);
      setConfig(null);
    } finally {
      setLoading(false);
    }
  }, [pageId]);

  useEffect(() => {
    fetchConfig();
  }, [fetchConfig]);

  // Refetch function không phụ thuộc vào fetchConfig để tránh loop
  const refetch = useCallback(async () => {
    if (!pageId) {
      setConfig(null);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const result = await getAutoReplyConfig(pageId);

      if (result.success) {
        setConfig(result.data);
      } else {
        // If no config found, set default config
        if (result.error?.code === 'PGRST116') {
          setConfig(null);
        } else {
          setError(result.error);
        }
      }
    } catch (err) {
      console.error('Error refetching auto reply config:', err);
      setError(err);
      setConfig(null);
    } finally {
      setLoading(false);
    }
  }, [pageId]);

  return {
    config,
    loading,
    error,
    refetch
  };
}

/**
 * Hook để quản lý page được chọn cho chatbot
 * @param {string} chatbotId - Chatbot ID
 * @returns {Object} - State và functions
 */
export function useSelectedPageForChatbot(chatbotId) {
  const [selectedPage, setSelectedPage] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchSelectedPage = useCallback(async () => {
    if (!chatbotId) {
      setSelectedPage(null);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const result = await getSelectedPageForChatbot(chatbotId);

      if (result.success) {
        setSelectedPage(result.data);
      } else {
        setError(result.error);
        setSelectedPage(null);
      }
    } catch (err) {
      console.error('Error fetching selected page:', err);
      setError(err);
      setSelectedPage(null);
    } finally {
      setLoading(false);
    }
  }, [chatbotId]);

  useEffect(() => {
    fetchSelectedPage();
  }, [fetchSelectedPage]);

  // Refetch function không phụ thuộc vào fetchSelectedPage để tránh loop
  const refetch = useCallback(async () => {
    if (!chatbotId) {
      setSelectedPage(null);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const result = await getSelectedPageForChatbot(chatbotId);

      if (result.success) {
        setSelectedPage(result.data);
      } else {
        setError(result.error);
        setSelectedPage(null);
      }
    } catch (err) {
      console.error('Error refetching selected page:', err);
      setError(err);
      setSelectedPage(null);
    } finally {
      setLoading(false);
    }
  }, [chatbotId]);

  return {
    selectedPage,
    loading,
    error,
    refetch
  };
}

/**
 * Hook để quản lý activity logs
 * @param {string} pageId - Page ID
 * @param {Object} options - Các tùy chọn
 * @returns {Object} - State và functions
 */
export function useFacebookActivityLogs(pageId, options = {}) {
  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Memoize options để tránh re-render không cần thiết
  const memoizedOptions = useMemo(() => options, [JSON.stringify(options)]);

  const fetchLogs = useCallback(async () => {
    if (!pageId) {
      setLogs([]);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const result = await getFacebookActivityLogs(pageId, memoizedOptions);

      if (result.success) {
        setLogs(result.data || []);
      } else {
        setError(result.error);
        setLogs([]);
      }
    } catch (err) {
      console.error('Error fetching activity logs:', err);
      setError(err);
      setLogs([]);
    } finally {
      setLoading(false);
    }
  }, [pageId, memoizedOptions]);

  useEffect(() => {
    fetchLogs();
  }, [fetchLogs]);

  // Refetch function không phụ thuộc vào fetchLogs để tránh loop
  const refetch = useCallback(async () => {
    if (!pageId) {
      setLogs([]);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const result = await getFacebookActivityLogs(pageId, memoizedOptions);

      if (result.success) {
        setLogs(result.data || []);
      } else {
        setError(result.error);
        setLogs([]);
      }
    } catch (err) {
      console.error('Error refetching activity logs:', err);
      setError(err);
      setLogs([]);
    } finally {
      setLoading(false);
    }
  }, [pageId, memoizedOptions]);

  return {
    logs,
    loading,
    error,
    refetch
  };
}

/**
 * Hook tổng hợp cho Facebook integration setup
 * @param {string} chatbotId - Chatbot ID
 * @returns {Object} - State và functions
 */
export function useFacebookIntegrationSetup(chatbotId) {
  const { accounts, loading: accountsLoading, refetch: refetchAccounts } = useFacebookAccounts();
  const { selectedPage, loading: selectedPageLoading, refetch: refetchSelectedPage } = useSelectedPageForChatbot(chatbotId);
  const { config, loading: configLoading, refetch: refetchConfig } = useAutoReplyConfig(selectedPage?.pageId);

  const loading = accountsLoading || selectedPageLoading || configLoading;

  // Stable refetch function để tránh dependency loop
  const refetchAll = useCallback(() => {
    refetchAccounts();
    refetchSelectedPage();
    if (selectedPage?.pageId) {
      refetchConfig();
    }
  }, [refetchAccounts, refetchSelectedPage, refetchConfig, selectedPage?.pageId]);

  return {
    accounts,
    selectedPage,
    config,
    loading,
    refetchAll,
    refetchAccounts,
    refetchSelectedPage,
    refetchConfig
  };
}
