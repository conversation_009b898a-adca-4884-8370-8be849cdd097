# Supabase Multi-Tenant Database Setup Guide

## 1. Tạo Tables Cơ Bản

### Bảng Tenants (Organizations)
```sql
-- Tạo bảng tenants
CREATE TABLE public.tenants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    slug TEXT UNIQUE NOT NULL,
    settings JSONB DEFAULT '{}',
    subscription_status TEXT DEFAULT 'trial',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE public.tenants ENABLE ROW LEVEL SECURITY;
```

### Bảng Profiles (User Profiles)
```sql
-- Tạo bảng profiles kết nối với auth.users
CREATE TABLE public.profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    tenant_id UUID NOT NULL REFERENCES public.tenants(id) ON DELETE CASCADE,
    email TEXT NOT NULL,
    full_name TEX<PERSON>,
    role TEXT DEFAULT 'member', -- admin, manager, member
    avatar_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Index for performance
CREATE INDEX idx_profiles_tenant_id ON public.profiles(tenant_id);
```

### Bảng Products (Ví dụ cho ecommerce)
```sql
-- Tạo bảng products
CREATE TABLE public.products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES public.tenants(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    sku TEXT,
    inventory_count INTEGER DEFAULT 0,
    status TEXT DEFAULT 'active', -- active, inactive, draft
    images JSONB DEFAULT '[]',
    metadata JSONB DEFAULT '{}',
    created_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;

-- Indexes for performance (tenant_id luôn là cột cuối)
CREATE INDEX idx_products_tenant_id ON public.products(tenant_id);
CREATE INDEX idx_products_status_tenant_id ON public.products(status, tenant_id);
CREATE INDEX idx_products_sku_tenant_id ON public.products(sku, tenant_id);
```

### Bảng Orders
```sql
-- Tạo bảng orders
CREATE TABLE public.orders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES public.tenants(id) ON DELETE CASCADE,
    customer_email TEXT NOT NULL,
    customer_name TEXT,
    total_amount DECIMAL(10,2) NOT NULL,
    status TEXT DEFAULT 'pending', -- pending, processing, shipped, delivered, cancelled
    shipping_address JSONB,
    billing_address JSONB,
    metadata JSONB DEFAULT '{}',
    created_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE public.orders ENABLE ROW LEVEL SECURITY;

-- Index
CREATE INDEX idx_orders_tenant_id ON public.orders(tenant_id);
CREATE INDEX idx_orders_status_tenant_id ON public.orders(status, tenant_id);
```

## 2. Tạo Helper Functions

### Function để lấy tenant_id từ auth
```sql
-- Function để lấy tenant_id từ app_metadata
CREATE OR REPLACE FUNCTION auth.tenant_id()
RETURNS UUID
LANGUAGE SQL
STABLE
SECURITY DEFINER
AS $$
  SELECT COALESCE(
    (current_setting('request.jwt.claims', true)::jsonb ->> 'app_metadata')::jsonb ->> 'tenant_id',
    NULL
  )::UUID
$$;

-- Function để kiểm tra user có role admin không
CREATE OR REPLACE FUNCTION auth.is_admin()
RETURNS BOOLEAN
LANGUAGE SQL
STABLE
SECURITY DEFINER
AS $$
  SELECT EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND role = 'admin'
    AND tenant_id = auth.tenant_id()
  )
$$;

-- Function để kiểm tra user có role manager không
CREATE OR REPLACE FUNCTION auth.is_manager_or_admin()
RETURNS BOOLEAN
LANGUAGE SQL
STABLE
SECURITY DEFINER
AS $$
  SELECT EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND role IN ('admin', 'manager')
    AND tenant_id = auth.tenant_id()
  )
$$;
```

## 3. Tạo RLS Policies

### Policies cho Tenants
```sql
-- Tenants: Users chỉ có thể xem tenant của mình
CREATE POLICY "Users can view own tenant"
  ON public.tenants
  FOR SELECT
  TO authenticated
  USING (id = auth.tenant_id());

-- Chỉ admin mới có thể update tenant settings
CREATE POLICY "Admins can update tenant"
  ON public.tenants
  FOR UPDATE
  TO authenticated
  USING (id = auth.tenant_id() AND (SELECT auth.is_admin()));
```

### Policies cho Profiles
```sql
-- Profiles: Users có thể xem profiles trong cùng tenant
CREATE POLICY "Users can view profiles in same tenant"
  ON public.profiles
  FOR SELECT
  TO authenticated
  USING (tenant_id = auth.tenant_id());

-- Users có thể update profile của chính mình
CREATE POLICY "Users can update own profile"
  ON public.profiles
  FOR UPDATE
  TO authenticated
  USING (id = auth.uid() AND tenant_id = auth.tenant_id())
  WITH CHECK (id = auth.uid() AND tenant_id = auth.tenant_id());

-- Admins có thể insert profiles mới trong tenant
CREATE POLICY "Admins can insert profiles"
  ON public.profiles
  FOR INSERT
  TO authenticated
  WITH CHECK (
    tenant_id = auth.tenant_id() 
    AND (SELECT auth.is_admin())
  );

-- Admins có thể delete profiles (trừ chính mình)
CREATE POLICY "Admins can delete other profiles"
  ON public.profiles
  FOR DELETE
  TO authenticated
  USING (
    tenant_id = auth.tenant_id() 
    AND id != auth.uid()
    AND (SELECT auth.is_admin())
  );
```

### Policies cho Products
```sql
-- Products: Users có thể xem products trong tenant
CREATE POLICY "Users can view products in tenant"
  ON public.products
  FOR SELECT
  TO authenticated
  USING (tenant_id = auth.tenant_id());

-- Managers và Admins có thể tạo products
CREATE POLICY "Managers can create products"
  ON public.products
  FOR INSERT
  TO authenticated
  WITH CHECK (
    tenant_id = auth.tenant_id() 
    AND (SELECT auth.is_manager_or_admin())
  );

-- Managers và Admins có thể update products
CREATE POLICY "Managers can update products"
  ON public.products
  FOR UPDATE
  TO authenticated
  USING (
    tenant_id = auth.tenant_id() 
    AND (SELECT auth.is_manager_or_admin())
  )
  WITH CHECK (
    tenant_id = auth.tenant_id() 
    AND (SELECT auth.is_manager_or_admin())
  );

-- Admins có thể delete products
CREATE POLICY "Admins can delete products"
  ON public.products
  FOR DELETE
  TO authenticated
  USING (
    tenant_id = auth.tenant_id() 
    AND (SELECT auth.is_admin())
  );
```

### Policies cho Orders
```sql
-- Orders: Users có thể xem orders trong tenant
CREATE POLICY "Users can view orders in tenant"
  ON public.orders
  FOR SELECT
  TO authenticated
  USING (tenant_id = auth.tenant_id());

-- All authenticated users có thể tạo orders
CREATE POLICY "Users can create orders"
  ON public.orders
  FOR INSERT
  TO authenticated
  WITH CHECK (tenant_id = auth.tenant_id());

-- Managers và Admins có thể update orders
CREATE POLICY "Managers can update orders"
  ON public.orders
  FOR UPDATE
  TO authenticated
  USING (
    tenant_id = auth.tenant_id() 
    AND (SELECT auth.is_manager_or_admin())
  )
  WITH CHECK (
    tenant_id = auth.tenant_id() 
    AND (SELECT auth.is_manager_or_admin())
  );
```

## 4. Triggers cho Updated_at

```sql
-- Function để tự động update updated_at
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers cho các tables
CREATE TRIGGER handle_updated_at_tenants
  BEFORE UPDATE ON public.tenants
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_updated_at_profiles
  BEFORE UPDATE ON public.profiles
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_updated_at_products
  BEFORE UPDATE ON public.products
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_updated_at_orders
  BEFORE UPDATE ON public.orders
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();
```

## 5. Thiết lập User khi đăng ký

### Function để tạo profile khi user đăng ký
```sql
-- Function để tự động tạo profile khi có user mới
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  tenant_uuid UUID;
BEGIN
  -- Lấy tenant_id từ app_metadata
  tenant_uuid := (NEW.raw_app_meta_data->>'tenant_id')::UUID;
  
  -- Tạo profile nếu có tenant_id
  IF tenant_uuid IS NOT NULL THEN
    INSERT INTO public.profiles (id, tenant_id, email, full_name)
    VALUES (
      NEW.id,
      tenant_uuid,
      NEW.email,
      NEW.raw_user_meta_data->>'full_name'
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger khi có user mới
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
```

## 6. Application Code Example

### Đăng ký user với tenant_id
```javascript
// Tạo tenant mới (chỉ service role)
const { data: tenant } = await supabase
  .from('tenants')
  .insert({
    name: 'My Company',
    slug: 'my-company'
  })
  .select()
  .single();

// Đăng ký user với tenant_id trong app_metadata
const { data: user, error } = await supabase.auth.signUp({
  email: '<EMAIL>',
  password: 'password123',
  options: {
    data: {
      full_name: 'John Doe'
    }
  }
});

// Update app_metadata với service role key
const { error: updateError } = await supabaseAdmin.auth.admin.updateUserById(
  user.user.id,
  {
    app_metadata: {
      tenant_id: tenant.id
    }
  }
);
```

### Query dữ liệu
```javascript
// Tất cả queries sẽ tự động filter theo tenant_id nhờ RLS
const { data: products } = await supabase
  .from('products')
  .select('*')
  .eq('status', 'active');

const { data: orders } = await supabase
  .from('orders')
  .select('*')
  .order('created_at', { ascending: false });
```

## 7. Performance Optimization

### Tối ưu indexes
```sql
-- Composite indexes với tenant_id ở cuối
CREATE INDEX idx_products_name_tenant_id ON public.products(name, tenant_id);
CREATE INDEX idx_orders_created_at_tenant_id ON public.orders(created_at DESC, tenant_id);

-- Partial indexes
CREATE INDEX idx_active_products_tenant_id 
  ON public.products(tenant_id) 
  WHERE status = 'active';
```

### Performance monitoring
```sql
-- Enable query analysis (development only)
ALTER ROLE authenticator SET pgrst.db_plan_enabled TO 'true';
NOTIFY pgrst, 'reload config';

-- Sử dụng .explain() trong client để analyze queries
const { data, error, count } = await supabase
  .from('products')
  .select('*', { count: 'exact' })
  .explain();
```

## 8. Security Best Practices

1. **Luôn sử dụng app_metadata** cho tenant_id (không thể sửa từ client)
2. **Enable RLS trên tất cả tables** trong public schema
3. **Sử dụng security definer functions** cho logic phức tạp
4. **Test thoroughly** các RLS policies với different roles
5. **Monitor performance** và optimize indexes thường xuyên
6. **Backup strategy** phù hợp với multi-tenant architecture

## 9. Testing RLS Policies

```sql
-- Test với user cụ thể
SET request.jwt.claims = '{"sub": "user-uuid", "app_metadata": {"tenant_id": "tenant-uuid"}}';

-- Test queries
SELECT * FROM products; -- Should only return tenant's products
SELECT * FROM orders;   -- Should only return tenant's orders

-- Reset
RESET request.jwt.claims;
```

Cấu trúc này đảm bảo:
- **Data isolation**: Mỗi tenant chỉ truy cập được dữ liệu của mình
- **Performance**: Indexes được tối ưu cho RLS queries
- **Security**: app_metadata không thể modify từ client
- **Scalability**: Có thể handle nhiều tenants trên cùng database
- **Flexibility**: Role-based permissions trong mỗi tenant

---

Tôi sẽ hướng dẫn bạn thiết lập database và tạo policies theo đúng cú pháp Supabase cho multi-tenant ecommerce SaaS:Đây là hướng dẫn chi tiết thiết lập multi-tenant database với Supabase theo đúng cú pháp. Một số điểm quan trọng:

## Các điểm cần lưu ý:

1. **Security**: 
   - Sử dụng `app_metadata` thay vì `user_metadata` cho `tenant_id`
   - Enable RLS trên tất cả tables trong public schema
   - Test kỹ lưỡng các policies

2. **Performance**: 
   - Đặt `tenant_id` làm cột cuối trong composite indexes
   - Sử dụng security definer functions để tránh joins trong RLS
   - Monitor query performance với `.explain()`

3. **Application Flow**:
   - Tạo tenant trước khi đăng ký user
   - Set `tenant_id` trong `app_metadata` khi user đăng ký
   - Tất cả queries sẽ tự động filter theo tenant nhờ RLS

4. **Role-based Permissions**:
   - Admin: Full access trong tenant
   - Manager: Quản lý products, orders
   - Member: View-only access

Bạn có cần tôi giải thích thêm về bất kỳ phần nào hoặc customize cho use case cụ thể của ứng dụng ecommerce không?