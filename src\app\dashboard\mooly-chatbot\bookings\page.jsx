'use client';

import { <PERSON>, Card, Grid, Stack, Button, Select, MenuItem, Container, Typography, FormControl } from '@mui/material';

import { DashboardContent } from 'src/layouts/dashboard';

import { Iconify } from 'src/components/iconify';
import { CustomBreadcrumbs } from 'src/components/custom-breadcrumbs';

// ----------------------------------------------------------------------

export default function BookingsPage() {
  const statsCards = [
    {
      title: 'Hôm nay',
      value: '0',
      subtitle: 'Lịch hẹn',
      icon: 'eva:calendar-fill',
      color: 'primary',
    },
    {
      title: 'Tuần này',
      value: '0',
      subtitle: 'Lịch hẹn',
      icon: 'eva:bar-chart-fill',
      color: 'success',
    },
    {
      title: 'Ch<PERSON> xác nhận',
      value: '0',
      subtitle: 'Lịch hẹn',
      icon: 'eva:clock-fill',
      color: 'warning',
    },
    {
      title: 'Đã hoàn thành',
      value: '0',
      subtitle: 'Lịch hẹn',
      icon: 'eva:checkmark-circle-fill',
      color: 'info',
    },
  ];

  return (
    <DashboardContent>
      <Container maxWidth="xl">
        <CustomBreadcrumbs
          heading="Quản lý đặt lịch"
          subheading="Theo dõi và quản lý các lịch hẹn từ khách hàng"
          links={[
            { name: 'Dashboard', href: '/dashboard' },
            { name: 'Đặt lịch' },
          ]}
          sx={{ mb: 3 }}
        />

        <Grid container spacing={3} sx={{ mb: 3 }}>
          {statsCards.map((card, index) => (
            <Grid item size={{xs: 12, sm: 6, md: 3}} key={index}>
              <Card sx={{ p: 3 }}>
                <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" sx={{ color: 'text.secondary' }}>
                    {card.title}
                  </Typography>
                  <Box
                    sx={{
                      width: 32,
                      height: 32,
                      borderRadius: 1.5,
                      bgcolor: `${card.color}.lighter`,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <Iconify icon={card.icon} width={16} sx={{ color: `${card.color}.main` }} />
                  </Box>
                </Stack>
                <Typography variant="h3" sx={{ mb: 0.5 }}>
                  {card.value}
                </Typography>
                <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                  {card.subtitle}
                </Typography>
              </Card>
            </Grid>
          ))}
        </Grid>

        <Grid container spacing={3}>
          <Grid item size={{xs: 12, lg: 8}}>
            <Card>
              <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ p: 3, borderBottom: 1, borderColor: 'divider' }}>
                <Typography variant="h6">Lịch hẹn gần đây</Typography>
                <Stack direction="row" spacing={2}>
                  <FormControl size="small" sx={{ minWidth: 160 }}>
                    <Select defaultValue="all" size="small">
                      <MenuItem value="all">Tất cả trạng thái</MenuItem>
                      <MenuItem value="pending">Chờ xác nhận</MenuItem>
                      <MenuItem value="confirmed">Đã xác nhận</MenuItem>
                      <MenuItem value="in-progress">Đang thực hiện</MenuItem>
                      <MenuItem value="completed">Hoàn thành</MenuItem>
                      <MenuItem value="cancelled">Đã hủy</MenuItem>
                    </Select>
                  </FormControl>
                  <Button variant="contained" startIcon={<Iconify icon="eva:plus-fill" />} size="small">
                    Tạo lịch hẹn
                  </Button>
                </Stack>
              </Stack>

              <Box sx={{ p: 6 }}>
                <Stack alignItems="center" spacing={3}>
                  <Box
                    sx={{
                      width: 96,
                      height: 96,
                      borderRadius: '50%',
                      bgcolor: 'grey.100',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <Iconify icon="eva:calendar-fill" width={48} sx={{ color: 'text.disabled' }} />
                  </Box>

                  <Typography variant="h6">Chưa có lịch hẹn nào</Typography>

                  <Typography variant="body1" sx={{ textAlign: 'center', maxWidth: 480, color: 'text.secondary' }}>
                    Khi khách hàng đặt lịch hẹn qua chatbot hoặc bạn tạo lịch hẹn thủ công, chúng sẽ hiển thị ở đây.
                  </Typography>

                  <Button variant="contained" size="large">
                    Tạo lịch hẹn đầu tiên
                  </Button>
                </Stack>
              </Box>
            </Card>
          </Grid>

          <Grid item size={{xs: 12, lg: 4}}>
            <Card>
              <Stack sx={{ p: 3, borderBottom: 1, borderColor: 'divider' }}>
                <Typography variant="h6">Lịch làm việc hôm nay</Typography>
                <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                  {new Date().toLocaleDateString('vi-VN', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </Typography>
              </Stack>
              <Box sx={{ p: 6 }}>
                <Stack alignItems="center" spacing={2}>
                  <Box
                    sx={{
                      width: 64,
                      height: 64,
                      borderRadius: '50%',
                      bgcolor: 'grey.100',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <Iconify icon="eva:clock-fill" width={32} sx={{ color: 'text.disabled' }} />
                  </Box>
                  <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                    Không có lịch hẹn nào hôm nay
                  </Typography>
                </Stack>
              </Box>
            </Card>
          </Grid>
        </Grid>
      </Container>
    </DashboardContent>
  );
}
