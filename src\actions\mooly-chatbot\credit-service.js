'use client';

// ✅ UPDATED: Credit service đã được cập nhật để sử dụng trực tiếp Supabase client
// với RLS đã được cấu hình đúng, không cần qua API endpoints nữa

import { safeAdd, toNumber } from 'src/utils/number-calculation';

import { getCachedTenantId } from './tenant-middleware';
import { fetchData, createData, updateData } from './supabase-utils';
import {
  PAYMENT_STATUSES,
  TENANT_CREDITS_TABLE,
  CREDIT_PACKAGES_TABLE,
  CREDIT_PAYMENTS_TABLE,
  CREDIT_TRANSACTION_TYPES,
  CREDIT_TRANSACTIONS_TABLE,
} from './credit-constants';

// Helper function để xử lý lỗi tham số đầu vào
const validateParam = (param, paramName) => {
  if (!param) {
    return { success: false, error: `${paramName} is required`, data: null };
  }
  return null;
};

/**
 * <PERSON><PERSON><PERSON> danh sách gói credit với các tùy chọn lọc
 * @param {Object} options - <PERSON><PERSON><PERSON> tùy chọn truy vấn
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getCreditPackages(options = {}) {
  const defaultOptions = {
    orderBy: 'sortOrder',
    ascending: true,
    filters: { isActive: true },
    ...options,
  };

  // Bảng credit_packages đã được thêm vào EXCLUDED_TABLES trong tenant-middleware.js
  // nên không cần thêm tenant_id vào filters
  return fetchData(CREDIT_PACKAGES_TABLE, defaultOptions);
}

/**
 * Lấy thông tin chi tiết gói credit theo ID
 * @param {string} packageId - ID gói credit
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getCreditPackageById(packageId) {
  const validationError = validateParam(packageId, 'packageId');
  if (validationError) return validationError;

  return fetchData(CREDIT_PACKAGES_TABLE, {
    filters: { id: packageId },
    single: true,
  });
}

/**
 * Lấy số dư credit của tenant trực tiếp từ Supabase với RLS
 * @returns {Promise<Object>} - Kết quả từ database
 */
export async function getTenantCredit() {
  try {
    // ✅ SỬ DỤNG TRỰC TIẾP SUPABASE CLIENT VỚI RLS ĐÃ ĐƯỢC CẤU HÌNH
    const result = await fetchData(TENANT_CREDITS_TABLE, {
      single: true,
      select: 'id, balance, last_updated, created_at, updated_at'
    });

    if (!result.success) {
      // Nếu không tìm thấy bản ghi credit, trả về balance = 0
      if (result.error?.code === 'PGRST116') {
        return {
          success: true,
          data: {
            balance: 0,
            lastUpdated: null,
            id: null
          },
          error: null
        };
      }
      return result;
    }

    return {
      success: true,
      data: {
        id: result.data.id,
        balance: result.data.balance || 0,
        lastUpdated: result.data.lastUpdated || result.data.updatedAt
      },
      error: null
    };
  } catch (error) {
    console.error('Error fetching tenant credit:', error);
    return { success: false, error: 'Failed to fetch credit balance', data: null };
  }
}

/**
 * Tạo hoặc cập nhật số dư credit của tenant trực tiếp với RLS
 * @param {number} balance - Số dư credit mới
 * @returns {Promise<Object>} - Kết quả từ database
 */
export async function updateTenantCredit(balance) {
  try {
    // ✅ SỬ DỤNG TRỰC TIẾP SUPABASE CLIENT VỚI RLS
    // Kiểm tra xem tenant đã có bản ghi credit chưa
    const { data: existingCredit } = await getTenantCredit();

    if (existingCredit && existingCredit.id) {
      // Cập nhật số dư hiện tại
      return updateData(
        TENANT_CREDITS_TABLE,
        {
          balance,
          lastUpdated: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        { id: existingCredit.id }
      );
    }

    // Tạo bản ghi credit mới - RLS sẽ tự động set tenant_id
    return createData(TENANT_CREDITS_TABLE, {
      balance,
      lastUpdated: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error updating tenant credit:', error);
    return { success: false, error: 'Failed to update credit balance', data: null };
  }
}

/**
 * Thêm credit transaction
 * @param {Object} transactionData - Dữ liệu giao dịch
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function addCreditTransaction(transactionData) {
  const { amount, type, description, referenceId, referenceType, createdBy } = transactionData;

  // Lấy số dư hiện tại của tenant
  const { data: tenantCredit } = await getTenantCredit();

  // Nếu tenant chưa có bản ghi credit, tạo mới với số dư 0
  const currentBalance = toNumber(tenantCredit ? tenantCredit.balance : 0);

  // Tính toán số dư mới
  const newBalance = safeAdd(currentBalance, amount);

  // ✅ SỬ DỤNG TRỰC TIẾP SUPABASE CLIENT VỚI RLS - tenant_id sẽ tự động được set
  const transactionResult = await createData(CREDIT_TRANSACTIONS_TABLE, {
    amount,
    balanceBefore: currentBalance,
    balanceAfter: newBalance,
    type,
    description,
    referenceId,
    referenceType,
    createdBy,
  });

  // Cập nhật số dư của tenant
  if (transactionResult.success) {
    await updateTenantCredit(newBalance);
  }

  return transactionResult;
}

/**
 * Lấy lịch sử giao dịch credit của tenant
 * @param {Object} options - Các tùy chọn truy vấn
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getCreditTransactions(options = {}) {
  // Không cần truyền tenantId vì supabase-utils sẽ tự động thêm tenant_id vào filters
  const defaultOptions = {
    orderBy: 'createdAt',
    ascending: false,
    ...options,
  };

  return fetchData(CREDIT_TRANSACTIONS_TABLE, defaultOptions);
}

/**
 * Tạo thanh toán credit mới
 * @param {Object} paymentData - Dữ liệu thanh toán
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function createCreditPayment(paymentData) {
  const { packageId, amount, creditAmount, paymentMethod, createdBy } = paymentData;

  if (!packageId) {
    return { success: false, error: 'packageId is required', data: null };
  }

  // ✅ SỬ DỤNG TRỰC TIẾP SUPABASE CLIENT VỚI RLS - tenant_id sẽ tự động được set
  return createData(CREDIT_PAYMENTS_TABLE, {
    packageId,
    amount,
    creditAmount,
    paymentMethod,
    paymentStatus: PAYMENT_STATUSES.PENDING,
    createdBy,
  });
}

/**
 * Lấy thông tin thanh toán theo ID
 * @param {string} paymentId - ID của thanh toán
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getPaymentById(paymentId) {
  const validationError = validateParam(paymentId, 'paymentId');
  if (validationError) return validationError;

  return fetchData(CREDIT_PAYMENTS_TABLE, {
    filters: { id: paymentId },
    single: true,
  });
}

/**
 * Lấy thông tin thanh toán theo order_code
 * @param {number} orderCode - Mã đơn hàng
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getPaymentByOrderCode(orderCode) {
  const validationError = validateParam(orderCode, 'orderCode');
  if (validationError) return validationError;

  return fetchData(CREDIT_PAYMENTS_TABLE, {
    filters: { orderCode },
    single: true,
  });
}

/**
 * Cập nhật dữ liệu thanh toán
 * @param {string} paymentId - ID của thanh toán
 * @param {Object} dataToUpdate - Dữ liệu cần cập nhật
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function updatePaymentData(paymentId, dataToUpdate) {
  const validationError = validateParam(paymentId, 'paymentId');
  if (validationError) return validationError;

  return updateData(
    CREDIT_PAYMENTS_TABLE,
    {
      ...dataToUpdate,
      updatedAt: new Date().toISOString(),
    },
    { id: paymentId }
  );
}

/**
 * Cập nhật trạng thái thanh toán sử dụng admin API
 * @param {string} paymentId - ID của thanh toán
 * @param {string} status - Trạng thái mới
 * @param {Object} paymentData - Dữ liệu thanh toán bổ sung
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function updatePaymentStatus(paymentId, status, paymentData = {}) {
  const validationError = validateParam(paymentId, 'paymentId');
  if (validationError) return validationError;

  try {
    // Sử dụng admin API để complete payment
    const response = await fetch('/api/admin/complete-payment', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        paymentId,
        status,
        paymentData,
      }),
    });

    const result = await response.json();

    if (!result.success) {
      return { success: false, error: result.error, data: null };
    }

    return {
      success: true,
      data: result.data,
      error: null
    };
  } catch (error) {
    console.error('Error updating payment status:', error);
    return { success: false, error: 'Failed to update payment status', data: null };
  }
}

/**
 * Lấy lịch sử thanh toán credit của tenant
 * @param {Object} options - Các tùy chọn truy vấn
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getCreditPayments(options = {}) {
  // Không cần truyền tenantId vì supabase-utils sẽ tự động thêm tenant_id vào filters
  const defaultOptions = {
    orderBy: 'createdAt',
    ascending: false,
    ...options,
  };

  return fetchData(CREDIT_PAYMENTS_TABLE, defaultOptions);
}

/**
 * Sử dụng credit cho một hoạt động sử dụng secure API
 * @param {number} amount - Số credit sử dụng (số dương)
 * @param {string} description - Mô tả hoạt động
 * @param {string} referenceId - ID tham chiếu (nếu có)
 * @param {string} referenceType - Loại tham chiếu (nếu có)
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function useCredit(amount, description, referenceId, referenceType) {
  try {
    // Validation đầu vào
    if (!amount || amount <= 0) {
      return { success: false, error: 'Amount must be a positive number', data: null };
    }

    if (!description) {
      return { success: false, error: 'Description is required', data: null };
    }

    const response = await fetch('/api/use-credit', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        amount: Math.abs(amount), // Đảm bảo amount là số dương
        description,
        referenceId,
        referenceType: referenceType || 'chatbot_usage',
      }),
    });

    const result = await response.json();

    if (!result.success) {
      return {
        success: false,
        error: result.error,
        data: result.currentBalance ? { balance: result.currentBalance } : null
      };
    }

    return {
      success: true,
      data: {
        transactionId: result.data.transactionId,
        previousBalance: result.data.previousBalance,
        newBalance: result.data.newBalance,
        amountUsed: result.data.amountUsed,
      },
      error: null
    };
  } catch (error) {
    console.error('Error using credit:', error);
    return { success: false, error: 'Failed to use credit', data: null };
  }
}
