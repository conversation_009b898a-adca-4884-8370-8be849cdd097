import { NextResponse } from 'next/server';

import { withTenantAuth, validateTenantId } from 'src/utils/server-auth';

/**
 * API route để cập nhật thông tin sản phẩm trong Weaviate theo ID
 * @param {Request} request - <PERSON><PERSON><PERSON> cầu HTTP
 * @returns {Promise<NextResponse>} - Phản hồi HTTP
 */
export const PUT = withTenantAuth(async (request, { tenantId, userId }) => {
  try {
    // Validate request body và kiểm tra tenant_id
    // const validation = await validateTenantId(request, request.tenant_id);
    // if (!validation.valid) {
    //   return NextResponse.json({ error: validation.error }, { status: validation.status });
    // }

    const { product_id, is_active, bot_id } = await request.json();

    // Kiểm tra dữ liệu đầu vào
    if (!product_id) {
      return NextResponse.json({ error: 'Missing required field: product_id' }, { status: 400 });
    }

    // Tạo đối tượng dữ liệu cập nhật
    const updateData = {
      tenant_id: request.tenant_id, // Sử dụng tenant_id từ xác thực
      product_id,
    };

    // Thêm các trường tùy chọn nếu có
    if (is_active !== undefined) {
      updateData.is_active = is_active;
    }

    if (bot_id !== undefined) {
      updateData.bot_id = bot_id;
    }

    // Lấy URL Weaviate từ biến môi trường
    const BACKEND_API_URL = process.env.BACKEND_API_URL || 'http://localhost:3000';

    // Gọi API Weaviate
    const response = await fetch(`${BACKEND_API_URL}/api/weaviate/products/update-by-id`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${request.user.accessToken}`,
      },
      body: JSON.stringify(updateData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      return NextResponse.json(
        { error: errorData.message || 'Failed to update product' },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in update-by-id API route:', error);
    return NextResponse.json({ error: error.message || 'Internal server error' }, { status: 500 });
  }
});
