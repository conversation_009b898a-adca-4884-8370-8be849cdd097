import { NextResponse } from 'next/server';
import { createClient } from 'src/utils/supabase/server';

/**
 * Facebook Integration Analytics API
 * Get analytics and activity data for Facebook/Instagram integration
 */

// GET: Fetch analytics data
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const pageId = searchParams.get('pageId');
    const period = searchParams.get('period') || '7d'; // 1d, 7d, 30d
    const limit = parseInt(searchParams.get('limit') || '50');
    
    const supabase = await createClient();
    
    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }
    
    // Calculate date range
    const now = new Date();
    let startDate;
    
    switch (period) {
      case '1d':
        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '7d':
      default:
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    }
    
    // Build base query
    let query = supabase
      .from('facebook_activity_logs')
      .select('*')
      .gte('created_at', startDate.toISOString())
      .order('created_at', { ascending: false });
    
    // Filter by page if specified
    if (pageId) {
      query = query.eq('page_id', pageId);
    }
    
    // Get activity logs
    const { data: activities, error: activitiesError } = await query.limit(limit);
    
    if (activitiesError) {
      console.error('❌ Error fetching activities:', activitiesError);
      return NextResponse.json({ 
        error: 'Failed to fetch activities',
        details: activitiesError.message 
      }, { status: 500 });
    }
    
    // Calculate analytics
    const analytics = calculateAnalytics(activities || [], period);
    
    // Get page information if pageId specified
    let pageInfo = null;
    if (pageId) {
      const { data: page } = await supabase
        .from('facebook_accounts')
        .select('page_id, page_name, instagram_account_id')
        .eq('page_id', pageId)
        .single();
      
      pageInfo = page;
    }
    
    return NextResponse.json({
      success: true,
      analytics,
      activities: activities?.map(activity => ({
        id: activity.id,
        pageId: activity.page_id,
        activity: activity.activity,
        metadata: activity.metadata,
        createdAt: activity.created_at
      })) || [],
      pageInfo,
      period,
      totalActivities: activities?.length || 0
    });
    
  } catch (error) {
    console.error('💥 Error in GET analytics:', error);
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error.message 
    }, { status: 500 });
  }
}

// Calculate analytics from activity data
function calculateAnalytics(activities, period) {
  const analytics = {
    totalActivities: activities.length,
    autoRepliesSent: 0,
    autoRepliesFailed: 0,
    commentsProcessed: 0,
    messagesProcessed: 0,
    instagramActivities: 0,
    facebookActivities: 0,
    activityByDay: {},
    activityByType: {},
    successRate: 0
  };
  
  // Process each activity
  activities.forEach(activity => {
    const date = new Date(activity.created_at).toISOString().split('T')[0];
    const activityType = activity.activity;
    
    // Count by day
    if (!analytics.activityByDay[date]) {
      analytics.activityByDay[date] = 0;
    }
    analytics.activityByDay[date]++;
    
    // Count by type
    if (!analytics.activityByType[activityType]) {
      analytics.activityByType[activityType] = 0;
    }
    analytics.activityByType[activityType]++;
    
    // Specific counters
    switch (activityType) {
      case 'auto_reply_sent':
        analytics.autoRepliesSent++;
        break;
      case 'auto_reply_failed':
        analytics.autoRepliesFailed++;
        break;
      case 'new_comment':
        analytics.commentsProcessed++;
        break;
      case 'private_message':
        analytics.messagesProcessed++;
        break;
    }
    
    // Platform detection (basic)
    if (activity.metadata?.platform === 'instagram' || 
        activity.metadata?.instagramAccountId) {
      analytics.instagramActivities++;
    } else {
      analytics.facebookActivities++;
    }
  });
  
  // Calculate success rate
  const totalReplies = analytics.autoRepliesSent + analytics.autoRepliesFailed;
  if (totalReplies > 0) {
    analytics.successRate = Math.round((analytics.autoRepliesSent / totalReplies) * 100);
  }
  
  // Fill missing days with 0
  const days = getDaysInPeriod(period);
  days.forEach(day => {
    if (!analytics.activityByDay[day]) {
      analytics.activityByDay[day] = 0;
    }
  });
  
  return analytics;
}

// Get array of days for the period
function getDaysInPeriod(period) {
  const days = [];
  const now = new Date();
  let numDays;
  
  switch (period) {
    case '1d':
      numDays = 1;
      break;
    case '30d':
      numDays = 30;
      break;
    case '7d':
    default:
      numDays = 7;
  }
  
  for (let i = numDays - 1; i >= 0; i--) {
    const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
    days.push(date.toISOString().split('T')[0]);
  }
  
  return days;
}

// POST: Log custom activity (for testing or manual events)
export async function POST(request) {
  try {
    const { pageId, activity, metadata = {} } = await request.json();
    
    if (!pageId || !activity) {
      return NextResponse.json({ 
        error: 'Page ID and activity type required' 
      }, { status: 400 });
    }
    
    const supabase = await createClient();
    
    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }
    
    // Verify page access
    const { data: page, error: pageError } = await supabase
      .from('facebook_accounts')
      .select('page_id')
      .eq('page_id', pageId)
      .single();
    
    if (pageError || !page) {
      return NextResponse.json({ 
        error: 'Page not found or access denied' 
      }, { status: 404 });
    }
    
    // Log the activity
    const { data, error } = await supabase
      .from('facebook_activity_logs')
      .insert({
        page_id: pageId,
        activity,
        metadata: {
          ...metadata,
          manual: true,
          userId: user.id
        }
      })
      .select()
      .single();
    
    if (error) {
      console.error('❌ Error logging activity:', error);
      return NextResponse.json({ 
        error: 'Failed to log activity',
        details: error.message 
      }, { status: 500 });
    }
    
    return NextResponse.json({
      success: true,
      message: 'Activity logged successfully',
      activity: {
        id: data.id,
        pageId: data.page_id,
        activity: data.activity,
        metadata: data.metadata,
        createdAt: data.created_at
      }
    });
    
  } catch (error) {
    console.error('💥 Error in POST analytics:', error);
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error.message 
    }, { status: 500 });
  }
}
