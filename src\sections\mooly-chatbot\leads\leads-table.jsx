'use client';

import { useState, useCallback, memo } from 'react';

import {
  Box,
  Chip,
  Table,
  Stack,
  Avatar,
  Tooltip,
  TableRow,
  Checkbox,
  TableBody,
  TableCell,
  TableHead,
  IconButton,
  Typography,
  TableContainer,
  CircularProgress,
} from '@mui/material';

import { Label } from 'src/components/label';
import { Iconify } from 'src/components/iconify';

import { getLeadStatusOptions } from 'src/actions/mooly-chatbot/chatbot-lead-service';

// =====================================================
// UTILITY FUNCTIONS
// =====================================================

// Utility function to format date
const formatDate = (date) => {
  if (!date) return '';
  const d = new Date(date);
  return d.toLocaleDateString('vi-VN', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });
};

const formatTime = (date) => {
  if (!date) return '';
  const d = new Date(date);
  return d.toLocaleTimeString('vi-VN', {
    hour: '2-digit',
    minute: '2-digit'
  });
};

// Utility function to extract lead data - đơn giản hóa
const getLeadData = (row) => {
  // supabase-utils đã tự động convert snake_case -> camelCase
  // Chỉ cần sử dụng trực tiếp các field đã được convert
  return {
    fullName: row.fullName || '',
    email: row.email || '',
    phone: row.phone || '',
    address: row.address || '',
    company: row.company || '',
  };
};

// =====================================================
// TABLE CONFIGURATION
// =====================================================

const TABLE_HEAD = [
  { id: 'select', width: 40 },
  { id: 'info', label: 'Thông tin Lead' },
  { id: 'status', label: 'Trạng thái' },
  { id: 'source', label: 'Nguồn' },
  { id: 'assigned', label: 'Phụ trách' },
  { id: 'followUp', label: 'Theo dõi' },
  { id: 'createdAt', label: 'Ngày tạo' },
  { id: 'actions', label: 'Thao tác', width: 140 },
];

// =====================================================
// MAIN COMPONENT
// =====================================================

const LeadsTable = memo(function LeadsTable({
  tableData,
  onSelectRow,
  onSelectAllRows,
  onEditRow,
  onViewRow,
  onDeleteRow,
  selectedRows,
  loading,
}) {
  const [dense] = useState(false);

  const statusOptions = getLeadStatusOptions();

  const handleSelectAllRows = useCallback(
    (checked) => {
      const newSelecteds = checked ? tableData.map((row) => row.id) : [];
      onSelectAllRows(checked, newSelecteds);
    },
    [onSelectAllRows, tableData]
  );

  return (
    <TableContainer sx={{ position: 'relative', overflow: 'unset', width: '100%' }}>
      <Table size={dense ? 'small' : 'medium'} sx={{ minWidth: 960 }}>
        <TableHead>
          <TableRow>
            {TABLE_HEAD.map((headCell) => (
              <TableCell
                key={headCell.id}
                align={headCell.align || 'left'}
                sx={{
                  width: headCell.width,
                  minWidth: headCell.minWidth,
                }}
              >
                {headCell.id === 'select' ? (
                  <Checkbox
                    indeterminate={
                      selectedRows.length > 0 && selectedRows.length < tableData.length
                    }
                    checked={tableData.length > 0 && selectedRows.length === tableData.length}
                    onChange={(event) => handleSelectAllRows(event.target.checked)}
                  />
                ) : (
                  headCell.label
                )}
              </TableCell>
            ))}
          </TableRow>
        </TableHead>

        <TableBody>
          {loading ? (
            <TableRow>
              <TableCell colSpan={TABLE_HEAD.length} align="center" sx={{ py: 10 }}>
                <CircularProgress />
              </TableCell>
            </TableRow>
          ) : tableData.length === 0 ? (
            <TableRow>
              <TableCell colSpan={TABLE_HEAD.length} align="center" sx={{ py: 10 }}>
                <Stack spacing={2} alignItems="center">
                  <Typography variant="h6" sx={{ color: 'text.secondary' }}>
                    Chưa có leads nào
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'text.disabled' }}>
                    Thêm leads mới để bắt đầu quản lý
                  </Typography>
                </Stack>
              </TableCell>
            </TableRow>
          ) : (
            tableData.map((row) => (
              <LeadTableRow
                key={row.id}
                row={row}
                selected={selectedRows.includes(row.id)}
                onSelectRow={() => onSelectRow(row.id)}
                onViewRow={() => onViewRow(row)}
                onEditRow={() => onEditRow(row)}
                onDeleteRow={() => onDeleteRow(row)}
                statusOptions={statusOptions}
              />
            ))
          )}
        </TableBody>
      </Table>
    </TableContainer>
  );
});

export default LeadsTable;

// =====================================================
// TABLE ROW COMPONENT
// =====================================================

const LeadTableRow = memo(function LeadTableRow({
  row,
  selected,
  onSelectRow,
  onViewRow,
  onEditRow,
  onDeleteRow,
  statusOptions,
}) {
  // Extract lead data
  const leadData = getLeadData(row);

  const statusOption = statusOptions.find((option) => option.value === row.status);

  // Get display name for avatar
  const getDisplayName = () => {
    if (leadData.fullName) return leadData.fullName;
    if (leadData.email) return leadData.email;
    if (leadData.phone) return leadData.phone;
    return 'Unknown';
  };

  const displayName = getDisplayName();

  const renderPrimary = (
    <TableRow hover selected={selected}>
      <TableCell padding="checkbox">
        <Checkbox checked={selected} onClick={onSelectRow} />
      </TableCell>

      <TableCell>
        <Stack direction="row" alignItems="center" spacing={2}>
          <Avatar sx={{ bgcolor: 'primary.main' }}>
            {displayName.charAt(0).toUpperCase()}
          </Avatar>

          <Stack sx={{ minWidth: 200 }}>
            <Typography variant="subtitle2" noWrap>
              {leadData.fullName || 'Chưa có tên'}
            </Typography>
            {
              leadData.email && (
                <Typography variant="body2" sx={{ color: 'text.secondary' }} noWrap>
                  {leadData.email}
                </Typography>
              )
            }

            {
              leadData.phone && (
                <Typography variant="body2" sx={{ color: 'text.secondary' }} noWrap>
                  {leadData.phone}
                </Typography>
              )
            }
          </Stack>
        </Stack>
      </TableCell>

      

      <TableCell>
        <Label
          variant="soft"
          color={statusOption?.color || 'default'}
        >
          {statusOption?.label || row.status || 'Không xác định'}
        </Label>
      </TableCell>

      <TableCell>
        <Chip
          size="small"
          variant="outlined"
          label={row.source || 'Unknown'}
          sx={{ textTransform: 'capitalize' }}
        />
      </TableCell>

      <TableCell>
        {row.assignedTo ? (
          <Stack direction="row" alignItems="center" spacing={1}>
            <Avatar sx={{ width: 24, height: 24 }}>
              {row.assignedTo.charAt(0).toUpperCase()}
            </Avatar>
            <Typography variant="body2" noWrap>
              {row.assignedTo}
            </Typography>
          </Stack>
        ) : (
          <Typography variant="body2" sx={{ color: 'text.disabled' }}>
            Chưa phân công
          </Typography>
        )}
      </TableCell>

      <TableCell>
        {row.nextFollowUpAt ? (
          <Stack>
            <Typography variant="body2">
              {formatDate(row.nextFollowUpAt)}
            </Typography>
            <Typography variant="caption" sx={{ color: 'text.secondary' }}>
              {formatTime(row.nextFollowUpAt)}
            </Typography>
          </Stack>
        ) : (
          <Typography variant="body2" sx={{ color: 'text.disabled' }}>
            Chưa đặt lịch
          </Typography>
        )}
      </TableCell>

      <TableCell>
        <Stack>
          <Typography variant="body2">
            {formatDate(row.createdAt)}
          </Typography>
          <Typography variant="caption" sx={{ color: 'text.secondary' }}>
            {formatTime(row.createdAt)}
          </Typography>
        </Stack>
      </TableCell>

      <TableCell align="right">
        <Stack direction="row" spacing={0.5}>
          <Tooltip title="Xem chi tiết">
            <IconButton
              size="small"
              color="primary"
              onClick={onViewRow}
            >
              <Iconify icon="solar:eye-bold" width={18} />
            </IconButton>
          </Tooltip>

          <Tooltip title="Chỉnh sửa">
            <IconButton
              size="small"
              color="default"
              onClick={onEditRow}
            >
              <Iconify icon="solar:pen-bold" width={18} />
            </IconButton>
          </Tooltip>

          <Tooltip title="Xóa">
            <IconButton
              size="small"
              color="error"
              onClick={onDeleteRow}
            >
              <Iconify icon="solar:trash-bin-trash-bold" width={18} />
            </IconButton>
          </Tooltip>
        </Stack>
      </TableCell>
    </TableRow>
  );

  return renderPrimary;
}); 