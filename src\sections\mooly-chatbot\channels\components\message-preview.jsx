'use client';

import PropTypes from 'prop-types';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Chip from '@mui/material/Chip';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import { alpha, useTheme } from '@mui/material/styles';

import { Iconify } from 'src/components/iconify';

import {
  MESSAGE_TYPES,
  BUTTON_TYPES,
  hasMessageFormatting
} from 'src/actions/mooly-chatbot/message-formatting-service';

// ----------------------------------------------------------------------

export default function MessagePreview({ rule, compact = false }) {
  const theme = useTheme();

  if (!rule) return null;

  const isFormatted = hasMessageFormatting(rule);

  if (compact) {
    // Compact preview for list view
    return (
      <Box>
        <Typography variant="body2" sx={{ color: 'text.secondary', mb: 0.5 }}>
          {rule.message}
        </Typography>
        
        {isFormatted && (
          <Stack direction="row" spacing={0.5}>
            {rule.messageFormatting?.images?.length > 0 && (
              <Chip
                size="small"
                label={`${rule.messageFormatting.images.length} hình`}
                color="info"
                variant="outlined"
                sx={{ fontSize: '0.75rem', height: 20 }}
              />
            )}
            {rule.messageFormatting?.buttons?.length > 0 && (
              <Chip
                size="small"
                label={`${rule.messageFormatting.buttons.length} nút`}
                color="success"
                variant="outlined"
                sx={{ fontSize: '0.75rem', height: 20 }}
              />
            )}
          </Stack>
        )}
      </Box>
    );
  }

  // Full preview for detail view
  return (
    <Card
      sx={{
        p: 2,
        maxWidth: 400,
        bgcolor: alpha(theme.palette.primary.main, 0.08),
        border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`
      }}
    >
      <Stack spacing={2}>
        {/* Message Content */}
        <Typography variant="body2">
          {rule.message}
        </Typography>

        {/* Images */}
        {rule.messageFormatting?.images?.length > 0 && (
          <Box>
            <Typography variant="caption" sx={{ color: 'text.secondary', mb: 1, display: 'block' }}>
              Hình ảnh ({rule.messageFormatting.images.length})
            </Typography>
            <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
              {rule.messageFormatting.images.map((image) => (
                <Box
                  key={image.id}
                  sx={{
                    width: 60,
                    height: 60,
                    borderRadius: 1,
                    overflow: 'hidden',
                    border: `1px solid ${alpha(theme.palette.grey[500], 0.3)}`
                  }}
                >
                  <img
                    src={image.url}
                    alt={image.alt}
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover'
                    }}
                  />
                </Box>
              ))}
            </Stack>
          </Box>
        )}

        {/* Buttons */}
        {rule.messageFormatting?.buttons?.length > 0 && (
          <Box>
            <Typography variant="caption" sx={{ color: 'text.secondary', mb: 1, display: 'block' }}>
              Nút bấm ({rule.messageFormatting.buttons.length})
            </Typography>
            <Stack spacing={1}>
              {rule.messageFormatting.buttons.map((button) => (
                <Button
                  key={button.id}
                  variant="outlined"
                  size="small"
                  startIcon={
                    <Iconify 
                      icon={button.type === BUTTON_TYPES.LINK ? 'eva:external-link-fill' : 'eva:flash-fill'} 
                      width={16} 
                    />
                  }
                  sx={{
                    justifyContent: 'flex-start',
                    textTransform: 'none',
                    color: 'text.primary',
                    borderColor: alpha(theme.palette.grey[500], 0.3),
                    '&:hover': {
                      borderColor: theme.palette.primary.main,
                      bgcolor: alpha(theme.palette.primary.main, 0.04)
                    }
                  }}
                  disabled
                >
                  <Box sx={{ textAlign: 'left', flexGrow: 1 }}>
                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                      {button.title}
                    </Typography>
                    <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                      {button.type === BUTTON_TYPES.LINK ? button.url : button.payload}
                    </Typography>
                  </Box>
                </Button>
              ))}
            </Stack>
          </Box>
        )}

        {/* Message Type Indicator */}
        <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
          <Chip
            size="small"
            label={rule.messageType === MESSAGE_TYPES.FORMATTED ? 'Tin nhắn định dạng' : 'Tin nhắn thường'}
            color={rule.messageType === MESSAGE_TYPES.FORMATTED ? 'primary' : 'default'}
            variant="outlined"
            sx={{ fontSize: '0.75rem' }}
          />
        </Box>
      </Stack>
    </Card>
  );
}

MessagePreview.propTypes = {
  rule: PropTypes.object,
  compact: PropTypes.bool,
};
