'use client';

import { useState, useEffect, useCallback } from 'react';

import { fetchData } from './supabase-utils';
import { TABLE_NAME, SHIPPING_FEE_TYPES } from './shipping-constants';

/**
 * Lấy phí vận chuyển cố định từ database
 * @returns {Promise<Object>} - K<PERSON><PERSON> quả từ API với phí vận chuyển cố định
 */
export async function getFixedShippingFee() {
  try {
    // Lấy phí vận chuyển cố định đang active
    const result = await fetchData(TABLE_NAME, {
      filters: {
        type: SHIPPING_FEE_TYPES.FIXED,
        isActive: true,
      },
      orderBy: 'sort_order',
      ascending: true,
      limit: 1, // Chỉ lấy 1 phí vận chuyển cố định đầu tiên
    });

    if (result.success && result.data && result.data.length > 0) {
      return {
        success: true,
        data: result.data[0],
        error: null,
      };
    }

    // Nế<PERSON> không tìm thấy phí vận chuyển cố định, trả về 0
    return {
      success: true,
      data: {
        type: SHIPPING_FEE_TYPES.FIXED,
        amount: 0,
      },
      error: null,
    };
  } catch (error) {
    console.error('Lỗi khi lấy phí vận chuyển cố định:', error);
    return {
      success: false,
      data: {
        type: SHIPPING_FEE_TYPES.FIXED,
        amount: 0,
      },
      error,
    };
  }
}

/**
 * Hook để lấy phí vận chuyển cố định
 * @returns {Object} - Phí vận chuyển cố định và các hàm liên quan
 */
export function useFixedShippingFee() {
  const [data, setData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchFixedShippingFee = useCallback(async () => {
    setIsLoading(true);
    try {
      const result = await getFixedShippingFee();
      setData(result.data);
      setError(result.error);
      return result.data;
    } catch (err) {
      console.error('Error fetching fixed shipping fee:', err);
      setError(err);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Tải dữ liệu khi component mount
  useEffect(() => {
    fetchFixedShippingFee();
  }, [fetchFixedShippingFee]);

  return {
    fixedShippingFee: data,
    isLoading,
    error,
    refetch: fetchFixedShippingFee,
  };
}

/**
 * Tính toán phí vận chuyển cho đơn hàng
 * @returns {Promise<number>} - Phí vận chuyển
 */
export async function calculateShippingFee() {
  try {
    // Lấy phí vận chuyển cố định
    const result = await getFixedShippingFee();

    if (result.success && result.data) {
      return result.data.amount || 0;
    }

    return 0;
  } catch (error) {
    console.error('Lỗi khi tính toán phí vận chuyển:', error);
    return 0;
  }
}
