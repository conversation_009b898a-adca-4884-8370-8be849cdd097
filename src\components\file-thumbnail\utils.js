import { CONFIG } from 'src/global-config';

// ----------------------------------------------------------------------

// Define more types here
const FORMAT_PDF = ['pdf'];
const FORMAT_TEXT = ['txt'];
const FORMAT_PHOTOSHOP = ['psd'];
const FORMAT_WORD = ['doc', 'docx'];
const FORMAT_EXCEL = ['xls', 'xlsx'];
const FORMAT_ZIP = ['zip', 'rar', 'iso'];
const FORMAT_ILLUSTRATOR = ['ai', 'esp'];
const FORMAT_POWERPOINT = ['ppt', 'pptx'];
const FORMAT_AUDIO = ['wav', 'aif', 'mp3', 'aac'];
const FORMAT_IMG = ['jpg', 'jpeg', 'gif', 'bmp', 'png', 'svg', 'webp'];
const FORMAT_VIDEO = ['m4v', 'avi', 'mpg', 'mp4', 'webm'];

const iconUrl = (icon) => `${CONFIG.assetsDir}/assets/icons/files/${icon}.svg`;

// ----------------------------------------------------------------------

export function fileFormat(fileUrl) {
  let format;

  const fileByUrl = fileTypeByUrl(fileUrl);

  if (FORMAT_TEXT.includes(fileByUrl)) {
    format = 'txt';
  } else if (FORMAT_ZIP.includes(fileByUrl)) {
    format = 'zip';
  } else if (FORMAT_AUDIO.includes(fileByUrl)) {
    format = 'audio';
  } else if (FORMAT_IMG.includes(fileByUrl)) {
    format = 'image';
  } else if (FORMAT_VIDEO.includes(fileByUrl)) {
    format = 'video';
  } else if (FORMAT_WORD.includes(fileByUrl)) {
    format = 'word';
  } else if (FORMAT_EXCEL.includes(fileByUrl)) {
    format = 'excel';
  } else if (FORMAT_POWERPOINT.includes(fileByUrl)) {
    format = 'powerpoint';
  } else if (FORMAT_PDF.includes(fileByUrl)) {
    format = 'pdf';
  } else if (FORMAT_PHOTOSHOP.includes(fileByUrl)) {
    format = 'photoshop';
  } else if (FORMAT_ILLUSTRATOR.includes(fileByUrl)) {
    format = 'illustrator';
  } else {
    format = fileTypeByUrl(fileUrl);
  }

  return format;
}

// ----------------------------------------------------------------------

export function fileThumb(fileUrl) {
  let thumb;

  switch (fileFormat(fileUrl)) {
    case 'folder':
      thumb = iconUrl('ic-folder');
      break;
    case 'txt':
      thumb = iconUrl('ic-txt');
      break;
    case 'zip':
      thumb = iconUrl('ic-zip');
      break;
    case 'audio':
      thumb = iconUrl('ic-audio');
      break;
    case 'video':
      thumb = iconUrl('ic-video');
      break;
    case 'word':
      thumb = iconUrl('ic-word');
      break;
    case 'excel':
      thumb = iconUrl('ic-excel');
      break;
    case 'powerpoint':
      thumb = iconUrl('ic-power_point');
      break;
    case 'pdf':
      thumb = iconUrl('ic-pdf');
      break;
    case 'photoshop':
      thumb = iconUrl('ic-pts');
      break;
    case 'illustrator':
      thumb = iconUrl('ic-ai');
      break;
    case 'image':
      thumb = iconUrl('ic-img');
      break;
    default:
      thumb = iconUrl('ic-file');
  }
  return thumb;
}

// ----------------------------------------------------------------------

export function fileTypeByUrl(fileUrl) {
  if (!fileUrl) return '';

  // Remove query parameters and fragments from URL
  const cleanUrl = fileUrl.split('?')[0].split('#')[0];

  // Extract file extension
  const extension = cleanUrl.split('.').pop();

  return extension ? extension.toLowerCase() : '';
}

// ----------------------------------------------------------------------

export function fileNameByUrl(fileUrl) {
  return fileUrl.split('/').pop();
}

// ----------------------------------------------------------------------

export function fileData(file) {
  // From url
  if (typeof file === 'string') {
    return {
      preview: file,
      name: fileNameByUrl(file),
      type: fileTypeByUrl(file),
      size: undefined,
      path: file,
      lastModified: undefined,
      lastModifiedDate: undefined,
    };
  }

  // From file
  return {
    name: file.name,
    size: file.size,
    path: file.path,
    type: file.type,
    preview: file.preview,
    lastModified: file.lastModified,
    lastModifiedDate: file.lastModifiedDate,
  };
}
