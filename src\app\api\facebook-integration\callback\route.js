import { NextResponse } from 'next/server';
import { createClient } from 'src/utils/supabase/server';

/**
 * Facebook Integration Callback Route
 * Handles the OAuth callback from Facebook and exchanges code for access token
 */
export async function GET(request) {
    console.log('🔗 Facebook Integration Callback hit');
    
    try {
        const { searchParams, origin } = new URL(request.url);
        const code = searchParams.get('code');
        const state = searchParams.get('state');
        const error = searchParams.get('error');
        const errorDescription = searchParams.get('error_description');

        // Handle Facebook OAuth errors
        if (error) {
            console.error('❌ Facebook OAuth Error:', { error, errorDescription });
            const errorRedirect = `${origin}/dashboard/mooly-chatbot/facebook-integration?error=${encodeURIComponent(error)}&message=${encodeURIComponent(errorDescription || 'Facebook authentication failed')}`;
            return NextResponse.redirect(errorRedirect);
        }

        // Validate required parameters
        if (!code) {
            console.error('❌ Missing authorization code from Facebook');
            const errorRedirect = `${origin}/dashboard/mooly-chatbot/facebook-integration?error=missing_code&message=${encodeURIComponent('Authorization code not received from Facebook')}`;
            return NextResponse.redirect(errorRedirect);
        }

        console.log('✅ Facebook callback received:', { 
            code: code ? 'present' : 'missing',
            state: state || 'none'
        });

        // Get Supabase client for server-side operations
        const supabase = await createClient();
        
        // Get authenticated user (required for tenant isolation)
        const { data: { user }, error: userError } = await supabase.auth.getUser();
        
        if (userError || !user) {
            console.error('❌ User not authenticated:', userError);
            const errorRedirect = `${origin}/auth/login?message=${encodeURIComponent('Please login first to connect Facebook')}`;
            return NextResponse.redirect(errorRedirect);
        }

        // Exchange authorization code for access token via server action
        const exchangeResponse = await fetch(`${origin}/api/facebook-integration/exchange-token`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Cookie': request.headers.get('Cookie') || '' // Forward cookies for auth
            },
            body: JSON.stringify({
                code,
                redirectUri: `${origin}/api/facebook-integration/callback`
            })
        });

        const exchangeResult = await exchangeResponse.json();

        if (!exchangeResult.success) {
            console.error('❌ Token exchange failed:', exchangeResult.error);
            const errorRedirect = `${origin}/dashboard/mooly-chatbot/facebook-integration?error=token_exchange_failed&message=${encodeURIComponent(exchangeResult.error?.message || 'Failed to exchange Facebook code for token')}`;
            return NextResponse.redirect(errorRedirect);
        }

        console.log('✅ Facebook token exchange successful');

        // Redirect back to Facebook Integration page with success
        const successRedirect = `${origin}/dashboard/mooly-chatbot/facebook-integration?success=true&message=${encodeURIComponent('Facebook account connected successfully')}`;
        return NextResponse.redirect(successRedirect);

    } catch (error) {
        console.error('💥 Facebook Integration Callback Error:', error);
        
        // Fallback error redirect
        const { origin } = new URL(request.url);
        const errorRedirect = `${origin}/dashboard/mooly-chatbot/facebook-integration?error=callback_error&message=${encodeURIComponent('An unexpected error occurred during Facebook authentication')}`;
        return NextResponse.redirect(errorRedirect);
    }
} 