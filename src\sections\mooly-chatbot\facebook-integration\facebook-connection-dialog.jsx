'use client';

import React, { useState, useCallback } from 'react';
import {
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    Button,
    Typography,
    Alert,
    Box,
    LinearProgress,
    List,
    ListItem,
    ListItemIcon,
    ListItemText,
    Card,
    CardContent,
    RadioGroup,
    FormControlLabel,
    Radio,
    Stack,
    Chip,
    Divider
} from '@mui/material';
import { LoadingButton } from '@mui/lab';
import { Iconify } from 'src/components/iconify';
import { toast } from 'src/components/snackbar';
import { useFacebookPopupAuth } from 'src/actions/mooly-chatbot/facebook-integration';

export default function SocialMediaConnectionDialog({ open, onClose, onSuccess, initialPlatform = 'instagram' }) {
    const [selectedPlatform, setSelectedPlatform] = useState(initialPlatform);
    const [connecting, setConnecting] = useState(false);
    
    // Use Facebook popup auth hook
    const { loading: popupLoading, openFacebookPopup } = useFacebookPopupAuth();

    // Update selectedPlatform when initialPlatform changes
    React.useEffect(() => {
        if (open) {
            setSelectedPlatform(initialPlatform);
        }
    }, [open, initialPlatform]);

    // Handle platform selection
    const handlePlatformChange = useCallback((event) => {
        setSelectedPlatform(event.target.value);
    }, []);

    // Handle Facebook popup connection
    const handleFacebookConnection = useCallback(async () => {
        try {
            setConnecting(true);
            console.log('🔗 Starting Facebook popup connection...');
            
            const result = await openFacebookPopup();
            
            if (result.success) {
                toast.success(result.message || 'Kết nối Facebook thành công!');
                onSuccess?.(result);
                onClose();
            } else {
                throw new Error(result.message || 'Kết nối Facebook thất bại');
            }
        } catch (error) {
            console.error('Facebook connection error:', error);
            toast.error(error.message || 'Lỗi khi kết nối Facebook');
        } finally {
            setConnecting(false);
        }
    }, [openFacebookPopup, onSuccess, onClose]);

    // Handle Instagram connection - sử dụng cùng pattern như Facebook
    const handleInstagramConnection = useCallback(async () => {
        try {
            setConnecting(true);
            console.log('📷 Starting Instagram popup connection...');

            // Tạo popup window cho Instagram OAuth - same pattern như Facebook
            const baseUrl = process.env.NEXT_PUBLIC_PUBLIC_SITE_URL || window.location.origin;
            const authUrl = `${baseUrl}/api/instagram-integration/authorize`;
            
            // Calculate popup position - same như Facebook
            const width = 600;
            const height = 700;
            const left = window.screen.width / 2 - width / 2;
            const top = window.screen.height / 2 - height / 2;

            const popup = window.open(
                authUrl,
                'instagram-oauth',
                `width=${width},height=${height},left=${left},top=${top},scrollbars=yes,resizable=yes,status=yes,location=yes`
            );

            if (!popup || popup.closed) {
                throw new Error('Popup bị chặn. Vui lòng cho phép popup và thử lại.');
            }

            // Set up global callback for popup to call - UNIFIED callback for both platforms
            window.facebookAuthCallback = (result) => {
                setConnecting(false);
                
                console.log('📥 Received Instagram OAuth result:', result);
                
                if (result.success) {
                    toast.success(result.message || 'Kết nối Instagram thành công!');
                    onSuccess?.(result);
                    onClose();
                } else {
                    toast.error(result.message || 'Kết nối Instagram thất bại');
                }
                
                // Cleanup
                delete window.facebookAuthCallback;
            };

            // Monitor popup for completion
            const checkClosed = setInterval(() => {
                if (popup.closed) {
                    clearInterval(checkClosed);
                    setConnecting(false);
                    
                    // If popup closed without callback, user likely cancelled
                    if (window.facebookAuthCallback) {
                        delete window.facebookAuthCallback;
                        console.log('⚠️ Instagram OAuth cancelled by user');
                        toast.warning('Kết nối Instagram bị hủy');
                    }
                }
            }, 1000);

            // Timeout after 5 minutes
            setTimeout(() => {
                if (!popup.closed) {
                    popup.close();
                    clearInterval(checkClosed);
                    setConnecting(false);
                    console.log('⏰ Instagram OAuth timeout');
                    toast.error('Kết nối Instagram timeout. Vui lòng thử lại.');
                    
                    if (window.facebookAuthCallback) {
                        delete window.facebookAuthCallback;
                    }
                }
            }, 5 * 60 * 1000);

        } catch (error) {
            console.error('Instagram connection error:', error);
            toast.error(error.message || 'Lỗi khi kết nối Instagram');
            setConnecting(false);
        }
    }, [onSuccess, onClose]);

    // Handle connection based on selected platform
    const handleConnect = useCallback(async () => {
        if (selectedPlatform === 'instagram') {
            await handleInstagramConnection();
        } else {
            await handleFacebookConnection();
        }
    }, [selectedPlatform, handleInstagramConnection, handleFacebookConnection]);

    // Platform configuration options
    const platformOptions = [
        {
            value: 'instagram',
            title: 'Instagram Direct (Khuyến nghị)',
            subtitle: 'API mới 2025 - Kết nối trực tiếp',
            icon: 'mdi:instagram',
            color: '#E4405F',
            features: [
                'Kết nối qua popup window',
                'Không cần Facebook Page',
                'OAuth đơn giản và bảo mật',
                'API endpoints riêng biệt',
                'Scopes mới: instagram_business_*'
            ],
            badge: 'Mới 2025',
            badgeColor: 'success'
        },
        {
            value: 'facebook',
            title: 'Facebook Integration',
            subtitle: 'Kết nối qua Facebook Pages',
            icon: 'mdi:facebook',
            color: '#1877F2',
            features: [
                'Popup OAuth đã tối ưu',
                'Quản lý cả Facebook + Instagram',
                'Thông qua Facebook Pages',
                'API truyền thống ổn định',
                'Multi-platform dashboard'
            ],
            badge: 'Stable',
            badgeColor: 'info'
        }
    ];

    // Combined loading state
    const isLoading = connecting || popupLoading;

    // Cleanup callback when dialog closes
    React.useEffect(() => () => {
        if (window.facebookAuthCallback) {
            delete window.facebookAuthCallback;
        }
    }, []);

    return (
        <Dialog 
            open={open} 
            onClose={!isLoading ? onClose : undefined} // Prevent close when loading
            maxWidth="md"
            fullWidth
            PaperProps={{
                sx: { minHeight: '500px' }
            }}
        >
            <DialogTitle>
                <Box display="flex" alignItems="center" gap={2}>
                    <Iconify icon="mdi:connection" size={28} color="primary.main" />
                    <Typography variant="h6">
                        Chọn phương thức kết nối Social Media
                    </Typography>
                </Box>
            </DialogTitle>

            <DialogContent>
                {/* Loading indicator */}
                {isLoading && <LinearProgress sx={{ mb: 2 }} />}

                <Alert severity="info" sx={{ mb: 3 }}>
                    <Typography variant="body2">
                        <strong>🎯 Popup Window:</strong> Cả hai phương thức đều sử dụng popup window để đăng nhập, 
                        giữ tab hiện tại không bị chuyển hướng. <br />
                        <strong>Instagram Direct</strong> được khuyến nghị cho trải nghiệm tối ưu.
                    </Typography>
                </Alert>

                <RadioGroup
                    value={selectedPlatform}
                    onChange={handlePlatformChange}
                    disabled={isLoading}
                >
                    <Stack spacing={2}>
                        {platformOptions.map((option) => (
                            <Card
                                key={option.value}
                                variant="outlined"
                                sx={{
                                    cursor: isLoading ? 'not-allowed' : 'pointer',
                                    opacity: isLoading ? 0.6 : 1,
                                    border: selectedPlatform === option.value 
                                        ? `2px solid ${option.color}` 
                                        : '1px solid',
                                    borderColor: selectedPlatform === option.value 
                                        ? option.color 
                                        : 'divider',
                                    '&:hover': !isLoading ? {
                                        borderColor: option.color,
                                        boxShadow: `0 0 0 1px ${option.color}25`
                                    } : {}
                                }}
                                onClick={() => !isLoading && setSelectedPlatform(option.value)}
                            >
                                <CardContent>
                                    <FormControlLabel
                                        value={option.value}
                                        control={<Radio sx={{ color: option.color }} />}
                                        disabled={isLoading}
                                        label={
                                            <Box display="flex" alignItems="center" gap={2} width="100%">
                                                <Iconify 
                                                    icon={option.icon} 
                                                    size={40} 
                                                    color={option.color}
                                                />
                                                <Box flex={1}>
                                                    <Box display="flex" alignItems="center" gap={1} mb={0.5}>
                                                        <Typography variant="h6">
                                                            {option.title}
                                                        </Typography>
                                                        <Chip 
                                                            label={option.badge}
                                                            color={option.badgeColor}
                                                            size="small"
                                                        />
                                                    </Box>
                                                    <Typography variant="body2" color="text.secondary" gutterBottom>
                                                        {option.subtitle}
                                                    </Typography>
                                                    <Box component="ul" sx={{ 
                                                        m: 0, 
                                                        pl: 2, 
                                                        fontSize: '0.875rem',
                                                        color: 'text.secondary'
                                                    }}>
                                                        {option.features.map((feature, index) => (
                                                            <li key={index}>{feature}</li>
                                                        ))}
                                                    </Box>
                                                </Box>
                                            </Box>
                                        }
                                        sx={{ margin: 0, width: '100%' }}
                                    />
                                </CardContent>
                            </Card>
                        ))}
                    </Stack>
                </RadioGroup>

                <Divider sx={{ my: 3 }} />

                <Alert severity="success" sx={{ mb: 2 }}>
                    <Typography variant="body2">
                        <strong>✨ Cải tiến UX:</strong> Popup window đảm bảo bạn không mất dữ liệu đang nhập trên tab hiện tại. 
                        Sau khi hoàn tất OAuth, tab sẽ tự động cập nhật danh sách kết nối.
                    </Typography>
                </Alert>

                {isLoading && (
                    <Alert severity="info">
                        <Typography variant="body2">
                            <strong>{selectedPlatform === 'instagram' ? '📷 Instagram' : '📘 Facebook'} đang kết nối...</strong>
                            <br />
                            Vui lòng hoàn tất đăng nhập trong popup window.
                        </Typography>
                    </Alert>
                )}
            </DialogContent>

            <DialogActions sx={{ p: 3 }}>
                <Button 
                    onClick={onClose} 
                    variant="outlined"
                    disabled={isLoading}
                >
                    Hủy
                </Button>
                <LoadingButton
                    loading={isLoading}
                    onClick={handleConnect}
                    variant="contained"
                    startIcon={
                        <Iconify 
                            icon={platformOptions.find(p => p.value === selectedPlatform)?.icon} 
                        />
                    }
                    sx={{
                        bgcolor: platformOptions.find(p => p.value === selectedPlatform)?.color,
                        '&:hover': {
                            bgcolor: platformOptions.find(p => p.value === selectedPlatform)?.color,
                            filter: 'brightness(0.9)'
                        }
                    }}
                >
                    {isLoading 
                        ? `Đang mở ${selectedPlatform === 'instagram' ? 'Instagram' : 'Facebook'}...` 
                        : `Mở Popup ${platformOptions.find(p => p.value === selectedPlatform)?.title}`
                    }
                </LoadingButton>
            </DialogActions>
        </Dialog>
    );
} 