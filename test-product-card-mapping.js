// Test field mapping
const testData = {
  productCardConfig: {
    enabled: true,
    send_method: 'direct_chat',
    button_texts: {
      order_button: 'Đặt mua',
      detail_button: 'Xem chi tiết'
    },
    card_style: 'compact',
    auto_show_price: true,
    auto_show_stock: true
  }
};

console.log('JS Object (camelCase):', JSON.stringify(testData, null, 2));

// Expected database field (snake_case)
const dbData = {
  product_card_config: {
    enabled: true,
    send_method: 'direct_chat',
    button_texts: {
      order_button: 'Đặt mua',
      detail_button: 'Xem chi tiết'
    },
    card_style: 'compact',
    auto_show_price: true,
    auto_show_stock: true
  }
};

console.log('Database Field (snake_case):', JSON.stringify(dbData, null, 2)); 