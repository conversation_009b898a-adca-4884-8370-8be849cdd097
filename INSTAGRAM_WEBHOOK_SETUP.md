# 🎯 Instagram Webhook Configuration Guide

## 📋 **<PERSON><PERSON><PERSON> hình theo hình bạn gửi**

### ✅ **Thông tin cần điền ch<PERSON>h xác:**

#### **1. URL gọi lại (Callback URL):**

**Cho Instagram qua Facebook (hiện tại):**
```
https://your-domain.com/api/instagram-webhook
```

**Cho Instagram Direct API (khuyến nghị 2025):**
```
https://your-domain.com/api/instagram-direct-webhook
```

#### **2. X<PERSON>c minh mã (Verify Token):**
```
<EMAIL>
```

---

## 🔧 **Environment Variables cần thiết:**

### **File .env.local:**
```bash
# Instagram Direct API (NEW 2025)
INSTAGRAM_APP_ID=your_instagram_app_id
INSTAGRAM_APP_SECRET=your_instagram_app_secret
INSTAGRAM_WEBHOOK_VERIFY_TOKEN=<EMAIL>

# Facebook Integration (Legacy - tương thích ngược)
NEXT_PUBLIC_FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret
FACEBOOK_WEBHOOK_VERIFY_TOKEN=<EMAIL>

# App URL
NEXT_PUBLIC_PUBLIC_SITE_URL=https://your-domain.com
```

---

## 🔀 **Hai phương thức Webhook:**

### **Option 1: Instagram Direct API Webhook (Khuyến nghị 2025)**
- **URL**: `https://your-domain.com/api/instagram-direct-webhook`
- **Ưu điểm**: Trực tiếp từ Instagram, không qua Facebook
- **Sử dụng**: `INSTAGRAM_APP_SECRET` để verify signature
- **Database**: Lưu vào `instagram_business_accounts` table

### **Option 2: Instagram qua Facebook Webhook (Legacy)**
- **URL**: `https://your-domain.com/api/instagram-webhook`
- **Ưu điểm**: Hoạt động với hệ thống hiện tại
- **Sử dụng**: `FACEBOOK_APP_SECRET` để verify signature
- **Database**: Lưu vào `facebook_accounts` table với `platform='instagram'`

---

## 📊 **Webhook Events được hỗ trợ:**

### **Instagram Direct API:**
- ✅ `messages` - Tin nhắn trực tiếp
- ✅ `comments` - Bình luận trên posts
- ✅ `mentions` - Nhắc đến trong stories/posts
- ✅ `media_insights` - Thống kê media

### **Instagram qua Facebook:**
- ✅ `comments` - Bình luận
- ✅ `messages` - Tin nhắn
- ✅ `mentions` - Nhắc đến
- ✅ `feed` - Hoạt động trên feed

---

## 🛠️ **Cách cấu hình từng bước:**

### **Bước 1: Tạo Instagram/Facebook App**

#### **Cho Instagram Direct:**
1. Vào [Meta Developers](https://developers.facebook.com/)
2. Tạo **Instagram App** mới
3. Thêm **Instagram Basic Display** product
4. Cấu hình **Instagram API** với scopes:
   - `instagram_business_basic`
   - `instagram_business_manage_messages`
   - `instagram_business_manage_comments`
   - `instagram_business_content_publish`

#### **Cho Instagram qua Facebook:**
1. Sử dụng Facebook App hiện tại
2. Thêm **Instagram Graph API** product
3. Cấu hình permissions:
   - `instagram_basic`
   - `instagram_manage_comments`
   - `instagram_manage_messages`

### **Bước 2: Cấu hình Webhooks**

#### **Instagram Direct Webhooks:**
1. Trong Instagram App dashboard
2. **Products** → **Webhooks**
3. **Add Subscription**:
   - **Callback URL**: `https://your-domain.com/api/instagram-direct-webhook`
   - **Verify Token**: `<EMAIL>`
   - **Subscription Fields**: `messages`, `comments`, `mentions`

#### **Facebook-based Instagram Webhooks:**
1. Trong Facebook App dashboard
2. **Products** → **Webhooks**
3. **Add Subscription** cho **Page**:
   - **Callback URL**: `https://your-domain.com/api/instagram-webhook`
   - **Verify Token**: `<EMAIL>`
   - **Subscription Fields**: `feed`, `comments`, `messaging`

---

## 🧪 **Testing Webhooks:**

### **Test Webhook Verification:**

#### **Instagram Direct:**
```bash
curl -X GET "https://your-domain.com/api/instagram-direct-webhook?hub.mode=subscribe&hub.verify_token=<EMAIL>&hub.challenge=test123"
# Should return: test123
```

#### **Instagram via Facebook:**
```bash
curl -X GET "https://your-domain.com/api/instagram-webhook?hub.mode=subscribe&hub.verify_token=<EMAIL>&hub.challenge=test123"
# Should return: test123
```

### **Test Event Processing:**
1. Comment trên Instagram post
2. Gửi Direct Message
3. Kiểm tra server logs
4. Verify database records

---

## 🔍 **Debug & Troubleshooting:**

### **Common Issues:**

#### **1. Webhook Verification Failed:**
```bash
# Check logs:
Expected: <EMAIL>
Received: [actual_token]

# Fix: Update VERIFY_TOKEN in .env
```

#### **2. Signature Verification Failed:**
```bash
# Check app secret configuration
INSTAGRAM_APP_SECRET=correct_secret
FACEBOOK_APP_SECRET=correct_secret
```

#### **3. Account Not Found:**
```bash
# Check database for Instagram account
# Instagram Direct: instagram_business_accounts table
# Facebook-based: facebook_accounts with platform='instagram'
```

### **Webhook URLs Summary:**

| Integration Type | Webhook URL | Verify Token | App Secret |
|------------------|-------------|--------------|------------|
| **Instagram Direct** | `/api/instagram-direct-webhook` | `<EMAIL>` | `INSTAGRAM_APP_SECRET` |
| **Instagram via Facebook** | `/api/instagram-webhook` | `<EMAIL>` | `FACEBOOK_APP_SECRET` |

---

## 📈 **Monitoring & Logs:**

### **Server Logs để check:**
```bash
# Verification logs
📨 Instagram webhook verification: { mode, tokenMatches, challenge }
✅ Instagram webhook verified successfully

# Event processing logs  
📨 Received Instagram webhook event: { hasSignature, bodyLength }
🔍 Parsed Instagram webhook data: { object, entryCount }
✅ Instagram webhook processed successfully
```

### **Database Tables để kiểm tra:**
- `instagram_business_accounts` - Instagram Direct accounts
- `instagram_activity_logs` - Webhook events & auto-replies
- `instagram_messaging_configs` - Auto-reply settings
- `facebook_accounts` - Facebook-based Instagram accounts (legacy)

---

## 🚀 **Production Checklist:**

- [ ] ✅ Environment variables configured
- [ ] ✅ HTTPS domain setup
- [ ] ✅ Webhook URL accessible
- [ ] ✅ App permissions granted
- [ ] ✅ Webhook subscriptions active
- [ ] ✅ Test events working
- [ ] ✅ Database logging working
- [ ] ✅ Auto-reply configuration ready

**Kết quả**: Webhook sẽ nhận được events real-time từ Instagram và tự động xử lý với AI-powered responses! 🎉 