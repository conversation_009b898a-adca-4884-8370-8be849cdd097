import { NextResponse } from 'next/server';
import { createClient } from 'src/utils/supabase/server';

// Required environment variables for Facebook integration
const REQUIRED_ENV_VARS = [
  'NEXT_PUBLIC_FACEBOOK_APP_ID',
  'FACEBOOK_APP_SECRET', 
  'FACEBOOK_WEBHOOK_VERIFY_TOKEN',
  'NEXT_PUBLIC_SUPABASE_URL',
  'NEXT_PUBLIC_SUPABASE_ANON_KEY'
];

// Required database tables
const REQUIRED_TABLES = [
  'facebook_accounts',
  'facebook_auto_reply_config', 
  'facebook_activity_logs'
];

/**
 * GET - Check setup status
 */
export async function GET() {
  try {
    // Tạo Supabase server client
    const supabase = await createClient();
    
    // Check if required tables exist bằng cách query trực tiếp
    const existingTables = [];
    
    for (const table of REQUIRED_TABLES) {
      try {
        // Sử dụng .select().limit(0) để chỉ kiểm tra table tồn tại, không lấy data
        const { error } = await supabase
          .from(table)
          .select('id')
          .limit(0);
        
        if (!error) {
          existingTables.push(table);
        }
      } catch (error) {
        // Table doesn't exist, continue
        console.log(`Table ${table} doesn't exist:`, error.message);
      }
    }

    const isSetup = existingTables.length === REQUIRED_TABLES.length;

    return NextResponse.json({
      success: true,
      isSetup,
      existingTables,
      requiredTables: REQUIRED_TABLES,
      missing: REQUIRED_TABLES.filter(table => !existingTables.includes(table))
    });

  } catch (error) {
    console.error('Error checking Facebook setup status:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error.message,
        isSetup: false,
        existingTables: [],
        requiredTables: REQUIRED_TABLES,
        missing: REQUIRED_TABLES
      },
      { status: 500 }
    );
  }
}

/**
 * POST - Setup or check environment
 */
export async function POST(request) {
  try {
    const body = await request.json();
    const { action } = body;

    if (action === 'check-environment') {
      return checkEnvironment();
    }

    if (action === 'setup-database') {
      return setupDatabase();
    }

    return NextResponse.json(
      { success: false, error: 'Invalid action' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Error in Facebook setup POST:', error);
    return NextResponse.json(
      { success: false, error: error.message },
      { status: 500 }
    );
  }
}

/**
 * Check environment variables
 */
function checkEnvironment() {
  const configured = [];
  const missing = [];

  for (const envVar of REQUIRED_ENV_VARS) {
    if (process.env[envVar]) {
      configured.push(envVar);
    } else {
      missing.push(envVar);
    }
  }

  const success = missing.length === 0;

  return NextResponse.json({
    success,
    configured,
    missing,
    requiredEnvVars: REQUIRED_ENV_VARS,
    message: success 
      ? 'Tất cả biến môi trường đã được cấu hình'
      : `Thiếu ${missing.length} biến môi trường`
  });
}

/**
 * Setup database - Sử dụng Supabase package chuẩn
 * Không tự động tạo table, mà hướng dẫn user chạy migration
 */
async function setupDatabase() {
  try {
    // Tạo Supabase server client
    const supabase = await createClient();
    
    // Kiểm tra xem user có quyền truy cập database không
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }
    
    // Kiểm tra từng bảng một cách chi tiết hơn
    const tableStatusChecks = await Promise.all(
      REQUIRED_TABLES.map(async (tableName) => {
        try {
          const { data, error } = await supabase
            .from(tableName)
            .select('id')
            .limit(1);
          
          return { 
            table: tableName, 
            exists: !error,
            error: error?.message || null
          };
        } catch (err) {
          return { 
            table: tableName, 
            exists: false,
            error: err.message 
          };
        }
      })
    );

    const existingTables = tableStatusChecks.filter(check => check.exists);
    const missingTables = tableStatusChecks.filter(check => !check.exists);

    if (missingTables.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'Tất cả bảng Facebook Integration đã tồn tại và sẵn sàng sử dụng',
        tablesStatus: tableStatusChecks,
        isSetup: true
      });
    }

    // Trả về thông tin chi tiết để user biết cần làm gì
    return NextResponse.json({
      success: false,
      message: 'Database chưa được setup hoàn chỉnh',
      isSetup: false,
      tablesStatus: tableStatusChecks,
      existingTables: existingTables.map(t => t.table),
      missingTables: missingTables.map(t => t.table),
      setupRequired: true,
      instructions: {
        step1: 'Các bảng Facebook Integration đã được tạo thông qua Supabase migrations',
        step2: 'Vui lòng kiểm tra Supabase Dashboard để xác nhận các bảng đã được tạo đúng',
        step3: 'Nếu bảng chưa tồn tại, hãy chạy: npx supabase db push',
        migrationFiles: [
          'supabase/migrations/20250127000000_create_facebook_integration_tables.sql'
        ]
      }
    }, { status: 400 });

  } catch (error) {
    console.error('Error setting up Facebook database:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Lỗi khi kiểm tra database setup',
        isSetup: false
      },
      { status: 500 }
    );
  }
} 