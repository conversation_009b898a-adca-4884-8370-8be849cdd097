'use client';

import { useState, useEffect, createContext, useContext } from 'react';

import { useAuthContext } from 'src/auth/hooks';

import { fetchData, updateData } from './supabase-utils';


/**
 * Business Configuration Service
 * Handles business type setup and feature configuration
 */

// Business type configurations
export const BUSINESS_TYPES = {
  retail: {
    name: 'Retail/E-commerce',
    description: 'Bán sản phẩm vật lý với quản lý kho và vận chuyển',
    icon: 'eva:shopping-bag-fill',
    features: {
      physicalProducts: true,
      digitalProducts: false,
      services: false,
      inventory: true,
      shipping: true,
      variants: true,
      bundles: true,
      weightDimensions: true,
      barcodeScanning: true,
      appointmentBooking: false,
      digitalDelivery: false,
      licenseManagement: false,
      timeBasedPricing: false,
      // Order management features
      orderProcessing: true,
      shippingCalculation: true,
      trackingNumbers: true,
      returnManagement: true,
      bulkOrders: true,
      // Inventory features
      stockTracking: true,
      lowStockAlerts: true,
      stockAdjustments: true,
      inventoryHistory: true,
      autoReorder: true,
      // Payment features
      multipleGateways: true,
      installmentPayments: true,
      refundManagement: true,
      paymentTracking: true,
      // Platform sync features
      platformSync: true
    },
    defaultSettings: {
      currency: 'VND',
      weightUnit: 'kg',
      dimensionUnit: 'cm',
      taxIncluded: true,
      inventoryTracking: true,
      lowStockThreshold: 10
    },
    uiConfig: {
      dashboard: {
        priority: ['inventory', 'orders', 'sales', 'shipping'],
        widgets: ['inventoryLevels', 'orderStatus', 'salesMetrics', 'lowStockAlerts']
      },
      productForm: {
        emphasize: ['weight', 'dimensions', 'sku', 'inventory'],
        hide: ['digitalFiles', 'serviceInfo', 'appointmentSettings']
      },
      navigation: {
        priority: ['products', 'inventory', 'orders', 'shipping'],
        hide: ['appointments', 'digitalDelivery', 'services']
      }
    },
    hiddenFeatures: [
      'appointmentBooking',
      'serviceScheduling',
      'digitalDelivery',
      'licenseManagement',
      'timeBasedPricing'
    ]
  },
  digital: {
    name: 'Digital Products',
    description: 'Bán sản phẩm số như phần mềm, khóa học, ebook',
    icon: 'eva:monitor-fill',
    features: {
      physicalProducts: false,
      digitalProducts: true,
      services: false,
      inventory: false,
      shipping: false,
      variants: false,
      bundles: true,
      digitalFiles: true,
      licenseKeys: true,
      downloadLimits: true,
      instantDelivery: true,
      subscriptionBilling: true,
      weightDimensions: false,
      appointmentBooking: false,
      // Digital-specific features
      downloadTracking: true,
      licenseManagement: true,
      accessControl: true,
      automaticDelivery: true,
      emailDelivery: true,
      downloadPortal: true,
      expirationDates: true,
      // Payment features
      instantPayments: true,
      trialPeriods: true,
      refundManagement: true,
      // Platform sync features
      platformSync: true
    },
    defaultSettings: {
      currency: 'VND',
      deliveryMethod: 'automatic',
      downloadLimit: 5,
      linkExpiration: '30 days',
      taxIncluded: true
    },
    uiConfig: {
      dashboard: {
        priority: ['sales', 'downloads', 'licenses', 'analytics'],
        widgets: ['salesMetrics', 'downloadStats', 'licenseUsage', 'customerActivity']
      },
      productForm: {
        emphasize: ['digitalFiles', 'licensing', 'accessControl', 'downloadLimits'],
        hide: ['weight', 'dimensions', 'inventory', 'shipping']
      },
      navigation: {
        priority: ['products', 'downloads', 'licenses', 'analytics'],
        hide: ['inventory', 'shipping', 'appointments']
      }
    },
    hiddenFeatures: [
      'inventoryTracking',
      'shippingCalculation',
      'weightDimensions',
      'stockAlerts',
      'physicalFulfillment',
      'appointmentBooking'
    ]
  },
  services: {
    name: 'Services',
    description: 'Cung cấp dịch vụ với đặt lịch và quản lý nhân viên',
    icon: 'eva:calendar-fill',
    features: {
      physicalProducts: false,
      digitalProducts: false,
      services: true,
      inventory: false,
      shipping: false,
      variants: false,
      bundles: true,
      appointmentBooking: true,
      timeBasedPricing: true,
      staffManagement: true,
      calendarIntegration: true,
      servicePackages: true,
      weightDimensions: false,
      digitalDelivery: false,
      // Service-specific features
      serviceCatalog: true,
      durationManagement: true,
      staffAssignment: true,
      appointmentScheduling: true,
      timeSlotManagement: true,
      recurringBookings: true,
      waitingList: true,
      // Staff management
      staffProfiles: true,
      skillsManagement: true,
      availabilityScheduling: true,
      workloadDistribution: true,
      // Customer management
      clientProfiles: true,
      serviceHistory: true,
      preferences: true,
      communicationLog: true,
      // Platform sync features
      platformSync: true
    },
    defaultSettings: {
      currency: 'VND',
      timeUnit: 'minutes',
      bookingAdvance: '7 days',
      cancellationPolicy: '24 hours',
      taxIncluded: true
    },
    uiConfig: {
      dashboard: {
        priority: ['appointments', 'staff', 'services', 'customers'],
        widgets: ['appointmentCalendar', 'staffUtilization', 'serviceMetrics', 'customerSatisfaction']
      },
      productForm: {
        emphasize: ['duration', 'pricing', 'staffRequirements', 'bookingSettings'],
        hide: ['weight', 'dimensions', 'inventory', 'shipping']
      },
      navigation: {
        priority: ['services', 'bookings', 'staff', 'customers'],
        hide: ['inventory', 'shipping', 'digitalDelivery']
      }
    },
    hiddenFeatures: [
      'inventoryTracking',
      'shippingCalculation',
      'stockManagement',
      'physicalProducts',
      'digitalDelivery',
      'weightDimensions'
    ]
  },
  hybrid: {
    name: 'Hybrid Business',
    description: 'Kết hợp nhiều loại hình kinh doanh',
    icon: 'eva:grid-fill',
    features: {
      physicalProducts: true,
      digitalProducts: true,
      services: true,
      inventory: true,
      shipping: true,
      variants: true,
      bundles: true,
      appointmentBooking: true,
      digitalDelivery: true,
      adaptiveInterface: true,
      smartCategorization: true,
      // All features enabled for hybrid
      weightDimensions: true,
      barcodeScanning: true,
      licenseManagement: true,
      timeBasedPricing: true,
      staffManagement: true,
      calendarIntegration: true,
      // Flexible ordering
      mixedOrderTypes: true,
      smartFulfillment: true,
      separateDelivery: true,
      combinedInvoicing: true,
      // Adaptive UI
      contextualInterface: true,
      smartNavigation: true,
      roleBasedViews: true,
      customizableDashboard: true,
      // Platform sync features
      platformSync: true
    },
    defaultSettings: {
      currency: 'VND',
      smartDefaults: true,
      contextualHelp: true,
      adaptiveInterface: true
    },
    uiConfig: {
      dashboard: {
        priority: ['overview', 'products', 'orders', 'analytics'],
        widgets: ['businessOverview', 'mixedMetrics', 'smartInsights', 'adaptiveRecommendations']
      },
      productForm: {
        emphasize: ['productType', 'smartCategorization', 'adaptiveFields'],
        hide: [], // Nothing hidden in hybrid mode
        conditional: {
          showInventoryWhen: 'hasPhysicalProducts',
          showShippingWhen: 'hasPhysicalProducts',
          showBookingWhen: 'hasServices',
          showDownloadsWhen: 'hasDigitalProducts'
        }
      },
      navigation: {
        priority: ['products', 'orders', 'customers', 'analytics'],
        hide: [], // Nothing hidden in hybrid mode
        adaptive: true
      }
    },
    conditionalFeatures: {
      showInventoryWhen: 'hasPhysicalProducts',
      showShippingWhen: 'hasPhysicalProducts',
      showBookingWhen: 'hasServices',
      showDownloadsWhen: 'hasDigitalProducts'
    }
  }
};

/**
 * Get business type configuration
 * @param {string} businessType - Business type key
 * @returns {Object} Business type configuration
 */
export function getBusinessTypeConfig(businessType) {
  return BUSINESS_TYPES[businessType] || BUSINESS_TYPES.retail;
}

/**
 * Get all available business types
 * @returns {Array} Array of business type options
 */
export function getBusinessTypeOptions() {
  return Object.entries(BUSINESS_TYPES).map(([key, config]) => ({
    value: key,
    label: config.name,
    description: config.description,
    icon: config.icon,
    features: Object.keys(config.features).filter(feature => config.features[feature])
  }));
}

/**
 * Update tenant business type
 * @param {string} businessType - Business type
 * @returns {Promise<Object>} Result
 */
export async function updateTenantBusinessType(businessType) {
  try {
    // Validate business type
    if (!['retail', 'digital', 'services', 'hybrid'].includes(businessType)) {
      return {
        success: false,
        error: {
          message: 'Invalid business type. Must be one of: retail, digital, services, hybrid',
          code: 'INVALID_BUSINESS_TYPE'
        },
        data: null
      };
    }

    // RLS sẽ tự động xác định tenant_id từ JWT token và filter
    // updateData sẽ tự động convert camelCase sang snake_case
    // businessType -> business_type trong database
    const result = await updateData(
      'tenants',
      {
        businessType,
        updatedAt: new Date().toISOString()
      }
      // Không cần where clause vì RLS sẽ tự động filter theo tenant_id
    );

    if (result.success) {
      console.log('✅ Business type updated successfully:', businessType);
    }

    return result;
  } catch (error) {
    console.error('❌ Error updating tenant business type:', error);
    return { success: false, data: null, error };
  }
}

/**
 * Get tenant business configuration
 * RLS sẽ tự động filter theo tenant_id từ JWT token
 * @returns {Promise<Object>} Business configuration
 */
export async function getTenantBusinessConfig() {
  try {
    console.log('🔍 Loading tenant business config...');

    // RLS sẽ tự động filter theo tenant_id từ JWT token
    // Lấy thêm thông tin tenant để debug và cache
    const result = await fetchData('tenants', {
      columns: 'id, name, business_type, updated_at',
      single: true
    });

    // Handle case where tenant is not found or query fails
    if (!result.success) {
      console.warn('⚠️ Tenant not found or query failed:', result.error);

      // Check if it's a PGRST116 error (no rows returned or multiple rows)
      if (result.error && (
          result.error.code === 'PGRST116' ||
          result.error.code === 'NO_ROWS_OR_MULTIPLE_ROWS' ||
          result.error.message?.includes('no rows returned') ||
          result.error.message?.includes('multiple (or no) rows returned')
        )) {
        console.warn('⚠️ Tenant does not exist or multiple tenants found, using default retail config');
        const config = getBusinessTypeConfig('retail');
        return {
          success: true,
          data: {
            businessType: 'retail',
            config,
            features: config.features,
            defaultSettings: config.defaultSettings,
            tenantInfo: null
          },
          error: null
        };
      }

      // For other errors, throw to be handled by caller
      throw new Error(result.error?.message || result.error);
    }

    // Lưu ý: fetchData đã convert snake_case sang camelCase
    // business_type từ DB -> businessType trong result.data
    const businessType = result.data?.businessType || 'retail';
    const config = getBusinessTypeConfig(businessType);

    console.log('✅ Business config loaded:', {
      tenantId: result.data?.id,
      tenantName: result.data?.name,
      businessType,
      updatedAt: result.data?.updatedAt
    });

    return {
      success: true,
      data: {
        businessType,
        config,
        features: config.features,
        defaultSettings: config.defaultSettings,
        tenantInfo: {
          id: result.data?.id,
          name: result.data?.name,
          updatedAt: result.data?.updatedAt
        }
      },
      error: null
    };
  } catch (error) {
    console.error('❌ Error getting tenant business config:', error);

    // Return default retail config as fallback
    const config = getBusinessTypeConfig('retail');
    return {
      success: true,
      data: {
        businessType: 'retail',
        config,
        features: config.features,
        defaultSettings: config.defaultSettings,
        tenantInfo: null
      },
      error: null
    };
  }
}

/**
 * Check if feature is enabled for business type
 * @param {string} businessType - Business type
 * @param {string} feature - Feature name
 * @returns {boolean} Whether feature is enabled
 */
export function isFeatureEnabled(businessType, feature) {
  const config = getBusinessTypeConfig(businessType);
  return config.features[feature] || false;
}

/**
 * Get enabled features for business type
 * @param {string} businessType - Business type
 * @returns {Array} Array of enabled features
 */
export function getEnabledFeatures(businessType) {
  const config = getBusinessTypeConfig(businessType);
  return Object.keys(config.features).filter(feature => config.features[feature]);
}

/**
 * Get hidden features for business type
 * @param {string} businessType - Business type
 * @returns {Array} Array of hidden features
 */
export function getHiddenFeatures(businessType) {
  const config = getBusinessTypeConfig(businessType);
  return config.hiddenFeatures || Object.keys(config.features).filter(feature => !config.features[feature]);
}

/**
 * Get UI configuration for business type
 * @param {string} businessType - Business type
 * @returns {Object} UI configuration
 */
export function getUIConfig(businessType) {
  const config = getBusinessTypeConfig(businessType);
  return config.uiConfig || {};
}

/**
 * Get navigation configuration for business type
 * @param {string} businessType - Business type
 * @returns {Object} Navigation configuration
 */
export function getNavigationConfig(businessType) {
  const uiConfig = getUIConfig(businessType);
  return uiConfig.navigation || {};
}

/**
 * Get dashboard configuration for business type
 * @param {string} businessType - Business type
 * @returns {Object} Dashboard configuration
 */
export function getDashboardConfig(businessType) {
  const uiConfig = getUIConfig(businessType);
  return uiConfig.dashboard || {};
}

/**
 * Get product form configuration for business type
 * @param {string} businessType - Business type
 * @returns {Object} Product form configuration
 */
export function getProductFormConfig(businessType) {
  const uiConfig = getUIConfig(businessType);
  return uiConfig.productForm || {};
}

/**
 * Check if UI element should be visible for business type
 * @param {string} businessType - Business type
 * @param {string} element - UI element name
 * @param {string} context - Context (navigation, dashboard, productForm)
 * @returns {boolean} Whether element should be visible
 */
export function shouldShowUIElement(businessType, element, context = 'navigation') {
  const config = getUIConfig(businessType);
  const contextConfig = config[context] || {};

  // Check if element is in hide list
  if (contextConfig.hide && contextConfig.hide.includes(element)) {
    return false;
  }

  // Check if feature is enabled
  return isFeatureEnabled(businessType, element);
}

/**
 * Get prioritized UI elements for business type
 * @param {string} businessType - Business type
 * @param {string} context - Context (navigation, dashboard, productForm)
 * @returns {Array} Prioritized elements
 */
export function getPrioritizedUIElements(businessType, context = 'navigation') {
  const config = getUIConfig(businessType);
  const contextConfig = config[context] || {};

  return contextConfig.priority || [];
}

/**
 * Apply business type defaults to product
 * @param {string} businessType - Business type
 * @param {Object} productData - Product data
 * @returns {Object} Product data with defaults applied
 */
export function applyBusinessTypeDefaults(businessType, productData) {
  const config = getBusinessTypeConfig(businessType);
  const defaults = config.defaultSettings;

  return {
    ...productData,
    // Apply inventory settings based on business type
    inventory_settings: {
      track_inventory: config.features.inventory,
      low_stock_threshold: defaults.lowStockThreshold || 10,
      allow_backorder: false,
      ...productData.inventory_settings
    },
    // Apply pricing settings
    pricing_settings: {
      tax_included: defaults.taxIncluded,
      currency: defaults.currency,
      ...productData.pricing_settings
    },
    // Apply dimensions if physical product
    dimensions: config.features.weightDimensions ? {
      unit_length: defaults.dimensionUnit || 'cm',
      unit_weight: defaults.weightUnit || 'kg',
      ...productData.dimensions
    } : {},
    // Apply digital product info if digital
    digital_product_info: config.features.digitalProducts ? {
      delivery_method: defaults.deliveryMethod || 'automatic',
      download_limit: defaults.downloadLimit || 5,
      link_expiration: defaults.linkExpiration || '30 days',
      ...productData.digital_product_info
    } : {},
    // Apply service info if service
    service_info: config.features.services ? {
      time_unit: defaults.timeUnit || 'minutes',
      booking_advance: defaults.bookingAdvance || '7 days',
      cancellation_policy: defaults.cancellationPolicy || '24 hours',
      ...productData.service_info
    } : {}
  };
}

/**
 * Validate product data against business type
 * @param {string} businessType - Business type
 * @param {Object} productData - Product data to validate
 * @returns {Object} Validation result
 */
export function validateProductForBusinessType(businessType, productData) {
  const config = getBusinessTypeConfig(businessType);
  const errors = [];

  // Check required fields based on business type
  if (config.features.digitalProducts && productData.type === 'digital') {
    if (!productData.digital_product_info || Object.keys(productData.digital_product_info).length === 0) {
      errors.push('Digital product information is required for digital products');
    }
  }

  if (config.features.services && productData.type === 'service') {
    if (!productData.service_info || Object.keys(productData.service_info).length === 0) {
      errors.push('Service information is required for services');
    }
  }

  // Check forbidden features
  if (!config.features.inventory && productData.inventory_settings?.track_inventory) {
    errors.push('Inventory tracking is not available for this business type');
  }

  if (!config.features.weightDimensions && (productData.dimensions?.weight || productData.dimensions?.length)) {
    errors.push('Weight and dimensions are not available for this business type');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Hook for business configuration
 * RLS sẽ tự động xử lý tenant_id từ JWT token
 * @returns {Object} Business configuration hook
 */
export function useBusinessConfig() {
  const [config, setConfig] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function loadConfig() {
      try {
        // RLS sẽ tự động filter theo tenant_id từ JWT token
        const result = await getTenantBusinessConfig();
        if (result.success) {
          setConfig(result.data);
        }
      } catch (error) {
        console.error('Error loading business config:', error);
      } finally {
        setLoading(false);
      }
    }

    loadConfig();
  }, []);

  const updateBusinessType = async (businessType) => {
    try {
      setLoading(true);
      // RLS sẽ tự động xử lý tenant_id
      const result = await updateTenantBusinessType(businessType);

      if (result.success) {
        // Reload config sau khi update
        const newConfig = await getTenantBusinessConfig();
        if (newConfig.success) {
          setConfig(newConfig.data);
        }
      }

      setLoading(false);
      return result;
    } catch (error) {
      setLoading(false);
      return { success: false, error: error.message };
    }
  };

  return {
    config,
    loading,
    updateBusinessType,
    businessType: config?.businessType,
    businessConfig: config?.config,
    isFeatureEnabled: (feature) => config ? isFeatureEnabled(config.businessType, feature) : false,
    getEnabledFeatures: () => config ? getEnabledFeatures(config.businessType) : [],
    getHiddenFeatures: () => config ? getHiddenFeatures(config.businessType) : []
  };
}

// Business Configuration Context
const BusinessConfigContext = createContext(null);

/**
 * Business Configuration Provider
 * Quản lý global state cho business configuration
 * RLS sẽ tự động xử lý tenant_id từ JWT token
 */
export function BusinessConfigProvider({ children }) {
  const { initialized: authInitialized } = useAuthContext();
  const [config, setConfig] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Load business configuration với improved error handling và caching
  const loadConfig = async (forceRefresh = false) => {
    try {
      setLoading(true);
      setError(null);

      console.log('🔄 Loading business config...', { forceRefresh });

      // RLS sẽ tự động filter theo tenant_id từ JWT token
      const result = await getTenantBusinessConfig();

      // getTenantBusinessConfig now always returns success with fallback config
      if (result.success) {
        setConfig(result.data);
        setError(null);
        console.log('✅ Business config loaded successfully:', result.data.businessType);
      } else {
        // This should rarely happen now due to fallback logic
        console.warn('⚠️ Unexpected error from getTenantBusinessConfig:', result.error);
        setError(result.error);
        // Set default retail config as fallback
        const defaultConfig = getBusinessTypeConfig('retail');
        setConfig({
          businessType: 'retail',
          config: defaultConfig,
          features: defaultConfig.features,
          defaultSettings: defaultConfig.defaultSettings,
          tenantInfo: null
        });
      }
    } catch (err) {
      console.error('❌ Error loading business config:', err);
      setError(err);
      // Set default retail config as fallback
      const defaultConfig = getBusinessTypeConfig('retail');
      setConfig({
        businessType: 'retail',
        config: defaultConfig,
        features: defaultConfig.features,
        defaultSettings: defaultConfig.defaultSettings,
        tenantInfo: null
      });
    } finally {
      setLoading(false);
    }
  };

  // Update business type
  const updateBusinessType = async (businessType) => {
    try {
      console.log('🔄 Updating business type to:', businessType);

      // RLS sẽ tự động xử lý tenant_id
      const result = await updateTenantBusinessType(businessType);

      if (result.success) {
        console.log('✅ Business type updated, reloading config...');
        // Reload config sau khi update với force refresh
        await loadConfig(true);
        return { success: true, data: result.data };
      }

      console.error('❌ Failed to update business type:', result.error);
      return result;
    } catch (err) {
      console.error('❌ Error updating business type:', err);
      return { success: false, error: err.message || err };
    }
  };

  // Load config khi auth initialized
  useEffect(() => {
    if (authInitialized) {
      console.log('🔄 Auth initialized, loading business config...');
      loadConfig();
    } else {
      console.log('⏳ Waiting for auth initialization...');
      // Reset state khi auth chưa initialized
      setConfig(null);
      setLoading(true);
      setError(null);
    }
  }, [authInitialized]);

  // Create fresh context value với enhanced utilities
  const contextValue = {
    // Core state
    config,
    loading,
    error,

    // Actions
    loadConfig,
    refreshConfig: () => loadConfig(true), // Force refresh
    updateBusinessType,

    // Quick access properties
    businessType: config?.businessType,
    businessConfig: config?.config,
    tenantInfo: config?.tenantInfo,

    // Feature utilities
    isFeatureEnabled: (feature) => config ? isFeatureEnabled(config.businessType, feature) : false,
    getEnabledFeatures: () => config ? getEnabledFeatures(config.businessType) : [],
    getHiddenFeatures: () => config ? getHiddenFeatures(config.businessType) : [],

    // UI utilities
    getUIConfig: () => config ? getUIConfig(config.businessType) : {},
    getNavigationConfig: () => config ? getNavigationConfig(config.businessType) : {},
    getDashboardConfig: () => config ? getDashboardConfig(config.businessType) : {},
    getProductFormConfig: () => config ? getProductFormConfig(config.businessType) : {},
    shouldShowUIElement: (element, context = 'navigation') =>
      config ? shouldShowUIElement(config.businessType, element, context) : false,
    getPrioritizedUIElements: (context = 'navigation') =>
      config ? getPrioritizedUIElements(config.businessType, context) : [],

    // Validation utilities
    validateProductForBusinessType: (productData) =>
      config ? validateProductForBusinessType(config.businessType, productData) : { isValid: true, errors: [] },
    applyBusinessTypeDefaults: (productData) =>
      config ? applyBusinessTypeDefaults(config.businessType, productData) : productData
  };

  return (
    <BusinessConfigContext.Provider value={contextValue}>
      {children}
    </BusinessConfigContext.Provider>
  );
}

/**
 * Hook để sử dụng Business Configuration Context
 * @returns {Object} Business configuration context
 */
export function useBusinessConfigContext() {
  const context = useContext(BusinessConfigContext);
  if (!context) {
    throw new Error('useBusinessConfigContext must be used within a BusinessConfigProvider');
  }
  return context;
}

/**
 * Debug business configuration
 * @returns {Promise<Object>} Debug information
 */
export async function debugBusinessConfig() {
  try {
    console.log('🔍 Debugging business configuration...');

    // Test direct database access
    const result = await fetchData('tenants', {
      columns: 'id, name, business_type, created_at, updated_at',
      single: true
    });

    console.log('📊 Debug Results:', {
      success: result.success,
      data: result.data,
      error: result.error
    });

    if (result.success && result.data) {
      const businessType = result.data.businessType || 'retail';
      const config = getBusinessTypeConfig(businessType);

      console.log('✅ Business Type Config:', {
        businessType,
        configName: config.name,
        enabledFeatures: Object.keys(config.features).filter(f => config.features[f]),
        hiddenFeatures: config.hiddenFeatures || []
      });
    }

    return result;
  } catch (error) {
    console.error('❌ Debug error:', error);
    return { success: false, error };
  }
}

/**
 * Refresh business config trong context
 * Sử dụng để force refresh config khi cần thiết
 */
export function refreshBusinessConfig() {
  // Function này sẽ được implement trong context
  console.warn('refreshBusinessConfig should be called from context');
}
