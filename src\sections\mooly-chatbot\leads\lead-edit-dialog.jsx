'use client';

import { useState, useEffect, useMemo } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z as zod } from 'zod';

import {
  Box,
  Stack,
  Dialog,
  Button,
  TextField,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Alert,
  Typography,
  Divider,
} from '@mui/material';
import { LoadingButton } from '@mui/lab';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';

import { toast } from 'src/components/snackbar';
import { Scrollbar } from 'src/components/scrollbar';

import { 
  updateLead, 
  getStatusOptionsFromWorkflow
} from 'src/actions/mooly-chatbot/chatbot-lead-service';
import { 
  useChatbotWorkflowStages,
  stagesToStatusOptions
} from 'src/actions/mooly-chatbot/unified-workflow-service';

// ----------------------------------------------------------------------

// Schema validation - đồng bộ với lead-new-dialog
const LeadSchema = zod.object({
  fullName: zod.string().min(1, { message: 'Họ và tên là bắt buộc!' }),
  phone: zod.string().min(1, { message: 'Số điện thoại là bắt buộc!' }),
  email: zod.string().email({ message: 'Email không hợp lệ!' }).optional().or(zod.literal('')),
  status: zod.string().min(1, { message: 'Trạng thái là bắt buộc!' }),
  source: zod.string().min(1, { message: 'Nguồn là bắt buộc!' }),
  notes: zod.string().optional(),
  leadScore: zod.number().min(0).max(100).optional(),
  nextFollowUpAt: zod.date().optional().nullable(),
});

// ----------------------------------------------------------------------

export default function LeadEditDialog({ open, onClose, currentLead, onSuccess }) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Load workflow stages để lấy status options đồng bộ
  const { stages } = useChatbotWorkflowStages(null);

  // Status options từ workflow - đồng bộ với kanban view
  const statusOptions = useMemo(() => getStatusOptionsFromWorkflow(stages), [stages]);
  
  // Source options - đồng bộ với lead-new-dialog
  const sourceOptions = [
    { value: 'manual', label: 'Thủ công' },
    { value: 'website', label: 'Website' },
    { value: 'facebook', label: 'Facebook' },
    { value: 'zalo', label: 'Zalo' },
    { value: 'phone', label: 'Điện thoại' },
    { value: 'referral', label: 'Giới thiệu' },
    { value: 'other', label: 'Khác' },
  ];

  const {
    reset,
    control,
    handleSubmit,
    formState: { errors, isDirty },
  } = useForm({
    resolver: zodResolver(LeadSchema),
    defaultValues: {
      fullName: '',
      phone: '',
      email: '',
      status: 'new',
      source: 'manual',
      notes: '',
      leadScore: 0,
      nextFollowUpAt: null,
    },
  });

  // Update form when currentLead changes
  useEffect(() => {
    if (currentLead && open) {
      try {
        // Parse leadData nếu là string, otherwise sử dụng trực tiếp
        let parsedLeadData = {};
        if (typeof currentLead.leadData === 'string') {
          parsedLeadData = JSON.parse(currentLead.leadData);
        } else if (typeof currentLead.leadData === 'object' && currentLead.leadData !== null) {
          parsedLeadData = currentLead.leadData;
        }
        
        const formData = {
          fullName: currentLead.fullName || parsedLeadData.fullName || parsedLeadData.full_name || '',
          email: currentLead.email || parsedLeadData.email || '',
          phone: currentLead.phone || parsedLeadData.phone || '',
          status: currentLead.status || 'new',
          source: currentLead.source || 'manual',
          notes: currentLead.notes || '',
          leadScore: currentLead.leadScore || parsedLeadData.leadScore || 0,
          nextFollowUpAt: currentLead.nextFollowUpAt ? new Date(currentLead.nextFollowUpAt) : null,
        };
        
        reset(formData);
      } catch (err) {
        console.error('Error parsing lead data:', err);
        // Fallback với dữ liệu basic
        reset({
          fullName: currentLead.fullName || '',
          email: currentLead.email || '',
          phone: currentLead.phone || '',
          status: currentLead.status || 'new',
          source: currentLead.source || 'manual',
          notes: currentLead.notes || '',
          leadScore: currentLead.leadScore || 0,
          nextFollowUpAt: currentLead.nextFollowUpAt ? new Date(currentLead.nextFollowUpAt) : null,
        });
      }
    }
  }, [currentLead, open, reset]);

  const onSubmit = async (data) => {
    if (!currentLead) return;

    try {
      setLoading(true);
      setError(null);

      // Prepare lead data - sử dụng camelCase, supabase-utils sẽ tự convert
      const leadData = {
        fullName: data.fullName,
        email: data.email || null,
        phone: data.phone,
        status: data.status,
        source: data.source,
        notes: data.notes || null,
        leadScore: data.leadScore || 0,
        nextFollowUpAt: data.nextFollowUpAt ? data.nextFollowUpAt.toISOString() : null,
      };

      const result = await updateLead(currentLead.id, leadData);

      if (result.success) {
        toast.success('Cập nhật lead thành công!');
        // Create updated lead object for optimistic update
        const updatedLead = {
          ...currentLead,
          ...leadData,
          id: currentLead.id,
          updatedAt: new Date().toISOString(),
        };
        onSuccess(updatedLead);
        onClose();
      } else {
        setError(result.error?.message || 'Có lỗi xảy ra khi cập nhật lead');
      }
    } catch (err) {
      setError(err.message || 'Có lỗi xảy ra khi cập nhật lead');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      setError(null);
      onClose();
    }
  };

  return (
    <Dialog 
      open={open} 
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          maxHeight: '90vh',
          display: 'flex',
          flexDirection: 'column',
        },
      }}
    >
      <DialogTitle>
        <Typography variant="h6">Chỉnh Sửa Lead</Typography>
      </DialogTitle>

      <DialogContent 
        sx={{ 
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          p: 0,
        }}
      >
        <Scrollbar sx={{ maxHeight: '60vh', px: 3, py: 2 }}>
          <form onSubmit={handleSubmit(onSubmit)}>
            <Stack spacing={3}>
              {error && (
                <Alert severity="error" onClose={() => setError(null)}>
                  {error}
                </Alert>
              )}

              {/* THÔNG TIN BẮT BUỘC */}
              <Typography variant="h6" color="primary">
                Thông tin bắt buộc
              </Typography>

              <Grid container spacing={2}>
                {/* Full Name */}
                <Grid size={{ xs: 12, md: 6 }}>
                  <Controller
                    name="fullName"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        fullWidth
                        label="Họ và tên *"
                        error={!!errors.fullName}
                        helperText={errors.fullName?.message}
                      />
                    )}
                  />
                </Grid>

                {/* Phone */}
                <Grid size={{ xs: 12, md: 6 }}>
                  <Controller
                    name="phone"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        fullWidth
                        label="Số điện thoại *"
                        error={!!errors.phone}
                        helperText={errors.phone?.message}
                      />
                    )}
                  />
                </Grid>

                {/* Status */}
                <Grid size={{ xs: 12, md: 6 }}>
                  <Controller
                    name="status"
                    control={control}
                    render={({ field }) => (
                      <FormControl fullWidth error={!!errors.status}>
                        <InputLabel>Trạng thái *</InputLabel>
                        <Select {...field} label="Trạng thái *">
                          {statusOptions.map((option) => (
                            <MenuItem key={option.value} value={option.value}>
                              {option.label}
                            </MenuItem>
                          ))}
                        </Select>
                        {errors.status && (
                          <Typography variant="caption" color="error" sx={{ mt: 0.5 }}>
                            {errors.status.message}
                          </Typography>
                        )}
                      </FormControl>
                    )}
                  />
                </Grid>

                {/* Source */}
                <Grid size={{ xs: 12, md: 6 }}>
                  <Controller
                    name="source"
                    control={control}
                    render={({ field }) => (
                      <FormControl fullWidth error={!!errors.source}>
                        <InputLabel>Nguồn *</InputLabel>
                        <Select {...field} label="Nguồn *">
                          {sourceOptions.map((option) => (
                            <MenuItem key={option.value} value={option.value}>
                              {option.label}
                            </MenuItem>
                          ))}
                        </Select>
                        {errors.source && (
                          <Typography variant="caption" color="error" sx={{ mt: 0.5 }}>
                            {errors.source.message}
                          </Typography>
                        )}
                      </FormControl>
                    )}
                  />
                </Grid>
              </Grid>

              <Divider />

              {/* THÔNG TIN BỔ SUNG */}
              <Typography variant="h6" color="text.secondary">
                Thông tin bổ sung
              </Typography>

              <Grid container spacing={2}>
                {/* Email */}
                <Grid size={{ xs: 12, md: 6 }}>
                  <Controller
                    name="email"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        fullWidth
                        label="Email"
                        type="email"
                        error={!!errors.email}
                        helperText={errors.email?.message}
                      />
                    )}
                  />
                </Grid>

                {/* Lead Score */}
                <Grid size={{ xs: 12, md: 6 }}>
                  <Controller
                    name="leadScore"
                    control={control}
                    render={({ field: { value, onChange, ...field } }) => (
                      <TextField
                        {...field}
                        fullWidth
                        label="Điểm lead (0-100)"
                        type="number"
                        value={value || 0}
                        onChange={(e) => onChange(Number(e.target.value))}
                        inputProps={{ min: 0, max: 100 }}
                        error={!!errors.leadScore}
                        helperText={errors.leadScore?.message}
                      />
                    )}
                  />
                </Grid>

                {/* Next Follow Up */}
                <Grid size={{ xs: 12 }}>
                  <Controller
                    name="nextFollowUpAt"
                    control={control}
                    render={({ field }) => (
                      <DateTimePicker
                        {...field}
                        label="Lịch theo dõi tiếp theo"
                        slotProps={{
                          textField: {
                            fullWidth: true,
                            error: !!errors.nextFollowUpAt,
                            helperText: errors.nextFollowUpAt?.message,
                          },
                        }}
                      />
                    )}
                  />
                </Grid>

                {/* Notes */}
                <Grid size={{ xs: 12 }}>
                  <Controller
                    name="notes"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        fullWidth
                        label="Ghi chú"
                        placeholder="Thêm ghi chú, thông tin công ty, địa chỉ và các thông tin bổ sung khác..."
                        multiline
                        rows={4}
                      />
                    )}
                  />
                </Grid>
              </Grid>
            </Stack>
          </form>
        </Scrollbar>
      </DialogContent>

      <DialogActions
        sx={{ 
          borderTop: 1, 
          borderColor: 'divider',
          bgcolor: 'background.paper',
          px: 3,
          py: 2,
        }}
      >
        <Button onClick={handleClose} disabled={loading}>
          Hủy bỏ
        </Button>
        <LoadingButton
          onClick={handleSubmit(onSubmit)}
          loading={loading}
          variant="contained"
          disabled={!isDirty}
        >
          Cập nhật Lead
        </LoadingButton>
      </DialogActions>
    </Dialog>
  );
} 