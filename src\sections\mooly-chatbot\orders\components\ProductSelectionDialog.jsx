'use client';

import PropTypes from 'prop-types';
import { useDebounce } from 'minimal-shared/hooks';
import React, { useMemo, useState, useCallback } from 'react';

import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import Avatar from '@mui/material/Avatar';
import Checkbox from '@mui/material/Checkbox';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import Pagination from '@mui/material/Pagination';
import DialogTitle from '@mui/material/DialogTitle';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import InputAdornment from '@mui/material/InputAdornment';
import CircularProgress from '@mui/material/CircularProgress';

import { fCurrency } from 'src/utils/format-number';

import { useProducts } from 'src/actions/mooly-chatbot/product-hooks';
import { isVariableProduct } from 'src/actions/mooly-chatbot/product-constants';

import { Iconify } from 'src/components/iconify';
import { Scrollbar } from 'src/components/scrollbar';

import { ProductVariantSelector } from './ProductVariantSelector';
import { getDisplayPrice, normalizeFieldData, createOrderItem } from './orderFieldConfig';

// Constants
const ITEMS_PER_PAGE = 20;

/**
 * Dialog chọn sản phẩm và biến thể
 */
export function ProductSelectionDialog({ open, onClose, onSelectProduct }) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedProducts, setSelectedProducts] = useState(new Set());
  const [currentPage, setCurrentPage] = useState(1);

  // Debounce search query để tránh call quá nhiều
  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  const { products: rawProducts, isLoading } = useProducts();
  const products = normalizeFieldData(rawProducts || [], 'product');

  // Lọc sản phẩm theo từ khóa tìm kiếm với debounce
  const filteredProducts = useMemo(() => {
    if (!debouncedSearchQuery.trim()) return products;

    const query = debouncedSearchQuery.toLowerCase();
    return products.filter(product =>
      product.name?.toLowerCase().includes(query) ||
      product.sku?.toLowerCase().includes(query)
    );
  }, [products, debouncedSearchQuery]);

  // Tính toán pagination
  const totalPages = Math.ceil(filteredProducts.length / ITEMS_PER_PAGE);
  const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
  const endIndex = startIndex + ITEMS_PER_PAGE;
  const paginatedProducts = filteredProducts.slice(startIndex, endIndex);

  // Reset page khi search query thay đổi
  const handleSearchChange = useCallback((event) => {
    setSearchQuery(event.target.value);
    setCurrentPage(1);
  }, []);

  // Kiểm tra sản phẩm có hết hàng không
  const isOutOfStock = useCallback((product) => {
    const stockQuantity = product.stockQuantity || 0;
    return stockQuantity <= 0;
  }, []);

  // Kiểm tra variant có hết hàng không
  const isVariantOutOfStock = useCallback((variant) => {
    const stockQuantity = variant.stockQuantity || 0;
    return stockQuantity <= 0;
  }, []);

  // Xử lý chọn sản phẩm
  const handleProductSelect = useCallback((product, isSelected) => {
    // Không cho phép chọn sản phẩm hết hàng
    if (isOutOfStock(product)) {
      return;
    }

    const newSelected = new Set(selectedProducts);

    if (isSelected) {
      newSelected.add(product.id);
    } else {
      newSelected.delete(product.id);
    }

    setSelectedProducts(newSelected);
  }, [selectedProducts, isOutOfStock]);

  // Xử lý chọn biến thể
  const handleVariantSelect = (product, variant) => {
    // Không cho phép chọn variant hết hàng
    if (isVariantOutOfStock(variant)) {
      return;
    }

    const orderItem = createOrderItem(product, variant);
    onSelectProduct(orderItem);
  };

  // Xử lý chọn sản phẩm đơn giản (không có biến thể)
  const handleSimpleProductSelect = (product) => {
    // Không cho phép chọn sản phẩm hết hàng
    if (isOutOfStock(product)) {
      return;
    }

    const orderItem = createOrderItem(product, null);
    onSelectProduct(orderItem);
  };

  // Xử lý hoàn tất chọn
  const handleComplete = useCallback(() => {
    // Chọn tất cả sản phẩm đơn giản đã được tick
    const simpleProducts = filteredProducts.filter(product =>
      selectedProducts.has(product.id) && !isVariableProduct(product)
    );

    simpleProducts.forEach(product => {
      handleSimpleProductSelect(product);
    });

    // Reset và đóng dialog
    setSelectedProducts(new Set());
    setSearchQuery('');
    setCurrentPage(1);
    onClose();
  }, [filteredProducts, selectedProducts, handleSimpleProductSelect, onClose]);

  // Xử lý hủy
  const handleCancel = useCallback(() => {
    setSelectedProducts(new Set());
    setSearchQuery('');
    setCurrentPage(1);
    onClose();
  }, [onClose]);

  // Xử lý thay đổi trang
  const handlePageChange = useCallback((_, page) => {
    setCurrentPage(page);
  }, []);

  return (
    <Dialog
      open={open}
      onClose={handleCancel}
      maxWidth="md"
      fullWidth
      // slotProps={{
      //   paper: {
      //     sx: { height: '80vh' },
      //   },
      // }}
    >
      <DialogTitle>
        <Stack direction="row" alignItems="center" justifyContent="space-between">
          <Typography variant="h6">Tất cả sản phẩm</Typography>
          <Button
            variant="text"
            color="inherit"
            onClick={handleCancel}
            sx={{ minWidth: 'auto', p: 1 }}
          >
            <Iconify icon="mingcute:close-line" />
          </Button>
        </Stack>
      </DialogTitle>

      <DialogContent sx={{ p: 0 }}>
        {/* Thanh tìm kiếm */}
        <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
          <TextField
            fullWidth
            placeholder="Tìm sản phẩm"
            value={searchQuery}
            onChange={handleSearchChange}
            slotProps={{
              input: {
                startAdornment: (
                  <InputAdornment position="start">
                    <Iconify icon="eva:search-fill" />
                  </InputAdornment>
                ),
              },
            }}
          />
        </Box>

        {/* Header bảng */}
        <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider', bgcolor: 'background.neutral' }}>
          <Stack direction="row" spacing={2} alignItems="center">
            <Box sx={{ width: 40 }} /> {/* Checkbox column */}
            <Typography variant="subtitle2" sx={{ flex: 1 }}>Sản phẩm</Typography>
            <Typography variant="subtitle2" sx={{ width: 120, textAlign: 'center' }}>SKU</Typography>
            <Typography variant="subtitle2" sx={{ width: 120, textAlign: 'center' }}>Giá bán</Typography>
            <Typography variant="subtitle2" sx={{ width: 100, textAlign: 'center' }}>Tồn kho</Typography>
          </Stack>
        </Box>

        {/* Danh sách sản phẩm */}
        <Scrollbar sx={{ height: 'calc(80vh - 280px)' }}>
          {isLoading ? (
            <Box sx={{ p: 3, textAlign: 'center' }}>
              <CircularProgress size={24} />
              <Typography sx={{ mt: 1 }}>Đang tải...</Typography>
            </Box>
          ) : paginatedProducts.length === 0 ? (
            <Box sx={{ p: 3, textAlign: 'center' }}>
              <Typography color="text.secondary">
                {debouncedSearchQuery ? 'Không tìm thấy sản phẩm nào' : 'Chưa có sản phẩm'}
              </Typography>
            </Box>
          ) : (
            <Box>
              {paginatedProducts.map((product) => (
                <ProductRow
                  key={product.id}
                  product={product}
                  isSelected={selectedProducts.has(product.id)}
                  onSelect={handleProductSelect}
                  onVariantSelect={handleVariantSelect}
                  onSimpleProductSelect={handleSimpleProductSelect}
                  isOutOfStock={isOutOfStock}
                  isVariantOutOfStock={isVariantOutOfStock}
                />
              ))}
            </Box>
          )}
        </Scrollbar>

        {/* Pagination */}
        {!isLoading && totalPages > 1 && (
          <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider', display: 'flex', justifyContent: 'center' }}>
            <Pagination
              count={totalPages}
              page={currentPage}
              onChange={handlePageChange}
              color="primary"
              showFirstButton
              showLastButton
            />
          </Box>
        )}
      </DialogContent>

      <DialogActions sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
        <Button onClick={handleCancel}>Hủy</Button>
        <Button
          variant="contained"
          onClick={handleComplete}
          disabled={selectedProducts.size === 0}
        >
          Hoàn tất chọn
        </Button>
      </DialogActions>
    </Dialog>
  );
}

/**
 * Component hiển thị một dòng sản phẩm
 */
function ProductRow({
  product,
  isSelected,
  onSelect,
  onVariantSelect,
  onSimpleProductSelect,
  isOutOfStock,
  isVariantOutOfStock
}) {
  const hasVariants = isVariableProduct(product);
  const outOfStock = isOutOfStock(product);
  const stockQuantity = product.stockQuantity || 0;

  const handleCheckboxChange = (event) => {
    if (!outOfStock) {
      onSelect(product, event.target.checked);
    }
  };

  const handleProductClick = () => {
    if (!hasVariants && !outOfStock) {
      onSimpleProductSelect(product);
    }
  };

  return (
    <Box>
      {/* Dòng sản phẩm chính */}
      <Stack
        direction="row"
        spacing={2}
        alignItems="center"
        sx={{
          p: 1.5,
          borderBottom: 1,
          borderColor: 'divider',
          '&:hover': !outOfStock ? { bgcolor: 'action.hover' } : {},
          cursor: !hasVariants && !outOfStock ? 'pointer' : 'default',
          opacity: outOfStock ? 0.6 : 1,
          bgcolor: outOfStock ? 'action.disabledBackground' : 'transparent'
        }}
        onClick={!hasVariants && !outOfStock ? handleProductClick : undefined}
      >
        {/* Checkbox chỉ hiển thị cho sản phẩm đơn giản */}
        {!hasVariants && (
          <Checkbox
            checked={isSelected}
            onChange={handleCheckboxChange}
            onClick={(e) => e.stopPropagation()}
            size="small"
            disabled={outOfStock}
          />
        )}

        {/* Placeholder cho sản phẩm có biến thể để căn chỉnh */}
        {hasVariants && <Box sx={{ width: 32 }} />}

        <Stack direction="row" spacing={2} alignItems="center" sx={{ flex: 1 }}>
          <Avatar
            src={product.avatar}
            sx={{ width: 36, height: 36 }}
          >
            {product.name?.charAt(0)}
          </Avatar>

          <Box sx={{ flex: 1 }}>
            <Typography
              variant="body2"
              sx={{
                fontWeight: 'medium',
                fontSize: '0.875rem',
                color: outOfStock ? 'text.disabled' : 'text.primary'
              }}
            >
              {product.name}
              {outOfStock && (
                <Typography
                  component="span"
                  variant="caption"
                  sx={{
                    ml: 1,
                    color: 'error.main',
                    fontWeight: 'bold',
                    fontSize: '0.7rem'
                  }}
                >
                  (Hết hàng)
                </Typography>
              )}
            </Typography>
            {hasVariants && (
              <Typography variant="caption" color="text.secondary">
                Có biến thể • Chọn bên dưới
              </Typography>
            )}
          </Box>
        </Stack>

        <Typography
          variant="body2"
          sx={{
            width: 120,
            textAlign: 'center',
            fontSize: '0.8rem',
            color: outOfStock ? 'text.disabled' : 'text.primary'
          }}
        >
          {product.sku || '-'}
        </Typography>

        <Typography
          variant="body2"
          sx={{
            width: 120,
            textAlign: 'center',
            fontSize: '0.8rem',
            color: outOfStock ? 'text.disabled' : 'text.primary'
          }}
        >
          {fCurrency(getDisplayPrice(product))}
        </Typography>

        <Typography
          variant="body2"
          sx={{
            width: 100,
            textAlign: 'center',
            fontSize: '0.8rem',
            color: outOfStock ? 'error.main' : (stockQuantity <= 5 ? 'warning.main' : 'text.primary'),
            fontWeight: outOfStock || stockQuantity <= 5 ? 'bold' : 'normal'
          }}
        >
          {stockQuantity}
          {stockQuantity <= 5 && stockQuantity > 0 && (
            <Typography
              component="span"
              variant="caption"
              sx={{
                ml: 0.5,
                color: 'warning.main',
                fontSize: '0.7rem'
              }}
            >
              (Sắp hết)
            </Typography>
          )}
        </Typography>
      </Stack>

      {/* Danh sách biến thể - luôn hiển thị nếu có */}
      {hasVariants && (
        <ProductVariantSelector
          product={product}
          onVariantSelect={onVariantSelect}
          isVariantOutOfStock={isVariantOutOfStock}
        />
      )}
    </Box>
  );
}

ProductSelectionDialog.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onSelectProduct: PropTypes.func.isRequired,
};

ProductRow.propTypes = {
  product: PropTypes.object.isRequired,
  isSelected: PropTypes.bool.isRequired,
  onSelect: PropTypes.func.isRequired,
  onVariantSelect: PropTypes.func.isRequired,
  onSimpleProductSelect: PropTypes.func.isRequired,
  isOutOfStock: PropTypes.func.isRequired,
  isVariantOutOfStock: PropTypes.func.isRequired,
};
