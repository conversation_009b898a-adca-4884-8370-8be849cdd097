'use client';

import { useRef, useState, useEffect, useCallback, useMemo } from 'react';
import {
  SortableContext,
  verticalListSortingStrategy,
  horizontalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSensor,
  DndContext,
  useSensors,
  MouseSensor,
  TouchSensor,
  closestCenter,
  pointerWithin,
  KeyboardSensor,
  MeasuringStrategy,
} from '@dnd-kit/core';

import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import Switch from '@mui/material/Switch';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import FormControlLabel from '@mui/material/FormControlLabel';


import { useBoolean } from 'src/hooks/use-boolean';

import { DashboardContent } from 'src/layouts/dashboard';

import { EmptyContent } from 'src/components/empty-content';
import { Iconify } from 'src/components/iconify';
import { toast } from 'src/components/snackbar';

import { kanbanClasses } from 'src/sections/kanban/classes';
import { coordinateGetter } from 'src/sections/kanban/utils';
import { KanbanColumnSkeleton } from 'src/sections/kanban/components/kanban-skeleton';


import {
  useLeads,
  useKanbanDisplayConfig,
  updateLeadStatus,
  getStatusOptionsFromWorkflow
} from 'src/actions/mooly-chatbot/chatbot-lead-service';
import {
  useChatbotWorkflowStages,
  stagesToStatusOptions
} from 'src/actions/mooly-chatbot/unified-workflow-service';


import LeadsKanbanTaskItem from './leads-kanban-task-item';
import LeadNewDialog from './lead-new-dialog';
import LeadEditDialog from './lead-edit-dialog';
import LeadDetailDialog from './lead-detail-dialog';
import LeadDeleteDialog from './lead-delete-dialog';
import LeadsKanbanDragOverlay from './leads-kanban-drag-overlay';
import UnifiedWorkflowBuilderDialog from './unified-workflow-builder-dialog';
import KanbanDisplayConfigDialog from './kanban-display-config-dialog';
import LeadsKanbanColumn from './leads-kanban-column';

// =====================================================
// CONSTANTS
// =====================================================



const cssVars = {
  '--item-gap': '16px',
  '--item-radius': '12px',
  '--column-gap': '24px',
  '--column-width': '336px',
  '--column-radius': '16px',
  '--column-padding': '20px 16px 16px 16px',
};

// =====================================================
// UTILITY FUNCTIONS
// =====================================================

/**
 * Transform leads data để phù hợp với Kanban structure
 */
const transformLeadsToKanbanData = (leads = [], stages = []) => {
  const statusOptions = stagesToStatusOptions(stages);
  
  // Luôn tạo columns từ status options (ngay cả khi chưa có leads)
  const columns = statusOptions.map((status) => ({
    id: status.value,
    name: status.label,
    color: status.color,
    icon: status.icon,
    description: status.description,
    order: status.order || 0,
  }));

  // Thêm column "Chưa phân loại" cho các leads có status không khớp
  const uncategorizedColumn = {
    id: 'uncategorized',
    name: 'Chưa phân loại',
    color: 'default',
    icon: 'solar:question-circle-bold',
    description: 'Leads có trạng thái không khớp với workflow hiện tại (chỉ xem, không thể chuyển vào)',
    order: -1, // Đặt ở đầu
    isReadOnly: true, // Đánh dấu là read-only
  };

  // Sắp xếp columns theo order
  const sortedColumns = [uncategorizedColumn, ...columns.sort((a, b) => (a.order || 0) - (b.order || 0))];

  // Khởi tạo tasks object cho tất cả columns
  const tasks = {};
  sortedColumns.forEach((column) => {
    tasks[column.id] = [];
  });

  // Lấy danh sách valid status values từ workflow
  const validStatuses = statusOptions.map(status => status.value);

  // Distribute leads into columns
  leads.forEach((lead) => {
    const leadStatus = lead.status || 'new';
    
    // Kiểm tra xem status có hợp lệ với workflow không
    const targetColumn = validStatuses.includes(leadStatus) ? leadStatus : 'uncategorized';
    
    if (tasks[targetColumn]) {
      tasks[targetColumn].push({
        id: lead.id,
        name: lead.fullName || lead.phone || lead.email || 'Không có tên',
        description: lead.notes || '',
        status: lead.status || 'new',
        priority: 'medium', // Default priority
        assignee: lead.assignedTo ? [{ id: '1', name: lead.assignedTo }] : [],
        reporter: { id: '1', name: 'System' },
        due: lead.nextFollowUpAt ? [lead.nextFollowUpAt, lead.nextFollowUpAt] : null,
        labels: [lead.source || 'unknown'],
        comments: [],
        attachments: [],
        // Original lead data for editing
        leadData: lead,
        // Flag để hiển thị warning cho uncategorized leads
        isUncategorized: targetColumn === 'uncategorized',
      });
    }
  });

  return { columns: sortedColumns, tasks };
};

// =====================================================
// MAIN COMPONENT
// =====================================================

export default function LeadsKanbanView() {
  const recentlyMovedToNewContainer = useRef(false);
  const lastOverId = useRef(null);
  const pendingUpdates = useRef(new Set()); // Track pending updates để tránh duplicate

  const [columnFixed, setColumnFixed] = useState(true);
  const [activeId, setActiveId] = useState(null);
  const [currentLead, setCurrentLead] = useState(null);


  // Dialog states
  const newDialog = useBoolean();
  const editDialog = useBoolean();
  const detailDialog = useBoolean();
  const deleteDialog = useBoolean();
  const workflowDialog = useBoolean();
  const displayConfigDialog = useBoolean();


  // Load leads data - null chatbotId to get all leads
  const leadsOptions = useMemo(() => ({
    page: 1, 
    limit: 1000, // Load nhiều để hiển thị trên Kanban
  }), []);

  const { leads, loading, error, mutate } = useLeads(null, leadsOptions);

  // ✅ IMPROVED: Load workflow stages với better error handling
  const { stages: rawStages, mutate: mutateStages, error: stagesError } = useChatbotWorkflowStages(null);

  // ✅ Enhanced stages processing với validation
  const stages = useMemo(() => {
    if (rawStages && Array.isArray(rawStages) && rawStages.length > 0) {
      // Validate và normalize stages từ database
      const validStages = rawStages.filter(stage =>
        stage &&
        typeof stage === 'object' &&
        stage.id &&
        stage.name
      ).map(stage => ({
        ...stage,
        // Ensure required fields
        order: stage.order || 0,
        color: stage.color || stage.display_color || 'default',
        icon: stage.icon || 'solar:circle-bold',
        description: stage.description || '',
      }));

      return validStages;
    }

    // ✅ Enhanced fallback stages
    return [
      {
        id: 'new',
        name: 'Lead Mới',
        color: 'info',
        display_color: 'info',
        icon: 'solar:user-plus-bold',
        order: 1,
        description: 'Lead vừa được tạo'
      },
      {
        id: 'contacted',
        name: 'Đã Liên Hệ',
        color: 'warning',
        display_color: 'warning',
        icon: 'solar:phone-bold',
        order: 2,
        description: 'Đã có liên hệ ban đầu'
      },
      {
        id: 'qualified',
        name: 'Tiềm Năng',
        color: 'secondary',
        display_color: 'secondary',
        icon: 'solar:star-bold',
        order: 3,
        description: 'Lead có tiềm năng chuyển đổi'
      },
      {
        id: 'converted',
        name: 'Đã Chuyển Đổi',
        color: 'success',
        display_color: 'success',
        icon: 'solar:check-circle-bold',
        order: 4,
        isFinal: true,
        description: 'Lead đã trở thành khách hàng'
      },
      {
        id: 'lost',
        name: 'Thất Bại',
        color: 'error',
        display_color: 'error',
        icon: 'solar:close-circle-bold',
        order: 5,
        isFinal: true,
        description: 'Lead không chuyển đổi'
      },
    ];
  }, [rawStages, stagesError]);

  // Load display configuration - user-based, không phụ thuộc chatbot
  const { config: displayConfig } = useKanbanDisplayConfig();

  // Status options for card rendering
  const statusOptions = useMemo(() => getStatusOptionsFromWorkflow(stages), [stages]);

  // ✅ IMPROVED: Transform leads data với enhanced validation
  const kanbanData = useMemo(
    () => {
      if (!leads || !stages) {
        return { columns: [], tasks: {} };
      }

      const result = transformLeadsToKanbanData(leads, stages);
      return result;
    },
    [leads, stages]
  );

  const { columns = [], tasks = {} } = kanbanData;
  const columnIds = columns.map((column) => column.id);
  const isSortingContainer = activeId != null ? columnIds.includes(activeId) : false;

  // DnD Sensors - Cải thiện để stable hơn
  const sensors = useSensors(
    useSensor(MouseSensor, {
      activationConstraint: { 
        distance: 10 // Minimum distance để kích hoạt drag
      },
    }),
    useSensor(TouchSensor, {
      activationConstraint: { 
        delay: 250, // Giảm delay cho responsive hơn
        tolerance: 5 // Tolerance nhỏ hơn cho precise hơn
      },
    }),
    useSensor(KeyboardSensor, { 
      coordinateGetter 
    })
  );

  // ✅ FIXED: Collision detection chính xác
  const collisionDetectionStrategy = useCallback((args) => {
    // Nếu đang kéo column, dùng closestCenter
    if (activeId && columnIds.includes(activeId)) {
      return closestCenter({
        ...args,
        droppableContainers: args.droppableContainers.filter(
          (container) => columnIds.includes(container.id)
        ),
      });
    }

    // Cho task drag, ưu tiên pointerWithin
    const pointerIntersections = pointerWithin(args);

    if (pointerIntersections.length > 0) {
      // Tìm column container hợp lệ
      for (const intersection of pointerIntersections) {
        const overId = intersection.id;
        if (columnIds.includes(overId) && overId !== 'uncategorized') {
          return [intersection];
        }
      }
    }

    // Fallback to closestCenter
    const centerIntersections = closestCenter(args);
    if (centerIntersections.length > 0) {
      for (const intersection of centerIntersections) {
        const overId = intersection.id;
        if (columnIds.includes(overId) && overId !== 'uncategorized') {
          return [intersection];
        }
      }
    }

    return [];
  }, [activeId, columnIds]);

  // Find column for task ID - Fixed để stable hơn
  const findColumn = useCallback((id) => {
    if (!id || !tasks) {
      return null;
    }

    // Nếu id là column ID
    if (id in tasks) {
      return id;
    }

    // Nếu id là task ID, tìm column chứa task đó
    const columnId = Object.keys(tasks).find((key) =>
      Array.isArray(tasks[key]) && tasks[key].some((item) => item.id === id)
    );

    return columnId || null;
  }, [tasks]);

  useEffect(() => {
    requestAnimationFrame(() => {
      recentlyMovedToNewContainer.current = false;
    });
  }, []);

  // Drag handlers
  const onDragStart = useCallback(({ active }) => {
    setActiveId(active.id);
    recentlyMovedToNewContainer.current = false;
  }, []);

  const onDragOver = useCallback(({ active, over }) => {
    const overId = over?.id;

    if (!overId || active.id === overId) {
      return;
    }

    const activeContainer = findColumn(active.id);
    let overContainer = findColumn(overId);

    // Nếu overId là column ID, sử dụng trực tiếp
    if (columnIds.includes(overId)) {
      overContainer = overId;
    }

    if (!activeContainer || !overContainer || activeContainer === overContainer) {
      return;
    }

    // Block uncategorized
    if (overContainer === 'uncategorized') {
      return;
    }

    // Validate workflow
    const validStatuses = stages?.map(stage => stage.id) || [];
    if (!validStatuses.includes(overContainer)) {
      return;
    }

    // Cache for onDragEnd
    lastOverId.current = overContainer;
    recentlyMovedToNewContainer.current = true;
  }, [findColumn, stages, columnIds]);

  const onDragEnd = useCallback(async ({ active, over }) => {
    const draggedId = active.id;
    let overId = over?.id;

    // Reset state
    setActiveId(null);
    recentlyMovedToNewContainer.current = false;

    // Fallback to cached overId
    if (!overId && lastOverId.current) {
      overId = lastOverId.current;
    }

    if (!overId || draggedId === overId) {
      lastOverId.current = null;
      return;
    }

    const activeContainer = findColumn(draggedId);
    let overContainer = findColumn(overId);

    // Nếu overId là column ID, sử dụng trực tiếp
    if (columnIds.includes(overId)) {
      overContainer = overId;
    }

    if (!activeContainer || !overContainer || activeContainer === overContainer) {
      lastOverId.current = null;
      return;
    }

    // Block uncategorized drop
    if (overContainer === 'uncategorized') {
      toast.warning('Không thể chuyển lead vào trạng thái chưa phân loại');
      lastOverId.current = null;
      return;
    }

    // Find active task
    const activeTask = tasks[activeContainer]?.find(task => task.id === draggedId);
    if (!activeTask) {
      lastOverId.current = null;
      return;
    }

    const leadId = activeTask.id;
    const newStatus = overContainer;
    const oldStatus = activeTask.leadData?.status || activeTask.status;

    // Skip if no change
    if (oldStatus === newStatus) {
      lastOverId.current = null;
      return;
    }

    // Prevent duplicate updates
    if (pendingUpdates.current.has(leadId)) {
      lastOverId.current = null;
      return;
    }

    // Workflow validation
    const validStatuses = stages?.map(stage => stage.id) || [];
    if (!validStatuses.includes(newStatus)) {
      const stageName = stages?.find(s => s.id === newStatus)?.name || newStatus;
      toast.warning(`Trạng thái "${stageName}" không hợp lệ trong workflow hiện tại`);
      lastOverId.current = null;
      return;
    }

    // Add to pending
    pendingUpdates.current.add(leadId);

    try {
      // Call API to update status
      const result = await updateLeadStatus(leadId, newStatus);

      if (result.success) {
        // SUCCESS: Refresh data and show success message
        mutate();

        const stageName = stages?.find(s => s.id === newStatus)?.name || newStatus;
        toast.success(`Đã chuyển lead sang "${stageName}"`);

      } else {
        // API FAILED: Show error
        const errorMsg = result.error?.message || 'Không thể cập nhật trạng thái lead';
        toast.error(errorMsg);
      }

    } catch (updateError) {
      // EXCEPTION: Show error
      const errorMsg = updateError.message || 'Lỗi hệ thống khi cập nhật trạng thái';
      toast.error(errorMsg);

    } finally {
      // Always cleanup
      pendingUpdates.current.delete(leadId);
      lastOverId.current = null;
    }
  }, [findColumn, columnIds, tasks, mutate, stages]);

  // Dialog handlers
  const handleNewLead = useCallback(() => {
    setCurrentLead(null);
    newDialog.onTrue();
  }, [newDialog]);

  const handleEditLead = useCallback((lead) => {
    setCurrentLead(lead);
    editDialog.onTrue();
  }, [editDialog]);

  const handleViewLead = useCallback((lead) => {
    setCurrentLead(lead);
    detailDialog.onTrue();
  }, [detailDialog]);

  const handleDeleteLead = useCallback((lead) => {
    setCurrentLead(lead);
    deleteDialog.onTrue();
  }, [deleteDialog]);

  // Stage management handlers removed as requested

  const handleCreateSuccess = useCallback((newLead) => {
    mutate((currentData) => {
      if (currentData?.success && Array.isArray(currentData.data)) {
        return {
          ...currentData,
          data: [newLead, ...currentData.data],
          count: (currentData.count || 0) + 1,
        };
      }
      return currentData;
    }, false);
    
    newDialog.onFalse();
  }, [mutate, newDialog]);

  const handleUpdateSuccess = useCallback((updatedLead) => {
    mutate((currentData) => {
      if (currentData?.success && Array.isArray(currentData.data)) {
        return {
          ...currentData,
          data: currentData.data.map(lead => 
            lead.id === updatedLead.id ? updatedLead : lead
          ),
        };
      }
      return currentData;
    }, false);
    
    editDialog.onFalse();
  }, [mutate, editDialog]);

  const handleDeleteSuccess = useCallback((deletedIds) => {
    mutate((currentData) => {
      if (currentData?.success && Array.isArray(currentData.data)) {
        const idsArray = Array.isArray(deletedIds) ? deletedIds : [deletedIds];
        return {
          ...currentData,
          data: currentData.data.filter(lead => !idsArray.includes(lead.id)),
          count: Math.max(0, (currentData.count || 0) - idsArray.length),
        };
      }
      return currentData;
    }, false);
    
    deleteDialog.onFalse();
  }, [mutate, deleteDialog]);

  // Render functions
  const renderLoading = () => (
    <Box sx={{ gap: 'var(--column-gap)', display: 'flex', alignItems: 'flex-start' }}>
      <KanbanColumnSkeleton />
    </Box>
  );

  const renderEmpty = () => {
    // Nếu có workflow nhưng chưa có leads, vẫn hiển thị columns
    if (columns.length > 0) {
      return renderList();
    }
    
    // Nếu chưa có workflow và chưa có leads
    return (
      <EmptyContent 
        filled 
        title="Chưa có quy trình làm việc"
        description="Thiết kế quy trình làm việc để bắt đầu quản lý leads hiệu quả"
        action={
          <Stack direction="row" spacing={2}>
            <Button
              variant="outlined"
              startIcon={<Iconify icon="solar:widget-5-bold" />}
              onClick={workflowDialog.onTrue}
            >
              Thiết kế Workflow
            </Button>
            <Button
              variant="contained"
              startIcon={<Iconify icon="mingcute:add-line" />}
              onClick={handleNewLead}
            >
              Thêm Lead
            </Button>
          </Stack>
        }
        sx={{ py: 10, height: '60vh', display: 'flex', alignItems: 'center', justifyContent: 'center' }} 
      />
    );
  };

  const renderList = () => (
    <DndContext
      id="dnd-leads-kanban"
      sensors={sensors}
      collisionDetection={collisionDetectionStrategy}
      measuring={{ droppable: { strategy: MeasuringStrategy.Always } }}
      onDragStart={onDragStart}
      onDragOver={onDragOver}
      onDragEnd={onDragEnd}
    >
      <Stack sx={{ flex: '1 1 auto', overflowX: 'auto', height: '100%' }}>
        <Stack
          sx={{
            pb: 3,
            display: 'unset',
            height: '100%',
            ...(columnFixed && { minHeight: 0, display: 'flex', flex: '1 1 auto' }),
          }}
        >
          <Box
            sx={[
              (theme) => ({
                display: 'flex',
                gap: 'var(--column-gap)',
                ...(columnFixed && {
                  minHeight: 0,
                  flex: '1 1 auto',
                  [`& .${kanbanClasses.columnList}`]: {
                    ...theme.mixins.hideScrollY,
                    flex: '1 1 auto',
                  },
                }),
              }),
            ]}
          >
            <SortableContext
              items={columnIds}
              strategy={horizontalListSortingStrategy}
            >
              {columns.map((column) => (
                <LeadsKanbanColumn
                  key={column.id}
                  column={column}
                  tasks={tasks[column.id] || []}
                  disabled={columnFixed}
                >
                  <SortableContext
                    items={(tasks[column.id] || []).map(task => task.id)}
                    strategy={verticalListSortingStrategy}
                  >
                    {(tasks[column.id] || []).map((task) => (
                      <LeadsKanbanTaskItem
                        key={task.id}
                        task={task}
                        columnId={column.id}
                        displayConfig={displayConfig}
                        statusOptions={statusOptions}
                        disabled={isSortingContainer}
                        onEdit={handleEditLead}
                        onView={handleViewLead}
                        onDelete={handleDeleteLead}
                      />
                    ))}
                  </SortableContext>
                </LeadsKanbanColumn>
              ))}
            </SortableContext>
          </Box>
        </Stack>
      </Stack>

      <LeadsKanbanDragOverlay
        columns={columns}
        tasks={tasks}
        activeId={activeId}
        sx={cssVars}
      />
    </DndContext>
  );

  return (
    <DashboardContent
      maxWidth={false}
      sx={{
        ...cssVars,
        pb: 0,
        pl: { sm: 3 },
        pr: { sm: 0 },
        height: '90vh', // Cấu hình height mặc định 90vh
        display: 'flex',
        overflow: 'hidden',
        flexDirection: 'column',
      }}
    >
      {/* Header */}
      <Box
        sx={{
          pr: { sm: 3 },
          mb: { xs: 3, md: 5 },
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}
      >
        <Stack>
          <Typography variant="h4">Leads Kanban</Typography>
          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
            Quản lý leads với giao diện Kanban trực quan
            {leads?.length > 0 && ` • ${leads.length} leads`}
            {stages?.length > 0 && ` • ${stages.length} giai đoạn`}
          </Typography>
        </Stack>

        <Stack direction="row" spacing={2} alignItems="center">
          <FormControlLabel
            label="Cố định cột"
            labelPlacement="start"
            control={
              <Switch
                checked={columnFixed}
                onChange={(event) => {
                  setColumnFixed(event.target.checked);
                }}
                slotProps={{ input: { id: 'fixed-column-switch' } }}
              />
            }
          />

          <Button
            variant="outlined"
            startIcon={<Iconify icon="solar:widget-5-bold" />}
            onClick={workflowDialog.onTrue}
          >
            Thiết kế Workflow
          </Button>

          {/* <Button
            variant="outlined"
            startIcon={<Iconify icon="solar:settings-bold" />}
            onClick={displayConfigDialog.onTrue}
          >
            Cấu hình hiển thị
          </Button> */}

          <Button
            variant="outlined"
            startIcon={<Iconify icon="solar:list-bold" />}
            href="/dashboard/mooly-chatbot/leads"
          >
            List View
          </Button>
          
          <Button
            variant="contained"
            startIcon={<Iconify icon="mingcute:add-line" />}
            onClick={handleNewLead}
          >
            Thêm Lead
          </Button>
        </Stack>
      </Box>

      {/* Error Display */}
      {error && (
        <Box sx={{ mb: 3 }}>
          <Typography color="error" variant="body2">
            {typeof error === 'string' ? error : error.message || 'Có lỗi xảy ra khi tải dữ liệu'}
          </Typography>
        </Box>
      )}


      {/* Main Content */}
      <Box sx={{ flex: '1 1 auto', overflow: 'hidden', minHeight: 0 }}>
        {loading ? renderLoading() : (
          <>
            {!columns.length 
              ? renderEmpty() 
              : renderList()
            }
          </>
        )}
      </Box>

      {/* Dialogs */}
      <LeadNewDialog
        open={newDialog.value}
        onClose={newDialog.onFalse}
        onSuccess={handleCreateSuccess}
      />

      <LeadEditDialog
        open={editDialog.value}
        onClose={editDialog.onFalse}
        currentLead={currentLead?.leadData}
        onSuccess={handleUpdateSuccess}
      />

      <LeadDetailDialog
        open={detailDialog.value}
        onClose={detailDialog.onFalse}
        currentLead={currentLead?.leadData}
        onEdit={(lead) => handleEditLead({ leadData: lead })}
      />

      <LeadDeleteDialog
        open={deleteDialog.value}
        onClose={deleteDialog.onFalse}
        currentLead={currentLead?.leadData}
        selectedLeads={[]}
        onSuccess={handleDeleteSuccess}
      />

      <UnifiedWorkflowBuilderDialog
        open={workflowDialog.value}
        onClose={workflowDialog.onFalse}
        chatbotId={null}
        onSuccess={() => {
          // Refresh workflow and leads data
          mutateStages(); // Refresh workflow stages
          mutate(); // Refresh leads data
        }}
      />

      <KanbanDisplayConfigDialog
        open={displayConfigDialog.value}
        onClose={displayConfigDialog.onFalse}
      />


    </DashboardContent>
  );
}