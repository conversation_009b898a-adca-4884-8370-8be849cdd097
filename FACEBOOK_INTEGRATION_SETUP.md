# 📱 Facebook Integration Setup Guide

## 🔧 **1. CẤU HÌNH FACEBOOK APP**

### **Bước 1: Tạo Facebook App**
1. <PERSON><PERSON><PERSON> cập [Facebook Developers](https://developers.facebook.com/)
2. Đăng nhập tài khoản Facebook của bạn
3. Click **"Create App"** → chọn **"Business"**
4. Nhập tên app và email contact

### **Bước 2: Thêm Products**
Trong Facebook App Dashboard, thêm các products sau:

#### **A. Facebook Login**
- Vào **Add Product** → **Facebook Login** 
- Click **Setup**
- **Valid OAuth Redirect URIs:**
  ```
  https://your-domain.com/api/facebook-integration/callback
  http://localhost:3000/api/facebook-integration/callback (for development)
  ```

#### **B. Webhooks**
- Vào **Add Product** → **Webhooks**
- Click **Setup**
- **Callback URL:** `https://your-domain.com/api/facebook-webhook`
- **Verify Token:** Tạo random string (lưu lại để config .env)
- **Subscription Fields:** 
  - ✅ messages
  - ✅ messaging_postbacks  
  - ✅ feed
  - ✅ message_deliveries
  - ✅ message_reads

#### **C. Instagram Basic Display (Optional)**
- Vào **Add Product** → **Instagram Basic Display**
- Thêm **Valid OAuth Redirect URIs** giống Facebook Login

### **Bước 3: App Permissions**
Trong **App Review**, request permissions sau:
- `pages_manage_metadata` - Quản lý Page metadata
- `pages_read_engagement` - Đọc comments và engagement  
- `pages_manage_posts` - Quản lý posts
- `pages_messaging` - Gửi/nhận messages
- `instagram_basic` - Truy cập Instagram basic info
- `instagram_manage_messages` - Quản lý Instagram DM

### **Bước 4: Lấy App Credentials**
- **App ID:** Trong **Settings** → **Basic**
- **App Secret:** Trong **Settings** → **Basic** (click **Show**)

---

## ⚙️ **2. CẤU HÌNH SERVER**

### **Environment Variables (.env.local)**
```bash
# Facebook App Credentials
NEXT_PUBLIC_FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret

# Webhook Configuration  
FACEBOOK_WEBHOOK_VERIFY_TOKEN=your_random_verify_token

# App URL (for production)
NEXT_PUBLIC_PUBLIC_SITE_URL=https://your-domain.com

# Supabase (already configured)
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

### **Database Migration**
Chạy migration để tạo bảng Facebook integration:
```bash
npx supabase db push
```

Migration file: `supabase/migrations/20250127000000_create_facebook_integration_tables.sql`

---

## 🔄 **3. LUỒNG HOẠT ĐỘNG**

### **OAuth Flow**
```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant A as API
    participant FB as Facebook
    participant DB as Database

    U->>F: Click "Kết nối Facebook"
    F->>A: GET /api/facebook-integration/auth
    A->>FB: Redirect to OAuth URL
    FB->>U: Login & Grant Permissions
    FB->>A: Callback with code
    A->>FB: Exchange code for token
    A->>FB: Get user pages
    A->>F: Redirect with pages data
    F->>U: Show page selection
    U->>F: Select page
    F->>A: POST /api/facebook-integration/pages/select
    A->>FB: Get long-lived tokens
    A->>DB: Save account & tokens
    A->>FB: Subscribe webhooks
    A->>F: Return success
```

### **Auto Reply Flow**
```mermaid
sequenceDiagram
    participant FB as Facebook
    participant W as Webhook
    participant AI as ChatBot AI
    participant API as Facebook API

    FB->>W: Comment/Message webhook
    W->>AI: Process with chatbot
    AI->>W: Generate response
    W->>API: Reply via Facebook API
    API->>FB: Post comment/send message
```

---

## 🚀 **4. TÍNH NĂNG**

### **✅ Auto Reply Comments**
- Tự động trả lời comment Facebook posts
- Tự động trả lời comment Instagram posts
- AI analysis và smart responses
- Configurable prompts

### **✅ Auto Messages**
- Gửi tin nhắn riêng cho user comment
- Facebook Messenger integration
- Instagram Direct Messages
- Personalized AI responses

### **✅ Token Management**
- Auto refresh long-lived tokens
- Token expiry monitoring
- Graceful error handling
- Webhook health checks

### **✅ Analytics & Monitoring**
- Activity logs
- Response metrics
- Error tracking
- Performance monitoring

---

## 🔧 **5. API ROUTES**

### **Authentication**
- `GET /api/facebook-integration/auth` - Start OAuth flow
- `GET /api/facebook-integration/callback` - Handle OAuth callback

### **Page Management**
- `POST /api/facebook-integration/pages/select` - Select & connect page
- `GET /api/facebook-integration/pages` - Get connected pages
- `DELETE /api/facebook-integration/pages` - Disconnect page

### **Configuration**
- `GET /api/facebook-integration/config` - Get AI config
- `POST /api/facebook-integration/ai-config` - Save AI config
- `POST /api/facebook-integration/features` - Update feature settings

### **Webhooks**
- `POST /api/facebook-webhook` - Receive Facebook webhooks
- `GET /api/facebook-webhook` - Webhook verification

---

## 🔐 **6. BẢO MẬT**

### **Server-side Token Exchange**
- ✅ Client secret không expose ra frontend
- ✅ Token exchange thực hiện server-side
- ✅ Long-lived tokens stored securely
- ✅ Auto token refresh

### **Webhook Verification**
- ✅ Verify token validation
- ✅ Signature verification cho webhooks
- ✅ Rate limiting
- ✅ Error handling

### **Data Protection**
- ✅ Encrypted token storage
- ✅ User data access controls
- ✅ Audit logs
- ✅ GDPR compliance ready

---

## 🧪 **7. TESTING**

### **Development Mode**
1. Sử dụng Facebook Test App
2. Test với page do bạn quản lý
3. Webhook localhost với ngrok:
   ```bash
   ngrok http 3000
   # Update webhook URL: https://abc123.ngrok.io/api/facebook-webhook
   ```

### **Production Checklist**
- [ ] App reviewed và approved bởi Facebook
- [ ] Webhook URL sử dụng HTTPS
- [ ] Environment variables configured
- [ ] Database migration completed
- [ ] SSL certificate valid
- [ ] Domain verification

---

## 🆘 **8. TROUBLESHOOTING**

### **Lỗi "Error validating client secret"**
- ✅ **Nguyên nhân:** Client secret được gọi từ frontend
- ✅ **Giải pháp:** Sử dụng server-side OAuth flow
- ✅ **Code:** API routes `/api/facebook-integration/*`

### **Lỗi "Invalid OAuth redirect URI"**
- Kiểm tra URL trong Facebook App Settings
- Đảm bảo URL match chính xác
- Thêm both HTTP (dev) và HTTPS (prod)

### **Webhook không hoạt động**
- Verify token configuration
- Check webhook URL accessibility  
- Validate subscription fields
- Check server logs

### **Token expired**
- Page tokens không expire (permanent)
- User tokens expire sau 60 ngày
- Implement auto refresh logic
- Monitor token health

---

## 📞 **9. HỖ TRỢ**

### **Facebook Developer Resources**
- [Facebook for Developers](https://developers.facebook.com/)
- [Graph API Explorer](https://developers.facebook.com/tools/explorer/)
- [Webhook Debugger](https://developers.facebook.com/tools/webhooks/)

### **Testing Tools**
- [Graph API Explorer](https://developers.facebook.com/tools/explorer/) - Test API calls
- [Access Token Debugger](https://developers.facebook.com/tools/debug/accesstoken/) - Validate tokens
- [Sharing Debugger](https://developers.facebook.com/tools/debug/) - Test URLs

---

## ✅ **SETUP HOÀN TẤT**

Sau khi hoàn thành setup:
1. ✅ Facebook App configured with products
2. ✅ Environment variables set
3. ✅ Database migration run
4. ✅ OAuth flow working
5. ✅ Webhooks receiving data
6. ✅ Auto reply functioning
7. ✅ Monitoring & logs active

**🎉 Facebook Integration sẵn sàng để khách hàng sử dụng!** 