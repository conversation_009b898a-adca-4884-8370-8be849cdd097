import { NextResponse } from 'next/server';

/**
 * API route để thêm FAQs vào Weaviate
 * @param {Request} request - <PERSON><PERSON><PERSON> cầu HTTP
 * @returns {Promise<NextResponse>} - <PERSON><PERSON><PERSON> hồi HTTP
 */
export async function POST(request) {
  try {
    const { faqs } = await request.json();

    // Kiểm tra dữ liệu đầu vào
    if (!faqs || !Array.isArray(faqs) || faqs.length === 0) {
      return NextResponse.json({ message: 'No FAQs to add' }, { status: 200 });
    }

    // Validate FAQs structure
    const invalidFaqs = faqs.filter(faq => !faq.topic || !faq.content || !faq.bot_id || !faq.tenant_id);
    if (invalidFaqs.length > 0) {
      return NextResponse.json(
        { error: 'Invalid FAQ structure. Each FAQ must have topic, content, bot_id, and tenant_id' },
        { status: 400 }
      );
    }

    // Lấy URL Backend từ biến môi trường
    const BACKEND_API_URL = process.env.BACKEND_API_URL || 'http://localhost:3000';

    // Gọi API Weaviate Backend
    const response = await fetch(`${BACKEND_API_URL}/api/weaviate/faqs`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ faqs }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      return NextResponse.json(
        { error: errorData.message || 'Failed to add FAQs to Weaviate' },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in FAQs API route:', error);
    return NextResponse.json({ error: error.message || 'Internal server error' }, { status: 500 });
  }
}

/**
 * API route để lấy FAQs từ Weaviate
 * @param {Request} request - Yêu cầu HTTP
 * @returns {Promise<NextResponse>} - Phản hồi HTTP
 */
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const botId = searchParams.get('bot_id');
    const tenantId = searchParams.get('tenant_id');

    if (!botId || !tenantId) {
      return NextResponse.json(
        { error: 'bot_id and tenant_id are required' },
        { status: 400 }
      );
    }

    // Lấy URL Backend từ biến môi trường
    const BACKEND_API_URL = process.env.BACKEND_API_URL || 'http://localhost:3000';

    // Gọi API Weaviate Backend
    const response = await fetch(
      `${BACKEND_API_URL}/api/weaviate/faqs?bot_id=${botId}&tenant_id=${tenantId}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      return NextResponse.json(
        { error: errorData.message || 'Failed to get FAQs from Weaviate' },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in FAQs GET API route:', error);
    return NextResponse.json({ error: error.message || 'Internal server error' }, { status: 500 });
  }
}

/**
 * API route để xóa FAQs từ Weaviate
 * @param {Request} request - Yêu cầu HTTP
 * @returns {Promise<NextResponse>} - Phản hồi HTTP
 */
export async function DELETE(request) {
  try {
    const { faq_ids, bot_id, tenant_id } = await request.json();

    // Kiểm tra dữ liệu đầu vào
    if (!faq_ids || !Array.isArray(faq_ids) || faq_ids.length === 0) {
      return NextResponse.json({ message: 'No FAQ IDs to delete' }, { status: 200 });
    }

    if (!bot_id || !tenant_id) {
      return NextResponse.json(
        { error: 'bot_id and tenant_id are required' },
        { status: 400 }
      );
    }

    // Lấy URL Backend từ biến môi trường
    const BACKEND_API_URL = process.env.BACKEND_API_URL || 'http://localhost:3000';

    // Gọi API Weaviate Backend
    const response = await fetch(`${BACKEND_API_URL}/api/weaviate/faqs`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ faq_ids, bot_id, tenant_id }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      return NextResponse.json(
        { error: errorData.message || 'Failed to delete FAQs from Weaviate' },
        { status: response.status }  
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in FAQs DELETE API route:', error);
    return NextResponse.json({ error: error.message || 'Internal server error' }, { status: 500 });
  }
} 