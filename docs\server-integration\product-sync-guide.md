# Hướng dẫn Upload Hình Ảnh Sản Phẩm - Server Integration

## Tổng quan

Hệ thống sử dụng Supabase Storage với multi-tenant isolation. Server cần tuân thủ cấu trúc đường dẫn và authentication để upload hình ảnh sản phẩm.

## Cấu trúc Storage

### Thông tin cơ bản
- **Supabase URL**: `https://vhduizefoibsipsiraqf.supabase.co`
- **Bucket**: `public`
- **Đường dẫn**: `products/{tenant_id_short}/{filename}`
- **tenant_id_short**: 8 ký tự đầu của tenant_id (để tối ưu performance)

### Ví dụ
```
Tenant ID (full): 4ebbbb49-1234-5678-9abc-123456789def
Tenant ID (short): 4ebbbb49
File path: products/4ebbbb49/img_1748665144151_9ckxz2.webp
Public URL: https://vhduizefoibsipsiraqf.supabase.co/storage/v1/object/public/products/products/4ebbbb49/img_1748665144151_9ckxz2.webp
```

## Authentication & Setup

### 1. Supabase Client Setup
```javascript
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  'https://vhduizefoibsipsiraqf.supabase.co',
  'YOUR_SERVICE_ROLE_KEY_HERE' // Sử dụng Service Role Key (không phải anon key)
);
```

### 2. Service Role Key
- **Quan trọng**: Phải sử dụng **Service Role Key** để có quyền upload
- **Không sử dụng**: Anon key (sẽ bị lỗi 403)
- **Bảo mật**: Chỉ sử dụng Service Role Key trên server-side

## Upload Hình Ảnh

### Function chính để upload
```javascript
async function uploadProductImage(tenantId, imageBuffer, originalFilename) {
  try {
    // 1. Tạo short tenant ID (8 ký tự đầu)
    const shortTenantId = tenantId.substring(0, 8);

    // 2. Tạo unique filename
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 8);
    const fileExtension = originalFilename.split('.').pop().toLowerCase();
    const uniqueFilename = `img_${timestamp}_${randomString}.${fileExtension}`;

    // 3. Tạo file path theo format: products/{short_tenant_id}/{filename}
    const filePath = `products/${shortTenantId}/${uniqueFilename}`;

    // 4. Upload lên Supabase Storage
    const { data, error } = await supabase.storage
      .from('public')
      .upload(filePath, imageBuffer, {
        cacheControl: '3600',
        upsert: true,
        contentType: `image/${fileExtension}`
      });

    if (error) {
      throw new Error(`Upload failed: ${error.message}`);
    }

    // 5. Lấy public URL
    const { data: urlData } = supabase.storage
      .from('public')
      .getPublicUrl(filePath);

    return {
      success: true,
      publicUrl: urlData.publicUrl,
      filePath: filePath,
      filename: uniqueFilename
    };

  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}
```

### Download hình ảnh từ URL external
```javascript
async function downloadImageFromUrl(imageUrl) {
  try {
    const response = await fetch(imageUrl, {
      timeout: 30000, // 30 seconds timeout
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; ProductSync/1.0)'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.startsWith('image/')) {
      throw new Error('URL không phải là hình ảnh hợp lệ');
    }

    return await response.arrayBuffer();
  } catch (error) {
    throw new Error(`Không thể download hình ảnh: ${error.message}`);
  }
}
```

### Đồng bộ sản phẩm với hình ảnh
```javascript
async function syncProductWithImages(tenantId, productData) {
  try {
    const uploadedImages = [];

    // Upload từng hình ảnh
    for (const imageUrl of productData.images || []) {
      try {
        // Download hình ảnh
        const imageBuffer = await downloadImageFromUrl(imageUrl);

        // Tạo filename từ URL
        const urlParts = imageUrl.split('/');
        const originalName = urlParts[urlParts.length - 1] || 'image.jpg';

        // Upload lên Supabase
        const uploadResult = await uploadProductImage(tenantId, imageBuffer, originalName);

        if (uploadResult.success) {
          uploadedImages.push(uploadResult.publicUrl);
        }
      } catch (imageError) {
        console.error(`Lỗi upload hình ảnh ${imageUrl}:`, imageError.message);
        // Tiếp tục với hình ảnh khác
      }
    }

    // Tạo sản phẩm với hình ảnh đã upload
    const finalProductData = {
      ...productData,
      tenant_id: tenantId,
      images: uploadedImages,
      avatar: uploadedImages[0] || null
    };

    // Insert vào database
    const { data: product, error } = await supabase
      .from('products')
      .insert(finalProductData)
      .select()
      .single();

    if (error) {
      throw error;
    }

    return {
      success: true,
      product: product,
      images_uploaded: uploadedImages.length
    };

  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}
```

## Error Handling

### Các lỗi thường gặp

1. **403 Unauthorized**
   ```
   Nguyên nhân: Sử dụng sai key hoặc không có quyền
   Giải pháp: Đảm bảo sử dụng Service Role Key
   ```

2. **Invalid file path**
   ```
   Nguyên nhân: Path không đúng format
   Giải pháp: Đảm bảo format: products/{short_tenant_id}/{filename}
   ```

3. **File too large**
   ```
   Nguyên nhân: File > 5MB
   Giải pháp: Compress hình ảnh trước khi upload
   ```

4. **Network timeout**
   ```
   Nguyên nhân: Kết nối chậm
   Giải pháp: Tăng timeout và implement retry
   ```

## Ví dụ sử dụng hoàn chỉnh

```javascript
// Ví dụ đồng bộ sản phẩm từ Shopee
async function syncShopeeProduct(tenantId, shopeeProductData) {
  const productData = {
    name: shopeeProductData.name,
    description: shopeeProductData.description,
    price: shopeeProductData.price,
    sku: `SHOPEE-${shopeeProductData.item_id}`,
    stock_quantity: shopeeProductData.stock,
    images: shopeeProductData.images, // Array of image URLs
    type: 'simple',
    is_active: true
  };

  const result = await syncProductWithImages(tenantId, productData);

  if (result.success) {
    console.log(`✅ Đồng bộ thành công: ${result.product.name}`);
    console.log(`📸 Upload ${result.images_uploaded} hình ảnh`);
  } else {
    console.error(`❌ Lỗi đồng bộ: ${result.error}`);
  }

  return result;
}
```

## Lưu ý quan trọng

1. **Tenant ID**: Luôn sử dụng full tenant_id khi gọi API, hệ thống sẽ tự động tạo short_id
2. **Service Role Key**: Bắt buộc phải có để upload được
3. **File format**: Hỗ trợ jpg, jpeg, png, webp
4. **File size**: Tối đa 5MB mỗi file
5. **Path format**: Luôn theo format `products/{short_tenant_id}/{filename}`
