-- =====================================================
-- MESSAGE FORMATTING UPDATE MIGRATION
-- Cập nhật cấu trúc JSON cho message formatting
-- Không thay đổi schema database, chỉ cập nhật dữ liệu
-- =====================================================

-- Function để migrate legacy followup rules sang format mới
CREATE OR REPLACE FUNCTION migrate_followup_rules_to_new_format()
RETURNS void AS $$
DECLARE
    rec RECORD;
    updated_rules JSONB;
    rule JSONB;
BEGIN
    -- Lặp qua tất cả records trong channel_automation_followup
    FOR rec IN 
        SELECT id, followup_rules 
        FROM channel_automation_followup 
        WHERE followup_rules IS NOT NULL 
        AND jsonb_array_length(followup_rules) > 0
    LOOP
        updated_rules := '[]'::jsonb;
        
        -- Lặp qua từng rule trong followup_rules
        FOR rule IN SELECT * FROM jsonb_array_elements(rec.followup_rules)
        LOOP
            -- Kiểm tra nếu rule chưa có messageType (legacy rule)
            IF NOT (rule ? 'messageType') THEN
                -- Thêm messageType và messageFormatting cho legacy rule
                rule := rule || jsonb_build_object(
                    'messageType', 'text',
                    'messageFormatting', null
                );
            END IF;
            
            -- Thêm rule đã update vào mảng mới
            updated_rules := updated_rules || jsonb_build_array(rule);
        END LOOP;
        
        -- Cập nhật record với rules đã migrate
        UPDATE channel_automation_followup 
        SET followup_rules = updated_rules,
            updated_at = NOW()
        WHERE id = rec.id;
        
        RAISE NOTICE 'Migrated rules for record ID: %', rec.id;
    END LOOP;
    
    RAISE NOTICE 'Migration completed successfully';
END;
$$ LANGUAGE plpgsql;

-- Chạy migration function
SELECT migrate_followup_rules_to_new_format();

-- Xóa function sau khi sử dụng
DROP FUNCTION migrate_followup_rules_to_new_format();

-- =====================================================
-- VALIDATION FUNCTIONS
-- =====================================================

-- Function để validate message formatting structure
CREATE OR REPLACE FUNCTION validate_message_formatting(formatting_data JSONB)
RETURNS boolean AS $$
BEGIN
    -- Kiểm tra structure cơ bản
    IF formatting_data IS NULL THEN
        RETURN true; -- NULL is valid (text message)
    END IF;
    
    -- Kiểm tra có images và buttons array
    IF NOT (formatting_data ? 'images' AND formatting_data ? 'buttons') THEN
        RETURN false;
    END IF;
    
    -- Kiểm tra images là array
    IF NOT jsonb_typeof(formatting_data->'images') = 'array' THEN
        RETURN false;
    END IF;
    
    -- Kiểm tra buttons là array
    IF NOT jsonb_typeof(formatting_data->'buttons') = 'array' THEN
        RETURN false;
    END IF;
    
    -- Kiểm tra giới hạn số lượng
    IF jsonb_array_length(formatting_data->'images') > 3 THEN
        RETURN false;
    END IF;
    
    IF jsonb_array_length(formatting_data->'buttons') > 3 THEN
        RETURN false;
    END IF;
    
    RETURN true;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Index cho message type trong followup_rules
CREATE INDEX IF NOT EXISTS idx_followup_rules_message_type 
ON channel_automation_followup 
USING GIN ((followup_rules));

-- =====================================================
-- COMMENTS
-- =====================================================

COMMENT ON FUNCTION validate_message_formatting(JSONB) IS 'Validates message formatting JSON structure';

-- =====================================================
-- EXAMPLE USAGE QUERIES
-- =====================================================

-- Query để kiểm tra rules đã được migrate
/*
SELECT 
    id,
    channel_id,
    jsonb_pretty(followup_rules) as formatted_rules
FROM channel_automation_followup 
WHERE followup_rules IS NOT NULL
LIMIT 5;
*/

-- Query để đếm số rules theo message type
/*
SELECT 
    rule->>'messageType' as message_type,
    COUNT(*) as count
FROM channel_automation_followup,
     jsonb_array_elements(followup_rules) as rule
WHERE followup_rules IS NOT NULL
GROUP BY rule->>'messageType';
*/

-- Query để tìm rules có message formatting
/*
SELECT 
    id,
    channel_id,
    rule->>'message' as message_content,
    rule->>'messageType' as message_type,
    rule->'messageFormatting' as formatting
FROM channel_automation_followup,
     jsonb_array_elements(followup_rules) as rule
WHERE followup_rules IS NOT NULL
  AND rule->>'messageType' = 'formatted'
  AND rule->'messageFormatting' IS NOT NULL;
*/
