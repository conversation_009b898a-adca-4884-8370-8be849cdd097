'use client';

import PropTypes from 'prop-types';
import { useState, useCallback } from 'react';

import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import Divider from '@mui/material/Divider';
import Typography from '@mui/material/Typography';
import DialogTitle from '@mui/material/DialogTitle';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';

import { AddressForm } from './address-form';

export function AddressList({ 
  addresses = [], 
  customerId, 
  onCreateAddress, 
  onUpdateAddress, 
  onDeleteAddress,
  isSubmitting 
}) {
  const [addressForm, setAddressForm] = useState({ open: false, address: null });
  const [deleteConfirm, setDeleteConfirm] = useState({ open: false, address: null });

  // Mở form thêm địa chỉ mới
  const handleOpenAddForm = useCallback(() => {
    setAddressForm({ open: true, address: null });
  }, []);

  // Mở form chỉnh sửa địa chỉ
  const handleOpenEditForm = useCallback((address) => {
    setAddressForm({ open: true, address });
  }, []);

  // Đóng form địa chỉ
  const handleCloseAddressForm = useCallback(() => {
    setAddressForm({ open: false, address: null });
  }, []);

  // Mở confirm dialog xóa địa chỉ
  const handleOpenDeleteConfirm = useCallback((address) => {
    setDeleteConfirm({ open: true, address });
  }, []);

  // Đóng confirm dialog
  const handleCloseDeleteConfirm = useCallback(() => {
    setDeleteConfirm({ open: false, address: null });
  }, []);

  // Xử lý submit form địa chỉ
  const handleSubmitAddress = useCallback(async (data) => {
    try {
      let result;
      
      if (addressForm.address) {
        // Cập nhật địa chỉ
        result = await onUpdateAddress(addressForm.address.id, data);
      } else {
        // Tạo địa chỉ mới
        result = await onCreateAddress(data);
      }

      return result;
    } catch (error) {
      console.error('Error submitting address:', error);
      throw error;
    }
  }, [addressForm.address, onCreateAddress, onUpdateAddress]);

  // Xử lý xóa địa chỉ
  const handleDeleteAddress = useCallback(async () => {
    try {
      if (!deleteConfirm.address) return;

      const result = await onDeleteAddress(deleteConfirm.address.id);
      
      if (result?.success) {
        setDeleteConfirm({ open: false, address: null });
        toast.success('✅ Đã xóa địa chỉ thành công');
      } else {
        const errorMessage = result?.error?.message || 'Có lỗi xảy ra khi xóa địa chỉ';
        toast.error(`❌ ${errorMessage}`);
      }
    } catch (error) {
      console.error('Error deleting address:', error);
      toast.error(`❌ ${error.message || 'Có lỗi xảy ra khi xóa địa chỉ'}`);
    }
  }, [deleteConfirm.address, onDeleteAddress]);

  const renderAddressCard = (address, index) => (
    <Card key={address.id || index} sx={{ p: 2, mb: 2 }}>
      <Stack direction="row" justifyContent="space-between" alignItems="flex-start">
        <Stack spacing={1} sx={{ flex: 1 }}>
          <Stack direction="row" alignItems="center" spacing={1}>
            <Typography variant="subtitle2">
              {address.fullName || 'Không có tên'}
            </Typography>
            {address.isDefault && (
              <Typography 
                variant="caption" 
                sx={{ 
                  px: 1, 
                  py: 0.5, 
                  bgcolor: 'primary.main', 
                  color: 'white', 
                  borderRadius: 1 
                }}
              >
                Mặc định
              </Typography>
            )}
          </Stack>
          
          <Typography variant="body2" color="text.secondary">
            📞 {address.phone || 'Không có số điện thoại'}
          </Typography>
          
          <Typography variant="body2">
            📍 {address.address}
          </Typography>
          
          {(address.ward || address.district || address.province) && (
            <Typography variant="body2" color="text.secondary">
              {[address.ward, address.district, address.province]
                .filter(Boolean)
                .join(', ')}
            </Typography>
          )}
          
          {address.notes && (
            <Typography variant="body2" color="text.secondary">
              💬 {address.notes}
            </Typography>
          )}
        </Stack>

        <Stack direction="row" spacing={1}>
          <Button
            size="small"
            startIcon={<Iconify icon="solar:pen-bold" />}
            onClick={() => handleOpenEditForm(address)}
          >
            Sửa
          </Button>
          <Button
            size="small"
            color="error"
            startIcon={<Iconify icon="solar:trash-bin-trash-bold" />}
            onClick={() => handleOpenDeleteConfirm(address)}
          >
            Xóa
          </Button>
        </Stack>
      </Stack>
    </Card>
  );

  return (
    <>
      <Card sx={{ p: 3 }}>
        <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 3 }}>
          <Typography variant="h6">
            Địa chỉ giao hàng ({addresses.length})
          </Typography>

          <Button
            variant="contained"
            startIcon={<Iconify icon="mingcute:add-line" />}
            onClick={handleOpenAddForm}
            disabled={!customerId}
          >
            Thêm địa chỉ
          </Button>
        </Stack>

        {addresses.length === 0 ? (
          <Stack alignItems="center" spacing={2} sx={{ py: 4 }}>
            <Iconify icon="solar:map-point-outline" sx={{ fontSize: 64, color: 'text.disabled' }} />
            <Typography variant="body2" color="text.secondary">
              Chưa có địa chỉ giao hàng nào
            </Typography>
            {customerId && (
              <Button
                variant="outlined"
                startIcon={<Iconify icon="mingcute:add-line" />}
                onClick={handleOpenAddForm}
              >
                Thêm địa chỉ đầu tiên
              </Button>
            )}
          </Stack>
        ) : (
          <Stack spacing={2}>
            {addresses.map(renderAddressCard)}
          </Stack>
        )}
      </Card>

      {/* Address Form Dialog */}
      <AddressForm
        open={addressForm.open}
        onClose={handleCloseAddressForm}
        address={addressForm.address}
        customerId={customerId}
        onSubmit={handleSubmitAddress}
        isSubmitting={isSubmitting}
      />

      {/* Delete Confirm Dialog */}
      <Dialog
        open={deleteConfirm.open}
        onClose={handleCloseDeleteConfirm}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          <Stack direction="row" alignItems="center" spacing={1}>
            <Iconify icon="solar:trash-bin-trash-bold" sx={{ color: 'error.main' }} />
            <Typography variant="h6">Xác nhận xóa địa chỉ</Typography>
          </Stack>
        </DialogTitle>

        <DialogContent>
          <Typography>
            Bạn có chắc chắn muốn xóa địa chỉ này không?
          </Typography>
          {deleteConfirm.address && (
            <Card sx={{ mt: 2, p: 2, bgcolor: 'grey.50' }}>
              <Typography variant="body2">
                📍 {deleteConfirm.address.address}
              </Typography>
              {deleteConfirm.address.isDefault && (
                <Typography variant="body2" sx={{ color: 'warning.main', fontWeight: 'bold' }}>
                  ⚠️ Đây là địa chỉ mặc định
                </Typography>
              )}
            </Card>
          )}
        </DialogContent>

        <DialogActions>
          <Button onClick={handleCloseDeleteConfirm} color="inherit">
            Hủy
          </Button>
          <Button onClick={handleDeleteAddress} color="error" variant="contained">
            Xóa
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}

AddressList.propTypes = {
  addresses: PropTypes.array,
  customerId: PropTypes.string,
  isSubmitting: PropTypes.bool,
  onCreateAddress: PropTypes.func,
  onDeleteAddress: PropTypes.func,
  onUpdateAddress: PropTypes.func,
};
