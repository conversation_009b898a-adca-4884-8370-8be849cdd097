'use client';

import { toast } from 'src/components/snackbar';

/**
 * Utility functions cho các thao tác bulk operations với sản phẩm
 */

/**
 * Xử lý bulk delete với progress tracking và error handling
 * @param {Array} productIds - <PERSON><PERSON> sách ID sản phẩm cần xóa
 * @param {Function} bulkDeleteFn - Function để thực hiện bulk delete
 * @param {Object} options - <PERSON><PERSON><PERSON> tùy chọn
 * @returns {Promise<Object>} - Kết quả thao tác
 */
export async function handleBulkDelete(productIds, bulkDeleteFn, options = {}) {
  const {
    showProgress = true,
    showSuccess = true,
    showError = true,
    onProgress = null,
    onSuccess = null,
    onError = null,
  } = options;

  if (!productIds || !Array.isArray(productIds) || productIds.length === 0) {
    const error = new Error('<PERSON>h sách sản phẩm không hợp lệ');
    if (showError) {
      toast.error(error.message);
    }
    if (onError) onError(error);
    return { success: false, error };
  }

  let loadingToast = null;
  
  try {
    // Hiển thị progress nếu được yêu cầu
    if (showProgress) {
      loadingToast = toast.loading(`Đang xóa ${productIds.length} sản phẩm...`);
    }
    
    if (onProgress) {
      onProgress({ status: 'starting', total: productIds.length });
    }

    // Thực hiện bulk delete
    const result = await bulkDeleteFn(productIds);

    // Đóng loading toast
    if (loadingToast) {
      toast.dismiss(loadingToast);
    }

    if (result.success) {
      // Hiển thị thông báo thành công
      if (showSuccess) {
        const successCount = result.data?.success_count || productIds.length;
        const failedCount = result.data?.failed_count || 0;
        
        if (failedCount > 0) {
          toast.warning(
            `Đã xóa ${successCount} sản phẩm thành công. ${failedCount} sản phẩm không thể xóa.`
          );
        } else {
          toast.success(`Đã xóa ${successCount} sản phẩm thành công!`);
        }
      }

      if (onSuccess) {
        onSuccess(result);
      }

      if (onProgress) {
        onProgress({ 
          status: 'completed', 
          total: productIds.length,
          success: result.data?.success_count || productIds.length,
          failed: result.data?.failed_count || 0
        });
      }

      return result;
    } else {
      throw new Error(result.error?.message || 'Có lỗi xảy ra khi xóa sản phẩm');
    }
  } catch (error) {
    // Đóng loading toast nếu có lỗi
    if (loadingToast) {
      toast.dismiss(loadingToast);
    }

    // Hiển thị thông báo lỗi
    if (showError) {
      toast.error(`Lỗi khi xóa sản phẩm: ${error.message}`);
    }

    if (onError) {
      onError(error);
    }

    if (onProgress) {
      onProgress({ status: 'error', error: error.message });
    }

    return { success: false, error };
  }
}

/**
 * Xử lý bulk update với progress tracking
 * @param {Array} updates - Danh sách các update operations
 * @param {Function} updateFn - Function để thực hiện update
 * @param {Object} options - Các tùy chọn
 * @returns {Promise<Object>} - Kết quả thao tác
 */
export async function handleBulkUpdate(updates, updateFn, options = {}) {
  const {
    showProgress = true,
    showSuccess = true,
    showError = true,
    batchSize = 10,
    onProgress = null,
    onSuccess = null,
    onError = null,
  } = options;

  if (!updates || !Array.isArray(updates) || updates.length === 0) {
    const error = new Error('Danh sách cập nhật không hợp lệ');
    if (showError) {
      toast.error(error.message);
    }
    if (onError) onError(error);
    return { success: false, error };
  }

  let loadingToast = null;
  const results = {
    success: [],
    failed: [],
    total: updates.length
  };

  try {
    if (showProgress) {
      loadingToast = toast.loading(`Đang cập nhật ${updates.length} sản phẩm...`);
    }

    if (onProgress) {
      onProgress({ status: 'starting', total: updates.length, completed: 0 });
    }

    // Xử lý theo batch để tránh quá tải
    for (let i = 0; i < updates.length; i += batchSize) {
      const batch = updates.slice(i, i + batchSize);
      
      const batchPromises = batch.map(async (update) => {
        try {
          const result = await updateFn(update);
          if (result.success) {
            results.success.push({ ...update, result });
          } else {
            results.failed.push({ ...update, error: result.error });
          }
          return result;
        } catch (error) {
          results.failed.push({ ...update, error });
          return { success: false, error };
        }
      });

      await Promise.all(batchPromises);

      // Cập nhật progress
      if (onProgress) {
        onProgress({
          status: 'processing',
          total: updates.length,
          completed: Math.min(i + batchSize, updates.length),
          success: results.success.length,
          failed: results.failed.length
        });
      }
    }

    // Đóng loading toast
    if (loadingToast) {
      toast.dismiss(loadingToast);
    }

    // Hiển thị kết quả
    if (showSuccess) {
      if (results.failed.length > 0) {
        toast.warning(
          `Đã cập nhật ${results.success.length} sản phẩm thành công. ${results.failed.length} sản phẩm không thể cập nhật.`
        );
      } else {
        toast.success(`Đã cập nhật ${results.success.length} sản phẩm thành công!`);
      }
    }

    if (onSuccess) {
      onSuccess(results);
    }

    if (onProgress) {
      onProgress({ 
        status: 'completed', 
        total: updates.length,
        completed: updates.length,
        success: results.success.length,
        failed: results.failed.length
      });
    }

    return {
      success: true,
      data: results,
      error: null
    };

  } catch (error) {
    if (loadingToast) {
      toast.dismiss(loadingToast);
    }

    if (showError) {
      toast.error(`Lỗi khi cập nhật sản phẩm: ${error.message}`);
    }

    if (onError) {
      onError(error);
    }

    if (onProgress) {
      onProgress({ status: 'error', error: error.message });
    }

    return { success: false, error };
  }
}

/**
 * Validate danh sách sản phẩm trước khi thực hiện bulk operations
 * @param {Array} productIds - Danh sách ID sản phẩm
 * @param {Object} options - Các tùy chọn validation
 * @returns {Object} - Kết quả validation
 */
export function validateProductIds(productIds, options = {}) {
  const { maxItems = 100, minItems = 1 } = options;

  if (!productIds || !Array.isArray(productIds)) {
    return {
      valid: false,
      error: 'Danh sách sản phẩm phải là một mảng'
    };
  }

  if (productIds.length < minItems) {
    return {
      valid: false,
      error: `Cần ít nhất ${minItems} sản phẩm`
    };
  }

  if (productIds.length > maxItems) {
    return {
      valid: false,
      error: `Không thể xử lý quá ${maxItems} sản phẩm cùng lúc`
    };
  }

  // Kiểm tra duplicate IDs
  const uniqueIds = [...new Set(productIds)];
  if (uniqueIds.length !== productIds.length) {
    return {
      valid: false,
      error: 'Danh sách chứa ID trùng lặp'
    };
  }

  // Kiểm tra format UUID
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  const invalidIds = productIds.filter(id => !uuidRegex.test(id));
  
  if (invalidIds.length > 0) {
    return {
      valid: false,
      error: `ID không hợp lệ: ${invalidIds.slice(0, 3).join(', ')}${invalidIds.length > 3 ? '...' : ''}`
    };
  }

  return {
    valid: true,
    productIds: uniqueIds
  };
}
