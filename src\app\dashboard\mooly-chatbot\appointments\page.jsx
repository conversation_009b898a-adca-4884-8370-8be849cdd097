'use client';

import { <PERSON>, <PERSON>, <PERSON>rid, <PERSON>ack, But<PERSON>, Container, Typography, IconButton } from '@mui/material';

import { DashboardContent } from 'src/layouts/dashboard';

import { Iconify } from 'src/components/iconify';
import { CustomBreadcrumbs } from 'src/components/custom-breadcrumbs';

// ----------------------------------------------------------------------

export default function AppointmentsPage() {
  const statsCards = [
    {
      title: 'Hôm nay',
      value: '0',
      subtitle: 'Cuộc hẹn',
      icon: 'eva:calendar-fill',
      color: 'primary',
    },
    {
      title: 'Tuần này',
      value: '0',
      subtitle: 'Cuộc hẹn',
      icon: 'eva:bar-chart-fill',
      color: 'success',
    },
    {
      title: 'Chờ xác nhận',
      value: '0',
      subtitle: 'Cuộc hẹn',
      icon: 'eva:clock-fill',
      color: 'warning',
    },
    {
      title: '<PERSON><PERSON><PERSON> thành',
      value: '0',
      subtitle: 'Cuộc hẹn',
      icon: 'eva:checkmark-circle-fill',
      color: 'info',
    },
  ];

  const currentDate = new Date();
  const currentMonth = currentDate.toLocaleDateString('vi-VN', { month: 'long', year: 'numeric' });

  return (
    <DashboardContent>
      <Container maxWidth="xl">
        <CustomBreadcrumbs
          heading="Quản lý lịch hẹn"
          subheading="Xem và quản lý tất cả các cuộc hẹn với khách hàng"
          links={[
            { name: 'Dashboard', href: '/dashboard' },
            { name: 'Lịch hẹn' },
          ]}
          sx={{ mb: 3 }}
        />

        <Grid container spacing={3} sx={{ mb: 3 }}>
          {statsCards.map((card, index) => (
            <Grid item size={{xs: 12, sm: 6, md: 3}} key={index}>
              <Card sx={{ p: 3 }}>
                <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" sx={{ color: 'text.secondary' }}>
                    {card.title}
                  </Typography>
                  <Box
                    sx={{
                      width: 32,
                      height: 32,
                      borderRadius: 1.5,
                      bgcolor: `${card.color}.lighter`,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <Iconify icon={card.icon} width={16} sx={{ color: `${card.color}.main` }} />
                  </Box>
                </Stack>
                <Typography variant="h3" sx={{ mb: 0.5 }}>
                  {card.value}
                </Typography>
                <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                  {card.subtitle}
                </Typography>
              </Card>
            </Grid>
          ))}
        </Grid>

        <Grid container spacing={3}>
          <Grid item size={{xs: 12, lg: 9}}>
            <Card>
              <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ p: 3, borderBottom: 1, borderColor: 'divider' }}>
                <Typography variant="h6">Lịch hẹn</Typography>
                <Stack direction="row" spacing={1}>
                  <Button variant="outlined" size="small">Ngày</Button>
                  <Button variant="outlined" size="small">Tuần</Button>
                  <Button variant="contained" size="small">Tháng</Button>
                  <Button variant="contained" startIcon={<Iconify icon="eva:plus-fill" />} size="small">
                    Tạo lịch hẹn
                  </Button>
                </Stack>
              </Stack>

              <Box sx={{ p: 3 }}>
                {/* Calendar Header */}
                <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 3 }}>
                  <Typography variant="h6">{currentMonth}</Typography>
                  <Stack direction="row" spacing={1}>
                    <IconButton size="small">
                      <Iconify icon="eva:arrow-left-fill" />
                    </IconButton>
                    <IconButton size="small">
                      <Iconify icon="eva:arrow-right-fill" />
                    </IconButton>
                  </Stack>
                </Stack>

                {/* Calendar Days Header */}
                <Grid container sx={{ mb: 1 }}>
                  {['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'].map((day) => (
                    <Grid item xs key={day}>
                      <Typography variant="subtitle2" sx={{ textAlign: 'center', color: 'text.secondary', p: 1 }}>
                        {day}
                      </Typography>
                    </Grid>
                  ))}
                </Grid>

                {/* Calendar Body */}
                <Grid container>
                  {Array.from({ length: 35 }, (_, i) => {
                    const day = i - 6 + 1; // Adjust for month start
                    const isCurrentMonth = day > 0 && day <= 31;
                    const isToday = day === currentDate.getDate() && isCurrentMonth;

                    return (
                      <Grid item xs key={i}>
                        <Box
                          sx={{
                            height: 80,
                            border: 1,
                            borderColor: 'divider',
                            p: 1,
                            cursor: 'pointer',
                            bgcolor: isCurrentMonth ? 'background.paper' : 'grey.50',
                            '&:hover': { bgcolor: 'grey.100' },
                            ...(isToday && {
                              bgcolor: 'primary.lighter',
                              borderColor: 'primary.main',
                            }),
                          }}
                        >
                          {isCurrentMonth && (
                            <Typography
                              variant="body2"
                              sx={{
                                fontWeight: isToday ? 'bold' : 'normal',
                                color: isToday ? 'primary.main' : 'text.primary',
                              }}
                            >
                              {day}
                            </Typography>
                          )}
                        </Box>
                      </Grid>
                    );
                  })}
                </Grid>
              </Box>
            </Card>
          </Grid>

          <Grid item size={{xs: 12, lg: 3}}>
            <Card>
              <Stack sx={{ p: 3, borderBottom: 1, borderColor: 'divider' }}>
                <Typography variant="h6">Hôm nay</Typography>
                <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                  {currentDate.toLocaleDateString('vi-VN', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </Typography>
              </Stack>
              <Box sx={{ p: 6 }}>
                <Stack alignItems="center" spacing={2}>
                  <Box
                    sx={{
                      width: 64,
                      height: 64,
                      borderRadius: '50%',
                      bgcolor: 'grey.100',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <Iconify icon="eva:calendar-fill" width={32} sx={{ color: 'text.disabled' }} />
                  </Box>
                  <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                    Không có lịch hẹn nào hôm nay
                  </Typography>
                </Stack>
              </Box>
            </Card>
          </Grid>
        </Grid>
      </Container>
    </DashboardContent>
  );
}
