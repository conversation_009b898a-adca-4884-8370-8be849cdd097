import { NextResponse } from 'next/server';

import { withTenantAuth, validateTenantId } from 'src/utils/server-auth';

/**
 * API route để xóa sản phẩm khỏi Weaviate dựa trên URL hình ảnh
 * @param {Request} request - <PERSON><PERSON><PERSON> cầu HTTP
 * @returns {Promise<NextResponse>} - Phản hồi HTTP
 */
export const DELETE = withTenantAuth(async (request, { tenantId, userId }) => {
  try {
    // Validate request body và kiểm tra tenant_id
    // const validation = await validateTenantId(request, tenantId);
    // if (!validation.valid) {
    //   return NextResponse.json({ error: validation.error }, { status: validation.status });
    // }

    const { image_url } = await request.json();

    // Kiểm tra dữ liệu đầu vào
    if (!image_url) {
      return NextResponse.json(
        { error: 'Missing required field: image_url' },
        { status: 400 }
      );
    }

    // Lấy URL Weaviate từ biến môi trường
    const BACKEND_API_URL = process.env.BACKEND_API_URL || 'http://localhost:3000';

    // Gọi API Weaviate
    const response = await fetch(`${BACKEND_API_URL}/api/weaviate/products/delete`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        image_url,
        tenant_id: tenantId, // Sử dụng tenant_id từ xác thực
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      return NextResponse.json(
        { error: errorData.message || 'Failed to delete product' },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in delete API route:', error);
    return NextResponse.json({ error: error.message || 'Internal server error' }, { status: 500 });
  }
});
