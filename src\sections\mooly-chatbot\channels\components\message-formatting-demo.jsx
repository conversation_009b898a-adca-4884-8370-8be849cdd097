'use client';

import { useState } from 'react';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import CardContent from '@mui/material/CardContent';

import MessageFormattingForm from './message-formatting-form';
import MessagePreview from './message-preview';

import { MESSAGE_TYPES } from 'src/actions/mooly-chatbot/message-formatting-service';

// ----------------------------------------------------------------------

export default function MessageFormattingDemo() {
  const [messageData, setMessageData] = useState({
    message: '',
    messageType: MESSAGE_TYPES.TEXT,
    messageFormatting: null
  });

  const [previewRule, setPreviewRule] = useState(null);

  // Handle message data change
  const handleMessageChange = (newMessageData) => {
    setMessageData(newMessageData);
    
    // Create preview rule
    const rule = {
      id: 'preview_rule',
      message: newMessageData.message,
      messageType: newMessageData.messageType,
      messageFormatting: newMessageData.messageFormatting,
      delayMinutes: 5,
      isEnabled: true,
      order: 1,
      createdAt: new Date().toISOString()
    };
    
    setPreviewRule(rule);
  };

  // Reset form
  const handleReset = () => {
    setMessageData({
      message: '',
      messageType: MESSAGE_TYPES.TEXT,
      messageFormatting: null
    });
    setPreviewRule(null);
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Message Formatting Demo
      </Typography>
      
      <Typography variant="body2" sx={{ color: 'text.secondary', mb: 3 }}>
        Demo tính năng định dạng tin nhắn với hình ảnh và nút bấm
      </Typography>

      <Stack spacing={3}>
        {/* Form Section */}
        <Card>
          <CardContent>
            <Stack direction="row" alignItems="center" justifyContent="space-between" mb={2}>
              <Typography variant="h6">
                Tạo tin nhắn định dạng
              </Typography>
              <Button
                variant="outlined"
                size="small"
                onClick={handleReset}
              >
                Reset
              </Button>
            </Stack>

            <MessageFormattingForm
              value={messageData}
              onChange={handleMessageChange}
              disabled={false}
              showTemplates
            />
          </CardContent>
        </Card>

        {/* Preview Section */}
        {previewRule && (
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Preview tin nhắn
              </Typography>
              
              <Stack spacing={2}>
                {/* Compact Preview */}
                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    Compact Preview (List View)
                  </Typography>
                  <MessagePreview rule={previewRule} compact />
                </Box>

                {/* Full Preview */}
                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    Full Preview (Detail View)
                  </Typography>
                  <MessagePreview rule={previewRule} compact={false} />
                </Box>
              </Stack>
            </CardContent>
          </Card>
        )}

        {/* JSON Output */}
        {previewRule && (
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                JSON Output (Database Format)
              </Typography>
              
              <Box
                component="pre"
                sx={{
                  p: 2,
                  bgcolor: 'grey.100',
                  borderRadius: 1,
                  overflow: 'auto',
                  fontSize: '0.875rem',
                  fontFamily: 'monospace'
                }}
              >
                {JSON.stringify(previewRule, null, 2)}
              </Box>
            </CardContent>
          </Card>
        )}

        {/* Instructions */}
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Hướng dẫn sử dụng
            </Typography>
            
            <Stack spacing={1}>
              <Typography variant="body2">
                • <strong>Tin nhắn thường:</strong> Chỉ có nội dung text đơn giản
              </Typography>
              <Typography variant="body2">
                • <strong>Tin nhắn định dạng:</strong> Có thể bao gồm hình ảnh và nút bấm
              </Typography>
              <Typography variant="body2">
                • <strong>Hình ảnh:</strong> Tối đa 3 hình, định dạng JPG/PNG/GIF/WebP, tối đa 5MB/file
              </Typography>
              <Typography variant="body2">
                • <strong>Nút bấm:</strong> Tối đa 3 nút, có thể là Link hoặc Postback
              </Typography>
              <Typography variant="body2">
                • <strong>Link button:</strong> Mở URL trong tab mới
              </Typography>
              <Typography variant="body2">
                • <strong>Postback button:</strong> Gửi payload về server để xử lý
              </Typography>
            </Stack>
          </CardContent>
        </Card>
      </Stack>
    </Box>
  );
}
