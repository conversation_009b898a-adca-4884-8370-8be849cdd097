/**
 * Facebook Integration Service
 * Provides auto reply for Facebook/Instagram comments and messages
 * with optimized token refresh and AI-powered responses
 */

import { fetchData, updateData, createData, deleteData } from './supabase-utils.js';

// Constants
const FACEBOOK_API_BASE_URL = 'https://graph.facebook.com';
const FACEBOOK_API_VERSION = 'v23.0';
const INSTAGRAM_API_BASE_URL = 'https://graph.instagram.com';

// Tables
const FACEBOOK_ACCOUNTS_TABLE = 'facebook_accounts';
const FACEBOOK_CONFIG_TABLE = 'facebook_auto_reply_config';
const FACEBOOK_LOGS_TABLE = 'facebook_activity_logs';

/**
 * Facebook Account Management
 */

/**
 * Lưu thông tin Facebook account và token - tenant_id được xử lý tự động bởi RLS
 */
export async function saveFacebookAccount(accountData) {
  try {
    const data = {
      pageId: accountData.pageId,
      pageName: accountData.pageName,
      accessToken: accountData.accessToken,
      tokenExpiresAt: accountData.tokenExpiresAt,
      instagramAccountId: accountData.instagramAccountId || null,
      isActive: true,
      connectedAt: new Date().toISOString(),
      lastSyncAt: new Date().toISOString()
    };

    const result = await createData(FACEBOOK_ACCOUNTS_TABLE, data);
    return result;
  } catch (error) {
    console.error('Error saving Facebook account:', error);
    return { success: false, error: error.message, data: null };
  }
}

/**
 * Lấy danh sách Facebook accounts - tenant_id được xử lý tự động bởi RLS
 */
export async function getFacebookAccounts() {
  try {
    const result = await fetchData(FACEBOOK_ACCOUNTS_TABLE, {
      columns: 'id, page_id, page_name, instagram_account_id, instagram_username, is_active, connected_at, last_sync_at, token_expires_at',
      filters: {
        is_active: true
      },
      orderBy: 'connected_at',
      ascending: false
    });

    // Transform snake_case to camelCase for frontend
    if (result.success && result.data) {
      result.data = result.data.map(account => ({
        id: account.id,
        pageId: account.page_id,
        pageName: account.page_name,
        instagramAccountId: account.instagram_account_id,
        instagramUsername: account.instagram_username,
        isActive: account.is_active,
        connectedAt: account.connected_at,
        lastSyncAt: account.last_sync_at,
        tokenExpiresAt: account.token_expires_at
      }));
    }

    return result;
  } catch (error) {
    console.error('Error fetching Facebook accounts:', error);
    return { success: false, error: error.message, data: null };
  }
}

/**
 * Cập nhật access token
 */
export async function updateAccessToken(accountId, newToken, expiresAt) {
  try {
    const result = await updateData(
      FACEBOOK_ACCOUNTS_TABLE,
      { 
        accessToken: newToken, 
        tokenExpiresAt: expiresAt, 
        lastSyncAt: new Date().toISOString() 
      },
      { id: accountId }
    );
    return result;
  } catch (error) {
    console.error('Error updating access token:', error);
    return { success: false, error: error.message, data: null };
  }
}

/**
 * Token Refresh Management
 */

/**
 * Refresh long-lived access token
 */
export async function refreshAccessToken(appId, appSecret, currentToken) {
  try {
    const url = `${FACEBOOK_API_BASE_URL}/${FACEBOOK_API_VERSION}/oauth/access_token`;
    const params = new URLSearchParams({
      grant_type: 'fb_exchange_token',
      client_id: appId,
      client_secret: appSecret,
      fb_exchange_token: currentToken
    });

    const response = await fetch(`${url}?${params}`);
    const data = await response.json();

    if (data.error) {
      throw new Error(`Facebook API Error: ${data.error.message}`);
    }

    // Long-lived token typically expires in 60 days
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 60);

    return {
      accessToken: data.access_token,
      expiresAt: expiresAt.toISOString()
    };
  } catch (error) {
    console.error('Error refreshing access token:', error);
    throw error;
  }
}

/**
 * Auto refresh tokens that will expire soon (within 7 days) - tenant_id được xử lý tự động bởi RLS
 */
export async function autoRefreshExpiringSoonTokens(appId, appSecret) {
  try {
    const sevenDaysFromNow = new Date();
    sevenDaysFromNow.setDate(sevenDaysFromNow.getDate() + 7);

    // Get accounts with tokens expiring soon
    const expiringAccounts = await fetchData(FACEBOOK_ACCOUNTS_TABLE, {
      columns: 'id, access_token, page_id',
      filters: {
        isActive: true,
        tokenExpiresAt: { 
          operator: 'lt', 
          value: sevenDaysFromNow.toISOString() 
        }
      }
    });

    if (!expiringAccounts.success || !expiringAccounts.data?.length) {
      return { success: true, refreshedCount: 0 };
    }

    let refreshedCount = 0;
    for (const account of expiringAccounts.data) {
      try {
        const refreshResult = await refreshAccessToken(appId, appSecret, account.accessToken);
        const updateResult = await updateAccessToken(account.id, refreshResult.accessToken, refreshResult.expiresAt);
        if (updateResult.success) {
          refreshedCount++;
        }
      } catch (error) {
        console.error(`Failed to refresh token for account ${account.pageId}:`, error);
        // Log error but continue with other accounts
        await logActivity(account.pageId, 'token_refresh_failed', { error: error.message });
      }
    }

    return { success: true, refreshedCount };
  } catch (error) {
    console.error('Error in auto refresh tokens:', error);
    return { success: false, error: error.message, refreshedCount: 0 };
  }
}

/**
 * Auto Reply Configuration Management
 */

/**
 * Lưu cấu hình auto reply
 */
export async function saveAutoReplyConfig(configData) {
  try {
    const data = {
      tenantId: configData.tenantId,
      pageId: configData.pageId,
      enableCommentReply: configData.enableCommentReply || false,
      enableMessageReply: configData.enableMessageReply || false,
      enableInstagramComments: configData.enableInstagramComments || false,
      enableInstagramMessages: configData.enableInstagramMessages || false,
      replyPrompt: configData.replyPrompt || '',
      replyTone: configData.replyTone || 'friendly',
      replyLanguage: configData.replyLanguage || 'vi',
      maxReplyLength: configData.maxReplyLength || 500,
      businessInfo: configData.businessInfo || '',
      products: configData.products || '',
      policies: configData.policies || '',
      excludeKeywords: configData.excludeKeywords || [],
      autoPrivateReply: configData.autoPrivateReply || false,
      updatedAt: new Date().toISOString()
    };

    // Check if config exists
    const existingConfig = await fetchData(
      FACEBOOK_CONFIG_TABLE,
      ['id'],
      [
        { column: 'tenant_id', operator: 'eq', value: configData.tenantId },
        { column: 'page_id', operator: 'eq', value: configData.pageId }
      ]
    );

    let result;
    if (existingConfig.success && existingConfig.data?.length > 0) {
      // Update existing config
      result = await updateData(
        FACEBOOK_CONFIG_TABLE,
        data,
        [
          { column: 'tenant_id', operator: 'eq', value: configData.tenantId },
          { column: 'page_id', operator: 'eq', value: configData.pageId }
        ]
      );
    } else {
      // Create new config
      data.createdAt = new Date().toISOString();
      result = await createData(FACEBOOK_CONFIG_TABLE, data);
    }

    return result;
  } catch (error) {
    console.error('Error saving auto reply config:', error);
    throw error;
  }
}

/**
 * Lấy cấu hình auto reply
 */
export async function getAutoReplyConfig(tenantId, pageId) {
  try {
    const result = await fetchData(
      FACEBOOK_CONFIG_TABLE,
      '*',
      [
        { column: 'tenant_id', operator: 'eq', value: tenantId },
        { column: 'page_id', operator: 'eq', value: pageId }
      ]
    );

    if (result.success && result.data?.length > 0) {
      return { success: true, data: result.data[0] };
    }

    // Return default config if not found
    return {
      success: true,
      data: {
        enableCommentReply: false,
        enableMessageReply: false,
        enableInstagramComments: false,
        enableInstagramMessages: false,
        replyPrompt: '',
        replyTone: 'friendly',
        replyLanguage: 'vi',
        maxReplyLength: 500,
        businessInfo: '',
        products: '',
        policies: '',
        excludeKeywords: [],
        autoPrivateReply: false
      }
    };
  } catch (error) {
    console.error('Error fetching auto reply config:', error);
    throw error;
  }
}

/**
 * Comment Reply Functions
 */

/**
 * Reply to Facebook comment
 */
export async function replyToFacebookComment(accessToken, commentId, message) {
  try {
    const url = `${FACEBOOK_API_BASE_URL}/${FACEBOOK_API_VERSION}/${commentId}/comments`;
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message: message,
        access_token: accessToken
      })
    });

    const data = await response.json();

    if (data.error) {
      throw new Error(`Facebook API Error: ${data.error.message}`);
    }

    return { success: true, commentId: data.id };
  } catch (error) {
    console.error('Error replying to Facebook comment:', error);
    throw error;
  }
}

/**
 * Reply to Instagram comment
 */
export async function replyToInstagramComment(accessToken, commentId, message) {
  try {
    const url = `${INSTAGRAM_API_BASE_URL}/${commentId}/replies`;
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message: message,
        access_token: accessToken
      })
    });

    const data = await response.json();

    if (data.error) {
      throw new Error(`Instagram API Error: ${data.error.message}`);
    }

    return { success: true, commentId: data.id };
  } catch (error) {
    console.error('Error replying to Instagram comment:', error);
    throw error;
  }
}

/**
 * Send private reply to Instagram comment author
 */
export async function sendInstagramPrivateReply(accessToken, pageId, commentId, message) {
  try {
    const url = `${FACEBOOK_API_BASE_URL}/${FACEBOOK_API_VERSION}/${pageId}/messages`;
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        recipient: { comment_id: commentId },
        message: { text: message },
        access_token: accessToken
      })
    });

    const data = await response.json();

    if (data.error) {
      throw new Error(`Instagram Private Reply Error: ${data.error.message}`);
    }

    return { success: true, messageId: data.message_id, recipientId: data.recipient_id };
  } catch (error) {
    console.error('Error sending Instagram private reply:', error);
    throw error;
  }
}

/**
 * Messaging Functions
 */

/**
 * Send Facebook message
 */
export async function sendFacebookMessage(accessToken, pageId, recipientId, message) {
  try {
    const url = `${FACEBOOK_API_BASE_URL}/${FACEBOOK_API_VERSION}/${pageId}/messages`;
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        recipient: { id: recipientId },
        message: { text: message },
        access_token: accessToken
      })
    });

    const data = await response.json();

    if (data.error) {
      throw new Error(`Facebook Messaging Error: ${data.error.message}`);
    }

    return { success: true, messageId: data.message_id };
  } catch (error) {
    console.error('Error sending Facebook message:', error);
    throw error;
  }
}

/**
 * Send Instagram message
 */
export async function sendInstagramMessage(accessToken, pageId, recipientId, message) {
  try {
    const url = `${INSTAGRAM_API_BASE_URL}/me/messages`;
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        recipient: { id: recipientId },
        message: { text: message },
        access_token: accessToken
      })
    });

    const data = await response.json();

    if (data.error) {
      throw new Error(`Instagram Messaging Error: ${data.error.message}`);
    }

    return { success: true, messageId: data.message_id };
  } catch (error) {
    console.error('Error sending Instagram message:', error);
    throw error;
  }
}

/**
 * AI Response Generation
 */

/**
 * Generate AI response based on config and context
 */
export async function generateAIResponse(config, messageText, userInfo = {}) {
  try {
    // Build context for AI
    const context = {
      businessInfo: config.businessInfo || '',
      products: config.products || '',
      policies: config.policies || '',
      tone: config.replyTone || 'friendly',
      language: config.replyLanguage || 'vi',
      maxLength: config.maxReplyLength || 500
    };

    // Check if message contains excluded keywords
    if (config.excludeKeywords?.length > 0) {
      const hasExcludedKeyword = config.excludeKeywords.some(keyword => 
        messageText.toLowerCase().includes(keyword.toLowerCase())
      );
      if (hasExcludedKeyword) {
        return null; // Don't reply to messages with excluded keywords
      }
    }

    // Build prompt for AI
    const prompt = `
${config.replyPrompt || 'Bạn là trợ lý AI của doanh nghiệp, hãy trả lời tin nhắn/comment của khách hàng một cách chuyên nghiệp và hữu ích.'}

Thông tin doanh nghiệp:
${context.businessInfo}

Sản phẩm/dịch vụ:
${context.products}

Chính sách:
${context.policies}

Hướng dẫn trả lời:
- Tone: ${context.tone}
- Ngôn ngữ: ${context.language}
- Độ dài tối đa: ${context.maxLength} ký tự
- Luôn lịch sự, chuyên nghiệp
- Cung cấp thông tin hữu ích
- Không đưa ra cam kết ngoài thẩm quyền

Tin nhắn khách hàng: "${messageText}"

Hãy tạo câu trả lời phù hợp:`;

    // Call your AI service here (OpenAI, Claude, etc.)
    // For now, return a placeholder response
    const response = await callAIService(prompt, context);
    
    return response;
  } catch (error) {
    console.error('Error generating AI response:', error);
    return null;
  }
}

/**
 * Call AI service (placeholder - implement with your preferred AI provider)
 */
async function callAIService(prompt, context) {
  // This is a placeholder - integrate with your AI service
  // Examples: OpenAI API, Claude API, local AI model, etc.
  
  // For demo purposes, return a simple response
  if (context.language === 'vi') {
    return 'Cảm ơn bạn đã liên hệ! Chúng tôi sẽ hỗ trợ bạn sớm nhất có thể.';
  } else {
    return 'Thank you for reaching out! We will assist you as soon as possible.';
  }
}

/**
 * Activity Logging
 */

/**
 * Log Facebook activity - tenant_id được xử lý tự động bởi RLS
 */
export async function logActivity(pageId, activity, metadata = {}) {
  try {
    const data = {
      pageId,
      activity,
      metadata,
      createdAt: new Date().toISOString()
    };

    await createData(FACEBOOK_LOGS_TABLE, data);
  } catch (error) {
    console.error('Error logging Facebook activity:', error);
    // Don't throw error for logging failures
  }
}

/**
 * Get activity logs - tenant_id được xử lý tự động bởi RLS
 */
export async function getActivityLogs(pageId, limit = 50) {
  try {
    const result = await fetchData(FACEBOOK_LOGS_TABLE, {
      columns: 'activity, metadata, created_at',
      filters: { 
        pageId: pageId 
      },
      orderBy: 'createdAt',
      ascending: false,
      limit: limit
    });
    return result;
  } catch (error) {
    console.error('Error fetching activity logs:', error);
    return { success: false, error: error.message, data: null };
  }
}

/**
 * Webhook Verification
 */

/**
 * Verify Facebook webhook signature
 */
export function verifyWebhookSignature(payload, signature, appSecret) {
  try {
    const crypto = require('crypto');
    const expectedSignature = crypto
      .createHmac('sha1', appSecret)
      .update(payload)
      .digest('hex');
    
    return `sha1=${expectedSignature}` === signature;
  } catch (error) {
    console.error('Error verifying webhook signature:', error);
    return false;
  }
}

/**
 * Process webhook event
 */
export async function processWebhookEvent(eventData) {
  try {
    const { object, entry } = eventData;
    
    if (object === 'page') {
      // Facebook Page events
      for (const entryItem of entry) {
        if (entryItem.messaging) {
          // Handle messages
          for (const message of entryItem.messaging) {
            await handleFacebookMessage(message);
          }
        }
        if (entryItem.changes) {
          // Handle post comments
          for (const change of entryItem.changes) {
            if (change.field === 'feed' && change.value?.verb === 'add' && change.value?.item === 'comment') {
              await handleFacebookComment(change.value);
            }
          }
        }
      }
    } else if (object === 'instagram') {
      // Instagram events
      for (const entryItem of entry) {
        if (entryItem.messaging) {
          // Handle Instagram messages
          for (const message of entryItem.messaging) {
            await handleInstagramMessage(message);
          }
        }
        if (entryItem.changes) {
          // Handle Instagram comments
          for (const change of entryItem.changes) {
            if (change.field === 'comments') {
              await handleInstagramComment(change.value);
            }
          }
        }
      }
    }

    return { success: true };
  } catch (error) {
    console.error('Error processing webhook event:', error);
    throw error;
  }
}

/**
 * Handle Facebook message
 */
async function handleFacebookMessage(messageData) {
  try {
    const { sender, recipient, message } = messageData;
    
    if (!message?.text || message.is_echo) {
      return; // Skip non-text messages and echoes
    }

    // Get account and config
    const account = await getFacebookAccountByPageId(recipient.id);
    if (!account?.data) return;

    const config = await getAutoReplyConfig(account.data.tenantId, recipient.id);
    if (!config?.data?.enableMessageReply) return;

    // Generate AI response
    const aiResponse = await generateAIResponse(config.data, message.text);
    if (!aiResponse) return;

    // Send reply
    await sendFacebookMessage(account.data.accessToken, recipient.id, sender.id, aiResponse);
    
    // Log activity
    await logActivity(recipient.id, 'message_replied', {
      originalMessage: message.text,
      replyMessage: aiResponse,
      senderId: sender.id
    });
  } catch (error) {
    console.error('Error handling Facebook message:', error);
  }
}

/**
 * Handle Instagram message
 */
async function handleInstagramMessage(messageData) {
  try {
    const { sender, recipient, message } = messageData;
    
    if (!message?.text || message.is_echo) {
      return; // Skip non-text messages and echoes
    }

    // Get account and config
    const account = await getFacebookAccountByPageId(recipient.id);
    if (!account?.data) return;

    const config = await getAutoReplyConfig(account.data.tenantId, recipient.id);
    if (!config?.data?.enableInstagramMessages) return;

    // Generate AI response
    const aiResponse = await generateAIResponse(config.data, message.text);
    if (!aiResponse) return;

    // Send reply
    await sendInstagramMessage(account.data.accessToken, recipient.id, sender.id, aiResponse);
    
    // Log activity
    await logActivity(recipient.id, 'instagram_message_replied', {
      originalMessage: message.text,
      replyMessage: aiResponse,
      senderId: sender.id
    });
  } catch (error) {
    console.error('Error handling Instagram message:', error);
  }
}

/**
 * Handle Facebook comment
 */
async function handleFacebookComment(commentData) {
  try {
    const { comment_id, message, post_id, parent_id } = commentData;
    
    if (!message || parent_id) {
      return; // Skip empty comments and replies to comments
    }

    // Get account and config
    const account = await getFacebookAccountByPostId(post_id);
    if (!account?.data) return;

    const config = await getAutoReplyConfig(account.data.tenantId, account.data.pageId);
    if (!config?.data?.enableCommentReply) return;

    // Generate AI response
    const aiResponse = await generateAIResponse(config.data, message);
    if (!aiResponse) return;

    // Reply to comment
    await replyToFacebookComment(account.data.accessToken, comment_id, aiResponse);
    
    // Log activity
    await logActivity(account.data.pageId, 'comment_replied', {
      originalComment: message,
      replyMessage: aiResponse,
      commentId: comment_id,
      postId: post_id
    });
  } catch (error) {
    console.error('Error handling Facebook comment:', error);
  }
}

/**
 * Handle Instagram comment
 */
async function handleInstagramComment(commentData) {
  try {
    const { id: commentId, text, media } = commentData;
    
    if (!text) return;

    // Get account and config
    const account = await getFacebookAccountByInstagramId(media?.id);
    if (!account?.data) return;

    const config = await getAutoReplyConfig(account.data.tenantId, account.data.pageId);
    if (!config?.data?.enableInstagramComments) return;

    // Generate AI response
    const aiResponse = await generateAIResponse(config.data, text);
    if (!aiResponse) return;

    // Reply to comment or send private message
    if (config.data.autoPrivateReply) {
      await sendInstagramPrivateReply(account.data.accessToken, account.data.pageId, commentId, aiResponse);
    } else {
      await replyToInstagramComment(account.data.accessToken, commentId, aiResponse);
    }
    
    // Log activity
    await logActivity(account.data.pageId, 'instagram_comment_replied', {
      originalComment: text,
      replyMessage: aiResponse,
      commentId: commentId,
      mediaId: media?.id,
      isPrivateReply: config.data.autoPrivateReply
    });
  } catch (error) {
    console.error('Error handling Instagram comment:', error);
  }
}

/**
 * Helper functions to get account by different IDs
 */
async function getFacebookAccountByPageId(pageId) {
  try {
    const result = await fetchData(FACEBOOK_ACCOUNTS_TABLE, {
      filters: {
        pageId: pageId,
        isActive: true
      },
      single: true
    });
    
    if (result.success && result.data) {
      return { success: true, data: result.data };
    }
    return { success: false, data: null };
  } catch (error) {
    console.error('Error fetching account by page ID:', error);
    return { success: false, data: null };
  }
}

async function getFacebookAccountByPostId(postId) {
  // Extract page ID from post ID if needed
  // Post ID format: pageId_postId
  const pageId = postId.split('_')[0];
  return getFacebookAccountByPageId(pageId);
}

async function getFacebookAccountByInstagramId(mediaId) {
  try {
    const result = await fetchData(FACEBOOK_ACCOUNTS_TABLE, {
      filters: {
        instagramAccountId: mediaId,
        isActive: true
      },
      single: true
    });
    
    if (result.success && result.data) {
      return { success: true, data: result.data };
    }
    return { success: false, data: null };
  } catch (error) {
    console.error('Error fetching account by Instagram ID:', error);
    return { success: false, data: null };
  }
}

/**
 * Get Facebook account for chatbot
 */
export async function getFacebookAccount(chatbotId) {
  try {
    const { data, error } = await fetchData(
      FACEBOOK_ACCOUNTS_TABLE,
      '*',
      [
        { column: 'chatbot_id', operator: 'eq', value: chatbotId },
        { column: 'is_active', operator: 'eq', value: true }
      ],
      null,
      1
    );

    return {
      success: true,
      data: data?.[0] || null,
      error: null
    };
  } catch (error) {
    console.error('Error fetching Facebook account:', error);
    return {
      success: false,
      data: null,
      error: error.message
    };
  }
}

/**
 * Get Facebook auto reply config
 */
export async function getFacebookConfig(chatbotId) {
  try {
    const { data, error } = await fetchData(
      FACEBOOK_CONFIG_TABLE,
      '*',
      [{ column: 'chatbot_id', operator: 'eq', value: chatbotId }],
      null,
      1
    );

    return {
      success: true,
      data: data?.[0] || null,
      error: null
    };
  } catch (error) {
    console.error('Error fetching Facebook config:', error);
    return {
      success: false,
      data: null,
      error: error.message
    };
  }
}

/**
 * Save Facebook auto reply config
 */
export async function saveFacebookConfig(chatbotId, config) {
  try {
    // Check if config exists
    const existingConfig = await getFacebookConfig(chatbotId);
    
    const configData = {
      chatbot_id: chatbotId,
      enable_auto_reply_comments: config.enableAutoReplyComments || false,
      enable_auto_reply_messages: config.enableAutoReplyMessages || false,
      enable_instagram_comments: config.enableInstagramComments || false,
      enable_instagram_messages: config.enableInstagramMessages || false,
      ai_prompt: config.aiPrompt || '',
      default_message: config.defaultMessage || '',
      updated_at: new Date()
    };

    let result;
    if (existingConfig.data) {
      // Update existing config
      result = await updateData(
        FACEBOOK_CONFIG_TABLE,
        configData,
        [{ column: 'chatbot_id', operator: 'eq', value: chatbotId }]
      );
    } else {
      // Create new config
      configData.created_at = new Date();
      result = await createData(FACEBOOK_CONFIG_TABLE, configData);
    }

    return {
      success: true,
      data: result.data,
      error: null
    };
  } catch (error) {
    console.error('Error saving Facebook config:', error);
    return {
      success: false,
      data: null,
      error: error.message
    };
  }
}

/**
 * Disconnect Facebook account
 */
export async function disconnectFacebookAccount(chatbotId) {
  try {
    const result = await updateData(
      FACEBOOK_ACCOUNTS_TABLE,
      { 
        is_active: false,
        disconnected_at: new Date()
      },
      [
        { column: 'chatbot_id', operator: 'eq', value: chatbotId },
        { column: 'is_active', operator: 'eq', value: true }
      ]
    );

    return {
      success: true,
      data: result.data,
      error: null
    };
  } catch (error) {
    console.error('Error disconnecting Facebook account:', error);
    return {
      success: false,
      data: null,
      error: error.message
    };
  }
}

/**
 * Get Facebook activity logs
 */
export async function getFacebookLogs(chatbotId, limit = 50) {
  try {
    const { data, error } = await fetchData(
      FACEBOOK_LOGS_TABLE,
      '*',
      [{ column: 'chatbot_id', operator: 'eq', value: chatbotId }],
      { column: 'created_at', ascending: false },
      limit
    );

    return {
      success: true,
      data: data || [],
      error: null
    };
  } catch (error) {
    console.error('Error fetching Facebook logs:', error);
    return {
      success: false,
      data: [],
      error: error.message
    };
  }
}

/**
 * Log Facebook activity
 */
export async function logFacebookActivity(chatbotId, activity) {
  try {
    const logData = {
      chatbot_id: chatbotId,
      activity_type: activity.type,
      activity_data: activity.data,
      status: activity.status || 'success',
      message: activity.message || '',
      created_at: new Date()
    };

    const result = await createData(FACEBOOK_LOGS_TABLE, logData);

    return {
      success: true,
      data: result.data,
      error: null
    };
  } catch (error) {
    console.error('Error logging Facebook activity:', error);
    return {
      success: false,
      data: null,
      error: error.message
    };
  }
}

/**
 * Refresh Facebook token
 */
export async function refreshFacebookToken(chatbotId) {
  try {
    // Get current account
    const accountResult = await getFacebookAccount(chatbotId);
    if (!accountResult.success || !accountResult.data) {
      throw new Error('No active Facebook account found');
    }

    const account = accountResult.data;

    // Call API to refresh token
    const response = await fetch('/api/facebook-integration/refresh-token', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        chatbotId,
        pageId: account.pageId,
        currentToken: account.pageAccessToken
      })
    });

    const data = await response.json();

    if (!data.success) {
      throw new Error(data.error || 'Failed to refresh token');
    }

    // Update token in database
    const updateResult = await updateData(
      FACEBOOK_ACCOUNTS_TABLE,
      {
        page_access_token: data.newToken,
        token_expires_at: data.expiresAt,
        last_sync_at: new Date()
      },
      [
        { column: 'chatbot_id', operator: 'eq', value: chatbotId },
        { column: 'is_active', operator: 'eq', value: true }
      ]
    );

    return {
      success: true,
      data: updateResult.data,
      error: null
    };
  } catch (error) {
    console.error('Error refreshing Facebook token:', error);
    return {
      success: false,
      data: null,
      error: error.message
    };
  }
} 