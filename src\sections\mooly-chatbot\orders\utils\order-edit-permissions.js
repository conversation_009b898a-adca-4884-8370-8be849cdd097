'use client';

import { ORDER_STATUS } from 'src/actions/mooly-chatbot/order-constants';

/**
 * <PERSON><PERSON><PERSON> tra quyền chỉnh sửa đơn hàng dựa trên trạng thái
 */

// Các trạng thái có thể chỉnh sửa thông tin cơ bản
export const EDITABLE_STATUSES = [
  ORDER_STATUS.PENDING,
  ORDER_STATUS.CONFIRMED,
];

// Các trạng thái có thể chỉnh sửa địa chỉ giao hàng
export const EDITABLE_ADDRESS_STATUSES = [
  ORDER_STATUS.PENDING,
  ORDER_STATUS.CONFIRMED,
  ORDER_STATUS.PAID,
];

// Các trạng thái có thể chỉnh sửa sản phẩm và số lượng
export const EDITABLE_ITEMS_STATUSES = [
  ORDER_STATUS.PENDING,
  ORDER_STATUS.CONFIRMED,
];

// <PERSON><PERSON><PERSON> trạng thái có thể chỉnh sửa giá, thuế, gi<PERSON>m giá
export const EDITABLE_PRICING_STATUSES = [
  ORDER_STATUS.PENDING,
  ORDER_STATUS.CONFIRMED,
  ORDER_STATUS.PAID,
];

// Các trạng thái có thể cập nhật trạng thái
export const UPDATABLE_STATUS_STATUSES = [
  ORDER_STATUS.PENDING,
  ORDER_STATUS.CONFIRMED,
  ORDER_STATUS.PAID,
  ORDER_STATUS.PROCESSING,
  ORDER_STATUS.SHIPPING,
  ORDER_STATUS.DELIVERED,
];

/**
 * Kiểm tra có thể chỉnh sửa thông tin cơ bản không
 * @param {string} status - Trạng thái đơn hàng
 * @returns {boolean}
 */
export function canEditBasicInfo(status) {
  return EDITABLE_STATUSES.includes(status);
}

/**
 * Kiểm tra có thể chỉnh sửa địa chỉ giao hàng không
 * @param {string} status - Trạng thái đơn hàng
 * @returns {boolean}
 */
export function canEditAddress(status) {
  return EDITABLE_ADDRESS_STATUSES.includes(status);
}

/**
 * Kiểm tra có thể chỉnh sửa sản phẩm và số lượng không
 * @param {string} status - Trạng thái đơn hàng
 * @returns {boolean}
 */
export function canEditItems(status) {
  return EDITABLE_ITEMS_STATUSES.includes(status);
}

/**
 * Kiểm tra có thể chỉnh sửa giá, thuế, giảm giá không
 * @param {string} status - Trạng thái đơn hàng
 * @returns {boolean}
 */
export function canEditPricing(status) {
  return EDITABLE_PRICING_STATUSES.includes(status);
}

/**
 * Kiểm tra có thể cập nhật trạng thái không
 * @param {string} status - Trạng thái đơn hàng
 * @returns {boolean}
 */
export function canUpdateStatus(status) {
  return UPDATABLE_STATUS_STATUSES.includes(status);
}

/**
 * Lấy danh sách các phần có thể chỉnh sửa
 * @param {string} status - Trạng thái đơn hàng
 * @returns {Object} - Object chứa các quyền chỉnh sửa
 */
export function getEditPermissions(status) {
  return {
    basicInfo: canEditBasicInfo(status),
    address: canEditAddress(status),
    items: canEditItems(status),
    pricing: canEditPricing(status),
    status: canUpdateStatus(status),
  };
}

/**
 * Lấy thông báo lý do không thể chỉnh sửa
 * @param {string} status - Trạng thái đơn hàng
 * @param {string} section - Phần muốn chỉnh sửa
 * @returns {string} - Thông báo lý do
 */
export function getEditRestrictionMessage(status, section) {
  const statusLabels = {
    [ORDER_STATUS.PENDING]: 'Chờ xác nhận',
    [ORDER_STATUS.CONFIRMED]: 'Đã xác nhận',
    [ORDER_STATUS.PAID]: 'Đã thanh toán',
    [ORDER_STATUS.PROCESSING]: 'Đang xử lý',
    [ORDER_STATUS.SHIPPING]: 'Đang giao hàng',
    [ORDER_STATUS.DELIVERED]: 'Đã giao hàng',
    [ORDER_STATUS.COMPLETED]: 'Hoàn thành',
    [ORDER_STATUS.CANCELLED]: 'Đã hủy',
    [ORDER_STATUS.REFUNDED]: 'Hoàn tiền',
  };

  const currentStatusLabel = statusLabels[status] || status;

  switch (section) {
    case 'basicInfo':
      return `Không thể chỉnh sửa thông tin cơ bản khi đơn hàng đã ở trạng thái "${currentStatusLabel}". Chỉ có thể chỉnh sửa khi đơn hàng ở trạng thái "Chờ xác nhận" hoặc "Đã xác nhận".`;
    
    case 'address':
      return `Không thể chỉnh sửa địa chỉ giao hàng khi đơn hàng đã ở trạng thái "${currentStatusLabel}". Chỉ có thể chỉnh sửa khi đơn hàng chưa bắt đầu xử lý.`;
    
    case 'items':
      return `Không thể chỉnh sửa sản phẩm khi đơn hàng đã ở trạng thái "${currentStatusLabel}". Chỉ có thể chỉnh sửa khi đơn hàng ở trạng thái "Chờ xác nhận" hoặc "Đã xác nhận".`;
    
    case 'pricing':
      return `Không thể chỉnh sửa giá, thuế, giảm giá khi đơn hàng đã ở trạng thái "${currentStatusLabel}". Chỉ có thể chỉnh sửa khi đơn hàng chưa bắt đầu xử lý.`;
    
    case 'status':
      return `Không thể cập nhật trạng thái từ "${currentStatusLabel}". Đơn hàng đã hoàn thành hoặc bị hủy.`;
    
    default:
      return `Không thể chỉnh sửa khi đơn hàng ở trạng thái "${currentStatusLabel}".`;
  }
}

/**
 * Kiểm tra có thể chỉnh sửa bất kỳ phần nào không
 * @param {string} status - Trạng thái đơn hàng
 * @returns {boolean}
 */
export function canEditAnySection(status) {
  const permissions = getEditPermissions(status);
  return Object.values(permissions).some(permission => permission);
}
