'use client';

import { useRef } from 'react';
import { useScroll } from 'framer-motion';

// ----------------------------------------------------------------------

export function useScrollProgress(target = 'document') {
  const elementRef = useRef(null);

  const options = { container: elementRef };

  const { scrollYProgress, scrollXProgress } = useScroll(
    target === 'container' ? options : undefined
  );

  // Removed memoization for better data freshness
  return { elementRef, scrollXProgress, scrollYProgress };
}
