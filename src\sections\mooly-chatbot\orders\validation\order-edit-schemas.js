import { z as zod } from 'zod';

// ----------------------------------------------------------------------

// Schema cho edit thông tin cơ bản
export const OrderBasicEditSchema = zod.object({
  customerName: zod
    .string()
    .min(1, 'Tên khách hàng là bắt buộc')
    .min(2, 'Tên khách hàng phải có ít nhất 2 ký tự'),
  customerPhone: zod
    .string()
    .min(1, 'Số điện thoại là bắt buộc')
    .regex(/^[0-9+\-\s()]+$/, 'Số điện thoại không hợp lệ'),
  customerEmail: zod
    .string()
    .email('Email không hợp lệ')
    .optional()
    .or(zod.literal('')),
  notes: zod.string().optional(),
});

// <PERSON><PERSON><PERSON> cho edit địa chỉ giao hàng
export const OrderAddressEditSchema = zod.object({
  fullName: zod
    .string()
    .min(1, 'Tên người nhận là bắt buộc')
    .min(2, 'Tên người nhận phải có ít nhất 2 ký tự'),
  phone: zod
    .string()
    .min(1, 'Số điện thoại là bắt buộc')
    .regex(/^[0-9+\-\s()]+$/, 'Số điện thoại không hợp lệ'),
  address: zod
    .string()
    .min(1, 'Địa chỉ là bắt buộc')
    .min(5, 'Địa chỉ phải có ít nhất 5 ký tự'),
  addressLine2: zod.string().optional(),
  ward: zod.string().optional(),
  district: zod.string().optional(),
  province: zod.string().optional(),
  city: zod.string().optional(),
  state: zod.string().optional(),
  postalCode: zod.string().optional(),
  country: zod.string().optional(),
  notes: zod.string().optional(),
});

// Schema cho edit pricing
export const OrderPricingEditSchema = zod.object({
  subtotal: zod
    .union([zod.string(), zod.number()])
    .transform((val) => Number(val))
    .refine((val) => !isNaN(val) && val >= 0, {
      message: 'Tổng tiền hàng phải là số và lớn hơn hoặc bằng 0',
    }),
  shippingAmount: zod
    .union([zod.string(), zod.number()])
    .transform((val) => Number(val))
    .refine((val) => !isNaN(val) && val >= 0, {
      message: 'Phí vận chuyển phải là số và lớn hơn hoặc bằng 0',
    }),
  discountAmount: zod
    .union([zod.string(), zod.number()])
    .transform((val) => Number(val) || 0)
    .refine((val) => !isNaN(val) && val >= 0, {
      message: 'Giảm giá phải là số và lớn hơn hoặc bằng 0',
    })
    .optional()
    .default(0),
  taxAmount: zod
    .union([zod.string(), zod.number()])
    .transform((val) => Number(val))
    .refine((val) => !isNaN(val) && val >= 0, {
      message: 'Thuế phải là số và lớn hơn hoặc bằng 0',
    }),
  paymentMethod: zod
    .string()
    .min(1, 'Phương thức thanh toán là bắt buộc'),
  shippingMethod: zod
    .string()
    .min(1, 'Phương thức vận chuyển là bắt buộc'),
});

// Schema cho edit order items
export const OrderItemsEditSchema = zod.object({
  orderItems: zod
    .array(
      zod.object({
        // ID để xác định item đã tồn tại hay mới
        id: zod.string().uuid().optional(),
        productId: zod.string().uuid('Product ID phải là UUID hợp lệ').optional(),
        variantId: zod.string().uuid('Variant ID phải là UUID hợp lệ').optional().or(zod.literal('')),
        name: zod.string().min(1, 'Tên sản phẩm là bắt buộc'),
        sku: zod.string().optional(),
        quantity: zod
          .union([zod.string(), zod.number()])
          .transform((val) => Number(val))
          .refine((val) => !isNaN(val) && val >= 1 && Number.isInteger(val), {
            message: 'Số lượng phải là số nguyên và lớn hơn 0',
          }),
        unitPrice: zod
          .union([zod.string(), zod.number()])
          .transform((val) => Number(val))
          .refine((val) => !isNaN(val) && val >= 0, {
            message: 'Đơn giá phải là số và lớn hơn hoặc bằng 0',
          }),
        totalPrice: zod
          .union([zod.string(), zod.number()])
          .transform((val) => Number(val))
          .refine((val) => !isNaN(val) && val >= 0, {
            message: 'Thành tiền phải là số và lớn hơn hoặc bằng 0',
          }),
        discountAmount: zod
          .union([zod.string(), zod.number()])
          .transform((val) => Number(val) || 0)
          .refine((val) => !isNaN(val) && val >= 0, {
            message: 'Giảm giá phải là số và lớn hơn hoặc bằng 0',
          })
          .optional()
          .default(0),
        taxAmount: zod
          .union([zod.string(), zod.number()])
          .transform((val) => Number(val) || 0)
          .refine((val) => !isNaN(val) && val >= 0, {
            message: 'Thuế phải là số và lớn hơn hoặc bằng 0',
          })
          .optional()
          .default(0),
        imageUrl: zod.string().optional(),
        variantInfo: zod.any().optional(),
        // Objects cho autocomplete - chỉ dùng trong UI, không gửi lên server
        productDetail: zod.any().optional().nullable(),
        variantDetail: zod.any().optional().nullable(),
        product: zod.any().optional().nullable(),
        variant: zod.any().optional().nullable(),
      })
    )
    .min(1, 'Đơn hàng phải có ít nhất 1 sản phẩm')
    .refine((items) => {
      // Kiểm tra không có duplicate productId + variantId
      const combinations = items.map(item => `${item.productId || ''}-${item.variantId || ''}`);
      return combinations.length === new Set(combinations).size;
    }, {
      message: 'Không được có sản phẩm trùng lặp trong đơn hàng',
    }),
});

// Backward compatibility - export functions that return schemas
export const createOrderBasicEditSchema = () => OrderBasicEditSchema;
export const createOrderAddressEditSchema = () => OrderAddressEditSchema;
export const createOrderPricingEditSchema = () => OrderPricingEditSchema;
export const createOrderItemsEditSchema = () => OrderItemsEditSchema;
