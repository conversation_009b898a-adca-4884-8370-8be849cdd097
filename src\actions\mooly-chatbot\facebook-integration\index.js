/**
 * Facebook Integration Service
 * Module tổng hợp các dịch vụ liên quan đến Facebook integration
 * Cung cấp các API, hooks, và tiện ích để quản lý Facebook/Instagram auto reply
 */

// Re-export từ các module con
export * from './facebook-constants';
export * from './facebook-api';
export * from './facebook-hooks';
export * from './facebook-mutations';
export * from './facebook-popup-service';

// Import các hàm cần thiết để export riêng
import { 
  getFacebookAccounts, 
  getFacebookAccountById,
  getFacebookAccountByPageId,
  getAutoReplyConfig,
  getSelectedPageForChatbot,
  upsertFacebookAccount,
  upsertAutoReplyConfig,
  logFacebookActivity
} from './facebook-api';

import { 
  useFacebookAccounts,
  useFacebookAccount,
  useAutoReplyConfig,
  useSelectedPageForChatbot,
  useFacebookIntegrationSetup
} from './facebook-hooks';

import { 
  useFacebookAccountMutations,
  useAutoReplyConfigMutations,
  usePageSelectionMutations,
  useFacebookMutations
} from './facebook-mutations';

import { 
  useFacebookPopupAuth,
  useFacebookConnection,
  validateFacebookPermissions,
  formatFacebookError
} from './facebook-popup-service';

import { 
  FACEBOOK_TABLES,
  ACTIVITY_TYPES,
  REPLY_TONES,
  REPLY_LANGUAGES,
  DEFAULT_CONFIG,
  API_ENDPOINTS,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES
} from './facebook-constants';

// Export các hàm chính để sử dụng trực tiếp
export { 
  // API functions
  getFacebookAccounts, 
  getFacebookAccountById,
  getFacebookAccountByPageId,
  getAutoReplyConfig,
  getSelectedPageForChatbot,
  upsertFacebookAccount,
  upsertAutoReplyConfig,
  logFacebookActivity,
  
  // Hooks
  useFacebookAccounts,
  useFacebookAccount,
  useAutoReplyConfig,
  useSelectedPageForChatbot,
  useFacebookIntegrationSetup,
  
  // Mutations
  useFacebookAccountMutations,
  useAutoReplyConfigMutations,
  usePageSelectionMutations,
  useFacebookMutations,
  
  // Popup service
  useFacebookPopupAuth,
  useFacebookConnection,
  validateFacebookPermissions,
  formatFacebookError,
  
  // Constants
  FACEBOOK_TABLES,
  ACTIVITY_TYPES,
  REPLY_TONES,
  REPLY_LANGUAGES,
  DEFAULT_CONFIG,
  API_ENDPOINTS,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES
};

/**
 * Main Facebook Integration Service Class
 * Cung cấp interface tổng hợp cho tất cả Facebook operations
 */
export class FacebookIntegrationService {
  constructor() {
    this.tables = FACEBOOK_TABLES;
    this.activityTypes = ACTIVITY_TYPES;
    this.defaultConfig = DEFAULT_CONFIG;
  }

  // Account management
  async getAccounts(options = {}) {
    return getFacebookAccounts(options);
  }

  async getAccountById(accountId) {
    return getFacebookAccountById(accountId);
  }

  async getAccountByPageId(pageId) {
    return getFacebookAccountByPageId(pageId);
  }

  async saveAccount(accountData) {
    return upsertFacebookAccount(accountData);
  }

  // Configuration management
  async getConfig(pageId) {
    return getAutoReplyConfig(pageId);
  }

  async saveConfig(configData) {
    return upsertAutoReplyConfig(configData);
  }

  // Chatbot integration
  async getSelectedPage(chatbotId) {
    return getSelectedPageForChatbot(chatbotId);
  }

  async selectPage(pageId, chatbotId, pageName) {
    return logFacebookActivity(
      pageId,
      ACTIVITY_TYPES.PAGE_SELECTED,
      { chatbotId, pageName }
    );
  }

  // Utility methods
  validatePermissions(grantedPermissions, requiredPermissions) {
    return validateFacebookPermissions(grantedPermissions, requiredPermissions);
  }

  formatError(error) {
    return formatFacebookError(error);
  }

  // Complete setup flow
  async setupIntegration(setupData) {
    const { accountData, configData, chatbotId } = setupData;

    try {
      // 1. Save Facebook account
      const accountResult = await this.saveAccount(accountData);
      if (!accountResult.success) {
        return accountResult;
      }

      // 2. Select page for chatbot
      const selectionResult = await this.selectPage(
        accountData.pageId,
        chatbotId,
        accountData.pageName
      );
      if (!selectionResult.success) {
        return selectionResult;
      }

      // 3. Save configuration if provided
      let configResult = null;
      if (configData) {
        configResult = await this.saveConfig({
          ...configData,
          pageId: accountData.pageId
        });
        if (!configResult.success) {
          return configResult;
        }
      }

      return {
        success: true,
        data: {
          account: accountResult.data,
          config: configResult?.data || null,
          selection: selectionResult.data
        },
        error: null
      };
    } catch (error) {
      return { success: false, error, data: null };
    }
  }
}

// Export singleton instance
export const facebookIntegrationService = new FacebookIntegrationService();
