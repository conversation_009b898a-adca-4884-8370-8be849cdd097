# App Initialization System

Hệ thống khởi tạo ứng dụng được thiết kế để giải quyết các vấn đề về race condition và loading state không đồng bộ giữa authentication và business configuration.

## Vấn đề đã giải quyết

1. **Race Condition**: AuthProvider và BusinessConfigProvider load dữ liệu độc lập
2. **Loading State không đồng bộ**: Components render trước khi tenant_id và business config sẵn sàng
3. **Error Handling**: Không có cơ chế retry và error recovery thống nhất
4. **Data Synchronization**: Business config không được refresh khi tenant_id thay đổi

## Kiến trúc mới

### 1. Enhanced AuthProvider
- Thêm `initialized` flag để track trạng thái initialization
- Improved retry logic với exponential backoff
- Better error handling và cleanup

### 2. Synchronized BusinessConfigProvider
- Lắng nghe thay đổi từ AuthContext
- Auto-reload config khi tenantId thay đổi
- Reset config khi user logout

### 3. AppInitializationWrapper
- Đảm bảo cả auth và business config đều sẵn sàng
- Hiển thị loading state thống nhất
- Error handling với retry mechanism

### 4. Business Config Guards
- Conditional rendering dựa trên business config
- Feature-based và business type-based guards
- Fallback components cho loading states

## Cách sử dụng

### 1. Layout Level (Đã được tích hợp)
```jsx
// src/layouts/dashboard/layout.jsx
import { AppInitializationWrapper } from 'src/components/app-initialization';

export function DashboardLayout({ children }) {
  return (
    <AppInitializationWrapper>
      {/* Layout content */}
    </AppInitializationWrapper>
  );
}
```

### 2. Component Level
```jsx
import { BusinessConfigGuard, FeatureGuard } from 'src/components/app-initialization';

function MyComponent() {
  return (
    <BusinessConfigGuard>
      <FeatureGuard feature="inventoryTracking">
        <InventoryComponent />
      </FeatureGuard>
    </BusinessConfigGuard>
  );
}
```

### 3. Hook Usage
```jsx
import { useAppInitialization } from 'src/hooks/use-app-initialization';

function MyComponent() {
  const { 
    ready, 
    loading, 
    error, 
    businessType, 
    isFeatureEnabled 
  } = useAppInitialization();

  if (loading) return <LoadingComponent />;
  if (error) return <ErrorComponent error={error} />;
  if (!ready) return null;

  return (
    <div>
      <h1>Business Type: {businessType}</h1>
      {isFeatureEnabled('inventory') && <InventoryWidget />}
    </div>
  );
}
```

### 4. Conditional Rendering
```jsx
import { CombinedBusinessGuard } from 'src/components/app-initialization';

function ProductForm() {
  return (
    <div>
      <BasicProductFields />
      
      <CombinedBusinessGuard 
        feature="inventory" 
        allowedTypes={['retail', 'hybrid']}
        fallback={<div>Inventory not available for this business type</div>}
      >
        <InventoryFields />
      </CombinedBusinessGuard>
      
      <CombinedBusinessGuard feature="appointmentBooking">
        <BookingFields />
      </CombinedBusinessGuard>
    </div>
  );
}
```

## API Reference

### Hooks

#### useAppInitialization()
Trả về trạng thái tổng thể của app initialization.

```jsx
const {
  status,           // 'initializing' | 'loading' | 'ready' | 'error'
  ready,            // boolean
  loading,          // boolean
  error,            // Error | null
  user,             // User object
  tenantId,         // string | null
  config,           // Business config object
  businessType,     // string
  isFeatureEnabled, // (feature: string) => boolean
  getEnabledFeatures // () => string[]
} = useAppInitialization();
```

#### useFeatureReady(feature)
Kiểm tra xem app có sẵn sàng cho một feature cụ thể.

#### useBusinessTypeReady(allowedTypes)
Kiểm tra xem app có sẵn sàng cho business types cụ thể.

### Components

#### AppInitializationWrapper
Wrapper chính cho toàn bộ app, đảm bảo initialization hoàn tất.

#### BusinessConfigGuard
Guard cho business configuration, hiển thị loading/error states.

#### FeatureGuard
Conditional rendering dựa trên feature availability.

#### BusinessTypeGuard
Conditional rendering dựa trên business type.

#### CombinedBusinessGuard
Kết hợp tất cả các guards above.

## Best Practices

1. **Sử dụng AppInitializationWrapper ở layout level** để đảm bảo toàn bộ app được initialize
2. **Sử dụng Guards cho conditional rendering** thay vì manual checking
3. **Sử dụng hooks để access initialization state** trong components
4. **Luôn provide fallback components** cho loading và error states
5. **Không bypass initialization system** bằng cách gọi API trực tiếp

## Migration Guide

### Từ useBusinessConfig sang useAppInitialization
```jsx
// Cũ
const { config, loading } = useBusinessConfig();

// Mới
const { config, loading, ready } = useAppInitialization();
```

### Từ manual checking sang Guards
```jsx
// Cũ
const { config } = useBusinessConfig();
if (config?.features?.inventory) {
  return <InventoryComponent />;
}

// Mới
<FeatureGuard feature="inventory">
  <InventoryComponent />
</FeatureGuard>
```

## Troubleshooting

### App không load được data
1. Kiểm tra console logs cho errors
2. Verify tenant_id trong AuthContext
3. Check business config loading state
4. Ensure proper error handling

### Race conditions vẫn xảy ra
1. Đảm bảo sử dụng AppInitializationWrapper
2. Không bypass initialization system
3. Sử dụng proper hooks và guards

### Performance issues
1. Minimize số lần re-render bằng proper memoization
2. Sử dụng MinimalAppInitializationWrapper cho small components
3. Optimize business config queries
