'use client';

import { useState, useEffect, useCallback } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z as zod } from 'zod';

import {
  Box,
  Card,
  Chip,
  Stack,
  Alert,
  Button,
  Dialog,
  Select,
  Switch,
  Divider,
  MenuItem,
  TextField,
  Typography,
  IconButton,
  FormControl,
  InputLabel,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControlLabel,
  CircularProgress,
  Checkbox,
} from '@mui/material';
import { LoadingButton } from '@mui/lab';

import { useBoolean } from 'src/hooks/use-boolean';

import { Iconify } from 'src/components/iconify';
import { toast } from 'src/components/snackbar';

import {
  useLeadFields,
  upsertLeadFields,
  getDefaultFields,
  getFieldTypeOptions,
} from 'src/actions/mooly-chatbot/chatbot-lead-service';

// ----------------------------------------------------------------------

// Schema cho custom field
const CustomFieldSchema = zod.object({
  name: zod.string().min(1, { message: 'Tên field là bắt buộc!' }),
  label: zod.string().min(1, { message: 'Nhãn hiển thị là bắt buộc!' }),
  type: zod.string().min(1, { message: 'Loại field là bắt buộc!' }),
  required: zod.boolean(),
  description: zod.string().min(1, { message: 'Mô tả cho AI là bắt buộc!' }),
});

const defaultCustomFieldValues = {
  name: '',
  label: '',
  type: 'text',
  required: false,
  description: '',
};

// ----------------------------------------------------------------------

export default function ChatbotLeadsTab({ chatbot }) {
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [allFields, setAllFields] = useState([]);
  const [editingField, setEditingField] = useState(null);
  const [editingIndex, setEditingIndex] = useState(null);

  const customFieldDialog = useBoolean();
  const editFieldDialog = useBoolean();

  // Lấy cấu hình fields hiện tại
  const { config, fields, isEnabled, loading, error: configError, mutate: refreshFields } = useLeadFields(chatbot?.id);

  // Form cho custom field
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isDirty },
  } = useForm({
    resolver: zodResolver(CustomFieldSchema),
    defaultValues: defaultCustomFieldValues,
  });

  // Khởi tạo fields khi load lần đầu
  useEffect(() => {
    if (fields && fields.length > 0) {
      setAllFields(fields);
    } else {
      // Nếu chưa có config, hiển thị default fields với enabled = false
      const defaultFields = getDefaultFields();
      setAllFields(defaultFields);
    }
  }, [fields]);

  // Xử lý toggle field enabled/disabled
  const handleToggleField = useCallback(async (fieldIndex, enabled) => {
    try {
      setSaving(true);
      setError(null);

      const newFields = [...allFields];
      newFields[fieldIndex] = { ...newFields[fieldIndex], enabled };

      // Kiểm tra xem có field nào được kích hoạt không
      const hasEnabledFields = newFields.some(field => field.enabled);

      // Lưu vào database
      const result = await upsertLeadFields(chatbot.id, newFields, hasEnabledFields);

      if (result.success) {
        setAllFields(newFields);
        await refreshFields();
        toast.success(enabled ? 'Đã kích hoạt field' : 'Đã tắt field');
      } else {
        setError(result.error);
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setSaving(false);
    }
  }, [allFields, chatbot.id, refreshFields]);

  // Xử lý toggle required
  const handleToggleRequired = useCallback(async (fieldIndex, required) => {
    try {
      setSaving(true);
      setError(null);

      const newFields = [...allFields];
      newFields[fieldIndex] = { ...newFields[fieldIndex], required };

      // Kiểm tra xem có field nào được kích hoạt không
      const hasEnabledFields = newFields.some(field => field.enabled);

      // Lưu vào database
      const result = await upsertLeadFields(chatbot.id, newFields, hasEnabledFields);

      if (result.success) {
        setAllFields(newFields);
        await refreshFields();
        toast.success(required ? 'Đã đặt field là bắt buộc' : 'Đã bỏ yêu cầu bắt buộc');
      } else {
        setError(result.error);
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setSaving(false);
    }
  }, [allFields, chatbot.id, refreshFields]);

  // Xử lý thêm custom field
  const handleAddCustomField = useCallback(async (data) => {
    try {
      setSaving(true);
      setError(null);

      const newField = {
        id: `custom_${Date.now()}`,
        ...data,
        enabled: true, // Mặc định kích hoạt khi thêm mới
      };

      const newFields = [...allFields, newField];
      
      // Tự động enable system vì có field mới được thêm
      const result = await upsertLeadFields(chatbot.id, newFields, true);

      if (result.success) {
        setAllFields(newFields);
        await refreshFields();
        toast.success('Thêm field tùy chỉnh thành công và đã kích hoạt!');
        reset(defaultCustomFieldValues);
        customFieldDialog.onFalse();
      } else {
        setError(result.error);
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setSaving(false);
    }
  }, [allFields, chatbot.id, refreshFields, reset, customFieldDialog]);

  // Xử lý edit field
  const handleEditField = useCallback(async (data) => {
    try {
      setSaving(true);
      setError(null);

      const newFields = [...allFields];
      newFields[editingIndex] = {
        ...newFields[editingIndex],
        ...data,
      };

      // Kiểm tra xem có field nào được kích hoạt không
      const hasEnabledFields = newFields.some(field => field.enabled);

      const result = await upsertLeadFields(chatbot.id, newFields, hasEnabledFields);

      if (result.success) {
        setAllFields(newFields);
        await refreshFields();
        toast.success('Cập nhật field thành công!');
        setEditingField(null);
        setEditingIndex(null);
        editFieldDialog.onFalse();
      } else {
        setError(result.error);
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setSaving(false);
    }
  }, [allFields, editingIndex, chatbot.id, refreshFields, editFieldDialog]);

  // Xử lý xóa field (chỉ custom fields)
  const handleDeleteField = useCallback(async (fieldIndex) => {
    const fieldToDelete = allFields[fieldIndex];
    
    // Không cho xóa default fields
    if (['full_name', 'phone', 'email', 'address', 'company'].includes(fieldToDelete.id)) {
      toast.error('Không thể xóa field mặc định!');
      return;
    }

    if (!window.confirm(`Bạn có chắc chắn muốn xóa field "${fieldToDelete.label}"?`)) {
      return;
    }

    try {
      setSaving(true);
      
      const newFields = allFields.filter((_, index) => index !== fieldIndex);
      
      // Kiểm tra xem có field nào được kích hoạt không
      const hasEnabledFields = newFields.some(field => field.enabled);
      
      const result = await upsertLeadFields(chatbot.id, newFields, hasEnabledFields);

      if (result.success) {
        setAllFields(newFields);
        await refreshFields();
        toast.success('Xóa field thành công!');
      } else {
        setError(result.error);
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setSaving(false);
    }
  }, [allFields, chatbot.id, refreshFields]);

  // Xử lý mở dialog edit
  const handleOpenEditDialog = useCallback((field, index) => {
    setEditingField(field);
    setEditingIndex(index);
    reset(field);
    setError(null);
    editFieldDialog.onTrue();
  }, [reset, editFieldDialog]);

  // Xử lý refresh
  const handleRefresh = useCallback(async () => {
    await refreshFields();
  }, [refreshFields]);

  // Xử lý toggle tổng - bật/tắt toàn bộ tính năng lead collection
  const handleToggleLeadCollection = useCallback(async (enabled) => {
    try {
      setSaving(true);
      setError(null);

      if (enabled) {
        // Nếu bật tính năng, kích hoạt ít nhất basic fields
        const fieldsToEnable = allFields.map(field => {
          // Tự động kích hoạt các field cơ bản khi bật tính năng
          if (['full_name', 'phone', 'email'].includes(field.id)) {
            return { ...field, enabled: true };
          }
          return field;
        });

        const result = await upsertLeadFields(chatbot.id, fieldsToEnable, true);
        if (result.success) {
          setAllFields(fieldsToEnable);
          await refreshFields();
          toast.success('Đã kích hoạt tính năng thu thập lead với các field cơ bản');
        } else {
          setError(result.error);
        }
      } else {
        // Nếu tắt tính năng, tắt tất cả fields
        const fieldsToDisable = allFields.map(field => ({ ...field, enabled: false }));
        
        const result = await upsertLeadFields(chatbot.id, fieldsToDisable, false);
        if (result.success) {
          setAllFields(fieldsToDisable);
          await refreshFields();
          toast.success('Đã tắt tính năng thu thập lead');
        } else {
          setError(result.error);
        }
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setSaving(false);
    }
  }, [allFields, chatbot.id, refreshFields]);

  // Render field item
  const renderFieldItem = (field, index) => {
    const isDefault = ['full_name', 'phone', 'email', 'address', 'company'].includes(field.id);
    
    return (
      <Card key={field.id} sx={{ p: 2 }}>
        <Stack direction="row" alignItems="center" spacing={2}>
          <Switch
            checked={field.enabled}
            onChange={(e) => handleToggleField(index, e.target.checked)}
            disabled={saving}
          />
          
          <Stack flexGrow={1} spacing={1}>
            <Stack direction="row" alignItems="center" spacing={1}>
              <Typography variant="subtitle2">{field.label}</Typography>
              <Chip
                label={getFieldTypeOptions().find(opt => opt.value === field.type)?.label || field.type}
                size="small"
                variant="outlined"
              />
              {field.required && (
                <Chip label="Bắt buộc" size="small" color="error" variant="outlined" />
              )}
              {isDefault && (
                <Chip label="Mặc định" size="small" color="primary" variant="outlined" />
              )}
            </Stack>
            
            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              <strong>Field:</strong> {field.name}
            </Typography>
            
            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              <strong>Mô tả AI:</strong> {field.description}
            </Typography>

            <Stack direction="row" alignItems="center" spacing={2}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={field.required}
                    onChange={(e) => handleToggleRequired(index, e.target.checked)}
                    disabled={saving}
                    size="small"
                  />
                }
                label="Bắt buộc"
                sx={{ margin: 0 }}
              />
            </Stack>
          </Stack>

          <Stack direction="row" spacing={1}>
            <IconButton
              size="small"
              onClick={() => handleOpenEditDialog(field, index)}
              disabled={saving}
            >
              <Iconify icon="solar:pen-bold" />
            </IconButton>
            
            {!isDefault && (
              <IconButton
                size="small"
                color="error"
                onClick={() => handleDeleteField(index)}
                disabled={saving}
              >
                <Iconify icon="solar:trash-bin-trash-bold" />
              </IconButton>
            )}
          </Stack>
        </Stack>
      </Card>
    );
  };

  // Hiển thị loading
  if (loading) {
    return (
      <Box sx={{ py: 10, textAlign: 'center' }}>
        <CircularProgress />
      </Box>
    );
  }

  const enabledCount = allFields.filter(field => field.enabled).length;

  return (
    <Stack spacing={3}>
      {/* Header */}
      <Stack direction="row" alignItems="center" justifyContent="space-between">
        <Box>
          <Typography variant="h6">Kích hoạt thu thập Lead</Typography>
          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
            Đặt tính năng này để chatbot tự động thu thập thông tin khách hàng
          </Typography>
        </Box>
        <Button
          variant="outlined"
          color="inherit"
          startIcon={<Iconify icon="solar:refresh-bold" />}
          onClick={handleRefresh}
        >
          Làm mới
        </Button>
      </Stack>

      {/* Master Switch - Thu thập Lead */}
      <Card sx={{ p: 3 }}>
        <Stack direction="row" alignItems="center" justifyContent="space-between">
          <Stack flexGrow={1}>
            <Stack direction="row" alignItems="center" spacing={2}>
              <Typography variant="h6">
                Thu thập thông tin Lead
              </Typography>
              <Switch
                size="medium"
                checked={isEnabled}
                onChange={(e) => handleToggleLeadCollection(e.target.checked)}
                disabled={saving}
                color="primary"
              />
            </Stack>
            <Typography variant="body2" sx={{ color: 'text.secondary', mt: 1 }}>
              {isEnabled ? (
                <>
                  Chatbot sẽ tự động thu thập thông tin khách hàng tiềm năng trong quá trình hội thoại. 
                  Bạn có thể cấu hình các field cụ thể bên dưới.
                </>
              ) : (
                <>
                  Tính năng thu thập lead đang tắt. Bật để chatbot có thể thu thập thông tin khách hàng 
                  và tự động lưu vào hệ thống CRM.
                </>
              )}
            </Typography>
          </Stack>
          
          <Stack alignItems="center" spacing={1}>
            <Chip
              label={isEnabled ? "ĐANG HOẠT ĐỘNG" : "TẮT"}
              color={isEnabled ? "success" : "default"}
              variant="filled"
              size="small"
              sx={{ fontWeight: 'bold' }}
            />
            {isEnabled && enabledCount > 0 && (
              <Typography variant="caption" sx={{ color: 'success.main' }}>
                {enabledCount} field đang hoạt động
              </Typography>
            )}
          </Stack>
        </Stack>
      </Card>

      {/* Error Alert */}
      {(error || configError) && (
        <Alert severity="error" onClose={() => setError(null)}>
          {error || configError}
        </Alert>
      )}

      {/* Advanced Configuration - Chỉ hiển thị khi tính năng được bật */}
      {isEnabled && (
        <>
          {/* Status Summary */}
          <Card sx={{ p: 3 }}>
            <Stack direction="row" alignItems="center" justifyContent="space-between">
              <Stack>
                <Typography variant="subtitle1">
                  Cấu hình chi tiết
                </Typography>
                <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                  {enabledCount} / {allFields.length} field được kích hoạt để thu thập
                </Typography>
              </Stack>
              
              <Button
                variant="contained"
                startIcon={<Iconify icon="mingcute:add-line" />}
                onClick={customFieldDialog.onTrue}
                disabled={saving}
              >
                Thêm Field Tùy Chỉnh
              </Button>
            </Stack>
          </Card>

          {/* Fields List */}
          <Stack spacing={2}>
            <Typography variant="subtitle1">
              Danh sách Fields để Thu Thập
            </Typography>
            
            {allFields.length > 0 ? (
              <Stack spacing={2}>
                {allFields.map((field, index) => renderFieldItem(field, index))}
              </Stack>
            ) : (
              <Card sx={{ p: 3, textAlign: 'center' }}>
                <Stack spacing={2} alignItems="center">
                  <Iconify icon="solar:inbox-bold" sx={{ width: 48, height: 48, color: 'text.disabled' }} />
                  <Typography variant="h6" sx={{ color: 'text.secondary' }}>
                    Chưa có field nào
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'text.disabled' }}>
                    Thêm field để bắt đầu thu thập thông tin lead từ chatbot AI
                  </Typography>
                </Stack>
              </Card>
            )}
          </Stack>
        </>
      )}

      {/* Hướng dẫn khi tính năng tắt */}
      {!isEnabled && (
        <Card sx={{ p: 4, textAlign: 'center', bgcolor: 'grey.50' }}>
          <Stack spacing={3} alignItems="center">
            <Iconify 
              icon="solar:inbox-line-bold" 
              sx={{ width: 64, height: 64, color: 'text.disabled' }} 
            />
            <Stack spacing={1} alignItems="center">
              <Typography variant="h6" sx={{ color: 'text.secondary' }}>
                Tính năng thu thập Lead đang tắt
              </Typography>
              <Typography variant="body2" sx={{ color: 'text.disabled', maxWidth: 480 }}>
                Kích hoạt switch ở trên để bật tính năng thu thập lead. Chatbot sẽ tự động 
                hỏi và lưu thông tin khách hàng tiềm năng trong quá trình hội thoại.
              </Typography>
            </Stack>
            <Button
              variant="contained"
              size="large"
              startIcon={<Iconify icon="solar:power-bold" />}
              onClick={() => handleToggleLeadCollection(true)}
              disabled={saving}
            >
              Kích hoạt Thu thập Lead
            </Button>
          </Stack>
        </Card>
      )}

      {/* Custom Field Dialog */}
      <Dialog
        open={customFieldDialog.value}
        onClose={customFieldDialog.onFalse}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Thêm Field Tùy Chỉnh</DialogTitle>

        <form onSubmit={handleSubmit(handleAddCustomField)}>
          <DialogContent>
            <Stack spacing={3} sx={{ pt: 1 }}>
              {error && (
                <Alert severity="error" onClose={() => setError(null)}>
                  {error}
                </Alert>
              )}

              <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>
                <Controller
                  name="name"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Tên field"
                      placeholder="custom_field_1, note, budget..."
                      error={!!errors.name}
                      helperText={errors.name?.message || 'Tên field trong database (snake_case)'}
                      fullWidth
                    />
                  )}
                />

                <Controller
                  name="label"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Nhãn hiển thị"
                      placeholder="Ghi chú, Ngân sách..."
                      error={!!errors.label}
                      helperText={errors.label?.message || 'Nhãn hiển thị cho người dùng'}
                      fullWidth
                    />
                  )}
                />
              </Stack>

              <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} alignItems="center">
                <Controller
                  name="type"
                  control={control}
                  render={({ field }) => (
                    <FormControl fullWidth error={!!errors.type}>
                      <InputLabel>Loại field</InputLabel>
                      <Select {...field} label="Loại field">
                        {getFieldTypeOptions().map((option) => (
                          <MenuItem key={option.value} value={option.value}>
                            {option.label}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  )}
                />

                <Controller
                  name="required"
                  control={control}
                  render={({ field }) => (
                    <FormControlLabel
                      control={<Checkbox {...field} checked={field.value} />}
                      label="Bắt buộc"
                      sx={{ minWidth: 120 }}
                    />
                  )}
                />
              </Stack>

              <Controller
                name="description"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Mô tả cho AI"
                    placeholder="Mô tả field này để AI chatbot hiểu và yêu cầu người dùng cung cấp thông tin chính xác..."
                    multiline
                    rows={3}
                    error={!!errors.description}
                    helperText={errors.description?.message || 'Mô tả chi tiết để AI hiểu và yêu cầu thông tin từ người dùng'}
                    fullWidth
                  />
                )}
              />
            </Stack>
          </DialogContent>

          <DialogActions>
            <Button onClick={customFieldDialog.onFalse} disabled={saving}>
              Hủy bỏ
            </Button>
            <LoadingButton
              type="submit"
              variant="contained"
              loading={saving}
              disabled={!isDirty}
            >
              Thêm Field (Tự động kích hoạt)
            </LoadingButton>
          </DialogActions>
        </form>
      </Dialog>

      {/* Edit Field Dialog */}
      <Dialog
        open={editFieldDialog.value}
        onClose={editFieldDialog.onFalse}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Chỉnh Sửa Field</DialogTitle>

        <form onSubmit={handleSubmit(handleEditField)}>
          <DialogContent>
            <Stack spacing={3} sx={{ pt: 1 }}>
              {error && (
                <Alert severity="error" onClose={() => setError(null)}>
                  {error}
                </Alert>
              )}

              <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>
                <Controller
                  name="name"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Tên field"
                      placeholder="custom_field_1, note, budget..."
                      error={!!errors.name}
                      helperText={errors.name?.message || 'Tên field trong database (snake_case)'}
                      fullWidth
                      disabled={editingField && ['full_name', 'phone', 'email', 'address', 'company'].includes(editingField.id)}
                    />
                  )}
                />

                <Controller
                  name="label"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Nhãn hiển thị"
                      placeholder="Ghi chú, Ngân sách..."
                      error={!!errors.label}
                      helperText={errors.label?.message || 'Nhãn hiển thị cho người dùng'}
                      fullWidth
                    />
                  )}
                />
              </Stack>

              <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} alignItems="center">
                <Controller
                  name="type"
                  control={control}
                  render={({ field }) => (
                    <FormControl fullWidth error={!!errors.type}>
                      <InputLabel>Loại field</InputLabel>
                      <Select {...field} label="Loại field">
                        {getFieldTypeOptions().map((option) => (
                          <MenuItem key={option.value} value={option.value}>
                            {option.label}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  )}
                />

                <Controller
                  name="required"
                  control={control}
                  render={({ field }) => (
                    <FormControlLabel
                      control={<Checkbox {...field} checked={field.value} />}
                      label="Bắt buộc"
                      sx={{ minWidth: 120 }}
                    />
                  )}
                />
              </Stack>

              <Controller
                name="description"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Mô tả cho AI"
                    placeholder="Mô tả field này để AI chatbot hiểu và yêu cầu người dùng cung cấp thông tin chính xác..."
                    multiline
                    rows={3}
                    error={!!errors.description}
                    helperText={errors.description?.message || 'Mô tả chi tiết để AI hiểu và yêu cầu thông tin từ người dùng'}
                    fullWidth
                  />
                )}
              />
            </Stack>
          </DialogContent>

          <DialogActions>
            <Button onClick={editFieldDialog.onFalse} disabled={saving}>
              Hủy bỏ
            </Button>
            <LoadingButton
              type="submit"
              variant="contained"
              loading={saving}
              disabled={!isDirty}
            >
              Cập nhật Field
            </LoadingButton>
          </DialogActions>
        </form>
      </Dialog>
    </Stack>
  );
} 