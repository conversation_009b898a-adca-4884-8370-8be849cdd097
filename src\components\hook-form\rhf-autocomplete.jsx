import { useCallback } from 'react';
import { Controller, useFormContext } from 'react-hook-form';

import TextField from '@mui/material/TextField';
import Autocomplete from '@mui/material/Autocomplete';

// ----------------------------------------------------------------------

export function RHFAutocomplete({ name, label, slotProps, helperText, placeholder, ...other }) {
  const { control } = useFormContext();

  const { textfield, ...otherSlotProps } = slotProps ?? {};

  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState: { error } }) => {
        // Xử lý giá trị an toàn cho multiple autocomplete
        const safeValue = other.multiple
          ? (Array.isArray(field.value) ? field.value : [])
          : field.value;

        // Sử dụng useCallback để tránh re-render không cần thiết
        // eslint-disable-next-line react-hooks/rules-of-hooks
        const handleChange = useCallback((event, newValue) => {
          // Đảm bảo giá trị luôn là array khi multiple=true
          const safeNewValue = other.multiple
            ? (Array.isArray(newValue) ? newValue : [])
            : newValue;

          // Sử dụng field.onChange thay vì setValue để tránh vòng lặp
          field.onChange(safeNewValue);
        }, [field, other.multiple]);

        return (
          <Autocomplete
            {...other}
            value={safeValue}
            id={`rhf-autocomplete-${name}`}
            onChange={handleChange}
            onBlur={field.onBlur}
            renderInput={(params) => (
              <TextField
                {...params}
                {...textfield}
                label={label}
                placeholder={placeholder}
                error={!!error}
                helperText={error?.message ?? helperText}
                slotProps={{
                  ...textfield?.slotProps,
                  htmlInput: {
                    ...params.inputProps,
                    autoComplete: 'new-password',
                    ...textfield?.slotProps?.htmlInput,
                  },
                }}
              />
            )}
            {...otherSlotProps}
          />
        );
      }}
    />
  );
}
