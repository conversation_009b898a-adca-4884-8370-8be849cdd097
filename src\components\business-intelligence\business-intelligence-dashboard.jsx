'use client';

import { useState, useEffect } from 'react';

import {
  Box,
  Grid,
  Card,
  Chip,
  Stack,
  Alert,
  Button,
  Select,
  MenuItem,
  Typography,
  InputLabel,
  CardContent,
  FormControl,
  CircularProgress
} from '@mui/material';

import { useBusinessConfigContext } from 'src/actions/mooly-chatbot/business-config-service';
import {
  getBusinessTypeAnalytics
} from 'src/actions/mooly-chatbot/business-intelligence-service';

import { Iconify } from 'src/components/iconify';

/**
 * Business Intelligence Dashboard
 * Hiển thị analytics và insights theo business type
 */
export function BusinessIntelligenceDashboard() {
  const { businessType, loading: configLoading } = useBusinessConfigContext();
  const [timeRange, setTimeRange] = useState('30d');
  const [loading, setLoading] = useState(true);
  const [analytics, setAnalytics] = useState(null);
  const [error, setError] = useState(null);

  // Load analytics data
  useEffect(() => {
    if (configLoading || !businessType) return;

    loadAnalytics();
  }, [businessType, timeRange, configLoading]);

  const loadAnalytics = async () => {
    try {
      setLoading(true);
      setError(null);

      const result = await getBusinessTypeAnalytics(businessType);
      setAnalytics(result);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Time range options
  const timeRangeOptions = [
    { value: '7d', label: '7 ngày qua' },
    { value: '30d', label: '30 ngày qua' },
    { value: '90d', label: '3 tháng qua' },
    { value: '365d', label: '1 năm qua' }
  ];

  if (configLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight={400}>
        <CircularProgress />
      </Box>
    );
  }

  if (!businessType) {
    return (
      <Alert severity="info">
        Vui lòng cấu hình business type để xem analytics chi tiết.
      </Alert>
    );
  }

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight={400}>
        <CircularProgress />
        <Typography variant="body2" sx={{ ml: 2 }}>
          Đang tải dữ liệu analytics...
        </Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" action={
        <Button color="inherit" size="small" onClick={loadAnalytics}>
          Thử lại
        </Button>
      }>
        Lỗi tải dữ liệu: {error}
      </Alert>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 3 }}>
        <Box>
          <Typography variant="h4" gutterBottom>
            Business Intelligence
          </Typography>
          <Stack direction="row" spacing={1} alignItems="center">
            <Chip
              label={getBusinessTypeLabel(businessType)}
              color="primary"
              size="small"
            />
            <Typography variant="body2" color="text.secondary">
              Analytics cho {getBusinessTypeLabel(businessType).toLowerCase()}
            </Typography>
          </Stack>
        </Box>

        <FormControl size="small" sx={{ minWidth: 150 }}>
          <InputLabel>Thời gian</InputLabel>
          <Select
            value={timeRange}
            label="Thời gian"
            onChange={(e) => setTimeRange(e.target.value)}
          >
            {timeRangeOptions.map((option) => (
              <MenuItem key={option.value} value={option.value}>
                {option.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Stack>

      {/* Alerts & Recommendations */}
      {analytics?.insights && (
        <AlertsAndRecommendations insights={analytics.insights} />
      )}

      {/* Analytics Content */}
      <Grid container spacing={3}>
        {/* Sales Analytics */}
        {analytics?.sales && (
          <Grid item size={{xs: 12, md: 6}}>
            <SalesAnalyticsCard data={analytics.sales} />
          </Grid>
        )}

        {/* Product Performance */}
        {analytics?.products && (
          <Grid item size={{xs: 12, md: 6}}>
            <ProductPerformanceCard data={analytics.products} />
          </Grid>
        )}

        {/* Customer Analytics */}
        {analytics?.customers && (
          <Grid item size={{xs: 12, md: 6}}>
            <CustomerAnalyticsCard data={analytics.customers} />
          </Grid>
        )}

        {/* Inventory Analytics (cho Retail/Hybrid) */}
        {analytics?.inventory && (
          <Grid item size={{xs: 12, md: 6}}>
            <InventoryAnalyticsCard data={analytics.inventory} />
          </Grid>
        )}

        {/* Business Type Specific Analytics */}
        {renderBusinessSpecificAnalytics(businessType, analytics)}
      </Grid>
    </Box>
  );
}

/**
 * Alerts and Recommendations Component
 */
function AlertsAndRecommendations({ insights }) {
  if (!insights?.alerts?.length && !insights?.recommendations?.length) {
    return null;
  }

  return (
    <Box sx={{ mb: 3 }}>
      {/* Alerts */}
      {insights.alerts?.map((alert, index) => (
        <Alert key={index} severity="warning" sx={{ mb: 1 }}>
          {alert}
        </Alert>
      ))}

      {/* Recommendations */}
      {insights.recommendations?.map((recommendation, index) => (
        <Alert key={index} severity="info" sx={{ mb: 1 }}>
          <strong>Gợi ý:</strong> {recommendation}
        </Alert>
      ))}
    </Box>
  );
}

/**
 * Sales Analytics Card
 */
function SalesAnalyticsCard({ data }) {
  const { summary } = data;

  return (
    <Card>
      <CardContent>
        <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 2 }}>
          <Iconify icon="solar:chart-2-bold" width={24} />
          <Typography variant="h6">Doanh số bán hàng</Typography>
        </Stack>

        <Grid container spacing={2}>
          <Grid item size={{xs: 6}}>
            <Typography variant="body2" color="text.secondary">
              Tổng doanh thu
            </Typography>
            <Typography variant="h5" color="primary">
              {formatCurrency(summary?.totalRevenue || 0)}
            </Typography>
          </Grid>
          <Grid item size={{xs: 6}}>
            <Typography variant="body2" color="text.secondary">
              Số đơn hàng
            </Typography>
            <Typography variant="h5">
              {summary?.totalOrders || 0}
            </Typography>
          </Grid>
          <Grid item size={{xs: 6}}>
            <Typography variant="body2" color="text.secondary">
              Giá trị TB/đơn
            </Typography>
            <Typography variant="h6">
              {formatCurrency(summary?.avgOrderValue || 0)}
            </Typography>
          </Grid>
          <Grid item size={{xs: 6}}>
            <Typography variant="body2" color="text.secondary">
              Doanh thu TB/ngày
            </Typography>
            <Typography variant="h6">
              {formatCurrency(summary?.dailyAverage || 0)}
            </Typography>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );
}

/**
 * Product Performance Card
 */
function ProductPerformanceCard({ data }) {
  const { insights } = data;
  const topProducts = data.data?.slice(0, 5) || [];

  return (
    <Card>
      <CardContent>
        <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 2 }}>
          <Iconify icon="solar:box-bold" width={24} />
          <Typography variant="h6">Hiệu suất sản phẩm</Typography>
        </Stack>

        {insights && (
          <Box sx={{ mb: 2 }}>
            <Typography variant="body2" color="text.secondary">
              Top performer: {insights.topPerformer?.name || 'N/A'}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Sản phẩm sắp hết: {insights.lowStockCount || 0}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Chưa bán được: {insights.noSalesCount || 0}
            </Typography>
          </Box>
        )}

        <Stack spacing={1}>
          {topProducts.map((product, index) => (
            <Box key={product.id} sx={{ p: 1, bgcolor: 'grey.50', borderRadius: 1 }}>
              <Stack direction="row" justifyContent="space-between">
                <Typography variant="body2" noWrap sx={{ flex: 1 }}>
                  {product.name}
                </Typography>
                <Typography variant="body2" color="primary">
                  {formatCurrency(product.total_revenue || 0)}
                </Typography>
              </Stack>
            </Box>
          ))}
        </Stack>
      </CardContent>
    </Card>
  );
}

/**
 * Customer Analytics Card
 */
function CustomerAnalyticsCard({ data }) {
  const { segments } = data;

  return (
    <Card>
      <CardContent>
        <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 2 }}>
          <Iconify icon="solar:users-group-rounded-bold" width={24} />
          <Typography variant="h6">Phân tích khách hàng</Typography>
        </Stack>

        {segments && (
          <Grid container spacing={2}>
            <Grid item size={{xs: 6}}>
              <Typography variant="body2" color="text.secondary">
                VIP
              </Typography>
              <Typography variant="h6" color="warning.main">
                {segments.vip || 0}
              </Typography>
            </Grid>
            <Grid item size={{xs: 6}}>
              <Typography variant="body2" color="text.secondary">
                Thường xuyên
              </Typography>
              <Typography variant="h6" color="success.main">
                {segments.regular || 0}
              </Typography>
            </Grid>
            <Grid item size={{xs: 6}}>
              <Typography variant="body2" color="text.secondary">
                Khách mới
              </Typography>
              <Typography variant="h6" color="info.main">
                {segments.newCustomers || 0}
              </Typography>
            </Grid>
            <Grid item size={{xs: 6}}>
              <Typography variant="body2" color="text.secondary">
                Không hoạt động
              </Typography>
              <Typography variant="h6" color="error.main">
                {segments.inactive || 0}
              </Typography>
            </Grid>
          </Grid>
        )}
      </CardContent>
    </Card>
  );
}

/**
 * Inventory Analytics Card
 */
function InventoryAnalyticsCard({ data }) {
  const { summary } = data;

  return (
    <Card>
      <CardContent>
        <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 2 }}>
          <Iconify icon="solar:box-minimalistic-bold" width={24} />
          <Typography variant="h6">Phân tích tồn kho</Typography>
        </Stack>

        {summary && (
          <Grid container spacing={2}>
            <Grid item size={{xs: 6}}>
              <Typography variant="body2" color="text.secondary">
                Giá trị tồn kho
              </Typography>
              <Typography variant="h6" color="primary">
                {formatCurrency(summary.totalValue || 0)}
              </Typography>
            </Grid>
            <Grid item size={{xs: 6}}>
              <Typography variant="body2" color="text.secondary">
                Tổng sản phẩm
              </Typography>
              <Typography variant="h6">
                {summary.totalItems || 0}
              </Typography>
            </Grid>
            <Grid item size={{xs: 6}}>
              <Typography variant="body2" color="text.secondary">
                Hết hàng
              </Typography>
              <Typography variant="h6" color="error.main">
                {summary.outOfStock || 0}
              </Typography>
            </Grid>
            <Grid item size={{xs: 6}}>
              <Typography variant="body2" color="text.secondary">
                Sắp hết
              </Typography>
              <Typography variant="h6" color="warning.main">
                {summary.lowStock || 0}
              </Typography>
            </Grid>
          </Grid>
        )}
      </CardContent>
    </Card>
  );
}

// Helper functions
function getBusinessTypeLabel(type) {
  const labels = {
    retail: 'Bán lẻ',
    digital: 'Sản phẩm số',
    services: 'Dịch vụ',
    hybrid: 'Kết hợp'
  };
  return labels[type] || type;
}

function formatCurrency(amount) {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND'
  }).format(amount);
}

function renderBusinessSpecificAnalytics(businessType, analytics) {
  if (!analytics || !businessType) return null;

  switch (businessType) {
    case 'retail':
      return (
        <>
          {analytics.shipping && (
            <Grid item size={{xs: 12, md: 6}}>
              <Card>
                <CardContent>
                  <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 2 }}>
                    <Iconify icon="solar:delivery-bold" width={24} />
                    <Typography variant="h6">Shipping Analytics</Typography>
                  </Stack>
                  <Typography variant="body2" color="text.secondary">
                    Phân tích vận chuyển cho retail business
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          )}
        </>
      );

    case 'digital':
      return (
        <>
          {analytics.downloads && (
            <Grid item size={{xs: 12, md: 6}}>
              <Card>
                <CardContent>
                  <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 2 }}>
                    <Iconify icon="solar:download-bold" width={24} />
                    <Typography variant="h6">Download Analytics</Typography>
                  </Stack>
                  <Typography variant="body2" color="text.secondary">
                    Phân tích downloads cho digital products
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          )}
          {analytics.licenses && (
            <Grid item size={{xs: 12, md: 6}}>
              <Card>
                <CardContent>
                  <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 2 }}>
                    <Iconify icon="solar:key-bold" width={24} />
                    <Typography variant="h6">License Analytics</Typography>
                  </Stack>
                  <Typography variant="body2" color="text.secondary">
                    Phân tích licenses và subscriptions
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          )}
        </>
      );

    case 'services':
      return (
        <>
          {analytics.appointments && (
            <Grid item size={{xs: 12, md: 6}}>
              <Card>
                <CardContent>
                  <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 2 }}>
                    <Iconify icon="solar:calendar-bold" width={24} />
                    <Typography variant="h6">Appointment Analytics</Typography>
                  </Stack>
                  <Typography variant="body2" color="text.secondary">
                    Phân tích lịch hẹn và bookings
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          )}
          {analytics.staff && (
            <Grid item size={{xs: 12, md: 6}}>
              <Card>
                <CardContent>
                  <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 2 }}>
                    <Iconify icon="solar:users-group-rounded-bold" width={24} />
                    <Typography variant="h6">Staff Analytics</Typography>
                  </Stack>
                  <Typography variant="body2" color="text.secondary">
                    Phân tích hiệu suất nhân viên
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          )}
        </>
      );

    case 'hybrid':
      return (
        <Grid item size={{xs: 12}}>
          <Card>
            <CardContent>
              <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 2 }}>
                <Iconify icon="solar:widget-bold" width={24} />
                <Typography variant="h6">Hybrid Business Analytics</Typography>
              </Stack>
              <Typography variant="body2" color="text.secondary">
                Phân tích tổng hợp cho business model kết hợp
              </Typography>
              <Grid container spacing={2} sx={{ mt: 1 }}>
                <Grid item size={{xs: 4}}>
                  <Typography variant="body2" color="primary">
                    Physical Products: {analytics.products?.data?.filter(p => p.product_type === 'simple').length || 0}
                  </Typography>
                </Grid>
                <Grid item size={{xs: 4}}>
                  <Typography variant="body2" color="info.main">
                    Digital Products: {analytics.products?.data?.filter(p => p.product_type === 'digital').length || 0}
                  </Typography>
                </Grid>
                <Grid item size={{xs: 4}}>
                  <Typography variant="body2" color="warning.main">
                    Services: {analytics.products?.data?.filter(p => p.product_type === 'service').length || 0}
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      );

    default:
      return null;
  }
}
