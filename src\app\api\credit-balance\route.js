import { NextResponse } from 'next/server';
import { headers } from 'next/headers';

import { withTenantAuth } from 'src/utils/server-auth';
import { createClient } from 'src/utils/supabase/server';

/**
 * Secure API endpoint để lấy credit balance với database-level security
 * @param {Request} request - Request object
 * @returns {Promise<Response>} - Response object
 */
export const GET = withTenantAuth(async (request, { tenantId, userId }) => {
  try {
    // Rate limiting check
    const headersList = await headers();
    const userAgent = headersList.get('user-agent') || '';
    const forwardedFor = headersList.get('x-forwarded-for') || '';
    
    // Kết nối với Supabase
    const supabase = await createClient();

    // Set request headers for audit logging
    await supabase.rpc('set_config', {
      setting_name: 'request.headers',
      setting_value: JSON.stringify({
        'user-agent': userAgent,
        'x-forwarded-for': forwardedFor
      }),
      is_local: true
    });

    // Sử dụng secure database function
    const { data: result, error } = await supabase.rpc('get_tenant_credit_balance', {
      p_tenant_id: tenantId
    });

    if (error) {
      console.error('Database function error:', error);
      return NextResponse.json(
        { success: false, error: 'Database operation failed' },
        { status: 500 }
      );
    }

    // Check function result
    if (!result || !result.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: result?.error || 'Failed to get credit balance'
        },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        balance: result.balance,
        lastUpdated: result.last_updated,
      },
    });

  } catch (error) {
    console.error('Error getting credit balance:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
});
