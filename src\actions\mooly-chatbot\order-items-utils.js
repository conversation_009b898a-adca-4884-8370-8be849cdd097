'use client';

/**
 * Utility functions cho xử lý order items
 */

/**
 * <PERSON>ẩn hóa dữ liệu order item tr<PERSON><PERSON>c khi gửi lên server
 * @param {Object} item - Order item raw data
 * @returns {Object} - Cleaned order item
 */
export function normalizeOrderItem(item) {
  // Loại bỏ các trường chỉ dùng cho UI
  const { productDetail, variantDetail, product, variant, ...cleanItem } = item;
  
  // Đảm bảo variantId là null thay vì empty string
  if (cleanItem.variantId === '' || cleanItem.variantId === undefined) {
    cleanItem.variantId = null;
  }
  
  // Chuẩn hóa dữ liệu
  return {
    id: cleanItem.id || null, // null cho item mới
    productId: cleanItem.productId,
    variantId: cleanItem.variantId,
    name: (cleanItem.name || '').trim(),
    sku: cleanItem.sku || null,
    quantity: Number(cleanItem.quantity || 0),
    unitPrice: Number(cleanItem.unitPrice || 0),
    totalPrice: Number(cleanItem.totalPrice || 0),
    discountAmount: Number(cleanItem.discountAmount || 0),
    taxAmount: Number(cleanItem.taxAmount || 0),
    imageUrl: cleanItem.imageUrl || null,
    variantInfo: cleanItem.variantInfo || null,
  };
}

/**
 * Validate order item data
 * @param {Object} item - Order item
 * @param {number} index - Item index for error messages
 * @throws {Error} - Validation error
 */
export function validateOrderItem(item, index = 0) {
  const itemLabel = `Item ${index + 1}`;
  
  if (!item.productId) {
    throw new Error(`${itemLabel}: Product ID là bắt buộc`);
  }
  
  if (!item.name || item.name.trim() === '') {
    throw new Error(`${itemLabel}: Tên sản phẩm là bắt buộc`);
  }
  
  if (!item.quantity || item.quantity <= 0) {
    throw new Error(`${itemLabel}: Số lượng phải lớn hơn 0`);
  }
  
  if (item.unitPrice < 0) {
    throw new Error(`${itemLabel}: Đơn giá không được âm`);
  }
  
  if (item.totalPrice < 0) {
    throw new Error(`${itemLabel}: Thành tiền không được âm`);
  }
  
  if (item.discountAmount < 0) {
    throw new Error(`${itemLabel}: Giảm giá không được âm`);
  }
  
  if (item.taxAmount < 0) {
    throw new Error(`${itemLabel}: Thuế không được âm`);
  }
}

/**
 * Kiểm tra duplicate items trong danh sách
 * @param {Array} items - Danh sách order items
 * @throws {Error} - Duplicate error
 */
export function checkDuplicateItems(items) {
  const combinations = items.map(item => `${item.productId || ''}-${item.variantId || ''}`);
  const uniqueCombinations = new Set(combinations);
  
  if (combinations.length !== uniqueCombinations.size) {
    throw new Error('Không được có sản phẩm trùng lặp trong đơn hàng');
  }
}

/**
 * Chuẩn bị dữ liệu order items để gửi lên server
 * @param {Array} rawItems - Raw order items từ form
 * @returns {Array} - Processed order items
 */
export function prepareOrderItemsForSubmit(rawItems) {
  if (!Array.isArray(rawItems)) {
    throw new Error('Order items phải là một mảng');
  }
  
  if (rawItems.length === 0) {
    throw new Error('Đơn hàng phải có ít nhất 1 sản phẩm');
  }
  
  // Chuẩn hóa từng item
  const normalizedItems = rawItems.map(normalizeOrderItem);
  
  // Validate từng item
  normalizedItems.forEach((item, index) => {
    validateOrderItem(item, index);
  });
  
  // Kiểm tra duplicate
  checkDuplicateItems(normalizedItems);
  
  return normalizedItems;
}

/**
 * So sánh hai order items để xác định thay đổi
 * @param {Object} oldItem - Item cũ
 * @param {Object} newItem - Item mới
 * @returns {Object} - Thông tin thay đổi
 */
export function compareOrderItems(oldItem, newItem) {
  const changes = {};
  
  if (oldItem.quantity !== newItem.quantity) {
    changes.quantity = {
      old: oldItem.quantity,
      new: newItem.quantity,
      diff: newItem.quantity - oldItem.quantity
    };
  }
  
  if (oldItem.unitPrice !== newItem.unitPrice) {
    changes.unitPrice = {
      old: oldItem.unitPrice,
      new: newItem.unitPrice
    };
  }
  
  if (oldItem.totalPrice !== newItem.totalPrice) {
    changes.totalPrice = {
      old: oldItem.totalPrice,
      new: newItem.totalPrice
    };
  }
  
  return {
    hasChanges: Object.keys(changes).length > 0,
    changes
  };
}

/**
 * Tính toán tổng tiền từ danh sách order items
 * @param {Array} items - Danh sách order items
 * @returns {Object} - Thông tin tổng tiền
 */
export function calculateOrderTotals(items) {
  const subtotal = items.reduce((sum, item) => sum + (item.totalPrice || 0), 0);
  const totalDiscount = items.reduce((sum, item) => sum + (item.discountAmount || 0), 0);
  const totalTax = items.reduce((sum, item) => sum + (item.taxAmount || 0), 0);
  
  return {
    subtotal,
    totalDiscount,
    totalTax,
    total: subtotal - totalDiscount + totalTax,
    itemCount: items.length,
    totalQuantity: items.reduce((sum, item) => sum + (item.quantity || 0), 0)
  };
}

/**
 * Format order item cho hiển thị
 * @param {Object} item - Order item
 * @returns {Object} - Formatted item
 */
export function formatOrderItemForDisplay(item) {
  return {
    ...item,
    unitPriceFormatted: new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(item.unitPrice || 0),
    totalPriceFormatted: new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(item.totalPrice || 0),
    discountAmountFormatted: new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(item.discountAmount || 0),
  };
}
