-- =====================================================
-- PHASE 3 ADVANCED FEATURES MIGRATION
-- Creates tables for Business Intelligence, Automation Rules,
-- Multi-channel Integration, and Performance Optimization
-- =====================================================

-- =====================================================
-- 1. AUTOMATION RULES TABLES
-- =====================================================

-- Create automation_rules table
CREATE TABLE IF NOT EXISTS automation_rules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    
    -- Rule Details
    name VARCHAR(255) NOT NULL,
    description TEXT,
    automation_type VARCHAR(50) NOT NULL, -- order_status, inventory_alert, customer_notification, etc.
    trigger_type VARCHAR(50) NOT NULL, -- order_created, inventory_low, etc.
    trigger_conditions JSONB DEFAULT '{}',
    actions JSONB DEFAULT '[]',
    
    -- Configuration
    is_active BOOLEAN DEFAULT true,
    business_type_filter VARCHAR(50), -- retail, digital, services, hybrid
    priority INTEGER DEFAULT 1,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CHECK (priority >= 1 AND priority <= 10)
);

-- Create automation_logs table
CREATE TABLE IF NOT EXISTS automation_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    rule_id UUID REFERENCES automation_rules(id) ON DELETE SET NULL,
    
    -- Log Details
    action VARCHAR(100) NOT NULL, -- rule_created, rule_executed, rule_updated, etc.
    details JSONB DEFAULT '{}',
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- 2. MULTI-CHANNEL INTEGRATION TABLES
-- =====================================================

-- Create channel_integrations table
CREATE TABLE IF NOT EXISTS channel_integrations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    
    -- Channel Details
    channel_type VARCHAR(50) NOT NULL, -- shopee, lazada, tiki, sendo, facebook, etc.
    channel_name VARCHAR(255) NOT NULL,
    api_credentials JSONB DEFAULT '{}',
    sync_settings JSONB DEFAULT '{}',
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    sync_status VARCHAR(50) DEFAULT 'pending', -- pending, connected, error
    sync_error TEXT,
    last_sync_at TIMESTAMPTZ,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create sync_logs table
CREATE TABLE IF NOT EXISTS sync_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    integration_id UUID NOT NULL REFERENCES channel_integrations(id) ON DELETE CASCADE,
    
    -- Sync Details
    sync_type VARCHAR(50) NOT NULL, -- products, inventory, orders, customers, pricing
    direction VARCHAR(20) NOT NULL, -- import, export, bidirectional
    result JSONB DEFAULT '{}',
    
    -- Timestamps
    synced_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- 3. BUSINESS INTELLIGENCE TABLES
-- =====================================================

-- Create business_analytics_cache table (for caching analytics results)
CREATE TABLE IF NOT EXISTS business_analytics_cache (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    
    -- Cache Details
    cache_key VARCHAR(255) NOT NULL,
    cache_type VARCHAR(50) NOT NULL, -- sales, products, customers, inventory
    business_type VARCHAR(50),
    time_range VARCHAR(20), -- 7d, 30d, 90d, 1y
    
    -- Cached Data
    data JSONB NOT NULL,
    
    -- Cache Management
    expires_at TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Indexes for fast lookup
    UNIQUE(tenant_id, cache_key, cache_type, business_type, time_range)
);

-- =====================================================
-- 4. PERFORMANCE OPTIMIZATION TABLES
-- =====================================================

-- Create performance_reports table
CREATE TABLE IF NOT EXISTS performance_reports (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    
    -- Report Details
    overall_score INTEGER NOT NULL CHECK (overall_score >= 0 AND overall_score <= 100),
    db_performance JSONB DEFAULT '{}',
    query_analysis JSONB DEFAULT '{}',
    index_analysis JSONB DEFAULT '{}',
    caching_analysis JSONB DEFAULT '{}',
    recommendations JSONB DEFAULT '[]',
    
    -- Timestamps
    generated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- 5. DIGITAL PRODUCT SPECIFIC TABLES (for Business Intelligence)
-- =====================================================

-- Create digital_downloads table (for tracking downloads)
CREATE TABLE IF NOT EXISTS digital_downloads (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    customer_id UUID REFERENCES customers(id) ON DELETE SET NULL,
    
    -- Download Details
    download_url TEXT,
    file_size BIGINT, -- in bytes
    download_count INTEGER DEFAULT 1,
    
    -- Timestamps
    downloaded_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create product_licenses table (for digital products)
CREATE TABLE IF NOT EXISTS product_licenses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    customer_id UUID REFERENCES customers(id) ON DELETE SET NULL,
    
    -- License Details
    license_key VARCHAR(255) UNIQUE NOT NULL,
    license_type VARCHAR(50) NOT NULL, -- single, subscription, enterprise
    status VARCHAR(20) DEFAULT 'active', -- active, expired, revoked
    
    -- Validity
    expires_at TIMESTAMPTZ,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- 6. SERVICE BUSINESS SPECIFIC TABLES
-- =====================================================

-- Create appointments table (for service businesses)
CREATE TABLE IF NOT EXISTS appointments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    customer_id UUID REFERENCES customers(id) ON DELETE SET NULL,
    staff_id UUID REFERENCES staff(id) ON DELETE SET NULL,
    
    -- Appointment Details
    service_name VARCHAR(255) NOT NULL,
    appointment_date TIMESTAMPTZ NOT NULL,
    duration_minutes INTEGER NOT NULL DEFAULT 60,
    status VARCHAR(20) DEFAULT 'scheduled', -- scheduled, completed, cancelled, no_show
    
    -- Pricing
    price NUMERIC(10,2),
    
    -- Notes
    notes TEXT,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create staff table (for service businesses)
CREATE TABLE IF NOT EXISTS staff (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    
    -- Staff Details
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(20),
    position VARCHAR(100),
    
    -- Availability
    is_active BOOLEAN DEFAULT true,
    working_hours JSONB DEFAULT '{}', -- schedule configuration
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- 7. INDEXES FOR PERFORMANCE
-- =====================================================

-- Automation Rules indexes
CREATE INDEX IF NOT EXISTS idx_automation_rules_tenant_type ON automation_rules(tenant_id, automation_type);
CREATE INDEX IF NOT EXISTS idx_automation_rules_active ON automation_rules(tenant_id, is_active);
CREATE INDEX IF NOT EXISTS idx_automation_logs_rule_id ON automation_logs(rule_id);

-- Channel Integration indexes
CREATE INDEX IF NOT EXISTS idx_channel_integrations_tenant_type ON channel_integrations(tenant_id, channel_type);
CREATE INDEX IF NOT EXISTS idx_channel_integrations_active ON channel_integrations(tenant_id, is_active);
CREATE INDEX IF NOT EXISTS idx_sync_logs_integration ON sync_logs(integration_id, synced_at);

-- Business Intelligence indexes
CREATE INDEX IF NOT EXISTS idx_analytics_cache_lookup ON business_analytics_cache(tenant_id, cache_key, cache_type);
CREATE INDEX IF NOT EXISTS idx_analytics_cache_expires ON business_analytics_cache(expires_at);

-- Performance Reports indexes
CREATE INDEX IF NOT EXISTS idx_performance_reports_tenant ON performance_reports(tenant_id, generated_at);

-- Digital Products indexes
CREATE INDEX IF NOT EXISTS idx_digital_downloads_product ON digital_downloads(product_id, downloaded_at);
CREATE INDEX IF NOT EXISTS idx_digital_downloads_customer ON digital_downloads(customer_id, downloaded_at);
CREATE INDEX IF NOT EXISTS idx_product_licenses_product ON product_licenses(product_id, status);
CREATE INDEX IF NOT EXISTS idx_product_licenses_customer ON product_licenses(customer_id, status);

-- Service Business indexes
CREATE INDEX IF NOT EXISTS idx_appointments_date ON appointments(tenant_id, appointment_date);
CREATE INDEX IF NOT EXISTS idx_appointments_customer ON appointments(customer_id, appointment_date);
CREATE INDEX IF NOT EXISTS idx_appointments_staff ON appointments(staff_id, appointment_date);
CREATE INDEX IF NOT EXISTS idx_staff_tenant_active ON staff(tenant_id, is_active);

-- =====================================================
-- 8. TRIGGERS FOR UPDATED_AT
-- =====================================================

-- Automation Rules trigger
CREATE OR REPLACE FUNCTION update_automation_rules_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_automation_rules_updated_at
    BEFORE UPDATE ON automation_rules
    FOR EACH ROW
    EXECUTE FUNCTION update_automation_rules_updated_at();

-- Channel Integrations trigger
CREATE OR REPLACE FUNCTION update_channel_integrations_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_channel_integrations_updated_at
    BEFORE UPDATE ON channel_integrations
    FOR EACH ROW
    EXECUTE FUNCTION update_channel_integrations_updated_at();

-- Product Licenses trigger
CREATE OR REPLACE FUNCTION update_product_licenses_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_product_licenses_updated_at
    BEFORE UPDATE ON product_licenses
    FOR EACH ROW
    EXECUTE FUNCTION update_product_licenses_updated_at();

-- Appointments trigger
CREATE OR REPLACE FUNCTION update_appointments_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_appointments_updated_at
    BEFORE UPDATE ON appointments
    FOR EACH ROW
    EXECUTE FUNCTION update_appointments_updated_at();

-- Staff trigger
CREATE OR REPLACE FUNCTION update_staff_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_staff_updated_at
    BEFORE UPDATE ON staff
    FOR EACH ROW
    EXECUTE FUNCTION update_staff_updated_at();

-- =====================================================
-- 9. COMMENTS
-- =====================================================

COMMENT ON TABLE automation_rules IS 'Automation rules for business process automation';
COMMENT ON TABLE automation_logs IS 'Logs for automation rule activities';
COMMENT ON TABLE channel_integrations IS 'Multi-channel platform integrations';
COMMENT ON TABLE sync_logs IS 'Logs for channel synchronization activities';
COMMENT ON TABLE business_analytics_cache IS 'Cache for business intelligence analytics';
COMMENT ON TABLE performance_reports IS 'Performance optimization reports';
COMMENT ON TABLE digital_downloads IS 'Download tracking for digital products';
COMMENT ON TABLE product_licenses IS 'License management for digital products';
COMMENT ON TABLE appointments IS 'Appointment scheduling for service businesses';
COMMENT ON TABLE staff IS 'Staff management for service businesses';
