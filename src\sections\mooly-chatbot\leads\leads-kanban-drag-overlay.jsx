'use client';

import { useId } from 'react';
import { DragOverlay as DndDragOverlay, defaultDropAnimationSideEffects } from '@dnd-kit/core';

import Portal from '@mui/material/Portal';

import ItemBase from 'src/sections/kanban/item/item-base';
import ColumnBase from 'src/sections/kanban/column/column-base';

import LeadsKanbanColumnToolbar from './leads-kanban-column-toolbar';

// ----------------------------------------------------------------------

const dropAnimation = {
  sideEffects: defaultDropAnimationSideEffects({
    styles: {
      active: {
        opacity: '0.5',
      },
    },
  }),
};

export default function LeadsKanbanDragOverlay({ columns, tasks, activeId, sx }) {
  const key = useId();

  const columnIds = columns.map((column) => column.id);
  const activeColumn = columns.find((column) => column.id === activeId);

  const allTasks = Object.values(tasks).flat();
  const activeTask = allTasks.find((task) => task.id === activeId);

  return (
    <Portal>
      <DndDragOverlay adjustScale={false} dropAnimation={dropAnimation}>
        {activeId != null ? (
          columnIds.includes(activeId) ? (
            <ColumnOverlay key={key} column={activeColumn} tasks={tasks[activeId]} sx={sx} />
          ) : (
            <TaskItemOverlay key={key} task={activeTask} sx={sx} />
          )
        ) : null}
      </DndDragOverlay>
    </Portal>
  );
}

// ----------------------------------------------------------------------

function ColumnOverlay({ column, tasks, sx }) {
  return (
    <ColumnBase
      slots={{
        header: (
          <LeadsKanbanColumnToolbar 
            columnName={column.name} 
            columnColor={column.color}
            totalTasks={tasks.length}
            disableActions
          />
        ),
        main: tasks.map((task) => <ItemBase key={task.id} task={task} />),
      }}
      stateProps={{ dragOverlay: true }}
      sx={sx}
    />
  );
}

// ----------------------------------------------------------------------

function TaskItemOverlay({ task, sx }) {
  return <ItemBase task={task} sx={sx} stateProps={{ dragOverlay: true }} />;
} 