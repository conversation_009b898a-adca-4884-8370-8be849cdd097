'use client';

import { useState } from 'react';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import CardContent from '@mui/material/CardContent';

import { DashboardContent } from 'src/layouts/dashboard';

import { toast } from 'src/components/snackbar';

import { useAuthContext } from 'src/auth/hooks';
import { useProfileMutations } from 'src/actions/mooly-chatbot/profile-service';

// ----------------------------------------------------------------------

export default function AccountTestPage() {
  const { user } = useAuthContext();
  const { updateProfile, updatePassword, isUpdating, isUpdatingPassword } = useProfileMutations();
  const [testResults, setTestResults] = useState([]);

  const addTestResult = (test, success, message) => {
    setTestResults(prev => [...prev, { test, success, message, timestamp: new Date() }]);
  };

  const testProfileUpdate = async () => {
    try {
      const testData = {
        displayName: 'Test User Updated',
        phoneNumber: '**********',
        about: 'Test profile update',
      };

      const result = await updateProfile(user?.id, testData);

      if (result.success) {
        addTestResult('Profile Update', true, 'Cập nhật profile thành công');
        toast.success('Test profile update thành công!');
      } else {
        addTestResult('Profile Update', false, result.error || 'Lỗi không xác định');
        toast.error('Test profile update thất bại');
      }
    } catch (error) {
      addTestResult('Profile Update', false, error.message);
      toast.error('Test profile update lỗi');
    }
  };

  const testStorageBucket = async () => {
    try {
      const { createClient } = await import('src/utils/supabase/client');
      const supabase = createClient();

      // Test bucket access
      const { data, error } = await supabase.storage.from('user-files').list('avatars', {
        limit: 1
      });

      if (error) {
        addTestResult('Storage Bucket', false, `Bucket error: ${error.message}`);
        toast.error('Storage bucket test thất bại');
      } else {
        addTestResult('Storage Bucket', true, 'Bucket user-files accessible');
        toast.success('Storage bucket test thành công!');
      }
    } catch (error) {
      addTestResult('Storage Bucket', false, error.message);
      toast.error('Storage bucket test lỗi');
    }
  };

  const testPasswordUpdate = async () => {
    try {
      const result = await updatePassword('', 'newpassword123');
      
      if (result.success) {
        addTestResult('Password Update', true, 'Cập nhật mật khẩu thành công');
        toast.success('Test password update thành công!');
      } else {
        addTestResult('Password Update', false, result.error || 'Lỗi không xác định');
        toast.error('Test password update thất bại');
      }
    } catch (error) {
      addTestResult('Password Update', false, error.message);
      toast.error('Test password update lỗi');
    }
  };

  return (
    <DashboardContent>
      <Typography variant="h4" sx={{ mb: 3 }}>
        Account System Test
      </Typography>

      <Box sx={{ display: 'flex', gap: 3, flexDirection: 'column' }}>
        <Card>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2 }}>
              User Information
            </Typography>
            <Typography variant="body2">ID: {user?.id}</Typography>
            <Typography variant="body2">Email: {user?.email}</Typography>
            <Typography variant="body2">Display Name: {user?.displayName}</Typography>
            <Typography variant="body2">Phone: {user?.phoneNumber}</Typography>
            <Typography variant="body2">Avatar: {user?.photoURL}</Typography>
          </CardContent>
        </Card>

        <Card>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2 }}>
              Test Functions
            </Typography>
            <Box sx={{ display: 'flex', gap: 2, mb: 2, flexWrap: 'wrap' }}>
              <Button
                variant="contained"
                onClick={testProfileUpdate}
                loading={isUpdating}
                disabled={!user?.id}
              >
                Test Profile Update
              </Button>
              <Button
                variant="contained"
                onClick={testPasswordUpdate}
                loading={isUpdatingPassword}
                disabled={!user?.id}
              >
                Test Password Update
              </Button>
              <Button
                variant="outlined"
                onClick={testStorageBucket}
                disabled={!user?.id}
              >
                Test Storage Bucket
              </Button>
            </Box>
          </CardContent>
        </Card>

        <Card>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2 }}>
              Test Results
            </Typography>
            {testResults.length === 0 ? (
              <Typography variant="body2" color="text.secondary">
                Chưa có test nào được chạy
              </Typography>
            ) : (
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                {testResults.map((result, index) => (
                  <Box
                    key={index}
                    sx={{
                      p: 2,
                      border: 1,
                      borderRadius: 1,
                      borderColor: result.success ? 'success.main' : 'error.main',
                      bgcolor: result.success ? 'success.lighter' : 'error.lighter',
                    }}
                  >
                    <Typography variant="subtitle2">
                      {result.test} - {result.success ? 'SUCCESS' : 'FAILED'}
                    </Typography>
                    <Typography variant="body2">{result.message}</Typography>
                    <Typography variant="caption" color="text.secondary">
                      {result.timestamp.toLocaleString()}
                    </Typography>
                  </Box>
                ))}
              </Box>
            )}
          </CardContent>
        </Card>
      </Box>
    </DashboardContent>
  );
}
