/**
 * Facebook Integration Constants
 * <PERSON><PERSON><PERSON> nghĩa các hằng số cho Facebook integration
 */

// Table names
export const FACEBOOK_TABLES = {
  ACCOUNTS: 'facebook_accounts',
  CONFIG: 'facebook_auto_reply_config',
  ACTIVITY_LOGS: 'facebook_activity_logs'
};

// Import từ config tập trung
import { FACEBOOK_CONFIG } from 'src/lib/facebook-config';

// Facebook permissions - Sử dụng từ config tập trung
export const FACEBOOK_PERMISSIONS = FACEBOOK_CONFIG.PERMISSIONS;

// Activity types
export const ACTIVITY_TYPES = {
  PAGE_CONNECTED: 'page_connected',
  PAGE_SELECTED: 'chatbot_page_selected',
  CONFIG_UPDATED: 'config_updated',
  CONFIG_DELETED: 'config_deleted',
  FEATURES_UPDATED: 'features_updated',
  WEBHOOK_SUBSCRIBED: 'webhook_subscribed',
  AUTO_REPLY_SENT: 'auto_reply_sent',
  ERROR_OCCURRED: 'error_occurred'
};

// Reply tones
export const REPLY_TONES = {
  FRIENDLY: 'friendly',
  PROFESSIONAL: 'professional',
  CASUAL: 'casual',
  FORMAL: 'formal',
  ENTHUSIASTIC: 'enthusiastic'
};

// Reply languages
export const REPLY_LANGUAGES = {
  VI: 'vi',
  EN: 'en'
};

// Default configuration
export const DEFAULT_CONFIG = {
  enableCommentReply: false,
  enableMessageReply: false,
  enableInstagramComments: false,
  enableInstagramMessages: false,
  autoPrivateReply: false,
  replyPrompt: '',
  replyTone: REPLY_TONES.FRIENDLY,
  replyLanguage: REPLY_LANGUAGES.VI,
  maxReplyLength: 500,
  businessInfo: '',
  products: '',
  policies: '',
  excludeKeywords: []
};

// API endpoints
export const API_ENDPOINTS = {
  PAGES: '/api/facebook-integration/pages',
  CONFIG: '/api/facebook-integration/config',
  AUTO_REPLY_CONFIG: '/api/facebook-integration/auto-reply-config',
  FEATURES: '/api/facebook-integration/features',
  POPUP_CALLBACK: '/api/facebook-integration/popup-callback',
  SUBSCRIBE_WEBHOOKS: '/api/facebook-integration/subscribe-webhooks'
};

// Error messages
export const ERROR_MESSAGES = {
  NO_PAGES: 'Không tìm thấy Facebook Page nào. Vui lòng đảm bảo bạn là admin của ít nhất một Page.',
  POPUP_BLOCKED: 'Popup bị chặn. Vui lòng cho phép popup từ trang này.',
  CONNECTION_FAILED: 'Kết nối Facebook thất bại. Vui lòng thử lại.',
  SAVE_CONFIG_FAILED: 'Lưu cấu hình thất bại. Vui lòng thử lại.',
  PAGE_NOT_FOUND: 'Không tìm thấy Facebook Page.',
  UNAUTHORIZED: 'Bạn không có quyền truy cập.',
  INVALID_TOKEN: 'Token không hợp lệ hoặc đã hết hạn.'
};

// Success messages
export const SUCCESS_MESSAGES = {
  PAGE_CONNECTED: 'Kết nối Facebook Page thành công!',
  PAGE_SELECTED: 'Đã chọn Facebook Page thành công!',
  CONFIG_SAVED: 'Đã lưu cấu hình AI thành công!',
  FEATURES_SAVED: 'Đã lưu cấu hình tính năng thành công!',
  WEBHOOKS_SUBSCRIBED: 'Đã đăng ký webhook thành công!'
};

// Facebook API configuration
export const FACEBOOK_API = {
  BASE_URL: 'https://graph.facebook.com',
  VERSION: 'v23.0',
  OAUTH_URL: 'https://www.facebook.com/v23.0/dialog/oauth'
};

// Popup configuration
export const POPUP_CONFIG = {
  WIDTH: 600,
  HEIGHT: 700,
  TIMEOUT: 5 * 60 * 1000, // 5 minutes
  POLL_INTERVAL: 1000 // 1 second
};
