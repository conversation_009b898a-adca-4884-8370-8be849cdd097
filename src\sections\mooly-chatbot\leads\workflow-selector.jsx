'use client';

import { useState, useEffect } from 'react';

import {
  <PERSON>,
  <PERSON>,
  Stack,
  Button,
  Typography,
  <PERSON>,
  Alert,
  Skeleton,
  Divider,
} from '@mui/material';
import { alpha } from '@mui/material/styles';

import { Iconify } from 'src/components/iconify';
import { Label } from 'src/components/label';

import { useGlobalWorkflow } from 'src/actions/mooly-chatbot/workflow-config-service';

// =====================================================
// COMPONENT
// =====================================================

export default function WorkflowSelector({ 
  onWorkflowBuilderOpen,
}) {
  // Load global workflow (không cần chatbot selection nữa)
  const { workflow, loading: workflowLoading, mutate } = useGlobalWorkflow();

  const renderWorkflowPreview = () => {
    if (workflowLoading) {
      return (
        <Box sx={{ p: 2 }}>
          <Skeleton variant="text" width="60%" />
          <Skeleton variant="rectangular" height={60} sx={{ mt: 1 }} />
        </Box>
      );
    }

    if (!workflow) {
      return (
        <Alert 
          severity="info" 
          action={
            <Button
              size="small"
              variant="outlined"
              startIcon={<Iconify icon="solar:widget-5-bold" />}
              onClick={onWorkflowBuilderOpen}
            >
              Thiết kế Workflow
            </Button>
          }
        >
          Hệ thống chưa có workflow chung. Hãy thiết kế workflow để quản lý leads hiệu quả hơn.
        </Alert>
      );
    }

    return (
      <Card sx={{ p: 2, bgcolor: 'grey.50' }}>
        <Stack spacing={2}>
          <Stack direction="row" alignItems="center" justifyContent="space-between">
            <Typography variant="subtitle2" fontWeight="600">
              {workflow.name}
            </Typography>
            <Button
              size="small"
              startIcon={<Iconify icon="solar:pen-bold" />}
              onClick={onWorkflowBuilderOpen}
            >
              Chỉnh sửa
            </Button>
          </Stack>

          {workflow.description && (
            <Typography variant="body2" color="text.secondary">
              {workflow.description}
            </Typography>
          )}

          <Box>
            <Typography variant="caption" color="text.secondary" gutterBottom>
              Các giai đoạn workflow:
            </Typography>
            <Stack direction="row" spacing={1} flexWrap="wrap" sx={{ mt: 1 }}>
              {workflow.stages?.map((stage, index) => (
                <Chip
                  key={stage.id}
                  label={`${index + 1}. ${stage.name}`}
                  size="small"
                  variant="outlined"
                  color={workflow.colors?.[stage.id] || 'default'}
                  icon={
                    workflow.icons?.[stage.id] ? (
                      <Iconify icon={workflow.icons[stage.id]} width={16} />
                    ) : null
                  }
                />
              )) || []}
            </Stack>
          </Box>

          {workflow.stages?.length > 0 && (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Label color="success" variant="soft" size="small">
                {workflow.stages.length} giai đoạn
              </Label>
              <Label color="info" variant="soft" size="small">
                Workflow chung hệ thống
              </Label>
            </Box>
          )}
        </Stack>
      </Card>
    );
  };

  return (
    <Card sx={{ p: 3 }}>
      <Stack spacing={3}>
        <Stack direction="row" alignItems="center" spacing={2}>
          <Iconify icon="solar:settings-bold" width={24} color="primary.main" />
          <Typography variant="h6">Workflow Chung Hệ Thống</Typography>
        </Stack>

        <Alert severity="info" sx={{ mb: 2 }}>
          <Typography variant="body2">
            <strong>Workflow chung:</strong> Tất cả leads trong hệ thống sẽ sử dụng workflow này. 
            Chatbot ID chỉ dùng để tracking hiệu quả từng chatbot.
          </Typography>
        </Alert>

        {/* Workflow Preview */}
        <Box>
          <Typography variant="subtitle2" gutterBottom>
            Workflow hiện tại
          </Typography>
          {renderWorkflowPreview()}
        </Box>

        {/* Quick Actions */}
        <Stack direction="row" spacing={2}>
          <Button
            variant="contained"
            startIcon={<Iconify icon="solar:widget-5-bold" />}
            onClick={onWorkflowBuilderOpen}
            fullWidth
          >
            {workflow ? 'Chỉnh sửa Workflow' : 'Thiết kế Workflow'}
          </Button>
          
          {workflow && (
            <Button
              variant="outlined"
              startIcon={<Iconify icon="solar:refresh-bold" />}
              onClick={() => mutate()}
            >
              Làm mới
            </Button>
          )}
        </Stack>
      </Stack>
    </Card>
  );
} 