'use client';

import { useState, useMemo, useEffect, useCallback } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z as zod } from 'zod';

import {
  Box,
  Stack,
  Dialog,
  Button,
  TextField,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Typography,
  Divider,
} from '@mui/material';
import { LoadingButton } from '@mui/lab';

import { useAuthContext } from 'src/auth/hooks';

import { toast } from 'src/components/snackbar';
import { Scrollbar } from 'src/components/scrollbar';

import { 
  createLead, 
  getStatusOptionsFromWorkflow
} from 'src/actions/mooly-chatbot/chatbot-lead-service';
import { 
  useChatbotWorkflowStages
} from 'src/actions/mooly-chatbot/unified-workflow-service';

// =====================================================
// VALIDATION SCHEMA
// =====================================================

const LeadSchema = zod.object({
  fullName: zod.string().min(1, 'Họ và tên là bắt buộc'),
  phone: zod.string().min(1, 'Số điện thoại là bắt buộc'),
  email: zod.string().email('Email không hợp lệ').optional().or(zod.literal('')),
  status: zod.string().min(1, 'Vui lòng chọn trạng thái'),
  source: zod.string().min(1, 'Vui lòng chọn nguồn'),
  notes: zod.string().optional(),
  leadScore: zod.number().min(0).max(100).optional(),
});

// =====================================================
// COMPONENT
// =====================================================

export default function LeadNewDialog({ open, onClose, onSuccess }) {
  const { user } = useAuthContext();
  const [loading, setLoading] = useState(false);

  // Load workflow stages để lấy status options đồng bộ
  const { stages } = useChatbotWorkflowStages(null);

  // Form setup
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(LeadSchema),
    defaultValues: {
      fullName: '',
      phone: '',
      email: '',
      status: 'new',
      source: 'manual',
      notes: '',
      leadScore: 0,
    },
  });

  // Status options từ workflow - đồng bộ với kanban view và edit dialog
  const statusOptions = useMemo(() => getStatusOptionsFromWorkflow(stages), [stages]);

  // Source options
  const sourceOptions = useMemo(() => [
    { value: 'manual', label: 'Thủ công' },
    { value: 'website', label: 'Website' },
    { value: 'facebook', label: 'Facebook' },
    { value: 'zalo', label: 'Zalo' },
    { value: 'phone', label: 'Điện thoại' },
    { value: 'referral', label: 'Giới thiệu' },
    { value: 'other', label: 'Khác' },
  ], []);

  // Reset form when dialog opens/closes
  useEffect(() => {
    if (!open) {
      reset();
    }
  }, [open, reset]);

  // Handle form submission
  const onSubmit = useCallback(async (data) => {
    setLoading(true);
    try {
      // Chuẩn bị dữ liệu lead - supabase-utils tự động convert camelCase -> snake_case
      const leadData = {
        tenantId: user.tenantId,
        chatbotId: null, // Cho phép null cho lead tạo thủ công
        fullName: data.fullName,
        phone: data.phone,
        email: data.email || null,
        status: data.status,
        source: data.source,
        notes: data.notes || null,
        leadScore: data.leadScore || 0,
        // leadData JSONB sẽ được tạo từ các field trên
        leadData: {
          fullName: data.fullName,
          email: data.email || null,
          phone: data.phone,
          notes: data.notes || null,
          source: data.source,
          status: data.status,
          leadScore: data.leadScore || 0,
        },
      };

      const result = await createLead(leadData);

      if (result.success) {
        toast.success('Tạo lead thành công!');
        onSuccess?.(result.data);
        onClose();
        reset();
      } else {
        console.error('Create lead error:', result.error);
        toast.error(result.error?.message || 'Có lỗi xảy ra khi tạo lead');
      }
    } catch (error) {
      console.error('Create lead error:', error);
      toast.error('Có lỗi xảy ra khi tạo lead');
    } finally {
      setLoading(false);
    }
  }, [user?.tenantId, onSuccess, onClose, reset]);

  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          maxHeight: '90vh',
          display: 'flex',
          flexDirection: 'column',
        },
      }}
    >
      <DialogTitle>
        <Typography variant="h6">Tạo Lead Mới</Typography>
      </DialogTitle>

      <DialogContent 
        sx={{ 
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          p: 0,
        }}
      >
        <Scrollbar sx={{ maxHeight: '60vh', px: 3, py: 2 }}>
          <form onSubmit={handleSubmit(onSubmit)}>
            <Stack spacing={3}>
              {/* THÔNG TIN BẮT BUỘC */}
              <Typography variant="h6" color="primary">
                Thông tin bắt buộc
              </Typography>

              <Grid container spacing={2}>
                <Grid size={{ xs: 12, md: 6 }}>
                  <Controller
                    name="fullName"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        fullWidth
                        label="Họ và tên *"
                        error={!!errors.fullName}
                        helperText={errors.fullName?.message}
                      />
                    )}
                  />
                </Grid>

                <Grid size={{ xs: 12, md: 6 }}>
                  <Controller
                    name="phone"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        fullWidth
                        label="Số điện thoại *"
                        error={!!errors.phone}
                        helperText={errors.phone?.message}
                      />
                    )}
                  />
                </Grid>

                <Grid size={{ xs: 12, md: 6 }}>
                  <Controller
                    name="status"
                    control={control}
                    render={({ field }) => (
                      <FormControl fullWidth error={!!errors.status}>
                        <InputLabel>Trạng thái *</InputLabel>
                        <Select
                          {...field}
                          label="Trạng thái *"
                        >
                          {statusOptions.map((option) => (
                            <MenuItem key={option.value} value={option.value}>
                              {option.label}
                            </MenuItem>
                          ))}
                        </Select>
                        {errors.status && (
                          <Typography variant="caption" color="error" sx={{ mt: 0.5 }}>
                            {errors.status.message}
                          </Typography>
                        )}
                      </FormControl>
                    )}
                  />
                </Grid>

                <Grid size={{ xs: 12, md: 6 }}>
                  <Controller
                    name="source"
                    control={control}
                    render={({ field }) => (
                      <FormControl fullWidth error={!!errors.source}>
                        <InputLabel>Nguồn *</InputLabel>
                        <Select
                          {...field}
                          label="Nguồn *"
                        >
                          {sourceOptions.map((option) => (
                            <MenuItem key={option.value} value={option.value}>
                              {option.label}
                            </MenuItem>
                          ))}
                        </Select>
                        {errors.source && (
                          <Typography variant="caption" color="error" sx={{ mt: 0.5 }}>
                            {errors.source.message}
                          </Typography>
                        )}
                      </FormControl>
                    )}
                  />
                </Grid>
              </Grid>

              <Divider />

              {/* THÔNG TIN BỔ SUNG */}
              <Typography variant="h6" color="text.secondary">
                Thông tin bổ sung
              </Typography>

              <Grid container spacing={2}>
                <Grid size={{ xs: 12, md: 6 }}>
                  <Controller
                    name="email"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        fullWidth
                        label="Email"
                        type="email"
                        error={!!errors.email}
                        helperText={errors.email?.message}
                      />
                    )}
                  />
                </Grid>

                <Grid size={{ xs: 12, md: 6 }}>
                  <Controller
                    name="leadScore"
                    control={control}
                    render={({ field: { value, onChange, ...field } }) => (
                      <TextField
                        {...field}
                        fullWidth
                        label="Điểm lead (0-100)"
                        type="number"
                        value={value || 0}
                        onChange={(e) => onChange(Number(e.target.value))}
                        inputProps={{ min: 0, max: 100 }}
                        error={!!errors.leadScore}
                        helperText={errors.leadScore?.message}
                      />
                    )}
                  />
                </Grid>

                <Grid size={{ xs: 12 }}>
                  <Controller
                    name="notes"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        fullWidth
                        label="Ghi chú"
                        placeholder="Thêm ghi chú, thông tin công ty, địa chỉ và các thông tin bổ sung khác..."
                        multiline
                        rows={4}
                      />
                    )}
                  />
                </Grid>
              </Grid>
            </Stack>
          </form>
        </Scrollbar>
      </DialogContent>

      <DialogActions 
        sx={{ 
          borderTop: 1, 
          borderColor: 'divider',
          bgcolor: 'background.paper',
          px: 3,
          py: 2,
        }}
      >
        <Button onClick={onClose} disabled={loading}>
          Hủy
        </Button>
        <LoadingButton
          onClick={handleSubmit(onSubmit)}
          loading={loading}
          variant="contained"
        >
          Tạo Lead
        </LoadingButton>
      </DialogActions>
    </Dialog>
  );
} 