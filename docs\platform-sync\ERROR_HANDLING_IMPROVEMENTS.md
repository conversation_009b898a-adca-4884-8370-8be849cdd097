# Cải thiện Error Handling cho Platform Sync API

## Vấn đề đã khắc phục

### Lỗi gốc
```
Backend sync failed, falling back to mock: Unexpected token '<', "<!DOCTYPE "... is not valid JSON
```

### Nguyên nhân
- Backend API trả về HTML thay vì JSON (thường là trang lỗi 404/500)
- Không kiểm tra Content-Type trước khi parse JSON
- Error handling không đủ chi tiết để debug

## Các cải thiện đã thực hiện

### 1. Tạo API Response Handler Utility (`src/utils/api-response-handler.js`)

#### Các function chính:
- `handleApiResponse()`: Xử lý response với kiểm tra Content-Type
- `fetchWithTimeout()`: Fetch với timeout và error handling
- `callExternalApi()`: Wrapper function hoàn chỉnh cho external API calls
- `createErrorResponse()` & `createSuccessResponse()`: Tạo response nhất quán

#### Tính năng:
- ✅ Kiểm tra Content-Type trước khi parse JSON
- ✅ Timeout handling (default 30s)
- ✅ Detailed logging cho debugging
- ✅ Consistent error/success response format

### 2. Cập nhật API Routes

#### Haravan API (`src/app/api/platform-sync/haravan/products/route.js`)
- Sử dụng `callExternalApi()` thay vì fetch trực tiếp
- Improved error logging với context
- Consistent response format

#### Sapo API (`src/app/api/platform-sync/sapo/products/route.js`)
- Tương tự như Haravan API
- Unified error handling approach

### 3. Cải thiện Client-side Error Handling

#### Platform Sync Service (`src/actions/mooly-chatbot/platform-sync-service.js`)
- Kiểm tra Content-Type ở client
- Better error messages cho user
- Network error detection

#### Platform Sync Form (`src/sections/mooly-chatbot/platform-sync/platform-sync-form.jsx`)
- Thông báo lỗi chi tiết hơn
- Phân biệt các loại lỗi khác nhau
- User-friendly error messages

## Các loại lỗi được xử lý

### 1. Backend không khả dụng
```
Error: Backend returned text/html instead of JSON
Toast: "Backend API không khả dụng. Hệ thống đã chuyển sang chế độ demo."
```

### 2. Lỗi mạng
```
Error: Network error: fetch failed
Toast: "Lỗi kết nối mạng. Vui lòng kiểm tra kết nối internet và thử lại."
```

### 3. Timeout
```
Error: Haravan sync timed out after 30000ms
Toast: "Yêu cầu đã hết thời gian chờ. Vui lòng thử lại."
```

### 4. Invalid JSON
```
Error: Backend returned invalid JSON response
Toast: "Server returned invalid JSON response"
```

## Logging cải thiện

### Server-side logs:
```
Haravan sync - Attempting to call: http://backend/api/platform-sync/haravan/products
Haravan sync - Response status: 200, Content-Type: application/json
Haravan sync - Success: Đồng bộ thành công 5 sản phẩm
```

### Client-side logs:
```
Sync error: Server returned invalid response format
Error syncing products from Haravan: Network error occurred
```

## Fallback Mechanism

1. **Thử backend thực** → Nếu có `BACKEND_API_URL`
2. **Log lỗi chi tiết** → Để debug
3. **Fallback về mock** → Đảm bảo UX không bị gián đoạn
4. **Thông báo user** → Về tình trạng hệ thống

## Environment Variables

```env
# Backend API URL - nếu không có hoặc lỗi sẽ fallback về mock
BACKEND_API_URL=http://localhost:3000

# Hoặc production URL
BACKEND_API_URL=https://api.yourdomain.com
```

## Testing

### Để test error handling:

1. **Test backend không khả dụng:**
   ```env
   BACKEND_API_URL=http://invalid-url
   ```

2. **Test HTML response:**
   ```env
   BACKEND_API_URL=https://google.com
   ```

3. **Test timeout:**
   - Set BACKEND_API_URL đến một server chậm
   - Hoặc modify timeout trong `callExternalApi()`

## Lợi ích

1. **Better UX**: User nhận được thông báo lỗi rõ ràng
2. **Better DX**: Developer có logs chi tiết để debug
3. **Reliability**: Fallback mechanism đảm bảo app không crash
4. **Maintainability**: Centralized error handling logic
5. **Consistency**: Unified response format across all APIs

## Sử dụng trong tương lai

Khi tạo API routes mới, sử dụng:

```javascript
import { callExternalApi, createErrorResponse, createSuccessResponse } from 'src/utils/api-response-handler';

// Trong API route
try {
  const data = await callExternalApi(url, options, 'Context name');
  return NextResponse.json(createSuccessResponse(data, 'Success message'));
} catch (error) {
  const errorResponse = createErrorResponse(error, 'Default error message');
  return NextResponse.json(errorResponse, { status: errorResponse.status });
}
```
