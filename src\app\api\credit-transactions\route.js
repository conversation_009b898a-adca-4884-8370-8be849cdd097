import { NextResponse } from 'next/server';

import { withTenantAuth } from 'src/utils/server-auth';
import { createClient } from 'src/utils/supabase/server';

/**
 * Secure API endpoint để lấy credit transactions với database-level security
 * @param {Request} request - Request object
 * @returns {Promise<Response>} - Response object
 */
export const GET = withTenantAuth(async (request, { tenantId, userId }) => {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '0', 10);
    const limit = parseInt(searchParams.get('limit') || '10', 10);
    const orderBy = searchParams.get('orderBy') || 'created_at';
    const ascending = searchParams.get('ascending') === 'true';

    // Validate pagination parameters
    if (page < 0 || limit < 1 || limit > 100) {
      return NextResponse.json(
        { success: false, error: 'Invalid pagination parameters' },
        { status: 400 }
      );
    }

    // Kết nối với Supabase
    const supabase = await createClient();

    // Get credit transactions with RLS protection
    let query = supabase
      .from('credit_transactions')
      .select('*')
      .eq('tenant_id', tenantId)
      .order(orderBy, { ascending })
      .range(page * limit, (page + 1) * limit - 1);

    const { data: transactions, error, count } = await query;

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json(
        { success: false, error: 'Database operation failed' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: transactions || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit),
      },
    });

  } catch (error) {
    console.error('Error getting credit transactions:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
});
