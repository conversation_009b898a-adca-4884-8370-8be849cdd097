import { NextResponse } from 'next/server';

import { withTenantAuth } from 'src/utils/server-auth';

/**
 * API route để xóa nhiều sản phẩm khỏi Weaviate dựa trên danh sách product_ids
 * @param {Request} request - <PERSON><PERSON><PERSON> cầu HTTP
 * @returns {Promise<NextResponse>} - Phản hồi HTTP
 */
export const DELETE = withTenantAuth(async (request, { tenantId, userId }) => {
  try {
    const { product_ids } = await request.json();

    // Kiểm tra dữ liệu đầu vào
    if (!product_ids || !Array.isArray(product_ids) || product_ids.length === 0) {
      return NextResponse.json(
        { error: 'Missing required field: product_ids (array)' },
        { status: 400 }
      );
    }

    // Lấy URL Weaviate từ biến môi trường
    const BACKEND_API_URL = process.env.BACKEND_API_URL || 'http://localhost:3000';

    // Gọi API Weaviate để xóa nhiều sản phẩm
    const response = await fetch(`${BACKEND_API_URL}/api/weaviate/products/delete-by-ids`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        product_ids,
        tenant_id: tenantId,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      return NextResponse.json(
        { error: errorData.message || 'Failed to delete products by IDs' },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in delete-by-ids API route:', error);
    return NextResponse.json({ error: error.message || 'Internal server error' }, { status: 500 });
  }
}); 