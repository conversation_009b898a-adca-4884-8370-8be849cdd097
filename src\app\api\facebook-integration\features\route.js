import { NextResponse } from 'next/server';
import { createClient } from 'src/utils/supabase/server';

/**
 * Facebook Integration Features API
 * Manage feature settings for Facebook/Instagram integration
 */

// POST: Save feature settings
export async function POST(request) {
  try {
    const { chatbotId, featureSettings } = await request.json();
    
    if (!chatbotId || !featureSettings) {
      return NextResponse.json({ 
        error: 'Chatbot ID and feature settings required' 
      }, { status: 400 });
    }
    
    console.log('💾 Saving feature settings for chatbot:', chatbotId);
    
    const supabase = await createClient();
    
    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }
    
    // Get selected page for this chatbot
    const { data: selectionLog } = await supabase
      .from('facebook_activity_logs')
      .select('page_id, metadata')
      .eq('activity', 'chatbot_page_selected')
      .contains('metadata', { chatbotId })
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (!selectionLog) {
      return NextResponse.json({ 
        error: 'No page selected for this chatbot' 
      }, { status: 404 });
    }
    
    const pageId = selectionLog.page_id;
    console.log('📋 Updating features for page:', pageId);
    
    // Update auto reply configuration with feature settings
    const configData = {
      page_id: pageId,
      enable_comment_reply: featureSettings.facebookComments || false,
      enable_message_reply: featureSettings.facebookMessages || false,
      enable_instagram_comments: featureSettings.instagramComments || false,
      enable_instagram_messages: featureSettings.instagramMessages || false,
      updated_at: new Date().toISOString()
    };
    
    // Upsert configuration
    const { data, error } = await supabase
      .from('facebook_auto_reply_config')
      .upsert(configData, { 
        onConflict: 'page_id',
        ignoreDuplicates: false 
      })
      .select()
      .single();
    
    if (error) {
      console.error('❌ Error saving feature settings:', error);
      return NextResponse.json({ 
        error: 'Failed to save feature settings',
        details: error.message 
      }, { status: 500 });
    }
    
    // Log the feature update
    await supabase
      .from('facebook_activity_logs')
      .insert({
        page_id: pageId,
        activity: 'features_updated',
        metadata: { 
          chatbotId,
          features: featureSettings
        }
      });
    
    console.log('✅ Feature settings saved for page:', pageId);
    
    return NextResponse.json({
      success: true,
      message: 'Feature settings saved successfully',
      data: {
        pageId,
        featureSettings: {
          facebookComments: data.enable_comment_reply,
          instagramComments: data.enable_instagram_comments,
          facebookMessages: data.enable_message_reply,
          instagramMessages: data.enable_instagram_messages,
          isComplete: featureSettings.isComplete || true
        }
      }
    });
    
  } catch (error) {
    console.error('❌ Facebook features API error:', error);
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error.message 
    }, { status: 500 });
  }
}

// GET: Get feature settings
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const chatbotId = searchParams.get('chatbotId');
    
    if (!chatbotId) {
      return NextResponse.json({ 
        error: 'Chatbot ID required' 
      }, { status: 400 });
    }
    
    const supabase = await createClient();
    
    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }
    
    // Get selected page for this chatbot
    const { data: selectionLog } = await supabase
      .from('facebook_activity_logs')
      .select('page_id, metadata')
      .eq('activity', 'chatbot_page_selected')
      .contains('metadata', { chatbotId })
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (!selectionLog) {
      return NextResponse.json({
        success: true,
        featureSettings: {
          facebookComments: false,
          instagramComments: false,
          facebookMessages: false,
          instagramMessages: false,
          isComplete: false
        }
      });
    }
    
    // Get auto reply configuration
    const { data: configData } = await supabase
      .from('facebook_auto_reply_config')
      .select('*')
      .eq('page_id', selectionLog.page_id)
      .single();

    const featureSettings = configData ? {
      facebookComments: configData.enable_comment_reply,
      instagramComments: configData.enable_instagram_comments,
      facebookMessages: configData.enable_message_reply,
      instagramMessages: configData.enable_instagram_messages,
      isComplete: true
    } : {
      facebookComments: false,
      instagramComments: false,
      facebookMessages: false,
      instagramMessages: false,
      isComplete: false
    };
    
    return NextResponse.json({
      success: true,
      featureSettings
    });
    
  } catch (error) {
    console.error('❌ Facebook features GET API error:', error);
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error.message 
    }, { status: 500 });
  }
}
