'use client';

import { useState, useCallback } from 'react';

import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Tooltip from '@mui/material/Tooltip';
import MenuList from '@mui/material/MenuList';
import MenuItem from '@mui/material/MenuItem';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';

import { usePopover } from 'minimal-shared/hooks';

import { Label } from 'src/components/label';
import { Iconify } from 'src/components/iconify';
import { CustomPopover } from 'src/components/custom-popover';

// ----------------------------------------------------------------------

export default function LeadsKanbanColumnToolbar({
  totalTasks,
  columnName,
  columnColor = 'default',
  columnDescription,
  isUncategorizedColumn = false,
  handleProps,
  onUpdateColumn,
  onClearColumn,
  onDeleteColumn,
  disableActions = false,
}) {
  const menuActions = usePopover();

  const handleClickDelete = useCallback(() => {
    menuActions.onClose();
    onDeleteColumn();
  }, [menuActions, onDeleteColumn]);

  const handleClickClear = useCallback(() => {
    menuActions.onClose();
    onClearColumn();
  }, [menuActions, onClearColumn]);

  const renderMenuActions = () => (
    <CustomPopover
      open={menuActions.open}
      anchorEl={menuActions.anchorEl}
      onClose={menuActions.onClose}
      slotProps={{ arrow: { placement: 'top-right' } }}
    >
      <MenuList>
        <MenuItem onClick={handleClickClear}>
          <Iconify icon="solar:trash-bin-trash-bold" />
          Xóa tất cả leads
        </MenuItem>

        <MenuItem onClick={handleClickDelete} sx={{ color: 'error.main' }}>
          <Iconify icon="solar:trash-bin-trash-bold" />
          Xóa cột
        </MenuItem>
      </MenuList>
    </CustomPopover>
  );

  return (
    <>
      <Stack
        spacing={2}
        direction="row"
        alignItems="center"
        sx={{
          py: 0.5,
          pr: 1,
          width: 1,
          minHeight: 40,
        }}
      >
        <Stack
          {...handleProps}
          direction="row"
          alignItems="center"
          sx={{ flex: '1 1 auto', cursor: 'grab' }}
        >
          <Stack direction="row" alignItems="center" spacing={1} sx={{ flex: '1 1 auto' }}>
            <Typography 
              variant="h6" 
              sx={{ 
                textTransform: 'capitalize',
                ...(isUncategorizedColumn && {
                  color: 'warning.main',
                }),
              }}
            >
              {columnName}
            </Typography>

            {isUncategorizedColumn && (
              <Tooltip 
                title="Cột này chứa các leads có trạng thái không khớp với workflow hiện tại. Hãy cập nhật trạng thái phù hợp cho từng lead."
                arrow
              >
                <Iconify 
                  icon="solar:danger-triangle-bold" 
                  sx={{ 
                    color: 'warning.main',
                    fontSize: 16,
                  }} 
                />
              </Tooltip>
            )}
          </Stack>

          <Label 
            color={isUncategorizedColumn ? 'warning' : columnColor} 
            sx={{ ml: 1 }}
          >
            {totalTasks}
          </Label>
        </Stack>

        {!disableActions && (
          <Tooltip title="Thao tác">
            <IconButton
              size="small"
              color="default"
              onClick={menuActions.onOpen}
              sx={{ 
                color: 'text.secondary',
                '&:hover': { color: 'text.primary' }
              }}
            >
              <Iconify icon="eva:more-horizontal-fill" />
            </IconButton>
          </Tooltip>
        )}
      </Stack>

      {/* Hiển thị description nếu có */}
      {columnDescription && (
        <Box sx={{ px: 1, pb: 1 }}>
          <Typography 
            variant="caption" 
            sx={{ 
              color: 'text.secondary',
              display: 'block',
              lineHeight: 1.4,
            }}
          >
            {columnDescription}
          </Typography>
        </Box>
      )}

      {renderMenuActions()}
    </>
  );
} 