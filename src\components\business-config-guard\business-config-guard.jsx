'use client';

import { useEffect } from 'react';
import { Box, Alert, Button, Typography } from '@mui/material';

import { useAuthContext } from 'src/auth/hooks';
import { useBusinessConfigContext } from 'src/actions/mooly-chatbot/business-config-service';
import { BusinessConfigLoading } from 'src/components/business-config-loading/business-config-loading';

// ----------------------------------------------------------------------

/**
 * Business Config Guard
 * Đảm bảo business config được load trước khi render children
 * Hiển thị loading state và error handling
 * RLS sẽ tự động xử lý tenant_id từ JWT token
 */
export function BusinessConfigGuard({ children, fallback = null }) {
  const { authenticated, initialized } = useAuthContext();
  const {
    config,
    loading,
    error,
    loadConfig,
    refreshConfig
  } = useBusinessConfigContext();

  // Auto-reload config khi auth state thay đổi
  useEffect(() => {
    if (initialized && authenticated && !config && !loading) {
      // RLS sẽ tự động xử lý tenant_id
      loadConfig();
    }
  }, [initialized, authenticated, config, loading, loadConfig]);

  // Nếu chưa authenticated hoặc chưa initialized, không cần business config
  if (!initialized || !authenticated) {
    return children;
  }

  // Với RLS system, không cần kiểm tra tenantId riêng biệt
  // RLS sẽ tự động xử lý việc không có tenant_id

  // Hiển thị loading state
  if (loading) {
    return fallback || <BusinessConfigLoading message="Đang tải cấu hình kinh doanh..." />;
  }

  // Hiển thị error state với retry option - chỉ khi có lỗi nghiêm trọng
  if (error && !config) {
    // Nếu lỗi là do tenant không tồn tại, không hiển thị error mà render children với default config
    if (error.message?.includes('Tenant không tồn tại') ||
        error.message?.includes('no rows returned') ||
        error.code === 'PGRST116') {
      console.warn('⚠️ Tenant not found, but continuing with default config');
      return children;
    }

    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: 200,
          p: 3,
        }}
      >
        <Alert
          severity="error"
          sx={{ mb: 2, maxWidth: 500 }}
          action={
            <Button
              color="inherit"
              size="small"
              onClick={() => refreshConfig()}
            >
              Thử lại
            </Button>
          }
        >
          <Typography variant="h6" sx={{ mb: 1 }}>
            Lỗi tải cấu hình
          </Typography>
          <Typography variant="body2">
            Không thể tải cấu hình kinh doanh. Vui lòng thử lại.
          </Typography>
          {error && (
            <Typography variant="caption" sx={{ mt: 1, display: 'block' }}>
              Chi tiết: {error.message || error}
            </Typography>
          )}
        </Alert>
      </Box>
    );
  }

  // Render children khi config đã sẵn sàng hoặc không cần thiết
  return children;
}

/**
 * Business Config Guard với feature checking
 * Chỉ render children nếu feature được enable
 */
export function FeatureGuard({ feature, children, fallback = null }) {
  const { config } = useBusinessConfigContext();

  if (!config) {
    return fallback;
  }

  const isFeatureEnabled = config.config?.features?.[feature]?.enabled;

  if (!isFeatureEnabled) {
    return fallback;
  }

  return children;
}

/**
 * Business Type Guard
 * Chỉ render children cho specific business types
 */
export function BusinessTypeGuard({ allowedTypes = [], children, fallback = null }) {
  const { config } = useBusinessConfigContext();

  if (!config || !config.businessType) {
    return fallback;
  }

  if (!allowedTypes.includes(config.businessType)) {
    return fallback;
  }

  return children;
}

/**
 * Combined Guard
 * Kết hợp business config guard với feature và business type checking
 */
export function CombinedBusinessGuard({ 
  feature = null, 
  allowedTypes = [], 
  children, 
  fallback = null 
}) {
  return (
    <BusinessConfigGuard fallback={fallback}>
      {feature ? (
        <FeatureGuard feature={feature} fallback={fallback}>
          {allowedTypes.length > 0 ? (
            <BusinessTypeGuard allowedTypes={allowedTypes} fallback={fallback}>
              {children}
            </BusinessTypeGuard>
          ) : (
            children
          )}
        </FeatureGuard>
      ) : allowedTypes.length > 0 ? (
        <BusinessTypeGuard allowedTypes={allowedTypes} fallback={fallback}>
          {children}
        </BusinessTypeGuard>
      ) : (
        children
      )}
    </BusinessConfigGuard>
  );
}
