'use client';

import { useState, useEffect } from 'react';
import { useForm, Controller } from 'react-hook-form';

import {
  <PERSON>,
  Tab,
  Ta<PERSON>,
  <PERSON>,
  <PERSON>ack,
  <PERSON>ert,
  <PERSON>ton,
  Dialog,
  TextField,
  Typography,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress
} from '@mui/material';

import {
  createAIOfferRule,
  getAvailableOffers,
  createScheduledFollowUpRule
} from 'src/actions/mooly-chatbot/automation-rules-service';

import { ScheduledMessageForm } from './scheduled-message-form';
import { AIOfferSelectionForm } from './ai-offer-selection-form';

// ----------------------------------------------------------------------

export function AutomationRuleCreateDialog({ open, onClose, onSuccess }) {
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [offers, setOffers] = useState([]);

  const { control, handleSubmit, reset, formState: { errors } } = useForm({
    defaultValues: {
      name: '',
      description: ''
    }
  });

  // Load offers when dialog opens
  useEffect(() => {
    if (open) {
      loadOffers();
    }
  }, [open]);

  const loadOffers = async () => {
    try {
      const result = await getAvailableOffers();
      if (result.success) {
        setOffers(result.data || []);
      }
    } catch (err) {
      console.error('Error loading offers:', err);
    }
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
    setError(null);
  };

  const handleClose = () => {
    reset();
    setActiveTab(0);
    setError(null);
    onClose();
  };

  const onSubmit = async (data) => {
    try {
      setLoading(true);
      setError(null);

      let result;
      if (activeTab === 0) {
        // AI Offer Selection
        result = await createAIOfferRule(data);
      } else {
        // Scheduled Message
        result = await createScheduledFollowUpRule(data);
      }

      if (result.success) {
        onSuccess();
        handleClose();
      } else {
        setError(result.error);
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { height: '80vh' }
      }}
    >
      <DialogTitle>
        <Typography variant="h5">
          Tạo Automation Rule Mới
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          Cấu hình quy tắc tự động để bám đuổi khách hàng hiệu quả
        </Typography>
      </DialogTitle>

      <DialogContent sx={{ p: 0 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={activeTab} onChange={handleTabChange} sx={{ px: 3 }}>
            <Tab label="AI Chọn Offer" />
            <Tab label="Tin Nhắn Theo Lịch" />
          </Tabs>
        </Box>

        <Box sx={{ p: 3 }}>
          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {typeof error === 'string' ? error : JSON.stringify(error)}
            </Alert>
          )}

          <form onSubmit={handleSubmit(onSubmit)}>
            {/* Basic Information */}
            <Card sx={{ p: 3, mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                Thông Tin Cơ Bản
              </Typography>

              <Stack spacing={3}>
                <Controller
                  name="name"
                  control={control}
                  rules={{ required: 'Tên rule là bắt buộc' }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Tên Rule"
                      error={!!errors.name}
                      helperText={errors.name?.message}
                      fullWidth
                    />
                  )}
                />

                <Controller
                  name="description"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Mô Tả"
                      multiline
                      rows={2}
                      fullWidth
                    />
                  )}
                />
              </Stack>
            </Card>

            {/* Tab Content */}
            {activeTab === 0 && (
              <AIOfferSelectionForm
                control={control}
                errors={errors}
                offers={offers}
                onOffersUpdate={loadOffers}
              />
            )}

            {activeTab === 1 && (
              <ScheduledMessageForm
                control={control}
                errors={errors}
              />
            )}
          </form>
        </Box>
      </DialogContent>

      <DialogActions sx={{ p: 3, pt: 0 }}>
        <Button onClick={handleClose} disabled={loading}>
          Hủy
        </Button>
        <Button
          variant="contained"
          onClick={handleSubmit(onSubmit)}
          disabled={loading}
          startIcon={loading && <CircularProgress size={20} />}
        >
          {loading ? 'Đang tạo...' : 'Tạo Rule'}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
