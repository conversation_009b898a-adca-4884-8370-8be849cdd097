'use client';

import { useCallback } from 'react';
import { CSS } from '@dnd-kit/utilities';
import { useSortable, defaultAnimateLayoutChanges } from '@dnd-kit/sortable';

import { Badge } from '@mui/material';

import { toast } from 'src/components/snackbar';

import ColumnBase from 'src/sections/kanban/column/column-base';

import LeadsKanbanColumnToolbar from './leads-kanban-column-toolbar';

// ----------------------------------------------------------------------

const animateLayoutChanges = (args) => defaultAnimateLayoutChanges({ ...args, wasDragging: true });

export default function LeadsKanbanColumn({ children, column, tasks, disabled, sx }) {
  const { attributes, isDragging, listeners, setNodeRef, transition, active, over, transform } =
    useSortable({
      id: column.id,
      data: { type: 'container', children: tasks },
      animateLayoutChanges,
    });

  const tasksIds = tasks.map((task) => task.id);

  const isOverContainer = over
    ? (column.id === over.id && active?.data.current?.type !== 'container') ||
      tasksIds.includes(over.id)
    : false;

  // Kiểm tra xem có phải uncategorized column không
  const isUncategorizedColumn = column.id === 'uncategorized';

  // Handle actions khác nhau cho từng loại column
  const handleUpdateColumn = useCallback(() => {
    if (isUncategorizedColumn) {
      toast.info('Đây là cột tự động cho leads chưa phân loại');
    } else {
      toast.info('Trạng thái leads được quản lý bởi workflow');
    }
  }, [isUncategorizedColumn]);

  const handleClearColumn = useCallback(() => {
    if (isUncategorizedColumn) {
      toast.info('Hãy cập nhật trạng thái cho từng lead thay vì xóa');
    } else {
      toast.info('Không thể xóa tất cả leads trong cột này');
    }
  }, [isUncategorizedColumn]);

  const handleDeleteColumn = useCallback(() => {
    if (isUncategorizedColumn) {
      toast.info('Cột "Chưa phân loại" là cột hệ thống, không thể xóa');
    } else {
      toast.info('Không thể xóa cột trạng thái leads');
    }
  }, [isUncategorizedColumn]);

  return (
    <ColumnBase
      ref={disabled ? undefined : setNodeRef}
      style={{
        transition,
        transform: CSS.Translate.toString(transform),
      }}
      sx={sx}
      stateProps={{
        dragging: isDragging,
        overContainer: isOverContainer,
        handleProps: { ...attributes, ...listeners },
      }}
      slots={{
        header: (
          <LeadsKanbanColumnToolbar
            handleProps={{ ...attributes, ...listeners }}
            totalTasks={tasks.length}
            columnName={column.name}
            columnColor={column.color}
            columnDescription={column.description}
            isUncategorizedColumn={isUncategorizedColumn}
            onUpdateColumn={handleUpdateColumn}
            onClearColumn={handleClearColumn}
            onDeleteColumn={handleDeleteColumn}
            // Disable actions for leads columns
            disableActions
          />
        ),
        main: children,
        // Không có action add vì leads được tạo từ dialog
        action: null,
      }}
    />
  );
} 