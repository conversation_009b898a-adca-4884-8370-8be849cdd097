import { createAdminClient } from 'src/utils/supabase/server';
import { FACEBOOK_CONFIG } from 'src/lib/facebook-config';

/**
 * Facebook Token Manager
 * Handles automatic token refresh and validation
 */
export class FacebookTokenManager {
  constructor() {
    this.appId = FACEBOOK_CONFIG.APP_ID;
    this.appSecret = FACEBOOK_CONFIG.APP_SECRET;
    this.apiVersion = FACEBOOK_CONFIG.API_VERSION;
  }

  /**
   * Get valid access token for a page, refresh if needed
   */
  async getValidPageToken(pageId) {
    try {
      const supabase = createAdminClient();
      
      // Get account data
      const { data: account, error } = await supabase
        .from('facebook_accounts')
        .select('*')
        .eq('page_id', pageId)
        .single();

      if (error || !account) {
        return null;
      }

      // Check if token needs refresh
      const needsRefresh = this.shouldRefreshToken(account);

      if (needsRefresh) {
        const refreshedToken = await this.refreshPageToken(account);

        if (refreshedToken) {
          // Update database with new token
          await this.updateTokenInDatabase(pageId, refreshedToken);
          return refreshedToken.access_token;
        }
      }

      // Return existing token (prioritize page_access_token)
      return account.page_access_token || account.access_token || account.user_access_token;
      
    } catch (error) {
      console.error('❌ Error getting valid page token:', error);
      return null;
    }
  }

  /**
   * Check if token should be refreshed
   */
  shouldRefreshToken(account) {
    if (!account.token_expires_at) {
      return false; // No expiry date means long-lived token
    }

    const expiryDate = new Date(account.token_expires_at);
    const now = new Date();
    const daysUntilExpiry = (expiryDate - now) / (1000 * 60 * 60 * 24);

    // Refresh if expires within 7 days
    return daysUntilExpiry <= 7;
  }

  /**
   * Refresh page access token
   */
  async refreshPageToken(account) {
    try {
      // Step 1: Extend user access token if needed
      const longLivedUserToken = await this.getLongLivedUserToken(
        account.user_access_token || account.access_token
      );

      if (!longLivedUserToken) {
        return null;
      }

      // Step 2: Get new page access token
      const pageTokenResponse = await fetch(
        FACEBOOK_CONFIG.getApiUrl(`/${account.page_id}?fields=access_token&access_token=${longLivedUserToken}`)
      );

      const pageTokenData = await pageTokenResponse.json();

      if (pageTokenData.error) {
        return null;
      }

      return {
        access_token: pageTokenData.access_token,
        user_access_token: longLivedUserToken,
        expires_at: null // Page tokens don't expire
      };

    } catch (error) {
      console.error('❌ Error refreshing page token:', error);
      return null;
    }
  }

  /**
   * Get long-lived user access token
   */
  async getLongLivedUserToken(shortLivedToken) {
    try {
      const response = await fetch(
        FACEBOOK_CONFIG.getApiUrl(FACEBOOK_CONFIG.ENDPOINTS.OAUTH_ACCESS_TOKEN) +
        `?grant_type=fb_exchange_token&` +
        `client_id=${this.appId}&` +
        `client_secret=${this.appSecret}&` +
        `fb_exchange_token=${shortLivedToken}`
      );

      const data = await response.json();

      if (data.error) {
        console.error('❌ Error getting long-lived token:', data.error);
        return null;
      }

      return data.access_token;

    } catch (error) {
      console.error('❌ Error in getLongLivedUserToken:', error);
      return null;
    }
  }

  /**
   * Update token in database
   */
  async updateTokenInDatabase(pageId, tokenData) {
    try {
      const supabase = createAdminClient();

      const updateData = {
        page_access_token: tokenData.access_token,
        user_access_token: tokenData.user_access_token,
        token_expires_at: tokenData.expires_at,
        last_sync_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const { error } = await supabase
        .from('facebook_accounts')
        .update(updateData)
        .eq('page_id', pageId);

      if (error) {
        console.error('❌ Error updating token in database:', error);
        return false;
      }

      console.log('✅ Token updated successfully for page:', pageId);
      return true;

    } catch (error) {
      console.error('❌ Error in updateTokenInDatabase:', error);
      return false;
    }
  }

  /**
   * Validate token by making a test API call
   */
  async validateToken(token) {
    try {
      const response = await fetch(
        FACEBOOK_CONFIG.getApiUrl(`/me?access_token=${token}`)
      );

      const data = await response.json();
      return !data.error;

    } catch (error) {
      return false;
    }
  }
}

// Export singleton instance
export const facebookTokenManager = new FacebookTokenManager();
