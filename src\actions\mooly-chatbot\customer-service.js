'use client';

import { useRef, useMemo, useState, useEffect, useCallback } from 'react';

import { supabase } from 'src/lib/supabase';

import storageService from './storage-service';
import { fetchData, createData, updateData, deleteData } from './supabase-utils';

// Tên bảng trong Supabase
const TABLE_NAME = 'customers';
const ADDRESS_TABLE_NAME = 'customer_addresses';

/**
 * Lấy danh sách khách hàng với các tùy chọn lọc
 * @param {Object} options - Các tùy chọn truy vấn
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getCustomers(options = {}) {
  try {
    // Truy vấn test để log ra kết quả
    const { data, error } = await supabase.from(TABLE_NAME).select('*');

    // Log kết quả để debug
    console.log('Test query result:', { data, error });

    // Vẫn sử dụng fetchData để lấy dữ liệu thực tế với options
    return fetchData(TABLE_NAME, options);
  } catch (error) {
    console.error('Error in test query:', error);
    return fetchData(TABLE_NAME, options);
  }
}

/**
 * Lấy chi tiết một khách hàng theo ID
 * @param {string} customerId - ID của khách hàng
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getCustomerById(customerId) {
  if (!customerId) return { success: false, error: 'Customer ID is required', data: null };

  return fetchData(TABLE_NAME, {
    filters: { id: customerId },
    single: true,
  });
}

/**
 * Lấy danh sách địa chỉ của khách hàng
 * @param {string} customerId - ID của khách hàng
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getCustomerAddresses(customerId) {
  if (!customerId) return { success: false, error: 'Customer ID is required', data: [] };

  return fetchData(ADDRESS_TABLE_NAME, {
    filters: { customer_id: customerId }, // Sử dụng field name chính xác trong database
  });
}

/**
 * Lấy thông tin địa chỉ theo ID
 * @param {string} addressId - ID của địa chỉ
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getAddressById(addressId) {
  if (!addressId) return { success: false, error: 'Address ID is required', data: null };

  return fetchData(ADDRESS_TABLE_NAME, {
    filters: { id: addressId },
    single: true,
  });
}

/**
 * Xử lý tải lên hình ảnh đại diện khách hàng
 * @param {File} imageFile - File hình ảnh cần tải lên
 * @param {string|null} oldImageUrl - URL hình ảnh cũ (nếu có)
 * @returns {Promise<Object>} - Kết quả từ API với URL hình ảnh
 */
export async function uploadCustomerAvatar(imageFile, oldImageUrl = null) {
  try {
    // Nếu không có file mới, trả về URL cũ hoặc null
    if (!imageFile) return { success: true, imageUrl: oldImageUrl };

    // Xóa hình ảnh cũ nếu có
    if (oldImageUrl) {
      try {
        console.log('Attempting to delete old image:', oldImageUrl);
        // Gọi trực tiếp hàm xóa hình ảnh để đảm bảo xử lý đường dẫn chính xác
        const deleteResult = await deleteCustomerAvatar(oldImageUrl);

        if (deleteResult.success) {
          console.log('Old image deleted successfully');
        } else {
          console.warn('Failed to delete old image, but will continue:', deleteResult.error);
        }
      } catch (deleteError) {
        // Bỏ qua lỗi khi xóa hình cũ, vẫn tiếp tục tải lên hình mới
        console.warn('Error deleting old image, but will continue:', deleteError);
      }
    }

    // Tạo tên file duy nhất
    const fileName = storageService.generateUniqueFileName(imageFile.name);

    const { data: { user } } = await supabase.auth.getUser();
    let tenantId = null;

    if (user) {
      // Lấy tenant_id từ bảng users
      const { data } = await supabase
        .from('users')
        .select('tenant_id')
        .eq('id', user.id)
        .single();

      tenantId = data?.tenant_id;
    }

    // Tạo đường dẫn lưu trữ với tenantId nếu có
    const filePath = storageService.buildFilePath('customers', tenantId, fileName);

    console.log('Uploading new image to path:', filePath);
    // Tải lên hình ảnh mới
    const uploadResult = await storageService.uploadFile('public', filePath, imageFile, {
      upsert: true,
      cacheControl: '3600',
    });

    if (uploadResult.success) {
      console.log('Image uploaded successfully, public URL:', uploadResult.publicUrl);
      return { success: true, imageUrl: uploadResult.publicUrl };
    }

    console.error('Failed to upload image:', uploadResult.error);
    return {
      success: false,
      error: uploadResult.error,
      imageUrl: null,
    };
  } catch (error) {
    console.error('Error uploading customer avatar:', error);
    return { success: false, error, imageUrl: null };
  }
}

/**
 * Kiểm tra xem khách hàng đã tồn tại chưa
 * @param {string} phone - Số điện thoại cần kiểm tra
 * @param {string} email - Email cần kiểm tra
 * @returns {Promise<Object>} - Kết quả kiểm tra
 */
export async function checkCustomerExists(phone, email) {
  // Nếu có số điện thoại, kiểm tra trùng lặp số điện thoại
  if (phone) {
    const phoneResult = await fetchData(TABLE_NAME, {
      filters: {
        phone,
      },
      limit: 1,
    });

    if (phoneResult.success && phoneResult.data && phoneResult.data.length > 0) {
      return {
        success: true,
        exists: true,
        duplicateField: 'phone',
        customer: phoneResult.data[0],
      };
    }
  }

  // Nếu có email và không rỗng, kiểm tra trùng lặp email
  if (email && email.trim() !== '') {
    const emailResult = await fetchData(TABLE_NAME, {
      filters: {
        email,
      },
      limit: 1,
    });

    if (emailResult.success && emailResult.data && emailResult.data.length > 0) {
      return {
        success: true,
        exists: true,
        duplicateField: 'email',
        customer: emailResult.data[0],
      };
    }
  }

  // Không tìm thấy khách hàng trùng lặp
  return {
    success: true,
    exists: false,
    duplicateField: null,
  };
}

/**
 * Tạo khách hàng mới
 * @param {Object} customerData - Dữ liệu khách hàng
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function createCustomer(customerData) {
  console.log(customerData);
  try {
    // Đảm bảo dữ liệu hợp lệ
    const cleanData = { ...customerData };

    // Loại bỏ các trường không cần thiết
    if (cleanData.addresses) {
      delete cleanData.addresses;
    }

    // Đảm bảo email không phải là null
    if (cleanData.email === null) {
      cleanData.email = '';
    }

    // Đảm bảo phone không phải là null
    if (cleanData.phone === null) {
      cleanData.phone = '';
    }

    // Kiểm tra xem khách hàng đã tồn tại chưa
    const existsCheck = await checkCustomerExists(cleanData.phone, cleanData.email);

    if (existsCheck.exists) {
      const fieldName = existsCheck.duplicateField === 'phone' ? 'Số điện thoại' : 'Email';
      return {
        success: false,
        error: {
          message: `${fieldName} này đã được sử dụng bởi một khách hàng khác.`,
          code: `DUPLICATE_${existsCheck.duplicateField.toUpperCase()}`,
          field: existsCheck.duplicateField,
          customer: existsCheck.customer,
        },
        data: null,
      };
    }

    // Log dữ liệu trước khi gửi để debug
    console.log('Clean customer data to create:', cleanData);

    // Nếu không có trùng lặp, tạo khách hàng mới
    const result = await createData(TABLE_NAME, cleanData);
    return result;
  } catch (error) {
    // Xử lý lỗi vi phạm ràng buộc unique
    if (error.code === '23505') {
      // Mã lỗi cho vi phạm ràng buộc unique
      if (error.message && error.message.includes('customers_phone_key')) {
        return {
          success: false,
          error: {
            message: 'Số điện thoại này đã được sử dụng bởi một khách hàng khác.',
            code: 'DUPLICATE_PHONE',
            field: 'phone',
          },
          data: null,
        };
      } else if (error.message && error.message.includes('customers_email_key')) {
        return {
          success: false,
          error: {
            message: 'Email này đã được sử dụng bởi một khách hàng khác.',
            code: 'DUPLICATE_EMAIL',
            field: 'email',
          },
          data: null,
        };
      }
    }

    // Xử lý lỗi "cannot extract elements from a scalar"
    if (error.message && error.message.includes('cannot extract elements from a scalar')) {
      console.error('Scalar error in createCustomer:', error, 'Data:', customerData);
      return {
        success: false,
        error: {
          message: 'Có lỗi xảy ra khi xử lý dữ liệu. Vui lòng thử lại.',
          code: 'SCALAR_ERROR',
          originalError: error.message,
        },
        data: null,
      };
    }

    // Xử lý các lỗi khác
    console.error('Error creating customer:', error);
    return {
      success: false,
      error: {
        message: error.message || 'Có lỗi xảy ra khi tạo khách hàng.',
        code: error.code || 'UNKNOWN_ERROR',
      },
      data: null,
    };
  }
}

/**
 * Cập nhật khách hàng
 * @param {string} customerId - ID của khách hàng
 * @param {Object} customerData - Dữ liệu cập nhật
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function updateCustomer(customerId, customerData) {
  if (!customerId) return { success: false, error: 'Customer ID is required', data: null };

  try {
    // Đảm bảo dữ liệu hợp lệ
    const cleanData = { ...customerData };

    // Loại bỏ các trường không cần thiết
    if (cleanData.addresses) {
      delete cleanData.addresses;
    }

    // Đảm bảo email không phải là null
    if (cleanData.email === null) {
      cleanData.email = '';
    }

    // Đảm bảo phone không phải là null
    if (cleanData.phone === null) {
      cleanData.phone = '';
    }

    // Lấy thông tin khách hàng hiện tại
    const currentCustomer = await getCustomerById(customerId);
    if (!currentCustomer.success || !currentCustomer.data) {
      return {
        success: false,
        error: currentCustomer.error || 'Không tìm thấy khách hàng',
        data: null,
      };
    }

    // Kiểm tra xem có thay đổi email hoặc số điện thoại không
    const emailChanged =
      cleanData.email !== undefined && cleanData.email !== currentCustomer.data.email;
    const phoneChanged =
      cleanData.phone !== undefined && cleanData.phone !== currentCustomer.data.phone;

    // Nếu có thay đổi email hoặc số điện thoại, kiểm tra trùng lặp
    if (emailChanged || phoneChanged) {
      // Chỉ kiểm tra email nếu có thay đổi và không rỗng
      const emailToCheck =
        emailChanged && cleanData.email && cleanData.email.trim() !== '' ? cleanData.email : null;

      // Chỉ kiểm tra số điện thoại nếu có thay đổi
      const phoneToCheck = phoneChanged ? cleanData.phone : null;

      if (emailToCheck || phoneToCheck) {
        const existsCheck = await checkCustomerExists(phoneToCheck, emailToCheck);

        // Nếu tìm thấy khách hàng trùng lặp và không phải là khách hàng hiện tại
        if (existsCheck.exists && existsCheck.customer.id !== customerId) {
          const fieldName = existsCheck.duplicateField === 'phone' ? 'Số điện thoại' : 'Email';
          return {
            success: false,
            error: {
              message: `${fieldName} này đã được sử dụng bởi một khách hàng khác.`,
              code: `DUPLICATE_${existsCheck.duplicateField.toUpperCase()}`,
              field: existsCheck.duplicateField,
            },
            data: null,
          };
        }
      }
    }

    // Log dữ liệu trước khi gửi để debug
    console.log('Clean customer data to update:', cleanData);

    // Nếu không có trùng lặp, cập nhật khách hàng
    return updateData(TABLE_NAME, cleanData, { id: customerId });
  } catch (error) {
    // Xử lý lỗi vi phạm ràng buộc unique
    if (error.code === '23505') {
      // Mã lỗi cho vi phạm ràng buộc unique
      if (error.message && error.message.includes('customers_phone_key')) {
        return {
          success: false,
          error: {
            message: 'Số điện thoại này đã được sử dụng bởi một khách hàng khác.',
            code: 'DUPLICATE_PHONE',
            field: 'phone',
          },
          data: null,
        };
      } else if (error.message && error.message.includes('customers_email_key')) {
        return {
          success: false,
          error: {
            message: 'Email này đã được sử dụng bởi một khách hàng khác.',
            code: 'DUPLICATE_EMAIL',
            field: 'email',
          },
          data: null,
        };
      }
    }

    // Xử lý lỗi "cannot extract elements from a scalar"
    if (error.message && error.message.includes('cannot extract elements from a scalar')) {
      console.error('Scalar error in updateCustomer:', error, 'Data:', customerData);
      return {
        success: false,
        error: {
          message: 'Có lỗi xảy ra khi xử lý dữ liệu. Vui lòng thử lại.',
          code: 'SCALAR_ERROR',
          originalError: error.message,
        },
        data: null,
      };
    }

    // Xử lý các lỗi khác
    console.error('Error updating customer:', error);
    return {
      success: false,
      error: {
        message: error.message || 'Có lỗi xảy ra khi cập nhật khách hàng.',
        code: error.code || 'UNKNOWN_ERROR',
      },
      data: null,
    };
  }
}

/**
 * Xóa khách hàng
 * @param {string} customerId - ID của khách hàng
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function deleteCustomer(customerId) {
  if (!customerId) return { success: false, error: 'Customer ID is required', data: null };

  return deleteData(TABLE_NAME, { id: customerId });
}

/**
 * Tạo địa chỉ mới cho khách hàng
 * @param {Object} addressData - Dữ liệu địa chỉ
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function createCustomerAddress(addressData) {
  try {
    console.log('Creating customer address with data:', addressData);

    // Kiểm tra customerId hoặc customer_id
    const customerId = addressData.customerId || addressData.customer_id;
    if (!customerId) {
      console.error('Missing customerId in address data:', addressData);
      return {
        success: false,
        error: {
          message: 'Thiếu ID khách hàng để tạo địa chỉ',
          code: 'MISSING_CUSTOMER_ID',
          field: 'customerId',
        },
        data: null,
      };
    }

    // Validation dữ liệu địa chỉ
    if (!addressData.address || addressData.address.trim() === '') {
      return {
        success: false,
        error: {
          message: 'Địa chỉ chi tiết là bắt buộc',
          code: 'MISSING_ADDRESS',
          field: 'address',
        },
        data: null,
      };
    }

    // Đảm bảo field mapping chính xác cho database
    const cleanData = { ...addressData };

    // Mapping customerId -> customer_id
    if (cleanData.customerId && !cleanData.customer_id) {
      cleanData.customer_id = cleanData.customerId;
      delete cleanData.customerId;
    }

    // Đảm bảo các field không null
    cleanData.full_name = cleanData.fullName || cleanData.full_name || '';
    cleanData.address = cleanData.address || '';
    cleanData.address_line2 = cleanData.addressLine2 || cleanData.address_line2 || '';
    cleanData.province = cleanData.province || '';
    cleanData.district = cleanData.district || '';
    cleanData.ward = cleanData.ward || '';
    cleanData.city = cleanData.city || '';
    cleanData.state = cleanData.state || '';
    cleanData.postal_code = cleanData.postalCode || cleanData.postal_code || '';
    cleanData.country = cleanData.country || 'Vietnam';
    cleanData.notes = cleanData.notes || '';
    cleanData.address_type = cleanData.addressType || cleanData.address_type || 'shipping';
    cleanData.is_default = cleanData.isDefault || cleanData.is_default || false;
    cleanData.is_default_shipping = cleanData.isDefaultShipping || cleanData.is_default_shipping || false;
    cleanData.is_default_billing = cleanData.isDefaultBilling || cleanData.is_default_billing || false;
    cleanData.is_billing = cleanData.isBilling || cleanData.is_billing || false;

    // Xóa các field camelCase để tránh conflict
    delete cleanData.fullName;
    delete cleanData.addressLine2;
    delete cleanData.postalCode;
    delete cleanData.addressType;
    delete cleanData.isDefault;
    delete cleanData.isDefaultShipping;
    delete cleanData.isDefaultBilling;
    delete cleanData.isBilling;

    console.log('Clean address data for database:', cleanData);

    const result = await createData(ADDRESS_TABLE_NAME, cleanData);

    if (result.success) {
      console.log('Address created successfully:', result.data);
    } else {
      console.error('Failed to create address:', result.error);
    }

    return result;
  } catch (error) {
    console.error('Error in createCustomerAddress:', error);
    return {
      success: false,
      error: {
        message: error.message || 'Có lỗi xảy ra khi tạo địa chỉ',
        code: 'CREATE_ADDRESS_ERROR',
      },
      data: null,
    };
  }
}

/**
 * Cập nhật địa chỉ khách hàng
 * @param {string} addressId - ID của địa chỉ
 * @param {Object} addressData - Dữ liệu cập nhật
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function updateCustomerAddress(addressId, addressData) {
  try {
    if (!addressId) {
      return {
        success: false,
        error: {
          message: 'Thiếu ID địa chỉ để cập nhật',
          code: 'MISSING_ADDRESS_ID',
          field: 'addressId',
        },
        data: null,
      };
    }

    console.log('Updating address with ID:', addressId, 'Data:', addressData);

    // Validation dữ liệu địa chỉ
    if (addressData.address !== undefined && (!addressData.address || addressData.address.trim() === '')) {
      return {
        success: false,
        error: {
          message: 'Địa chỉ chi tiết là bắt buộc',
          code: 'MISSING_ADDRESS',
          field: 'address',
        },
        data: null,
      };
    }

    // Đảm bảo field mapping chính xác cho database
    const cleanData = { ...addressData };

    // Mapping camelCase -> snake_case cho các field cần thiết
    if (cleanData.fullName !== undefined) {
      cleanData.full_name = cleanData.fullName;
      delete cleanData.fullName;
    }
    if (cleanData.addressLine2 !== undefined) {
      cleanData.address_line2 = cleanData.addressLine2;
      delete cleanData.addressLine2;
    }
    if (cleanData.postalCode !== undefined) {
      cleanData.postal_code = cleanData.postalCode;
      delete cleanData.postalCode;
    }
    if (cleanData.addressType !== undefined) {
      cleanData.address_type = cleanData.addressType;
      delete cleanData.addressType;
    }
    if (cleanData.isDefault !== undefined) {
      cleanData.is_default = cleanData.isDefault;
      delete cleanData.isDefault;
    }
    if (cleanData.isDefaultShipping !== undefined) {
      cleanData.is_default_shipping = cleanData.isDefaultShipping;
      delete cleanData.isDefaultShipping;
    }
    if (cleanData.isDefaultBilling !== undefined) {
      cleanData.is_default_billing = cleanData.isDefaultBilling;
      delete cleanData.isDefaultBilling;
    }
    if (cleanData.isBilling !== undefined) {
      cleanData.is_billing = cleanData.isBilling;
      delete cleanData.isBilling;
    }

    // Xóa customerId nếu có (không cần update)
    delete cleanData.customerId;
    delete cleanData.customer_id;

    console.log('Clean address data for update:', cleanData);

    const result = await updateData(ADDRESS_TABLE_NAME, cleanData, { id: addressId });

    if (result.success) {
      console.log('Address updated successfully:', result.data);
    } else {
      console.error('Failed to update address:', result.error);
    }

    return result;
  } catch (error) {
    console.error('Error in updateCustomerAddress:', error);
    return {
      success: false,
      error: {
        message: error.message || 'Có lỗi xảy ra khi cập nhật địa chỉ',
        code: 'UPDATE_ADDRESS_ERROR',
      },
      data: null,
    };
  }
}

/**
 * Xóa địa chỉ khách hàng
 * @param {string} addressId - ID của địa chỉ
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function deleteCustomerAddress(addressId) {
  if (!addressId) return { success: false, error: 'Address ID is required', data: null };

  return deleteData(ADDRESS_TABLE_NAME, { id: addressId });
}

/**
 * Đặt địa chỉ mặc định cho khách hàng
 * @param {string} customerId - ID của khách hàng
 * @param {string} addressId - ID của địa chỉ
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function setDefaultAddress(customerId, addressId) {
  if (!customerId || !addressId) {
    return { success: false, error: 'Customer ID and Address ID are required', data: null };
  }

  // Đầu tiên, đặt tất cả địa chỉ của khách hàng thành không mặc định
  await updateData(ADDRESS_TABLE_NAME, { is_default: false }, { customer_id: customerId });

  // Sau đó, đặt địa chỉ được chọn thành mặc định
  return updateData(ADDRESS_TABLE_NAME, { is_default: true }, { id: addressId });
}

/**
 * Xóa hình ảnh đại diện khách hàng
 * @param {string} imageUrl - URL hình ảnh cần xóa
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function deleteCustomerAvatar(imageUrl) {
  try {
    if (!imageUrl) return { success: true };

    console.log('Deleting image with URL:', imageUrl);

    // Sử dụng hàm mới để trích xuất đường dẫn từ URL
    const imagePath = storageService.extractPathFromUrl(imageUrl, 'public');
    if (!imagePath) {
      console.warn('Could not extract path from image URL:', imageUrl);
      return { success: false, error: 'Invalid image URL format' };
    }

    // Kiểm tra xem đường dẫn có chứa 'public/' ở đầu không
    const cleanPath = imagePath.startsWith('public/')
      ? imagePath.substring(7) // Cắt bỏ 'public/' ở đầu nếu có
      : imagePath;

    console.log('Clean path for deletion:', cleanPath);

    // Kiểm tra xem file có tồn tại trước khi xóa
    const fileExists = await storageService.checkFileExists('public', cleanPath);
    if (!fileExists.exists) {
      console.warn('Image file not found in storage:', cleanPath);
      // Vẫn trả về success vì mục tiêu là đảm bảo hình ảnh không còn tồn tại
      return { success: true, data: null, message: 'File not found in storage' };
    }

    // Xóa hình ảnh từ storage
    const deleteResult = await storageService.deleteFiles('public', cleanPath);
    if (deleteResult.success) {
      console.log('Image deleted successfully:', cleanPath);
    } else {
      console.error('Failed to delete image:', deleteResult.error);
    }
    return deleteResult;
  } catch (error) {
    console.error('Error deleting customer avatar:', error);
    return { success: false, error };
  }
}

/**
 * Hook để lấy danh sách khách hàng
 * @param {Object} options - Các tùy chọn truy vấn
 * @returns {Object} - Kết quả và các hàm điều khiển
 */
export function useCustomers(options = {}) {
  const [data, setData] = useState(null);
  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isValidating, setIsValidating] = useState(false);

  // Use useRef to avoid re-renders when only storing values
  const optionsRef = useRef(options);

  // Update ref when options change
  useEffect(() => {
    optionsRef.current = options;
  }, [options]);

  const fetchCustomers = useCallback(async () => {
    setIsValidating(true);
    try {
      // Use ref value to always get the latest options
      const currentOptions = optionsRef.current;

      const result = await getCustomers(currentOptions);
      setData(result);
      setError(result.error || null);
      return result;
    } catch (err) {
      console.error('Error fetching customers:', err);
      setError(err);
      return { success: false, error: err, data: null };
    } finally {
      setIsValidating(false);
      setIsLoading(false);
    }
  }, []); // No dependency on options because we're using ref

  // Hàm để tải lại dữ liệu
  const mutate = useCallback(async () => {
    console.log('Manually refreshing customers data...');
    return fetchCustomers();
  }, [fetchCustomers]);

  // Tải dữ liệu khi component mount
  useEffect(() => {
    console.log('Initial customers data load');
    setIsLoading(true);
    fetchCustomers();
  }, [fetchCustomers]);

  // Track options changes and reload data when needed
  const prevOptionsRef = useRef('');

  useEffect(() => {
    const optionsString = JSON.stringify(options);
    // Only call API when options actually change (deep comparison)
    if (prevOptionsRef.current !== optionsString) {
      console.log('Customers options changed, reloading data');
      prevOptionsRef.current = optionsString;
      setIsLoading(true);
      fetchCustomers();
    }
  }, [options, fetchCustomers]);

  const memoizedValue = useMemo(() => {
    const result = {
      customers: data?.data || [],
      totalCount: data?.count || 0,
      isLoading,
      isError: !!error,
      error,
      isValidating,
      mutate,
      isEmpty: !isLoading && !isValidating && (!data?.data || data.data.length === 0),
    };

    return result;
  }, [data, error, isLoading, isValidating, mutate]);

  return memoizedValue;
}

/**
 * Hook để lấy chi tiết khách hàng
 * @param {string} customerId - ID của khách hàng
 * @returns {Object} - Kết quả và các hàm điều khiển
 */
export function useCustomer(customerId) {
  const [data, setData] = useState(null);
  const [addresses, setAddresses] = useState([]);
  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isValidating, setIsValidating] = useState(false);

  const fetchCustomer = useCallback(async () => {
    if (!customerId) {
      setData(null);
      setAddresses([]);
      setError(null);
      setIsLoading(false);
      setIsValidating(false);
      return null;
    }

    setIsValidating(true);
    try {
      // Lấy thông tin khách hàng
      const customerResult = await getCustomerById(customerId);
      setData(customerResult);

      // Lấy danh sách địa chỉ của khách hàng
      const addressesResult = await getCustomerAddresses(customerId);
      setAddresses(addressesResult.data || []);

      setError(customerResult.error || null);
      return { customer: customerResult, addresses: addressesResult };
    } catch (err) {
      setError(err);
      return { success: false, error: err, data: null, addresses: [] };
    } finally {
      setIsValidating(false);
      setIsLoading(false);
    }
  }, [customerId]);

  // Hàm để tải lại dữ liệu
  const mutate = useCallback(async () => fetchCustomer(), [fetchCustomer]);

  // Tải dữ liệu khi component mount hoặc customerId thay đổi
  useEffect(() => {
    if (customerId) {
      setIsLoading(true);
      fetchCustomer();
    }
  }, [fetchCustomer, customerId]);

  const memoizedValue = useMemo(
    () => ({
      customer: data?.data || null,
      addresses,
      isLoading,
      isError: !!error,
      error,
      isValidating,
      mutate,
    }),
    [data, addresses, error, isLoading, isValidating, mutate]
  );

  return memoizedValue;
}

/**
 * Hook để tạo, cập nhật, xóa khách hàng
 * @returns {Object} - Các hàm mutation
 */
export function useCustomerMutations() {
  const [loadingStates, setLoadingStates] = useState({
    creating: false,
    updating: false,
    deleting: false,
    uploadingAvatar: false,
    creatingAddress: false,
    updatingAddress: false,
    deletingAddress: false,
  });

  // Helper function to handle loading state and error handling
  const withLoadingState = async (stateKey, asyncFn) => {
    setLoadingStates((prev) => ({ ...prev, [stateKey]: true }));
    try {
      return await asyncFn();
    } finally {
      setLoadingStates((prev) => ({ ...prev, [stateKey]: false }));
    }
  };

  // Mutation functions using the helper
  const createCustomerMutation = (customerData) =>
    withLoadingState('creating', () => createCustomer(customerData));

  const updateCustomerMutation = (id, data) =>
    withLoadingState('updating', () => updateCustomer(id, data));

  const deleteCustomerMutation = (customerId) =>
    withLoadingState('deleting', () => deleteCustomer(customerId));

  // Hàm xử lý tải lên hình ảnh đại diện
  const uploadAvatarMutation = (imageFile, oldImageUrl) =>
    withLoadingState('uploadingAvatar', () => uploadCustomerAvatar(imageFile, oldImageUrl));

  // Hàm xóa hình ảnh đại diện
  const deleteAvatarMutation = (imageUrl) =>
    withLoadingState('uploadingAvatar', () => deleteCustomerAvatar(imageUrl));

  // Hàm tạo địa chỉ mới
  const createAddressMutation = (addressData) =>
    withLoadingState('creatingAddress', () => createCustomerAddress(addressData));

  // Hàm cập nhật địa chỉ
  const updateAddressMutation = (addressId, addressData) =>
    withLoadingState('updatingAddress', () => updateCustomerAddress(addressId, addressData));

  // Hàm xóa địa chỉ
  const deleteAddressMutation = (addressId) =>
    withLoadingState('deletingAddress', () => deleteCustomerAddress(addressId));

  // Hàm đặt địa chỉ mặc định
  const setDefaultAddressMutation = (customerId, addressId) =>
    withLoadingState('updatingAddress', () => setDefaultAddress(customerId, addressId));

  // Calculate if any mutation is in progress
  const isMutating = Object.values(loadingStates).some(Boolean);

  return {
    createCustomer: createCustomerMutation,
    updateCustomer: updateCustomerMutation,
    deleteCustomer: deleteCustomerMutation,
    uploadCustomerAvatar: uploadAvatarMutation,
    deleteCustomerAvatar: deleteAvatarMutation,
    createCustomerAddress: createAddressMutation,
    updateCustomerAddress: updateAddressMutation,
    deleteCustomerAddress: deleteAddressMutation,
    setDefaultAddress: setDefaultAddressMutation,
    isCreating: loadingStates.creating,
    isUpdating: loadingStates.updating,
    isDeleting: loadingStates.deleting,
    isUploadingAvatar: loadingStates.uploadingAvatar,
    isCreatingAddress: loadingStates.creatingAddress,
    isUpdatingAddress: loadingStates.updatingAddress,
    isDeletingAddress: loadingStates.deletingAddress,
    isMutating,
  };
}
