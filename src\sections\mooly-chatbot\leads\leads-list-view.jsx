'use client';

import { useState, useCallback, useEffect, useMemo } from 'react';

import {
  Box,
  Card,
  Stack,
  Button,
  Container,
  Typography,
  Alert,
  alpha,
} from '@mui/material';

import { useBoolean } from 'src/hooks/use-boolean';
import { useDebounce } from 'minimal-shared/hooks';

import { DashboardContent } from 'src/layouts/dashboard';

import { Iconify } from 'src/components/iconify';
import { Scrollbar } from 'src/components/scrollbar';
import { useSettingsContext } from 'src/components/settings';

import LeadsTableToolbar from './leads-table-toolbar';
import LeadsTable from './leads-table';
import LeadNewDialog from './lead-new-dialog';
import LeadEditDialog from './lead-edit-dialog';
import LeadDetailDialog from './lead-detail-dialog';
import LeadDeleteDialog from './lead-delete-dialog';
import WorkflowBuilderDialog from './workflow-builder-dialog';
import WorkflowSelector from './workflow-selector';

import { useLeads } from 'src/actions/mooly-chatbot/chatbot-lead-service';
import { useChatbotWorkflow } from 'src/actions/mooly-chatbot/workflow-config-service';

// =====================================================
// COMPONENT
// =====================================================

export default function LeadsListView() {
  const settings = useSettingsContext();

  const [selectedLeads, setSelectedLeads] = useState([]);
  const [currentLead, setCurrentLead] = useState(null);
  const [filters, setFilters] = useState({
    name: '',
    status: 'all',
    startDate: null,
    endDate: null,
  });

  // Dialog states
  const newDialog = useBoolean();
  const editDialog = useBoolean();
  const detailDialog = useBoolean();
  const deleteDialog = useBoolean();
  const workflowDialog = useBoolean();

  // Debounce search để tránh gọi API quá nhiều
  const debouncedSearchName = useDebounce(filters.name, 500);

  // Memoize các primitive values để tránh re-render không cần thiết
  const statusFilter = useMemo(() => 
    filters.status !== 'all' ? filters.status : undefined
  , [filters.status]);

  const searchFilter = useMemo(() => 
    debouncedSearchName?.trim() || undefined
  , [debouncedSearchName]);

  // Memoize options object với primitive values để tránh object reference thay đổi
  const leadsOptions = useMemo(() => {
    const options = {
      page: 1,
      limit: 100,
    };

    // Chỉ thêm vào options nếu có giá trị thực sự
    if (statusFilter) {
      options.status = statusFilter;
    }
    if (searchFilter) {
      options.search = searchFilter;
    }

    return options;
  }, [statusFilter, searchFilter]);

  // Load leads data using hook - null chatbotId to get all leads
  const { leads, loading, error, mutate } = useLeads(null, leadsOptions);

  // Handle filter change
  const handleFilters = useCallback((name, value) => {
    setFilters(prev => ({
      ...prev,
      [name]: value,
    }));
  }, []);

  // Reset filters
  const handleResetFilters = useCallback(() => {
    setFilters({
      name: '',
      status: 'all',
      startDate: null,
      endDate: null,
    });
  }, []);

  // Handle lead selection
  const handleSelectRow = useCallback((id) => {
    setSelectedLeads(prev => {
      if (prev.includes(id)) {
        return prev.filter(leadId => leadId !== id);
      }
      return [...prev, id];
    });
  }, []);

  const handleSelectAllRows = useCallback((checked, newSelecteds) => {
    if (checked) {
      setSelectedLeads(newSelecteds);
    } else {
      setSelectedLeads([]);
    }
  }, []);

  // Handle new lead
  const handleNewLead = useCallback(() => {
    setCurrentLead(null);
    newDialog.onTrue();
  }, [newDialog]);

  // Handle edit lead
  const handleEditLead = useCallback((lead) => {
    setCurrentLead(lead);
    editDialog.onTrue();
  }, [editDialog]);

  // Handle view lead detail
  const handleViewLead = useCallback((lead) => {
    setCurrentLead(lead);
    detailDialog.onTrue();
  }, [detailDialog]);

  // Handle delete lead
  const handleDeleteLead = useCallback((lead) => {
    setCurrentLead(lead);
    deleteDialog.onTrue();
  }, [deleteDialog]);

  // Handle delete multiple leads
  const handleDeleteSelected = useCallback(() => {
    if (selectedLeads.length > 0) {
      deleteDialog.onTrue();
    }
  }, [selectedLeads.length, deleteDialog]);

  // Handle workflow config
  const handleWorkflowConfig = useCallback(() => {
    workflowDialog.onTrue();
  }, [workflowDialog]);

  // Handle create success - optimistic update đơn giản
  const handleCreateSuccess = useCallback((newLead) => {
    // supabase-utils đã convert key, chỉ cần thêm vào đầu danh sách
    const leadToAdd = Array.isArray(newLead) ? newLead[0] : newLead;

    mutate((currentData) => {
      if (currentData?.success && Array.isArray(currentData.data)) {
        return {
          ...currentData,
          data: [leadToAdd, ...currentData.data],
          count: (currentData.count || 0) + 1,
        };
      }
      return currentData;
    }, false);

    setSelectedLeads([]);
  }, [mutate]);

  // Handle update success - optimistic update đơn giản
  const handleUpdateSuccess = useCallback((updatedLead) => {
    // supabase-utils đã convert key, chỉ cần update trong danh sách
    const leadToUpdate = Array.isArray(updatedLead) ? updatedLead[0] : updatedLead;

    mutate((currentData) => {
      if (currentData?.success && Array.isArray(currentData.data)) {
        return {
          ...currentData,
          data: currentData.data.map(lead =>
            lead.id === leadToUpdate.id ? leadToUpdate : lead
          )
        };
      }
      return currentData;
    }, false);

    setCurrentLead(null);
  }, [mutate]);

  // Handle delete success - refresh data với optimistic update
  const handleDeleteSuccess = useCallback((deletedIds) => {
    const idsArray = Array.isArray(deletedIds) ? deletedIds : [deletedIds];
    
    // Sử dụng mutate với optimistic update
    mutate((currentData) => {
      if (currentData?.success && Array.isArray(currentData.data)) {
        const filteredData = currentData.data.filter(lead => !idsArray.includes(lead.id));
        return {
          ...currentData,
          data: filteredData,
          count: Math.max((currentData.count || 0) - idsArray.length, 0),
        };
      }
      return currentData;
    }, false); // false để không revalidate ngay lập tức
    
    setSelectedLeads([]);
    setCurrentLead(null);
  }, [mutate]);

  // Handle refresh - force revalidate
  const handleRefresh = useCallback(() => {
    mutate(); // Revalidate với server
  }, [mutate]);

  // Stats calculations - memoize với shallow comparison của leads array
  const stats = useMemo(() => {
    if (!Array.isArray(leads)) return [];
    
    const total = leads.length;
    const newLeads = leads.filter(l => l.status === 'new').length;
    const contacted = leads.filter(l => l.status === 'contacted').length;
    const converted = leads.filter(l => l.status === 'converted').length;

    return [
      { label: 'Tổng Leads', value: total, color: 'primary' },
      { label: 'Leads Mới', value: newLeads, color: 'info' },
      { label: 'Đã Liên Hệ', value: contacted, color: 'warning' },
      { label: 'Đã Chuyển Đổi', value: converted, color: 'success' },
    ];
  }, [leads]);

  // Memoize table handlers để tránh re-render table không cần thiết
  const tableHandlers = useMemo(() => ({
    onSelectRow: handleSelectRow,
    onSelectAllRows: handleSelectAllRows,
    onEditRow: handleEditLead,
    onViewRow: handleViewLead,
    onDeleteRow: handleDeleteLead,
  }), [handleSelectRow, handleSelectAllRows, handleEditLead, handleViewLead, handleDeleteLead]);

  return (
    <DashboardContent>
      <Container maxWidth='lg'>
        <Stack spacing={3}>
          {/* Header */}
          <Stack direction="row" alignItems="center" justifyContent="space-between">
            <Stack>
              <Typography variant="h4">Quản lý Leads</Typography>
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                Quản lý và theo dõi leads từ AI chatbot với tính năng mini CRM
              </Typography>
            </Stack>

            <Stack direction="row" spacing={2}>
              <Button
                variant="outlined"
                startIcon={<Iconify icon="solar:refresh-bold" />}
                onClick={handleRefresh}
                disabled={loading}
              >
                Làm mới
              </Button>
              
              <Button
                variant="outlined"
                startIcon={<Iconify icon="solar:settings-bold" />}
                onClick={handleWorkflowConfig}
              >
                Cấu hình Workflow
              </Button>

              <Button
                variant="outlined"
                startIcon={<Iconify icon="solar:widget-5-bold" />}
                href="/dashboard/mooly-chatbot/leads/kanban"
              >
                Kanban View
              </Button>
              
              <Button
                variant="contained"
                startIcon={<Iconify icon="mingcute:add-line" />}
                onClick={handleNewLead}
              >
                Thêm Lead
              </Button>
            </Stack>
          </Stack>

          {/* Error Alert */}
          {error && (
            <Alert severity="error">
              {typeof error === 'string' ? error : error.message || 'Có lỗi xảy ra khi tải dữ liệu'}
            </Alert>
          )}

          {/* Stats Cards */}
          <Stack direction="row" spacing={3}>
            {stats.map((stat) => (
              <Card key={stat.label} sx={{ p: 3, flexGrow: 1 }}>
                <Stack>
                  <Typography variant="h3" color={`${stat.color}.main`}>
                    {stat.value}
                  </Typography>
                  <Typography variant="subtitle2" sx={{ color: 'text.secondary' }}>
                    {stat.label}
                  </Typography>
                </Stack>
              </Card>
            ))}
          </Stack>

          {/* Table */}
          <Card>
            <LeadsTableToolbar
              filters={filters}
              onFilters={handleFilters}
              onResetFilters={handleResetFilters}
              selectedLeads={selectedLeads}
              onDeleteSelected={handleDeleteSelected}
            />

            <Scrollbar>
              <LeadsTable
                tableData={leads}
                onSelectRow={tableHandlers.onSelectRow}
                onSelectAllRows={tableHandlers.onSelectAllRows}
                onEditRow={tableHandlers.onEditRow}
                onViewRow={tableHandlers.onViewRow}
                onDeleteRow={tableHandlers.onDeleteRow}
                selectedRows={selectedLeads}
                loading={loading}
              />
            </Scrollbar>
          </Card>
        </Stack>

        {/* Dialogs */}
        <LeadNewDialog
          open={newDialog.value}
          onClose={newDialog.onFalse}
          onSuccess={(newLead) => {
            handleCreateSuccess(newLead);
            newDialog.onFalse();
          }}
        />

        <LeadEditDialog
          open={editDialog.value}
          onClose={editDialog.onFalse}
          currentLead={currentLead}
          onSuccess={(updatedLead) => {
            handleUpdateSuccess(updatedLead);
            editDialog.onFalse();
          }}
        />

        <LeadDetailDialog
          open={detailDialog.value}
          onClose={detailDialog.onFalse}
          currentLead={currentLead}
          onEdit={tableHandlers.onEditRow}
        />

        <LeadDeleteDialog
          open={deleteDialog.value}
          onClose={deleteDialog.onFalse}
          currentLead={currentLead}
          selectedLeads={selectedLeads}
          onSuccess={(deletedIds) => {
            handleDeleteSuccess(deletedIds);
            deleteDialog.onFalse();
          }}
        />

        <WorkflowBuilderDialog
          open={workflowDialog.value}
          onClose={workflowDialog.onFalse}
          onSuccess={() => {
            // Refresh leads data
            mutate();
          }}
        />
      </Container>
    </DashboardContent>
  );
} 