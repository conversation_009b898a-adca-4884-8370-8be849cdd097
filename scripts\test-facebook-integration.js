#!/usr/bin/env node

/**
 * Facebook Integration Test Script
 * Test all Facebook integration functionality
 */

const fetch = require('node-fetch');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Configuration
const BASE_URL = process.env.NEXTAUTH_URL || 'http://localhost:3000';
const TEST_PAGE_ID = process.env.TEST_PAGE_ID;

// Test scenarios
const TEST_SCENARIOS = [
  {
    name: 'Product Inquiry',
    message: '<PERSON>n chào, tôi muốn hỏi về sản phẩm của bạn',
    eventType: 'facebook_comment'
  },
  {
    name: 'Price Question',
    message: '<PERSON><PERSON><PERSON> bao nhiều vậy?',
    eventType: 'facebook_comment'
  },
  {
    name: 'Contact Request',
    message: 'Làm sao để liên hệ với bạn?',
    eventType: 'facebook_message'
  },
  {
    name: 'Instagram Comment',
    message: 'Sản phẩm này còn không?',
    eventType: 'instagram_comment'
  },
  {
    name: 'Co<PERSON><PERSON><PERSON>',
    message: 'T<PERSON><PERSON> không hài lòng với dịch vụ',
    eventType: 'facebook_message'
  }
];

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function testWebhookEndpoint() {
  log('\n🔍 Testing webhook endpoint...', 'cyan');
  
  try {
    // Test webhook verification
    const verifyUrl = `${BASE_URL}/api/facebook-webhooks?hub.mode=subscribe&hub.verify_token=${process.env.FACEBOOK_WEBHOOK_VERIFY_TOKEN}&hub.challenge=test_challenge`;
    
    const response = await fetch(verifyUrl);
    const text = await response.text();
    
    if (response.ok && text === 'test_challenge') {
      log('✅ Webhook verification successful', 'green');
      return true;
    } else {
      log('❌ Webhook verification failed', 'red');
      log(`Response: ${response.status} - ${text}`, 'red');
      return false;
    }
  } catch (error) {
    log(`❌ Webhook test error: ${error.message}`, 'red');
    return false;
  }
}

async function testAutoReplyConfig(pageId) {
  log('\n🔧 Testing auto reply configuration...', 'cyan');
  
  try {
    // Get current config
    const getResponse = await fetch(`${BASE_URL}/api/facebook-integration/auto-reply-config?pageId=${pageId}`);
    const getData = await getResponse.json();
    
    if (!getResponse.ok) {
      log(`❌ Failed to get config: ${getData.error}`, 'red');
      return false;
    }
    
    log('✅ Auto reply config retrieved successfully', 'green');
    
    // Test config update
    const testConfig = {
      pageId,
      enableCommentReply: true,
      enableMessageReply: true,
      enableInstagramComments: true,
      enableInstagramMessages: true,
      replyPrompt: 'Test prompt for auto reply',
      replyTone: 'friendly',
      replyLanguage: 'vi',
      maxReplyLength: 500,
      businessInfo: 'Test business information',
      products: 'Test products',
      policies: 'Test policies'
    };
    
    const postResponse = await fetch(`${BASE_URL}/api/facebook-integration/auto-reply-config`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(testConfig)
    });
    
    const postData = await postResponse.json();
    
    if (postResponse.ok && postData.success) {
      log('✅ Auto reply config updated successfully', 'green');
      return true;
    } else {
      log(`❌ Failed to update config: ${postData.error}`, 'red');
      return false;
    }
  } catch (error) {
    log(`❌ Config test error: ${error.message}`, 'red');
    return false;
  }
}

async function testAutoReplyGeneration(pageId) {
  log('\n🤖 Testing auto reply generation...', 'cyan');
  
  let successCount = 0;
  const totalTests = TEST_SCENARIOS.length;
  
  for (const scenario of TEST_SCENARIOS) {
    try {
      log(`\n  Testing: ${scenario.name}`, 'yellow');
      log(`  Message: "${scenario.message}"`, 'blue');
      log(`  Type: ${scenario.eventType}`, 'blue');
      
      const response = await fetch(`${BASE_URL}/api/facebook-integration/test-auto-reply`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          pageId,
          testMessage: scenario.message,
          eventType: scenario.eventType,
          skipSend: true
        })
      });
      
      const data = await response.json();
      
      if (response.ok && data.success) {
        log(`  ✅ Generated response: "${data.test.generatedResponse}"`, 'green');
        successCount++;
      } else {
        log(`  ❌ Failed: ${data.message || data.error}`, 'red');
      }
      
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 1000));
      
    } catch (error) {
      log(`  ❌ Test error: ${error.message}`, 'red');
    }
  }
  
  log(`\n📊 Auto reply test results: ${successCount}/${totalTests} successful`, 
      successCount === totalTests ? 'green' : 'yellow');
  
  return successCount === totalTests;
}

async function testAnalytics(pageId) {
  log('\n📈 Testing analytics...', 'cyan');
  
  try {
    const response = await fetch(`${BASE_URL}/api/facebook-integration/analytics?pageId=${pageId}&period=7d`);
    const data = await response.json();
    
    if (response.ok && data.success) {
      log('✅ Analytics retrieved successfully', 'green');
      log(`  Total activities: ${data.analytics.totalActivities}`, 'blue');
      log(`  Auto replies sent: ${data.analytics.autoRepliesSent}`, 'blue');
      log(`  Success rate: ${data.analytics.successRate}%`, 'blue');
      return true;
    } else {
      log(`❌ Analytics test failed: ${data.error}`, 'red');
      return false;
    }
  } catch (error) {
    log(`❌ Analytics test error: ${error.message}`, 'red');
    return false;
  }
}

async function testWebhookSecurity() {
  log('\n🔒 Testing webhook security...', 'cyan');
  
  try {
    // Test without signature
    const response1 = await fetch(`${BASE_URL}/api/facebook-webhooks`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ test: 'data' })
    });
    
    if (response1.status === 403) {
      log('✅ Webhook correctly rejects requests without signature', 'green');
    } else {
      log('❌ Webhook should reject requests without signature', 'red');
      return false;
    }
    
    // Test with invalid signature
    const response2 = await fetch(`${BASE_URL}/api/facebook-webhooks`, {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        'x-hub-signature-256': 'sha256=invalid_signature'
      },
      body: JSON.stringify({ test: 'data' })
    });
    
    if (response2.status === 403) {
      log('✅ Webhook correctly rejects requests with invalid signature', 'green');
      return true;
    } else {
      log('❌ Webhook should reject requests with invalid signature', 'red');
      return false;
    }
  } catch (error) {
    log(`❌ Security test error: ${error.message}`, 'red');
    return false;
  }
}

async function runAllTests() {
  log('🚀 Starting Facebook Integration Tests', 'bright');
  log('=====================================', 'bright');
  
  // Check environment variables
  if (!process.env.FACEBOOK_WEBHOOK_VERIFY_TOKEN) {
    log('❌ FACEBOOK_WEBHOOK_VERIFY_TOKEN not set', 'red');
    return;
  }
  
  let pageId = TEST_PAGE_ID;
  if (!pageId) {
    pageId = await question('Enter test page ID: ');
    if (!pageId) {
      log('❌ Page ID required for testing', 'red');
      return;
    }
  }
  
  const results = [];
  
  // Run tests
  results.push(await testWebhookEndpoint());
  results.push(await testWebhookSecurity());
  results.push(await testAutoReplyConfig(pageId));
  results.push(await testAutoReplyGeneration(pageId));
  results.push(await testAnalytics(pageId));
  
  // Summary
  const passed = results.filter(Boolean).length;
  const total = results.length;
  
  log('\n📋 Test Summary', 'bright');
  log('===============', 'bright');
  log(`Passed: ${passed}/${total}`, passed === total ? 'green' : 'yellow');
  
  if (passed === total) {
    log('🎉 All tests passed! Facebook integration is working correctly.', 'green');
  } else {
    log('⚠️  Some tests failed. Please check the configuration and try again.', 'yellow');
  }
  
  rl.close();
}

// Run tests
if (require.main === module) {
  runAllTests().catch(error => {
    log(`💥 Test runner error: ${error.message}`, 'red');
    process.exit(1);
  });
}

module.exports = {
  testWebhookEndpoint,
  testAutoReplyConfig,
  testAutoReplyGeneration,
  testAnalytics,
  testWebhookSecurity
};
