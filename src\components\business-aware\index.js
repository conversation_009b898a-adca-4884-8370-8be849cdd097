/**
 * Business-Aware Components
 *
 * This module provides components that adapt their behavior and appearance
 * based on the business type configuration. These components help create
 * a tailored user experience for different business models.
 */

// Navigation components - now using optimized single config
// Removed useBusinessAwareNavData - use navData from nav-config-dashboard directly

// Dashboard components
export {
  BusinessAwareDashboard,
  BusinessAwareQuickActions
} from './business-aware-dashboard';

// Product form components
export {
  ConditionalFormField,
  BusinessTypeIndicator,
  ConditionalFormSection,
  useBusinessFormContext,
  BusinessAwareProductForm,
  useBusinessAwareFieldOrder,
  useBusinessAwareValidation
} from './business-aware-product-form';

// Re-export business configuration utilities for convenience
export {
  BUSINESS_TYPES,
  isFeatureEnabled,
  useBusinessConfig,
  useBusinessConfigContext,
  BusinessConfigProvider,
  getHiddenFeatures,
  getDashboardConfig,
  getEnabledFeatures,
  shouldShowUIElement,
  getNavigationConfig,
  getProductFormConfig,
  getPrioritizedUIElements,
  refreshBusinessConfig
} from 'src/actions/mooly-chatbot/business-config-service';
