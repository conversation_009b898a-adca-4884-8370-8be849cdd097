import { NextResponse } from 'next/server';

/**
 * API route để cập nhật FAQ trong Weaviate
 * @param {Request} request - <PERSON><PERSON><PERSON> cầu HTTP
 * @returns {Promise<NextResponse>} - <PERSON><PERSON><PERSON> hồi HTTP
 */
export async function PUT(request) {
  try {
    const { supabase_id, tenant_id, updateData } = await request.json();

    // Kiểm tra dữ liệu đầu vào
    if (!supabase_id || !tenant_id || !updateData) {
      return NextResponse.json(
        { error: 'supabase_id, tenant_id và updateData là bắt buộc' },
        { status: 400 }
      );
    }

    // Validate updateData structure
    if (!updateData.topic && !updateData.content && !updateData.bot_id) {
      return NextResponse.json(
        { error: 'updateData phải chứa ít nhất một trong các trường: topic, content, bot_id' },
        { status: 400 }
      );
    }

    // Lấy URL Backend từ biến môi trường
    const BACKEND_API_URL = process.env.BACKEND_API_URL || 'http://localhost:3000';

    // Gọi API Weaviate Backend
    const response = await fetch(`${BACKEND_API_URL}/api/weaviate/faqs/update`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        supabase_id,
        tenant_id,
        updateData,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      return NextResponse.json(
        { error: errorData.message || 'Failed to update FAQ in Weaviate' },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in FAQ update API route:', error);
    return NextResponse.json({ error: error.message || 'Internal server error' }, { status: 500 });
  }
} 