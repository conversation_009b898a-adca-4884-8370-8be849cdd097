import { NextResponse } from 'next/server';
import { createClient } from 'src/utils/supabase/server';

/**
 * Facebook Pages Management API
 * Get connected pages and their status
 */

// GET: Fetch connected Facebook pages
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const chatbotId = searchParams.get('chatbotId');
    const includeConfig = searchParams.get('includeConfig') === 'true';
    const includeActivity = searchParams.get('includeActivity') === 'true';

    const supabase = await createClient();

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      console.error('❌ Authentication error:', userError);
      return NextResponse.json({
        success: false,
        error: 'Authentication required',
        data: []
      }, { status: 401 });
    }

    // Get connected Facebook pages with optimized query
    const { data: pages, error: pagesError } = await supabase
      .from('facebook_accounts')
      .select(`
        id,
        page_id,
        page_name,
        instagram_account_id,
        instagram_username,
        is_active,
        connected_at,
        last_sync_at,
        token_expires_at
      `)
      .eq('is_active', true)
      .order('connected_at', { ascending: false });

    if (pagesError) {
      console.error('❌ Error fetching pages:', pagesError);
      return NextResponse.json({
        success: false,
        error: 'Failed to fetch Facebook pages',
        details: pagesError.message,
        data: []
      }, { status: 500 });
    }

    // Return basic data if no additional info requested
    if (!includeConfig && !includeActivity) {
      const basicPages = (pages || []).map(page => ({
        id: page.id,
        pageId: page.page_id,
        pageName: page.page_name,
        instagramAccountId: page.instagram_account_id,
        instagramUsername: page.instagram_username,
        isActive: page.is_active,
        connectedAt: page.connected_at,
        lastSyncAt: page.last_sync_at,
        tokenExpiresAt: page.token_expires_at,
        hasInstagram: !!page.instagram_account_id
      }));

      return NextResponse.json({
        success: true,
        data: basicPages,
        totalPages: basicPages.length
      });
    }

    // Get additional data if requested (optimized with batch queries)
    let configsMap = new Map();
    let activityCountsMap = new Map();

    if (includeConfig && pages?.length > 0) {
      const pageIds = pages.map(p => p.page_id);
      const { data: configs } = await supabase
        .from('facebook_auto_reply_config')
        .select('*')
        .in('page_id', pageIds);

      configs?.forEach(config => {
        configsMap.set(config.page_id, config);
      });
    }

    if (includeActivity && pages?.length > 0) {
      const pageIds = pages.map(p => p.page_id);
      const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();

      const { data: activities } = await supabase
        .from('facebook_activity_logs')
        .select('page_id')
        .in('page_id', pageIds)
        .gte('created_at', yesterday);

      // Count activities per page
      activities?.forEach(activity => {
        const count = activityCountsMap.get(activity.page_id) || 0;
        activityCountsMap.set(activity.page_id, count + 1);
      });
    }

    // Build enhanced pages data
    const enhancedPages = (pages || []).map(page => {
      const config = configsMap.get(page.page_id);
      const activityCount = activityCountsMap.get(page.page_id) || 0;

      return {
        id: page.id,
        pageId: page.page_id,
        pageName: page.page_name,
        instagramAccountId: page.instagram_account_id,
        instagramUsername: page.instagram_username,
        isActive: page.is_active,
        connectedAt: page.connected_at,
        lastSyncAt: page.last_sync_at,
        tokenExpiresAt: page.token_expires_at,
        hasInstagram: !!page.instagram_account_id,
        autoReplyConfig: config ? {
          enableCommentReply: config.enable_comment_reply,
          enableMessageReply: config.enable_message_reply,
          enableInstagramComments: config.enable_instagram_comments,
          enableInstagramMessages: config.enable_instagram_messages,
          autoPrivateReply: config.auto_private_reply
        } : null,
        recentActivityCount: activityCount
      };
    });

    // Find selected page for chatbot if chatbotId provided
    let selectedPage = null;
    if (chatbotId && enhancedPages.length > 0) {
      // For now, just return the first active page
      // In a real implementation, you'd link chatbot to specific page
      selectedPage = enhancedPages[0];
    }

    return NextResponse.json({
      success: true,
      data: enhancedPages,
      selectedPage,
      totalPages: enhancedPages.length
    });

  } catch (error) {
    console.error('💥 Error in GET pages:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      details: error.message,
      data: []
    }, { status: 500 });
  }
}

// POST: Select a page for chatbot integration
export async function POST(request) {
  try {
    const { chatbotId, pageId } = await request.json();
    
    if (!chatbotId || !pageId) {
      return NextResponse.json({ 
        error: 'Chatbot ID and Page ID required' 
      }, { status: 400 });
    }
    
    const supabase = await createClient();
    
    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }
    
    // Verify page exists and user has access
    const { data: page, error: pageError } = await supabase
      .from('facebook_accounts')
      .select('*')
      .eq('page_id', pageId)
      .single();
    
    if (pageError || !page) {
      return NextResponse.json({ 
        error: 'Page not found or access denied' 
      }, { status: 404 });
    }
    
    // Create or update chatbot-page association
    // For now, we'll store this in the activity logs as a selection event
    await supabase
      .from('facebook_activity_logs')
      .insert({
        page_id: pageId,
        activity: 'chatbot_page_selected',
        metadata: { chatbotId, pageName: page.page_name }
      });
    
    // Subscribe to webhooks for this page
    try {
      const webhookResponse = await fetch(`${request.url.split('/api')[0]}/api/facebook-integration/subscribe-webhooks`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cookie': request.headers.get('cookie') || ''
        },
        body: JSON.stringify({
          pageId: pageId,
          subscriptions: ['feed', 'comments', 'messaging']
        })
      });
      
      const webhookData = await webhookResponse.json();
      console.log('🔔 Webhook subscription result:', webhookData);
      
    } catch (webhookError) {
      console.error('⚠️ Failed to subscribe to webhooks:', webhookError);
      // Don't fail the page selection if webhook subscription fails
    }
    
    return NextResponse.json({
      success: true,
      message: 'Page selected successfully',
      page: {
        pageId: page.page_id,
        pageName: page.page_name,
        instagramAccountId: page.instagram_account_id,
        hasInstagram: !!page.instagram_account_id
      }
    });
    
  } catch (error) {
    console.error('💥 Error in POST pages:', error);
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error.message 
    }, { status: 500 });
  }
}
