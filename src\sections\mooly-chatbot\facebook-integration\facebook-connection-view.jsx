'use client';

import React, { useState, useCallback } from 'react';
import {
    Box,
    Card,
    CardContent,
    Typography,
    Button,
    Alert,
    IconButton,
    Chip,
    Stack,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    LinearProgress,
    Tooltip,
    Skeleton,
    Avatar
} from '@mui/material';
import { LoadingButton } from '@mui/lab';
import { toast } from 'src/components/snackbar';

import {
    useFacebookAccounts,
    useFacebookAccountMutations
} from 'src/actions/mooly-chatbot/facebook-integration';

import FacebookConfigDialog from './facebook-config-dialog';
import SocialMediaConnectionDialog from './facebook-connection-dialog';
import { Iconify } from 'src/components/iconify';
import { DashboardContent } from 'src/layouts/dashboard';

// Error Boundary Component
class FacebookErrorBoundary extends React.Component {
    constructor(props) {
        super(props);
        this.state = { hasError: false, error: null };
    }

    static getDerivedStateFromError(error) {
        return { hasError: true, error };
    }

    componentDidCatch(error, errorInfo) {
        console.error('Facebook Integration Error:', error, errorInfo);
    }

    render() {
        if (this.state.hasError) {
            return (
                <DashboardContent>
                    <Card>
                        <CardContent sx={{ textAlign: 'center', py: 6 }}>
                            <Avatar sx={{ 
                                width: 80, 
                                height: 80, 
                                mx: 'auto', 
                                mb: 2, 
                                bgcolor: 'error.main',
                                fontSize: 40
                            }}>
                                <Iconify icon="mdi:alert-circle" />
                            </Avatar>
                            <Typography variant="h6" color="error" gutterBottom>
                                Đã xảy ra lỗi
                            </Typography>
                            <Typography variant="body2" color="text.secondary" mb={3}>
                                Không thể tải trang kết nối Facebook. Vui lòng thử lại.
                            </Typography>
                            <Button
                                variant="contained"
                                onClick={() => window.location.reload()}
                                startIcon={<Iconify icon="mdi:refresh" />}
                                size="large"
                            >
                                Tải lại trang
                            </Button>
                        </CardContent>
                    </Card>
                </DashboardContent>
            );
        }

        return this.props.children;
    }
}

function FacebookConnectionView() {
    // Use Facebook hooks for data management with basic data only
    const {
        accounts,
        loading: accountsLoading,
        error: accountsError,
        refetch: refetchAccounts
    } = useFacebookAccounts({
        includeConfig: false, // Only load basic data for performance
        includeActivity: false
    });

    const {
        deleteAccount,
        loading: mutationLoading,
        error: mutationError
    } = useFacebookAccountMutations();

    // Local state
    const [refreshing, setRefreshing] = useState(false);
    const [selectedAccount, setSelectedAccount] = useState(null);
    const [showConfigDialog, setShowConfigDialog] = useState(false);
    const [showConnectionDialog, setShowConnectionDialog] = useState(false);
    const [selectedConnectionType, setSelectedConnectionType] = useState('instagram'); // Track connection type
    const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
    const [accountToDelete, setAccountToDelete] = useState(null);

    // Combined loading state
    const loading = accountsLoading || mutationLoading;

    // Error handling
    React.useEffect(() => {
        if (accountsError) {
            console.error('Facebook accounts error:', accountsError);
            toast.error('Lỗi khi tải danh sách tài khoản Facebook');
        }
    }, [accountsError]);

    React.useEffect(() => {
        if (mutationError) {
            console.error('Facebook mutation error:', mutationError);
            toast.error('Lỗi khi thực hiện thao tác');
        }
    }, [mutationError]);

    // Debounce refetch để tránh spam requests
    const debouncedRefetch = useCallback(
        React.useMemo(() => {
            let timeoutId;
            return () => {
                clearTimeout(timeoutId);
                timeoutId = setTimeout(() => {
                    refetchAccounts();
                }, 300); // 300ms debounce
            };
        }, [refetchAccounts]),
        [refetchAccounts]
    );

    // Auto refresh expiring tokens - sẽ được implement server-side
    const handleRefreshTokens = useCallback(async () => {
        try {
            setRefreshing(true);

            // Call server-side API to refresh tokens
            const response = await fetch('/api/facebook-integration/refresh-tokens', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();

            if (result.success) {
                toast.success(
                    result.message || 'Đã làm mới token thành công'
                );
                await refetchAccounts(); // Sử dụng trực tiếp refetchAccounts
            } else {
                throw new Error(result.error || 'Không thể làm mới token');
            }
        } catch (error) {
            console.error('Error refreshing tokens:', error);
            toast.error(`Lỗi khi làm mới token: ${error.message}`);
        } finally {
            setRefreshing(false);
        }
    }, [refetchAccounts]);

    // Handle account connection success - accounts are already saved by popup callback
    const handleConnectionSuccess = useCallback(async (result) => {
        try {
            if (result.success) {
                toast.success(result.message || 'Kết nối Facebook thành công!');
                setShowConnectionDialog(false);
                debouncedRefetch(); // Sử dụng debounced refetch
            } else {
                throw new Error(result.message || 'Không thể kết nối Facebook');
            }
        } catch (error) {
            console.error('Error handling connection success:', error);
            toast.error('Lỗi khi xử lý kết nối Facebook');
        }
    }, [debouncedRefetch]);

    // Handle delete account
    const handleDeleteAccount = useCallback(async () => {
        if (!accountToDelete) return;

        try {
            console.log('🗑️ Deleting Facebook account:', accountToDelete.id);

            const result = await deleteAccount(accountToDelete.id);

            if (result.success) {
                toast.success('Đã ngắt kết nối tài khoản Facebook thành công');
                setDeleteDialogOpen(false);
                setAccountToDelete(null);
                debouncedRefetch(); // Sử dụng debounced refetch
            } else {
                throw new Error(result.error?.message || 'Không thể ngắt kết nối tài khoản');
            }
        } catch (error) {
            console.error('Error deleting Facebook account:', error);
            toast.error('Lỗi khi ngắt kết nối tài khoản');
        }
    }, [accountToDelete, deleteAccount, debouncedRefetch]);

    // Get connection status với focus vào việc hoạt động hay không
    const getConnectionStatus = (account) => {
        const now = new Date();
        const expiresAt = account.tokenExpiresAt ? new Date(account.tokenExpiresAt) : null;
        
        // Kiểm tra token có hết hạn không
        const isTokenExpired = expiresAt && expiresAt < now;
        
        if (!account.isActive) {
            return { status: 'inactive', label: 'Tạm dừng', color: 'default', icon: 'mdi:pause-circle' };
        } else if (isTokenExpired) {
            return { status: 'expired', label: 'Cần cập nhật', color: 'error', icon: 'mdi:alert-circle' };
        } else {
            return { status: 'active', label: 'Đang hoạt động', color: 'success', icon: 'mdi:check-circle' };
        }
    };

    // Handle URL parameters from OAuth callback (fallback for non-popup flows)
    React.useEffect(() => {
        const urlParams = new URLSearchParams(window.location.search);
        const success = urlParams.get('success');
        const error = urlParams.get('error');
        const message = urlParams.get('message');
        const platform = urlParams.get('platform');

        // Only show notifications if we have success/error params
        if (success === 'true') {
            const platformName = platform === 'instagram' ? 'Instagram' : 'Facebook';
            toast.success(decodeURIComponent(message || `${platformName} kết nối thành công!`));
            
            // Refresh data after successful connection
            debouncedRefetch();
            
            // Clean URL
            window.history.replaceState({}, document.title, window.location.pathname);
        } else if (error) {
            toast.error(decodeURIComponent(message || 'Lỗi khi kết nối social media'));
            // Clean URL
            window.history.replaceState({}, document.title, window.location.pathname);
        }
    }, [debouncedRefetch]);

    // Handle connection type selection
    const handleShowConnectionDialog = useCallback((connectionType) => {
        setSelectedConnectionType(connectionType);
        setShowConnectionDialog(true);
    }, []);

    return (
        <DashboardContent>
            {/* Header */}
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
                <Box>
                    <Typography variant="h5" component="h1">
                        Social Media Integration Center
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                        🚀 Kết nối Instagram Business & Facebook Pages - AI tự động trả lời 24/7
                    </Typography>
                </Box>
                <Stack direction="row" spacing={2}>
                    <Tooltip title="Làm mới danh sách">
                        <LoadingButton
                            loading={accountsLoading}
                            onClick={() => debouncedRefetch()}
                            startIcon={<Iconify icon="mdi:refresh" />}
                            variant="outlined"
                            size="small"
                        >
                            Làm mới
                        </LoadingButton>
                    </Tooltip>
                    <Button
                        variant="contained"
                        startIcon={<Iconify icon="mdi:instagram" />}
                        onClick={() => handleShowConnectionDialog('instagram')}
                        color="primary"
                        sx={{ 
                            background: 'linear-gradient(45deg, #E4405F 30%, #FCCC63 90%)',
                            '&:hover': {
                                background: 'linear-gradient(45deg, #C13584 30%, #E4405F 90%)',
                            }
                        }}
                    >
                        Kết nối Instagram
                    </Button>
                    <Button
                        variant="outlined"
                        startIcon={<Iconify icon="mdi:facebook" />}
                        onClick={() => handleShowConnectionDialog('facebook')}
                        sx={{ 
                            color: '#1877F2',
                            borderColor: '#1877F2',
                            '&:hover': {
                                backgroundColor: '#1877F2',
                                color: 'white',
                            }
                        }}
                    >
                        Kết nối Facebook
                    </Button>
                </Stack>
            </Box>

            {/* Info Alert - Cập nhật với thông tin API mới */}
            <Alert severity="info" sx={{ mb: 3 }}>
                <Typography variant="subtitle2" gutterBottom>
                    🆕 Multi-Platform Social Media Integration 2025
                </Typography>
                <Typography variant="body2" component="div" sx={{ mt: 1 }}>
                    <strong>Instagram Direct (Khuyến nghị):</strong> Kết nối trực tiếp không cần Facebook Page • 
                    API mới 2025 với scopes cập nhật • Đơn giản và bảo mật
                    <br />
                    <strong>Facebook Integration:</strong> Kết nối qua Facebook Pages • 
                    Hỗ trợ cả Facebook + Instagram Business • Phù hợp cho multi-platform management
                </Typography>
            </Alert>

            {/* Loading */}
            {loading && <LinearProgress sx={{ mb: 2 }} />}

            {/* Accounts List */}
            {loading ? (
                // Loading skeleton - đơn giản hóa
                <Stack spacing={2}>
                    {[1, 2].map((index) => (
                        <Card key={index}>
                            <CardContent>
                                <Box display="flex" justifyContent="space-between" alignItems="center">
                                    <Box display="flex" alignItems="center" gap={2}>
                                        <Skeleton variant="circular" width={40} height={40} />
                                        <Box>
                                            <Skeleton variant="text" width={200} height={24} />
                                            <Skeleton variant="text" width={120} height={16} />
                                        </Box>
                                    </Box>
                                    <Stack direction="row" spacing={1}>
                                        <Skeleton variant="rectangular" width={100} height={24} sx={{ borderRadius: 1 }} />
                                        <Skeleton variant="circular" width={32} height={32} />
                                        <Skeleton variant="circular" width={32} height={32} />
                                    </Stack>
                                </Box>
                            </CardContent>
                        </Card>
                    ))}
                </Stack>
            ) : accounts.length === 0 ? (
                <Card>
                    <CardContent sx={{ textAlign: 'center', py: 6 }}>
                        <Avatar sx={{ 
                            width: 80, 
                            height: 80, 
                            mx: 'auto', 
                            mb: 2, 
                            bgcolor: 'primary.main',
                            fontSize: 40
                        }}>
                            <Iconify icon="mdi:facebook" />
                        </Avatar>
                        <Typography variant="h6" color="text.secondary" gutterBottom>
                            Chưa có trang nào được kết nối
                        </Typography>
                        <Typography variant="body2" color="text.secondary" mb={3}>
                            Kết nối Facebook Page hoặc Instagram để bắt đầu tự động trả lời khách hàng
                        </Typography>
                        <Button
                            variant="contained"
                            size="large"
                            startIcon={<Iconify icon="mdi:plus" />}
                            onClick={() => handleShowConnectionDialog('instagram')}
                        >
                            Kết nối trang đầu tiên
                        </Button>
                    </CardContent>
                </Card>
            ) : (
                <Stack spacing={2}>
                    {accounts.map((account) => {
                        const connectionStatus = getConnectionStatus(account);

                        return (
                            <Card key={account.id} elevation={1}>
                                <CardContent sx={{ p: 3 }}>
                                    <Box display="flex" justifyContent="space-between" alignItems="center">
                                        <Box display="flex" alignItems="center" gap={2}>
                                            {/* Platform Icon */}
                                            <Avatar 
                                                sx={{ 
                                                    width: 48, 
                                                    height: 48,
                                                    bgcolor: '#1877F2',
                                                    fontSize: 24
                                                }}
                                            >
                                                <Iconify icon="mdi:facebook" />
                                            </Avatar>
                                            
                                            {/* Page Info */}
                                            <Box>
                                                <Typography variant="h6" component="div" sx={{ fontWeight: 600 }}>
                                                    {account.pageName}
                                                </Typography>
                                                <Box display="flex" alignItems="center" gap={1} mt={0.5}>
                                                    <Chip
                                                        label={connectionStatus.label}
                                                        color={connectionStatus.color}
                                                        size="small"
                                                        icon={<Iconify icon={connectionStatus.icon} />}
                                                    />
                                                    {/* Hiển thị Instagram nếu có */}
                                                    {account.instagramAccountId && (
                                                        <Chip
                                                            label="Instagram"
                                                            size="small"
                                                            variant="outlined"
                                                            icon={<Iconify icon="mdi:instagram" />}
                                                        />
                                                    )}
                                                </Box>
                                                <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 0.5 }}>
                                                    Kết nối từ {new Date(account.connectedAt).toLocaleDateString('vi-VN')}
                                                </Typography>
                                            </Box>
                                        </Box>

                                        {/* Actions */}
                                        <Stack direction="row" alignItems="center" spacing={1}>
                                            <Tooltip title="Cài đặt">
                                                <IconButton
                                                    onClick={() => {
                                                        setSelectedAccount(account);
                                                        setShowConfigDialog(true);
                                                    }}
                                                    color="primary"
                                                    size="large"
                                                >
                                                    <Iconify icon="mdi:cog" />
                                                </IconButton>
                                            </Tooltip>

                                            <Tooltip title="Ngắt kết nối">
                                                <IconButton
                                                    onClick={() => {
                                                        setAccountToDelete(account);
                                                        setDeleteDialogOpen(true);
                                                    }}
                                                    color="error"
                                                    size="large"
                                                >
                                                    <Iconify icon="mdi:link-off" />
                                                </IconButton>
                                            </Tooltip>
                                        </Stack>
                                    </Box>
                                </CardContent>
                            </Card>
                        );
                    })}
                </Stack>
            )}

            {/* Connection Dialog */}
            <SocialMediaConnectionDialog
                open={showConnectionDialog}
                onClose={() => setShowConnectionDialog(false)}
                onSuccess={handleConnectionSuccess}
                initialPlatform={selectedConnectionType} // Pass the selected type
            />

            {/* Config Dialog */}
            <FacebookConfigDialog
                open={showConfigDialog}
                onClose={() => {
                    setShowConfigDialog(false);
                    setSelectedAccount(null);
                }}
                account={selectedAccount}
                onConfigSaved={debouncedRefetch}
            />

            {/* Delete Confirmation Dialog */}
            <Dialog
                open={deleteDialogOpen}
                onClose={() => setDeleteDialogOpen(false)}
                maxWidth="sm"
                fullWidth
            >
                <DialogTitle>
                    <Box display="flex" alignItems="center" gap={2}>
                        <Iconify icon="mdi:link-off" color="error.main" />
                        <Typography variant="h6">Ngắt kết nối trang</Typography>
                    </Box>
                </DialogTitle>
                <DialogContent>
                    <Typography variant="body1" gutterBottom>
                        Bạn có chắc chắn muốn ngắt kết nối trang <strong>{accountToDelete?.pageName}</strong>?
                    </Typography>
                    <Alert severity="warning" sx={{ mt: 2 }}>
                        <Typography variant="body2">
                            ⚠️ Sau khi ngắt kết nối:
                        </Typography>
                        <Typography variant="body2" component="ul" sx={{ mt: 1, pl: 2 }}>
                            <li>Tính năng tự động trả lời sẽ dừng hoạt động</li>
                            <li>Không thể nhận tin nhắn mới từ trang này</li>
                            <li>Có thể kết nối lại bất cứ lúc nào</li>
                        </Typography>
                    </Alert>
                </DialogContent>
                <DialogActions sx={{ p: 3 }}>
                    <Button 
                        onClick={() => setDeleteDialogOpen(false)}
                        variant="outlined"
                    >
                        Hủy
                    </Button>
                    <Button
                        onClick={handleDeleteAccount}
                        color="error"
                        variant="contained"
                        startIcon={<Iconify icon="mdi:link-off" />}
                    >
                        Ngắt kết nối
                    </Button>
                </DialogActions>
            </Dialog>
        </DashboardContent>
    );
}

// Export component wrapped with Error Boundary
export default function FacebookConnectionViewWithErrorBoundary() {
    return (
        <FacebookErrorBoundary>
            <FacebookConnectionView />
        </FacebookErrorBoundary>
    );
}