'use client';

import { useRef, useState, useEffect } from 'react';

import { getProducts } from './product-api';
import { fetchData } from './supabase-utils';
import { DEFAULT_PRODUCT_OPTIONS } from './product-constants';
import { getProductVariants } from './product-variant-service';

/**
 * Hook để lấy danh sách sản phẩm
 * @param {Object} options - <PERSON><PERSON><PERSON> tùy chọn
 * @returns {Object} - Kết quả từ API
 */
export function useProducts(options = {}) {
  const [products, setProducts] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isValidating, setIsValidating] = useState(false);
  const [totalCount, setTotalCount] = useState(0);

  // Use useRef to avoid re-renders when only storing values
  const optionsRef = useRef({ ...DEFAULT_PRODUCT_OPTIONS, ...options });

  // Update ref when options change
  useEffect(() => {
    optionsRef.current = { ...DEFAULT_PRODUCT_OPTIONS, ...options };
  }, [options]);

  // Removed useCallback for better data freshness
  const fetchProducts = async () => {
    setIsValidating(true);
    try {
      // Use ref value to always get the latest options
      const currentOptions = optionsRef.current;

      // Ensure we get count for pagination
      const countEnabled = currentOptions.count !== false;
      if (countEnabled) {
        currentOptions.count = true;
      }

      const result = await getProducts(currentOptions);

      if (result.success) {
        setProducts(result.data || []);
        if (countEnabled) {
          setTotalCount(result.count || 0);
        }
      } else {
        setError(result.error);
      }

      return result;
    } catch (err) {
      setError(err);
      return { success: false, error: err, data: null };
    } finally {
      setIsValidating(false);
      setIsLoading(false);
    }
  };

  // Hàm để tải lại dữ liệu - removed useCallback
  const mutate = async () => fetchProducts();

  // Tải dữ liệu khi component mount
  useEffect(() => {
    setIsLoading(true);
    fetchProducts();
  }, []); // Removed fetchProducts dependency

  // Track options changes and reload data when needed
  const prevOptionsRef = useRef('');

  useEffect(() => {
    const optionsString = JSON.stringify(options);
    // Only call API when options actually change (deep comparison)
    if (prevOptionsRef.current !== optionsString) {
      prevOptionsRef.current = optionsString;
      setIsLoading(true);
      fetchProducts();
    }
  }, [options]); // Removed fetchProducts dependency

  // Removed memoization for better data freshness
  return {
    products,
    isLoading,
    isError: !!error,
    error,
    isValidating,
    totalCount,
    mutate,
    isEmpty: !isLoading && !isValidating && (!products || products.length === 0),
  };
}

/**
 * Hook để lấy thông tin chi tiết sản phẩm
 * @param {string} productId - ID sản phẩm cần lấy
 * @returns {Promise<Object>} - Kết quả từ API
 */
const getProductDetail = async (productId) => {
  if (!productId) {
    return { success: false, error: new Error('Thiếu ID sản phẩm'), data: null };
  }

  // Sử dụng fetchData từ supabase-utils để tự động thêm tenant_id vào truy vấn
  return fetchData('products', {
    filters: { id: productId },
    columns: '*, category_id(id, name)',
    single: true,
  });
};

export function useProduct(productId) {
  const [product, setProduct] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isValidating, setIsValidating] = useState(false);
  // Removed useCallback for better data freshness
  const fetchProduct = async () => {
    if (!productId) {
      setProduct(null);
      return;
    }

    setIsValidating(true);
    setError(null);
    try {
      // Sử dụng Promise.all để thực hiện các truy vấn song song
      const [productResult, variantsResult] = await Promise.all([
        // Lấy thông tin sản phẩm
        getProductDetail(productId),
        // Lấy thông tin biến thể của sản phẩm
        getProductVariants(productId),
      ]);
      if (productResult.success && productResult.data) {
        const productData = productResult.data;
        // Gộp dữ liệu
        const fullProduct = {
          ...productData,
          categoryId: productData.categoryId?.id || null,
          metaKeywords: productData.metaKeywords || [],
          images: productData.images || [],
          variants: variantsResult.success ? variantsResult.data : [],
        };

        setProduct(fullProduct);
      } else {
        setError(productResult.error || new Error('Không tìm thấy sản phẩm'));
      }
    } catch (err) {
      setError(err);
    } finally {
      setIsValidating(false);
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (productId) {
      setIsLoading(true);
      fetchProduct();
    }
    return undefined;
  }, [productId]); // Removed fetchProduct dependency

  return {
    product,
    isLoading,
    isError: !!error,
    error,
    isValidating,
    refetch: fetchProduct,
  };
}
