# Hướng dẫn cấu hình Supabase cho Port 3032

## 📋 Tổng quan

Dự án hiện tại đang chạy trên port **3032** thay vì port mặc định 3000. Để Supabase Auth hoạt động ch<PERSON>h xá<PERSON>, c<PERSON><PERSON> c<PERSON><PERSON> hình các URL redirect phù hợp.

## 🔧 Cấu hình Supabase Dashboard

### 1. Truy cập Supabase Dashboard
- URL: https://supabase.com/dashboard/project/vhduizefoibsipsiraqf
- Đăng nhập với tài khoản admin

### 2. Cấu hình URL Configuration

**Đi đến: Authentication > URL Configuration**

#### Site URL (Development)
```
http://localhost:3032
```

#### Additional Redirect URLs
Thêm các URL sau vào danh sách cho phép:

```
http://localhost:3032/**
http://localhost:3032/auth/callback
http://localhost:3032/dashboard/mooly-chatbot/chatbots
http://localhost:3032/api/facebook-integration/popup-callback
http://localhost:3032/api/instagram-integration/callback
```

### 3. Cấu hình Google OAuth (nếu sử dụng)

**Đi đến: Authentication > Providers > Google**

#### Authorized redirect URIs trong Google Cloud Console:
```
https://vhduizefoibsipsiraqf.supabase.co/auth/v1/callback
```

#### Authorized JavaScript origins:
```
http://localhost:3032
https://your-production-domain.com
```

## 🌐 Environment Variables

### Development (.env)
```bash
# Site URL Configuration - Port 3032
NEXT_PUBLIC_PUBLIC_SITE_URL=http://localhost:3032
NEXTAUTH_URL=http://localhost:3032

# Supabase
NEXT_PUBLIC_SUPABASE_URL=https://vhduizefoibsipsiraqf.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Instagram Integration
INSTAGRAM_REDIRECT_URI=http://localhost:3032/api/instagram-integration/callback

# Facebook Integration (sử dụng popup callback)
# Không cần thay đổi vì sử dụng popup window
```

### Production (.env.production)
```bash
# Site URL Configuration - Production
NEXT_PUBLIC_PUBLIC_SITE_URL=https://your-domain.com
NEXTAUTH_URL=https://your-domain.com

# Các biến khác giữ nguyên như development
```

## 🔄 Cách hoạt động của Auth Flow

### 1. Sign Up Flow
```javascript
// src/auth/context/supabase/action.jsx
export const signUp = async ({ email, password, firstName, lastName }) => {
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      emailRedirectTo: getAuthRedirectURL(paths.dashboard.root), // http://localhost:3032/dashboard
      data: { display_name: `${firstName} ${lastName}` },
    },
  });
};
```

### 2. OAuth Callback Flow
```javascript
// src/app/auth/callback/route.js
export async function GET(request) {
  const { searchParams, origin } = new URL(request.url);
  const code = searchParams.get('code');
  
  // origin sẽ là http://localhost:3032 trong development
  if (isLocalEnv) {
    return NextResponse.redirect(`${origin}${next}`);
  }
}
```

### 3. Email Confirmation Flow
Khi user click vào link trong email xác nhận:
```
http://localhost:3032/auth/confirm?token_hash=xxx&type=email
```

## 🛠️ Utilities đã tối ưu

### URL Helper Functions
```javascript
// src/utils/url-helper.js

// Lấy base URL dựa trên môi trường
getBaseURL() // → http://localhost:3032 (dev) hoặc https://domain.com (prod)

// Tạo auth redirect URL
getAuthRedirectURL('/dashboard') // → http://localhost:3032/dashboard

// Tạo OAuth callback URL
getOAuthCallbackURL('facebook') // → http://localhost:3032/api/facebook-integration/popup-callback
```

## 🔍 Troubleshooting

### Lỗi thường gặp:

#### 1. "Invalid redirect URL"
**Nguyên nhân**: URL không được thêm vào whitelist trong Supabase
**Giải pháp**: Kiểm tra và thêm URL vào Additional Redirect URLs

#### 2. "CORS error"
**Nguyên nhân**: Origin không được phép
**Giải pháp**: Đảm bảo `http://localhost:3032` được thêm vào Site URL

#### 3. "Session not found after redirect"
**Nguyên nhân**: Cookie domain mismatch
**Giải pháp**: Kiểm tra middleware và cookie configuration

### Debug Commands:
```bash
# Kiểm tra port đang chạy
netstat -an | grep 3032

# Kiểm tra environment variables
echo $NEXT_PUBLIC_PUBLIC_SITE_URL

# Test auth callback
curl http://localhost:3032/auth/callback?code=test
```

## 📝 Checklist cấu hình

- [ ] Cập nhật Site URL trong Supabase Dashboard
- [ ] Thêm redirect URLs vào whitelist
- [ ] Cập nhật environment variables
- [ ] Test sign up flow
- [ ] Test OAuth login
- [ ] Test email confirmation
- [ ] Test password reset
- [ ] Kiểm tra Facebook/Instagram integration

## 🚀 Production Deployment

Khi deploy production, nhớ:

1. Cập nhật Site URL thành domain thực
2. Thêm production URLs vào redirect whitelist
3. Cập nhật Google OAuth authorized origins
4. Cập nhật Facebook/Instagram app settings
5. Test toàn bộ auth flow trên production

## 📞 Hỗ trợ

Nếu gặp vấn đề, kiểm tra:
1. Supabase Dashboard logs
2. Browser Network tab
3. Next.js console logs
4. Environment variables
