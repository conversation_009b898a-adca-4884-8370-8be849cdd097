'use client';

import { useSearchParams } from 'next/navigation';
import { useState, useEffect, useCallback } from 'react';

import Box from '@mui/material/Box';
import Tab from '@mui/material/Tab';
import Tabs from '@mui/material/Tabs';
import Grid from '@mui/material/Grid';
import Card from '@mui/material/Card';
import Alert from '@mui/material/Alert';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';
import CardContent from '@mui/material/CardContent';
import CircularProgress from '@mui/material/CircularProgress';

import { paths } from 'src/routes/paths';

import { getPaymentInfo } from 'src/actions/mooly-chatbot/payos-service';
import {
  getTenantCredit,
  getCreditPayments,
  updatePaymentStatus,
  getCreditTransactions,
} from 'src/actions/mooly-chatbot/credit-service';

import { toast } from 'src/components/snackbar';
import { useSettingsContext } from 'src/components/settings';
import { CustomBreadcrumbs } from 'src/components/custom-breadcrumbs';

import {
  CreditBalanceCard,
  CreditPaymentList,
  CreditTransactionList,
} from 'src/sections/mooly-chatbot/credits';

// ----------------------------------------------------------------------

export default function CreditsPage() {
  const settings = useSettingsContext();
  const searchParams = useSearchParams();

  const [currentTab, setCurrentTab] = useState('transactions');
  const [loading, setLoading] = useState(false);
  const [creditBalance, setCreditBalance] = useState(0);
  const [lastUpdated, setLastUpdated] = useState(null);
  const [transactions, setTransactions] = useState([]);
  const [payments, setPayments] = useState([]);

  const handleChangeTab = (_, newValue) => {
    setCurrentTab(newValue);
  };

  const loadCreditBalance = useCallback(async () => {
    try {
      setLoading(true);
      // Sử dụng secure API để lấy credit balance
      const { success, data, error } = await getTenantCredit();
      if (success && data) {
        setCreditBalance(data.balance);
        setLastUpdated(data.lastUpdated);
      } else if (error) {
        console.error('Error loading credit balance:', error);
        setCreditBalance(0);
        setLastUpdated(null);
      }
    } catch (error) {
      console.error('Error loading credit balance:', error);
      setCreditBalance(0);
      setLastUpdated(null);
    } finally {
      setLoading(false);
    }
  }, []);

  const loadTransactions = useCallback(async () => {
    try {
      setLoading(true);
      // Không cần truyền tenant_id vì supabase-utils sẽ tự động thêm tenant_id vào filters
      const { success, data, error } = await getCreditTransactions();
      if (success) {
        setTransactions(data);
      } else if (error) {
        console.error('Error loading transactions:', error);
      }
    } catch (error) {
      console.error('Error loading transactions:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  const loadPayments = useCallback(async () => {
    try {
      setLoading(true);
      // Không cần truyền tenant_id vì supabase-utils sẽ tự động thêm tenant_id vào filters
      const { success, data, error } = await getCreditPayments();
      if (success) {
        setPayments(data);
      } else if (error) {
        console.error('Error loading payments:', error);
      }
    } catch (error) {
      console.error('Error loading payments:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  const loadData = useCallback(() => {
    loadCreditBalance();
    loadTransactions();
    loadPayments();
  }, [loadCreditBalance, loadTransactions, loadPayments]);

  // Xử lý kết quả thanh toán từ PayOS
  const handlePaymentResult = useCallback(async () => {
    const status = searchParams.get('status');
    const paymentId = searchParams.get('payment_id');

    if (!status || !paymentId) return;

    try {
      // Nếu thanh toán thành công, kiểm tra với PayOS
      if (status === 'success') {
        // Lấy thông tin thanh toán từ PayOS
        const paymentInfo = await getPaymentInfo(paymentId);

        if (paymentInfo.success && paymentInfo.data) {
          // Cập nhật trạng thái thanh toán
          await updatePaymentStatus(paymentId, 'completed', paymentInfo.data);
          toast.success('Thanh toán thành công!');
        }
      } else if (status === 'cancel') {
        // Cập nhật trạng thái thanh toán thành thất bại
        await updatePaymentStatus(paymentId, 'failed');
        toast.warning('Thanh toán đã bị hủy');
      }

      // Tải lại dữ liệu
      loadData();
    } catch (error) {
      console.error('Error handling payment result:', error);
    }
  }, [searchParams, loadData]);

  useEffect(() => {
    loadData();
  }, [loadData]);

  useEffect(() => {
    handlePaymentResult();
  }, [handlePaymentResult]);

  const TABS = [
    { value: 'transactions', label: 'Lịch sử giao dịch' },
    { value: 'payments', label: 'Lịch sử thanh toán' },
  ];

  return (
    <Container maxWidth={settings.themeStretch ? false : 'xl'}>
      <CustomBreadcrumbs
        heading="Quản lý Credit"
        links={[
          { name: 'Dashboard', href: paths.dashboard.root },
          { name: 'Quản lý Credit' },
        ]}
        sx={{
          mb: { xs: 3, md: 5 },
        }}
      />

      <Grid container spacing={3}>
        <Grid item size={{ xs: 12, md: 4 }}>
          <CreditBalanceCard
            balance={creditBalance}
            loading={loading}
            onRefresh={loadData}
            lastUpdated={lastUpdated}
            securityLevel="high"
          />
        </Grid>

        <Grid item size={{ xs: 12, md: 8 }}>
          <Card>
            <CardContent>
              <Typography variant="subtitle1" sx={{ mb: 2 }}>
                {`Credit là đơn vị thanh toán cho việc sử dụng chatbot.
                Mỗi lần chatbot "Trả Lời Câu Hỏi" hoặc "Đào Tạo Chatbot", bạn sẽ tiêu thụ 1 credit cho mỗi lần thực hiện.`}
              </Typography>
              <Alert severity="info" sx={{ mb: 3 }}>
                Bạn có thể mua thêm credit bất kỳ lúc nào bằng cách nhấn vào nút &quot;Mua thêm Credit&ldquo;.
              </Alert>
            </CardContent>
          </Card>
        </Grid>

        <Grid item size={{ xs: 12 }}>
          <Tabs
            value={currentTab}
            onChange={handleChangeTab}
            sx={{
              px: 2,
              bgcolor: 'background.neutral',
              borderTopLeftRadius: 1,
              borderTopRightRadius: 1,
            }}
          >
            {TABS.map((tab) => (
              <Tab key={tab.value} value={tab.value} label={tab.label} />
            ))}
          </Tabs>

          {loading ? (
            <Box sx={{ p: 5, display: 'flex', justifyContent: 'center' }}>
              <CircularProgress />
            </Box>
          ) : (
            <>
              {currentTab === 'transactions' && (
                <CreditTransactionList transactions={transactions} loading={loading} />
              )}

              {currentTab === 'payments' && (
                <CreditPaymentList payments={payments} loading={loading} />
              )}
            </>
          )}
        </Grid>
      </Grid>
    </Container>
  );
}
