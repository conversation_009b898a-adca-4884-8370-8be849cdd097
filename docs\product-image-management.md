# Quản Lý Hình Ảnh Sản Phẩm

## Tổng Quan

Hệ thống quản lý hình ảnh sản phẩm được thiết kế để:
- Tự động xóa hình ảnh không còn sử dụng khi cập nhật sản phẩm
- <PERSON><PERSON><PERSON> bảo tenant isolation cho storage
- Tối ưu hóa dung lượng lưu trữ
- Đồng bộ hóa với Weaviate vector database

## Cấu Trúc Lưu Trữ

### Storage Buckets
- **public**: Bucket chính cho hình ảnh sản phẩm
- **products**: Bucket backup (deprecated)

### Cấu Trúc Th<PERSON>
```
public/
├── products/
│   ├── {tenant_id_short}/
│   │   ├── {product_id}/
│   │   │   ├── image1.jpg
│   │   │   ├── image2.jpg
│   │   │   └── avatar.jpg
```

### Tenant Isolation
- Mỗi tenant có thư mục riêng dựa trên 8 ký tự đầu của tenant_id
- RLS policies đảm bảo chỉ tenant owner có thể truy cập files của mình

## Logic Xử Lý Hình Ảnh

### 1. <PERSON>hi Tạo Sản Phẩm Mới
```javascript
// Upload tất cả hình ảnh mới
const uploadResults = await Promise.all(
  images.map(image => storageService.uploadFile('public', filePath, image))
);

// Thay thế File objects bằng URLs
productData.images = uploadResults.map(result => result.publicUrl);
```

### 2. Khi Cập Nhật Sản Phẩm
```javascript
// So sánh hình ảnh cũ và mới
const imageChanges = compareImages(defaultValues.images, productData.images);

// Upload hình ảnh mới
const newImageUrls = await uploadNewImages(imageChanges.toUpload);

// Xóa hình ảnh không còn sử dụng
await deleteUnusedImages(imageChanges.toDelete);

// Xử lý avatar riêng biệt
await handleAvatarChanges(oldAvatar, newAvatar, productData.images);
```

### 3. Hàm So Sánh Hình Ảnh
```javascript
function compareImages(defaultImages = [], currentImages = []) {
  return {
    toDelete: [], // URLs cần xóa
    toUpload: [], // File objects cần upload
    unchanged: [] // URLs giữ nguyên
  };
}
```

## RLS Policies

### Storage Objects Policies
```sql
-- Upload policy
CREATE POLICY "Tenant users can upload product images to public bucket"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (
  bucket_id = 'public' AND 
  (storage.foldername(name))[1] = 'products' AND 
  (storage.foldername(name))[2] = get_user_tenant_id_short(auth.uid())
);

-- Delete policy
CREATE POLICY "Tenant users can delete their product images in public bucket"
ON storage.objects FOR DELETE
TO authenticated
USING (
  bucket_id = 'public' AND 
  (storage.foldername(name))[1] = 'products' AND 
  (storage.foldername(name))[2] = get_user_tenant_id_short(auth.uid())
);
```

### Helper Function
```sql
CREATE OR REPLACE FUNCTION get_user_tenant_id_short(user_id UUID)
RETURNS TEXT AS $$
BEGIN
  RETURN (
    SELECT substring(u.tenant_id::text, 1, 8)
    FROM public.users u
    WHERE u.id = user_id
    LIMIT 1
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## Xử Lý Lỗi

### 1. Upload Failures
- Retry mechanism cho failed uploads
- Rollback nếu có lỗi critical
- Log chi tiết cho debugging

### 2. Delete Failures
- Non-blocking: không fail toàn bộ operation
- Warning logs cho failed deletions
- Cleanup job định kỳ

### 3. Path Extraction Failures
- Validate URL format trước khi extract
- Fallback mechanisms
- Detailed error logging

## Testing

### Unit Tests
```javascript
// Test image comparison logic
const result = compareImages(defaultImages, currentImages);
expect(result.toDelete).toEqual(['old-image.jpg']);
expect(result.toUpload).toHaveLength(1);

// Test avatar handling
const shouldDelete = shouldDeleteOldAvatar(oldAvatar, newAvatar, images);
expect(shouldDelete).toBe(true);
```

### Integration Tests
- Test với real Supabase storage
- Verify tenant isolation
- Test cleanup operations

## Monitoring & Maintenance

### Metrics
- Upload success rate
- Delete success rate
- Storage usage per tenant
- Failed operations count

### Cleanup Jobs
- Orphaned files cleanup
- Failed upload cleanup
- Storage optimization

## Best Practices

### 1. File Naming
- Use UUID-based names để tránh conflicts
- Include timestamp cho versioning
- Maintain original extension

### 2. Error Handling
- Always handle upload failures gracefully
- Log detailed error information
- Provide user-friendly error messages

### 3. Performance
- Use Promise.all cho parallel uploads
- Implement proper retry logic
- Optimize image sizes before upload

### 4. Security
- Validate file types và sizes
- Sanitize file names
- Implement rate limiting

## Troubleshooting

### Common Issues
1. **RLS Policy Violations**: Check tenant_id mapping
2. **Path Extraction Failures**: Verify URL format
3. **Upload Timeouts**: Check file sizes và network
4. **Delete Failures**: Verify file existence và permissions

### Debug Tools
- Storage service logs
- Browser network tab
- Supabase dashboard
- Test component: `/dashboard/test-image-cleanup`
