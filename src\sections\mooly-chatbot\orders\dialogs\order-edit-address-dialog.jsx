'use client';

import { useEffect } from 'react';
import PropTypes from 'prop-types';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import LoadingButton from '@mui/lab/LoadingButton';
import DialogTitle from '@mui/material/DialogTitle';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';

import { useOrderMutations } from 'src/actions/mooly-chatbot/order-mutations';
import { createData, updateData } from 'src/actions/mooly-chatbot/supabase-utils';

import { toast } from 'src/components/snackbar';
import { Form, Field } from 'src/components/hook-form';

import { OrderAddressEditSchema } from '../validation/order-edit-schemas';

// ----------------------------------------------------------------------

export function OrderEditAddressDialog({ open, onClose, order, shippingAddress, billingAddress, onSuccess }) {
  const { updateOrder, isMutating } = useOrderMutations();

  const defaultValues = {
    fullName: shippingAddress?.fullName || order?.customerName || '',
    phone: shippingAddress?.phone || order?.customerPhone || '',
    address: shippingAddress?.address || '',
    addressLine2: shippingAddress?.addressLine2 || '',
    ward: shippingAddress?.ward || '',
    district: shippingAddress?.district || '',
    province: shippingAddress?.province || '',
    city: shippingAddress?.city || '',
    state: shippingAddress?.state || '',
    postalCode: shippingAddress?.postalCode || '',
    country: shippingAddress?.country || 'Vietnam',
    notes: shippingAddress?.notes || '',
  };

  const methods = useForm({
    resolver: zodResolver(OrderAddressEditSchema),
    defaultValues,
  });

  const { handleSubmit, reset } = methods;

  // Reset form khi dialog mở và có dữ liệu mới
  useEffect(() => {
    if (open && (order || shippingAddress)) {
      const newValues = {
        fullName: shippingAddress?.fullName || order?.customerName || '',
        phone: shippingAddress?.phone || order?.customerPhone || '',
        address: shippingAddress?.address || '',
        addressLine2: shippingAddress?.addressLine2 || '',
        ward: shippingAddress?.ward || '',
        district: shippingAddress?.district || '',
        province: shippingAddress?.province || '',
        city: shippingAddress?.city || '',
        state: shippingAddress?.state || '',
        postalCode: shippingAddress?.postalCode || '',
        country: shippingAddress?.country || 'Vietnam',
        notes: shippingAddress?.notes || '',
      };
      reset(newValues);
    }
  }, [open, order, shippingAddress, reset]);

  const onSubmit = handleSubmit(async (data) => {
    try {
      let addressId = order?.shippingAddressId || shippingAddress?.id;

      // Chuẩn bị dữ liệu địa chỉ
      const addressData = {
        customerId: order?.customerId,
        fullName: data.fullName,
        phone: data.phone,
        address: data.address,
        addressLine2: data.addressLine2 || null,
        ward: data.ward || null,
        district: data.district || null,
        province: data.province || null,
        city: data.city || null,
        state: data.state || null,
        postalCode: data.postalCode || null,
        country: data.country || 'Vietnam',
        notes: data.notes || null,
        addressType: 'shipping',
        isDefault: false,
        isDefaultShipping: true,
        isDefaultBilling: false,
      };

      if (addressId) {
        // Cập nhật địa chỉ hiện có
        const addressResult = await updateData('customer_addresses', addressData, { id: addressId });
        if (!addressResult.success) {
          throw new Error(addressResult.error || 'Cập nhật địa chỉ thất bại');
        }
      } else {
        // Tạo địa chỉ mới
        const addressResult = await createData('customer_addresses', addressData);
        if (!addressResult.success) {
          throw new Error(addressResult.error || 'Tạo địa chỉ thất bại');
        }
        addressId = addressResult.data[0].id;

        // Cập nhật order với shipping_address_id mới
        const orderResult = await updateOrder(order.id, {
          shippingAddressId: addressId,
        });

        if (!orderResult.success) {
          throw new Error(orderResult.error || 'Cập nhật đơn hàng thất bại');
        }
      }

      toast.success('Cập nhật địa chỉ giao hàng thành công!');
      onSuccess?.();
      onClose();
    } catch (error) {
      console.error('Error updating address:', error);
      toast.error(error.message || 'Cập nhật địa chỉ thất bại!');
    }
  });

  const handleClose = () => {
    reset();
    onClose();
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>Chỉnh sửa địa chỉ giao hàng</DialogTitle>

      <Form methods={methods} onSubmit={onSubmit}>
        <DialogContent>
          <Stack spacing={3} sx={{ pt: 1 }}>
            <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>
              <Field.Text
                name="fullName"
                label="Tên người nhận"
                required
                placeholder="Nhập tên người nhận"
              />

              <Field.Text
                name="phone"
                label="Số điện thoại"
                required
                placeholder="Nhập số điện thoại"
              />
            </Stack>

            <Field.Text
              name="address"
              label="Địa chỉ"
              required
              placeholder="Nhập địa chỉ cụ thể"
            />

            <Field.Text
              name="addressLine2"
              label="Địa chỉ bổ sung"
              placeholder="Tòa nhà, căn hộ, v.v. (tùy chọn)"
            />

            <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>
              <Field.Text
                name="ward"
                label="Phường/Xã"
                placeholder="Nhập phường/xã"
              />

              <Field.Text
                name="district"
                label="Quận/Huyện"
                placeholder="Nhập quận/huyện"
              />

              <Field.Text
                name="province"
                label="Tỉnh/Thành phố"
                placeholder="Nhập tỉnh/thành phố"
              />
            </Stack>

            <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>
              <Field.Text
                name="city"
                label="Thành phố"
                placeholder="Nhập thành phố"
              />

              <Field.Text
                name="state"
                label="Tiểu bang"
                placeholder="Nhập tiểu bang"
              />

              <Field.Text
                name="postalCode"
                label="Mã bưu chính"
                placeholder="Nhập mã bưu chính"
              />
            </Stack>

            <Field.Text
              name="country"
              label="Quốc gia"
              placeholder="Nhập quốc gia"
            />

            <Field.Text
              name="notes"
              label="Ghi chú giao hàng"
              multiline
              rows={3}
              placeholder="Ghi chú đặc biệt cho việc giao hàng"
            />
          </Stack>
        </DialogContent>

        <DialogActions>
          <Button onClick={handleClose} color="inherit">
            Hủy
          </Button>
          <LoadingButton
            type="submit"
            variant="contained"
            loading={isMutating}
          >
            Cập nhật
          </LoadingButton>
        </DialogActions>
      </Form>
    </Dialog>
  );
}

OrderEditAddressDialog.propTypes = {
  open: PropTypes.bool,
  onClose: PropTypes.func,
  order: PropTypes.object,
  shippingAddress: PropTypes.object,
  billingAddress: PropTypes.object,
  onSuccess: PropTypes.func,
};
