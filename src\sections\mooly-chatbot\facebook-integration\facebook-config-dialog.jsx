'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import Button from '@mui/material/Button';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import FormControlLabel from '@mui/material/FormControlLabel';
import Switch from '@mui/material/Switch';
import Stack from '@mui/material/Stack';
import Alert from '@mui/material/Alert';
import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import Divider from '@mui/material/Divider';
import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import FormControl from '@mui/material/FormControl';
import InputLabel from '@mui/material/InputLabel';
import Slider from '@mui/material/Slider';
import CircularProgress from '@mui/material/CircularProgress';

import { Iconify } from 'src/components/iconify';

import { LoadingButton } from '@mui/lab';

import { Form, Field } from 'src/components/hook-form';
import { toast } from 'src/components/snackbar';

import {
  useAutoReplyConfig,
  useAutoReplyConfigMutations,
  DEFAULT_CONFIG,
  REPLY_TONES,
  REPLY_LANGUAGES
} from 'src/actions/mooly-chatbot/facebook-integration';

// Validation Schema
const FacebookConfigSchema = z.object({
  isEnabled: z.boolean().default(true),
  replyToComments: z.boolean().default(true),
  replyToMessages: z.boolean().default(true),
  replyToInstagramComments: z.boolean().default(true),
  replyToInstagramMessages: z.boolean().default(true),
  excludeKeywords: z.string().default(''),
  businessInfo: z.string().default(''),
  replyTone: z.string().default('friendly'),
  replyLanguage: z.string().default('vi'),
  maxReplyLength: z.number().min(100).max(1000).default(500),
  aiPrompt: z.string().default(`Bạn là trợ lý AI của {business_name}. Hãy trả lời một cách thân thiện, chuyên nghiệp và hữu ích. 
Sử dụng thông tin kinh doanh sau để trả lời phù hợp: {business_info}

Quy tắc trả lời:
- Giữ câu trả lời ngắn gọn, dưới {max_length} ký tự
- Sử dụng ngôn ngữ {language}
- Tông giọng: {tone}
- Không trả lời nếu comment/message chứa từ khóa loại trừ
- Luôn kết thúc bằng lời mời liên hệ hoặc tham quan

Message từ khách hàng: {customer_message}`)
});

const DEFAULT_VALUES = {
  isEnabled: true,
  replyToComments: true,
  replyToMessages: true,
  replyToInstagramComments: true,
  replyToInstagramMessages: true,
  excludeKeywords: '',
  businessInfo: '',
  replyTone: 'friendly',
  replyLanguage: 'vi',
  maxReplyLength: 500,
  aiPrompt: `Bạn là trợ lý AI của {business_name}. Hãy trả lời một cách thân thiện, chuyên nghiệp và hữu ích. 
Sử dụng thông tin kinh doanh sau để trả lời phù hợp: {business_info}

Quy tắc trả lời:
- Giữ câu trả lời ngắn gọn, dưới {max_length} ký tự
- Sử dụng ngôn ngữ {language}
- Tông giọng: {tone}
- Không trả lời nếu comment/message chứa từ khóa loại trừ
- Luôn kết thúc bằng lời mời liên hệ hoặc tham quan

Message từ khách hàng: {customer_message}`
};

export default function FacebookConfigDialog({ open, onClose, account, onConfigSaved }) {
  const [expanded, setExpanded] = useState('basic');

  // Use hooks for data management
  const {
    config,
    loading: configLoading,
    refetch: refetchConfig
  } = useAutoReplyConfig(account?.pageId);

  const {
    saveConfig,
    loading: saveLoading
  } = useAutoReplyConfigMutations();

  const methods = useForm({
    resolver: zodResolver(FacebookConfigSchema),
    defaultValues: DEFAULT_VALUES,
  });

  const { handleSubmit, reset, watch, setValue } = methods;

  // Combined loading state
  const loading = configLoading || saveLoading;

  // Load existing config when dialog opens
  useEffect(() => {
    if (open && account?.pageId) {
      if (config) {
        // Map config data to form format
        const formData = {
          ...DEFAULT_VALUES,
          isEnabled: config.enableCommentReply || config.enableMessageReply || false,
          replyToComments: config.enableCommentReply || false,
          replyToMessages: config.enableMessageReply || false,
          replyToInstagramComments: config.enableInstagramComments || false,
          replyToInstagramMessages: config.enableInstagramMessages || false,
          excludeKeywords: (config.excludeKeywords || []).join(', '),
          businessInfo: config.businessInfo || '',
          replyTone: config.replyTone || REPLY_TONES.FRIENDLY,
          replyLanguage: config.replyLanguage || REPLY_LANGUAGES.VI,
          maxReplyLength: config.maxReplyLength || 500,
          aiPrompt: config.replyPrompt || DEFAULT_VALUES.aiPrompt
        };
        reset(formData);
      } else {
        reset(DEFAULT_VALUES);
      }
    }
  }, [open, account?.pageId, config, reset]);

  const onSubmit = handleSubmit(async (data) => {
    try {
      // Map form data to API format
      const configData = {
        pageId: account.pageId,
        enableCommentReply: data.replyToComments,
        enableMessageReply: data.replyToMessages,
        enableInstagramComments: data.replyToInstagramComments,
        enableInstagramMessages: data.replyToInstagramMessages,
        autoPrivateReply: false, // Default value
        replyPrompt: data.aiPrompt,
        replyTone: data.replyTone,
        replyLanguage: data.replyLanguage,
        maxReplyLength: data.maxReplyLength,
        businessInfo: data.businessInfo,
        products: '', // Default value
        policies: '', // Default value
        excludeKeywords: data.excludeKeywords.split(',').map(k => k.trim()).filter(k => k)
      };

      const result = await saveConfig(configData);

      if (result.success) {
        toast.success('Cấu hình đã được lưu thành công!');
        await refetchConfig(); // Refresh config data
        onConfigSaved?.();
        onClose();
      } else {
        throw new Error(result.error?.message || 'Không thể lưu cấu hình');
      }
    } catch (error) {
      console.error('Error saving config:', error);
      toast.error('Lỗi khi lưu cấu hình');
    }
  });

  const handleAccordionChange = (panel) => (event, isExpanded) => {
    setExpanded(isExpanded ? panel : false);
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  if (!account) return null;

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { height: '90vh' }
      }}
    >
      <DialogTitle>
        <Box display="flex" alignItems="center" gap={2}>
          <Iconify icon="ic:baseline-facebook" sx={{ color: 'primary.main' }} />
          <Box>
            <Typography variant="h6">Cấu hình Auto Reply</Typography>
            <Typography variant="body2" color="text.secondary">
              {account.pageName}
            </Typography>
          </Box>
        </Box>
      </DialogTitle>

      <DialogContent dividers>
        {loading ? (
          <Box display="flex" justifyContent="center" py={4}>
            <CircularProgress />
          </Box>
        ) : (
          <Form methods={methods} onSubmit={onSubmit}>
            <Stack spacing={2}>
              {/* Basic Settings */}
              <Accordion 
                expanded={expanded === 'basic'} 
                onChange={handleAccordionChange('basic')}
              >
                <AccordionSummary expandIcon={<Iconify icon="eva:arrow-ios-downward-fill" />}>
                  <Typography variant="h6">Cài đặt cơ bản</Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <Stack spacing={3}>
                    <Field.Switch
                      name="isEnabled"
                      label="Bật Auto Reply"
                    />

                    <Divider />

                    <Typography variant="subtitle2">Tự động trả lời:</Typography>
                    <Stack spacing={2}>
                      <Field.Switch
                        name="replyToComments"
                        label={
                          <Box display="flex" alignItems="center" gap={1}>
                            <Iconify icon="ic:baseline-facebook" sx={{ fontSize: 16 }} />
                            <span>Facebook Comments</span>
                          </Box>
                        }
                      />

                      <Field.Switch
                        name="replyToMessages"
                        label={
                          <Box display="flex" alignItems="center" gap={1}>
                            <Iconify icon="ic:baseline-facebook" sx={{ fontSize: 16 }} />
                            <span>Facebook Messenger</span>
                          </Box>
                        }
                      />

                      {account.instagramAccountId && (
                        <>
                          <Field.Switch
                            name="replyToInstagramComments"
                            label={
                              <Box display="flex" alignItems="center" gap={1}>
                                <Iconify icon="skill-icons:instagram" sx={{ fontSize: 16 }} />
                                <span>Instagram Comments</span>
                              </Box>
                            }
                          />

                          <Field.Switch
                            name="replyToInstagramMessages"
                            label={
                              <Box display="flex" alignItems="center" gap={1}>
                                <Iconify icon="skill-icons:instagram" sx={{ fontSize: 16 }} />
                                <span>Instagram Direct</span>
                              </Box>
                            }
                          />
                        </>
                      )}
                    </Stack>
                  </Stack>
                </AccordionDetails>
              </Accordion>

              {/* AI Configuration */}
              <Accordion 
                expanded={expanded === 'ai'} 
                onChange={handleAccordionChange('ai')}
              >
                <AccordionSummary expandIcon={<Iconify icon="eva:arrow-ios-downward-fill" />}>
                  <Typography variant="h6">
                    <Iconify icon="material-symbols:smart-toy" sx={{ mr: 1, verticalAlign: 'middle' }} />
                    Cấu hình AI
                  </Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <Stack spacing={3}>
                    <Field.Text
                      name="businessInfo"
                      label="Thông tin kinh doanh"
                      multiline
                      rows={4}
                      placeholder="Mô tả về doanh nghiệp, sản phẩm, dịch vụ, địa chỉ, giờ mở cửa, v.v..."
                      helperText="AI sẽ sử dụng thông tin này để trả lời phù hợp với ngữ cảnh kinh doanh"
                    />

                    <Field.Select
                      name="replyTone"
                      label="Tông giọng trả lời"
                    >
                      <MenuItem value={REPLY_TONES.FRIENDLY}>Thân thiện</MenuItem>
                      <MenuItem value={REPLY_TONES.PROFESSIONAL}>Chuyên nghiệp</MenuItem>
                      <MenuItem value={REPLY_TONES.CASUAL}>Thoải mái</MenuItem>
                      <MenuItem value={REPLY_TONES.FORMAL}>Trang trọng</MenuItem>
                      <MenuItem value={REPLY_TONES.ENTHUSIASTIC}>Nhiệt tình</MenuItem>
                    </Field.Select>

                    <Field.Select
                      name="replyLanguage"
                      label="Ngôn ngữ trả lời"
                    >
                      <MenuItem value={REPLY_LANGUAGES.VI}>Tiếng Việt</MenuItem>
                      <MenuItem value={REPLY_LANGUAGES.EN}>English</MenuItem>
                      <MenuItem value="auto">Tự động theo ngôn ngữ khách hàng</MenuItem>
                    </Field.Select>

                    <Box>
                      <Typography gutterBottom>
                        Độ dài tối đa: {watch('maxReplyLength')} ký tự
                      </Typography>
                      <Field.Slider
                        name="maxReplyLength"
                        min={100}
                        max={1000}
                        step={50}
                        marks={[
                          { value: 100, label: '100' },
                          { value: 500, label: '500' },
                          { value: 1000, label: '1000' }
                        ]}
                      />
                    </Box>

                    <Field.Text
                      name="excludeKeywords"
                      label="Từ khóa loại trừ"
                      placeholder="spam, quảng cáo, bán hàng"
                      helperText="Các từ khóa cách nhau bằng dấu phẩy. AI sẽ không trả lời nếu phát hiện các từ này"
                    />
                  </Stack>
                </AccordionDetails>
              </Accordion>

              {/* Advanced AI Prompt */}
              <Accordion 
                expanded={expanded === 'prompt'} 
                onChange={handleAccordionChange('prompt')}
              >
                <AccordionSummary expandIcon={<Iconify icon="eva:arrow-ios-downward-fill" />}>
                  <Typography variant="h6">Prompt AI nâng cao</Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <Stack spacing={2}>
                    <Alert severity="info">
                      <Typography variant="body2">
                        Bạn có thể tùy chỉnh prompt để AI trả lời theo cách riêng của bạn.
                        Sử dụng các biến: {'{business_name}'}, {'{business_info}'}, {'{tone}'}, {'{language}'}, {'{max_length}'}, {'{customer_message}'}
                      </Typography>
                    </Alert>

                    <Field.Text
                      name="aiPrompt"
                      label="AI Prompt"
                      multiline
                      rows={8}
                      fullWidth
                    />
                  </Stack>
                </AccordionDetails>
              </Accordion>
            </Stack>
          </Form>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose}>Hủy</Button>
        <LoadingButton
          loading={methods.formState.isSubmitting}
          onClick={methods.handleSubmit(onSubmit)}
          variant="contained"
          disabled={loading}
        >
          Lưu cấu hình
        </LoadingButton>
      </DialogActions>
    </Dialog>
  );
} 