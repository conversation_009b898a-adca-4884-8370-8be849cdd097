'use client';

import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { alpha, useTheme } from '@mui/material/styles';

import { Iconify } from 'src/components/iconify';
import { getBotTypeProductConfig } from 'src/actions/mooly-chatbot/chatbot-product-filter-service';

// ----------------------------------------------------------------------

/**
 * Component hiển thị thông tin về loại sản phẩm phù hợp với bot type
 * @param {Object} props - Props
 * @param {string} props.botType - Loại bot ('sale_bot' hoặc 'rag_bot')
 * @param {string} props.variant - Ki<PERSON>u hiển thị ('info' hoặc 'description')
 * @param {Object} props.sx - Custom styles
 */
export default function BotTypeProductInfo({ botType, variant = 'info', sx, ...other }) {
  const theme = useTheme();
  const productConfig = getBotTypeProductConfig(botType);

  if (variant === 'description') {
    return (
      <Typography variant="body2" color="text.secondary" sx={sx} {...other}>
        {productConfig.description}
      </Typography>
    );
  }

  return (
    <Box
      sx={{
        p: 2,
        bgcolor: alpha(theme.palette.info.main, 0.08),
        borderRadius: 1,
        border: `1px solid ${alpha(theme.palette.info.main, 0.24)}`,
        ...sx,
      }}
      {...other}
    >
      <Stack direction="row" spacing={1} alignItems="center">
        <Iconify 
          icon="eva:info-fill" 
          sx={{ color: 'info.main', width: 20, height: 20 }} 
        />
        <Typography variant="body2" color="info.dark">
          {productConfig.message}
        </Typography>
      </Stack>
    </Box>
  );
}

/**
 * Component hiển thị badge cho bot type
 * @param {Object} props - Props
 * @param {string} props.botType - Loại bot
 * @param {Object} props.sx - Custom styles
 */
export function BotTypeBadge({ botType, sx, ...other }) {
  const theme = useTheme();
  
  const getBotTypeConfig = (type) => {
    switch (type) {
      case 'rag_bot':
        return {
          color: theme.palette.secondary.main,
          bgColor: alpha(theme.palette.secondary.main, 0.1),
          label: 'RAG BOT',
          icon: 'solar:chat-round-dots-bold',
        };
      case 'sale_bot':
      default:
        return {
          color: theme.palette.primary.main,
          bgColor: alpha(theme.palette.primary.main, 0.1),
          label: 'SALE BOT',
          icon: 'solar:cart-large-2-bold',
        };
    }
  };

  const config = getBotTypeConfig(botType);

  return (
    <Box
      sx={{
        display: 'inline-flex',
        alignItems: 'center',
        gap: 0.5,
        px: 1,
        py: 0.5,
        borderRadius: 1,
        bgcolor: config.bgColor,
        color: config.color,
        fontSize: '0.75rem',
        fontWeight: 600,
        ...sx,
      }}
      {...other}
    >
      <Iconify icon={config.icon} sx={{ width: 14, height: 14 }} />
      {config.label}
    </Box>
  );
}

/**
 * Component hiển thị thông tin chi tiết về product type
 * @param {Object} props - Props
 * @param {string} props.productType - Loại sản phẩm
 * @param {Object} props.sx - Custom styles
 */
export function ProductTypeBadge({ productType, sx, ...other }) {
  const theme = useTheme();
  
  const getProductTypeConfig = (type) => {
    switch (type) {
      case 'simple':
        return {
          color: theme.palette.success.main,
          bgColor: alpha(theme.palette.success.main, 0.1),
          label: 'Đơn giản',
          icon: 'eva:cube-outline',
        };
      case 'variable':
        return {
          color: theme.palette.warning.main,
          bgColor: alpha(theme.palette.warning.main, 0.1),
          label: 'Biến thể',
          icon: 'eva:options-2-outline',
        };
      case 'digital':
        return {
          color: theme.palette.info.main,
          bgColor: alpha(theme.palette.info.main, 0.1),
          label: 'Số hóa',
          icon: 'eva:cloud-download-outline',
        };
      case 'service':
        return {
          color: theme.palette.secondary.main,
          bgColor: alpha(theme.palette.secondary.main, 0.1),
          label: 'Dịch vụ',
          icon: 'eva:settings-2-outline',
        };
      default:
        return {
          color: theme.palette.grey[600],
          bgColor: alpha(theme.palette.grey[600], 0.1),
          label: 'Khác',
          icon: 'eva:question-mark-circle-outline',
        };
    }
  };

  const config = getProductTypeConfig(productType);

  return (
    <Box
      sx={{
        display: 'inline-flex',
        alignItems: 'center',
        gap: 0.5,
        px: 1,
        py: 0.5,
        borderRadius: 1,
        bgcolor: config.bgColor,
        color: config.color,
        fontSize: '0.75rem',
        fontWeight: 500,
        ...sx,
      }}
      {...other}
    >
      <Iconify icon={config.icon} sx={{ width: 14, height: 14 }} />
      {config.label}
    </Box>
  );
}
