/**
 * Unified Social Media Integration Constants
 * <PERSON><PERSON><PERSON> nghĩa các hằng số chung cho tất cả social media platforms
 */

// Supported Platforms
export const PLATFORMS = {
  FACEBOOK: 'facebook',
  INSTAGRAM: 'instagram',
  TIKTOK: 'tiktok',
  YOUTUBE: 'youtube'
};

// Account Types
export const ACCOUNT_TYPES = {
  PAGE: 'page',
  BUSINESS: 'business', 
  CREATOR: 'creator',
  PERSONAL: 'personal'
};

// Interaction Types
export const INTERACTION_TYPES = {
  COMMENT: 'comment',
  MESSAGE: 'message',
  MENTION: 'mention',
  STORY_REPLY: 'story_reply',
  DIRECT_MESSAGE: 'direct_message'
};

// Webhook Fields
export const WEBHOOK_FIELDS = {
  FACEBOOK: ['feed', 'comments', 'messages', 'messaging_postbacks', 'messaging_optins'],
  INSTAGRAM: ['comments', 'messages', 'mentions', 'story_insights'],
  TIKTOK: ['comments', 'mentions'],
  YOUTUBE: ['comments', 'mentions']
};

// API Endpoints
export const API_ENDPOINTS = {
  FACEBOOK: {
    BASE_URL: 'https://graph.facebook.com',
    VERSION: 'v23.0'
  },
  INSTAGRAM: {
    BASE_URL: 'https://graph.facebook.com',
    VERSION: 'v23.0'
  },
  TIKTOK: {
    BASE_URL: 'https://open-api.tiktok.com',
    VERSION: 'v1.3'
  },
  YOUTUBE: {
    BASE_URL: 'https://www.googleapis.com/youtube',
    VERSION: 'v3'
  }
};

// Database Tables
export const TABLES = {
  ACCOUNTS: 'facebook_accounts', // Unified table for all platforms
  CONFIG: 'facebook_auto_reply_config',
  ACTIVITY_LOGS: 'facebook_activity_logs',
  WEBHOOKS: 'social_media_webhooks',
  INTERACTIONS: 'social_media_interactions'
};

// Activity Types
export const ACTIVITY_TYPES = {
  ACCOUNT_CONNECTED: 'account_connected',
  ACCOUNT_DISCONNECTED: 'account_disconnected',
  TOKEN_REFRESHED: 'token_refreshed',
  WEBHOOK_SUBSCRIBED: 'webhook_subscribed',
  WEBHOOK_UNSUBSCRIBED: 'webhook_unsubscribed',
  AUTO_REPLY_SENT: 'auto_reply_sent',
  AUTO_REPLY_FAILED: 'auto_reply_failed',
  COMMENT_RECEIVED: 'comment_received',
  MESSAGE_RECEIVED: 'message_received',
  MENTION_RECEIVED: 'mention_received'
};

// Reply Tones
export const REPLY_TONES = {
  FRIENDLY: 'friendly',
  PROFESSIONAL: 'professional',
  CASUAL: 'casual',
  ENTHUSIASTIC: 'enthusiastic',
  HELPFUL: 'helpful'
};

// Reply Languages
export const REPLY_LANGUAGES = {
  VIETNAMESE: 'vi',
  ENGLISH: 'en',
  AUTO_DETECT: 'auto'
};

// Default Configuration
export const DEFAULT_CONFIG = {
  ENABLE_COMMENT_REPLY: false,
  ENABLE_MESSAGE_REPLY: false,
  ENABLE_INSTAGRAM_COMMENTS: false,
  ENABLE_INSTAGRAM_MESSAGES: false,
  ENABLE_INSTAGRAM_STORY_REPLIES: false,
  AUTO_PRIVATE_REPLY: false,
  REPLY_TONE: REPLY_TONES.FRIENDLY,
  REPLY_LANGUAGE: REPLY_LANGUAGES.VIETNAMESE,
  MAX_REPLY_LENGTH: 500,
  INSTAGRAM_REPLY_DELAY_SECONDS: 5,
  ENABLE_AUTO_LIKE_COMMENTS: false
};

// Platform-specific Features
export const PLATFORM_FEATURES = {
  [PLATFORMS.FACEBOOK]: {
    SUPPORTS_COMMENTS: true,
    SUPPORTS_MESSAGES: true,
    SUPPORTS_MENTIONS: true,
    SUPPORTS_STORY_REPLIES: false,
    SUPPORTS_AUTO_LIKE: true,
    MAX_MESSAGE_LENGTH: 2000,
    MAX_COMMENT_LENGTH: 8000
  },
  [PLATFORMS.INSTAGRAM]: {
    SUPPORTS_COMMENTS: true,
    SUPPORTS_MESSAGES: true,
    SUPPORTS_MENTIONS: true,
    SUPPORTS_STORY_REPLIES: true,
    SUPPORTS_AUTO_LIKE: true,
    MAX_MESSAGE_LENGTH: 1000,
    MAX_COMMENT_LENGTH: 2200
  },
  [PLATFORMS.TIKTOK]: {
    SUPPORTS_COMMENTS: true,
    SUPPORTS_MESSAGES: false,
    SUPPORTS_MENTIONS: true,
    SUPPORTS_STORY_REPLIES: false,
    SUPPORTS_AUTO_LIKE: false,
    MAX_MESSAGE_LENGTH: 0,
    MAX_COMMENT_LENGTH: 150
  },
  [PLATFORMS.YOUTUBE]: {
    SUPPORTS_COMMENTS: true,
    SUPPORTS_MESSAGES: false,
    SUPPORTS_MENTIONS: true,
    SUPPORTS_STORY_REPLIES: false,
    SUPPORTS_AUTO_LIKE: true,
    MAX_MESSAGE_LENGTH: 0,
    MAX_COMMENT_LENGTH: 10000
  }
};

// Error Messages
export const ERROR_MESSAGES = {
  PLATFORM_NOT_SUPPORTED: 'Platform không được hỗ trợ',
  ACCOUNT_NOT_FOUND: 'Không tìm thấy tài khoản',
  INVALID_TOKEN: 'Token không hợp lệ hoặc đã hết hạn',
  WEBHOOK_SUBSCRIPTION_FAILED: 'Không thể đăng ký webhook',
  AUTO_REPLY_FAILED: 'Không thể gửi tin nhắn tự động',
  RATE_LIMIT_EXCEEDED: 'Đã vượt quá giới hạn API',
  INSUFFICIENT_PERMISSIONS: 'Không đủ quyền truy cập',
  NETWORK_ERROR: 'Lỗi kết nối mạng',
  INVALID_CONFIGURATION: 'Cấu hình không hợp lệ'
};

// Success Messages
export const SUCCESS_MESSAGES = {
  ACCOUNT_CONNECTED: 'Kết nối tài khoản thành công',
  ACCOUNT_DISCONNECTED: 'Ngắt kết nối tài khoản thành công',
  CONFIG_SAVED: 'Lưu cấu hình thành công',
  WEBHOOK_SUBSCRIBED: 'Đăng ký webhook thành công',
  AUTO_REPLY_SENT: 'Gửi tin nhắn tự động thành công',
  TOKEN_REFRESHED: 'Làm mới token thành công'
};

// Validation Rules
export const VALIDATION_RULES = {
  MAX_ACCOUNTS_PER_TENANT: 10,
  MIN_REPLY_LENGTH: 1,
  MAX_REPLY_LENGTH: 2000,
  MIN_DELAY_SECONDS: 1,
  MAX_DELAY_SECONDS: 300,
  MAX_EXCLUDE_KEYWORDS: 50,
  MAX_KEYWORD_LENGTH: 100
};

// Rate Limits (per platform)
export const RATE_LIMITS = {
  [PLATFORMS.FACEBOOK]: {
    COMMENTS_PER_HOUR: 200,
    MESSAGES_PER_HOUR: 1000,
    API_CALLS_PER_HOUR: 4800
  },
  [PLATFORMS.INSTAGRAM]: {
    COMMENTS_PER_HOUR: 60,
    MESSAGES_PER_HOUR: 100,
    API_CALLS_PER_HOUR: 240
  },
  [PLATFORMS.TIKTOK]: {
    COMMENTS_PER_HOUR: 100,
    MESSAGES_PER_HOUR: 0,
    API_CALLS_PER_HOUR: 1000
  },
  [PLATFORMS.YOUTUBE]: {
    COMMENTS_PER_HOUR: 100,
    MESSAGES_PER_HOUR: 0,
    API_CALLS_PER_HOUR: 10000
  }
};

// OAuth Scopes
export const OAUTH_SCOPES = {
  [PLATFORMS.FACEBOOK]: [
    'pages_manage_metadata',
    'pages_read_engagement', 
    'pages_manage_posts',
    'pages_messaging',
    'instagram_basic',
    'instagram_manage_comments',
    'instagram_manage_messages'
  ],
  [PLATFORMS.INSTAGRAM]: [
    'instagram_basic',
    'instagram_manage_comments',
    'instagram_manage_messages',
    'instagram_manage_insights'
  ]
};

// Platform Colors (for UI)
export const PLATFORM_COLORS = {
  [PLATFORMS.FACEBOOK]: '#1877F2',
  [PLATFORMS.INSTAGRAM]: '#E4405F',
  [PLATFORMS.TIKTOK]: '#000000',
  [PLATFORMS.YOUTUBE]: '#FF0000'
};

// Platform Icons
export const PLATFORM_ICONS = {
  [PLATFORMS.FACEBOOK]: 'mdi:facebook',
  [PLATFORMS.INSTAGRAM]: 'mdi:instagram',
  [PLATFORMS.TIKTOK]: 'mdi:tiktok',
  [PLATFORMS.YOUTUBE]: 'mdi:youtube'
};

// Webhook Event Types
export const WEBHOOK_EVENTS = {
  FACEBOOK: {
    FEED: 'feed',
    COMMENTS: 'comments',
    MESSAGES: 'messages',
    MESSAGING_POSTBACKS: 'messaging_postbacks',
    MESSAGING_OPTINS: 'messaging_optins'
  },
  INSTAGRAM: {
    COMMENTS: 'comments',
    MESSAGES: 'messages',
    MENTIONS: 'mentions',
    STORY_INSIGHTS: 'story_insights'
  }
};

// Auto-reply Templates
export const AUTO_REPLY_TEMPLATES = {
  GREETING: {
    vi: 'Xin chào! Cảm ơn bạn đã quan tâm đến chúng tôi. Chúng tôi sẽ phản hồi bạn sớm nhất có thể.',
    en: 'Hello! Thank you for your interest. We will get back to you as soon as possible.'
  },
  RECEIVED: {
    vi: 'Đã nhận',
    en: 'Received'
  },
  THANK_YOU: {
    vi: 'Cảm ơn bạn đã liên hệ!',
    en: 'Thank you for contacting us!'
  }
};
