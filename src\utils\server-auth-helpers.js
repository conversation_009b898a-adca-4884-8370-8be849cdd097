// =====================================================
// SERVER AUTH HELPERS - OPTIMIZED FOR RLS SYSTEM
// =====================================================
// Các utility functions hỗ trợ server-side authentication
// với full trust vào RLS policies và database triggers

import { createClient, createAdminClient } from './supabase/server';

/**
 * ✅ OPTIMIZED: Lấy user info với tenant validation
 * @param {string} userId - User ID
 * @param {Object} supabase - Supabase client (optional)
 * @returns {Promise<Object>} - User information với tenant data
 */
export async function getUserWithTenant(userId, supabase = null) {
  try {
    const client = supabase || await createClient();
    
    const { data, error } = await client
      .from('users')
      .select(`
        id,
        email,
        full_name,
        tenant_id,
        is_active,
        is_tenant_owner,
        avatar_url,
        phone,
        tenants:tenant_id (
          id,
          name,
          slug,
          is_active,
          subscription_plan,
          subscription_status
        )
      `)
      .eq('id', userId)
      .single();

    if (error) {
      console.error('Error fetching user with tenant:', error);
      return { success: false, error, data: null };
    }

    if (!data.is_active) {
      return { 
        success: false, 
        error: { 
          message: 'Tài khoản đã bị vô hiệu hóa',
          code: 'USER_INACTIVE'
        }, 
        data: null 
      };
    }

    if (!data.tenants?.is_active) {
      return { 
        success: false, 
        error: { 
          message: 'Tenant không hoạt động',
          code: 'TENANT_INACTIVE'
        }, 
        data: null 
      };
    }

    return { success: true, error: null, data };
  } catch (error) {
    console.error('Error in getUserWithTenant:', error);
    return { success: false, error, data: null };
  }
}

/**
 * ✅ OPTIMIZED: Validate user permissions với RLS trust
 * @param {string} userId - User ID
 * @param {string} permission - Permission code
 * @param {Object} supabase - Supabase client (optional)
 * @returns {Promise<boolean>} - True nếu có quyền
 */
export async function validateUserPermission(userId, permission, supabase = null) {
  try {
    const client = supabase || await createClient();
    
    // RLS policies sẽ tự động filter theo tenant_id
    const { data, error } = await client
      .from('tenant_user_roles')
      .select(`
        role_id,
        roles:role_id (
          id,
          name,
          role_permissions (
            permission_id,
            permissions:permission_id (
              code
            )
          )
        )
      `)
      .eq('user_id', userId);

    if (error) {
      console.error('Error validating user permission:', error);
      return false;
    }

    // Kiểm tra permission trong tất cả roles của user
    for (const userRole of data) {
      const rolePermissions = userRole.roles?.role_permissions || [];
      for (const rolePermission of rolePermissions) {
        if (rolePermission.permissions?.code === permission) {
          return true;
        }
      }
    }

    return false;
  } catch (error) {
    console.error('Error in validateUserPermission:', error);
    return false;
  }
}

/**
 * ✅ OPTIMIZED: Lấy tenant statistics với RLS filtering
 * @param {string} tenantId - Tenant ID
 * @param {Object} supabase - Supabase client (optional)
 * @returns {Promise<Object>} - Tenant statistics
 */
export async function getTenantStats(tenantId, supabase = null) {
  try {
    const client = supabase || await createClient();
    
    // RLS policies sẽ tự động filter theo tenant_id
    const [
      { count: usersCount },
      { count: chatbotsCount },
      { count: ordersCount },
      { count: productsCount }
    ] = await Promise.all([
      client.from('users').select('*', { count: 'exact', head: true }),
      client.from('chatbot_configurations').select('*', { count: 'exact', head: true }),
      client.from('orders').select('*', { count: 'exact', head: true }),
      client.from('products').select('*', { count: 'exact', head: true })
    ]);

    return {
      success: true,
      data: {
        users: usersCount || 0,
        chatbots: chatbotsCount || 0,
        orders: ordersCount || 0,
        products: productsCount || 0
      }
    };
  } catch (error) {
    console.error('Error getting tenant stats:', error);
    return { success: false, error, data: null };
  }
}

/**
 * ✅ OPTIMIZED: Validate tenant subscription status
 * @param {string} tenantId - Tenant ID
 * @param {Object} supabase - Supabase client (optional)
 * @returns {Promise<Object>} - Subscription validation result
 */
export async function validateTenantSubscription(tenantId, supabase = null) {
  try {
    const client = supabase || await createClient();
    
    const { data, error } = await client
      .from('tenants')
      .select('subscription_plan, subscription_status, subscription_end_date, max_users, max_stores')
      .eq('id', tenantId)
      .single();

    if (error) {
      console.error('Error validating tenant subscription:', error);
      return { success: false, error, data: null };
    }

    const now = new Date();
    const endDate = data.subscription_end_date ? new Date(data.subscription_end_date) : null;
    
    const isExpired = endDate && endDate < now;
    const isActive = data.subscription_status === 'active' && !isExpired;

    return {
      success: true,
      data: {
        ...data,
        is_expired: isExpired,
        is_active: isActive,
        days_remaining: endDate ? Math.ceil((endDate - now) / (1000 * 60 * 60 * 24)) : null
      }
    };
  } catch (error) {
    console.error('Error in validateTenantSubscription:', error);
    return { success: false, error, data: null };
  }
}

/**
 * ✅ OPTIMIZED: Log user activity với RLS auto-tenant
 * @param {string} userId - User ID
 * @param {string} action - Action performed
 * @param {Object} metadata - Additional metadata
 * @param {Object} supabase - Supabase client (optional)
 * @returns {Promise<boolean>} - Success status
 */
export async function logUserActivity(userId, action, metadata = {}, supabase = null) {
  try {
    const client = supabase || await createClient();
    
    // Database trigger sẽ tự động set tenant_id
    const { error } = await client
      .from('user_activity_logs')
      .insert({
        user_id: userId,
        action,
        metadata,
        ip_address: metadata.ip_address || null,
        user_agent: metadata.user_agent || null
      });

    if (error) {
      console.error('Error logging user activity:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error in logUserActivity:', error);
    return false;
  }
}

/**
 * ✅ OPTIMIZED: Cleanup expired sessions với admin privileges
 * @returns {Promise<number>} - Number of cleaned sessions
 */
export async function cleanupExpiredSessions() {
  try {
    const adminClient = createAdminClient();
    
    // Cleanup sessions older than 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const { data, error } = await adminClient
      .from('user_sessions')
      .delete()
      .lt('last_activity', thirtyDaysAgo.toISOString())
      .select('id');

    if (error) {
      console.error('Error cleaning up expired sessions:', error);
      return 0;
    }

    console.log(`✅ Cleaned up ${data?.length || 0} expired sessions`);
    return data?.length || 0;
  } catch (error) {
    console.error('Error in cleanupExpiredSessions:', error);
    return 0;
  }
}

/**
 * ✅ OPTIMIZED: Refresh user tenant cache
 * @param {string} userId - User ID
 * @returns {Promise<boolean>} - Success status
 */
export async function refreshUserTenantCache(userId) {
  try {
    // Implement cache refresh logic here if using Redis/Memcached
    // For now, just return true as RLS handles real-time data
    console.log(`🔄 Refreshing tenant cache for user: ${userId}`);
    return true;
  } catch (error) {
    console.error('Error refreshing user tenant cache:', error);
    return false;
  }
}
