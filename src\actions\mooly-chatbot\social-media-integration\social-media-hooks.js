/**
 * Unified Social Media Integration Hooks
 * React hooks cho quản lý multi-platform social media integration
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useDebounce } from 'minimal-shared/hooks';
import { 
  PLATFORMS, 
  TABLES, 
  ERROR_MESSAGES,
  SUCCESS_MESSAGES 
} from './social-media-constants.js';
import { isPlatformSupported } from './social-media-utils.js';
import { fetchData, updateData, createData } from '../supabase-utils.js';

/**
 * Hook để lấy danh sách tài khoản social media
 * @param {string} platform - Platform filter (optional)
 * @param {Object} options - Additional options
 * @returns {Object} Hook state và functions
 */
export function useSocialMediaAccounts(platform = null, options = {}) {
  const [accounts, setAccounts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastFetch, setLastFetch] = useState(null);

  const { 
    autoRefresh = false,
    refreshInterval = 60000,
    includeInactive = false 
  } = options;

  const fetchAccounts = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const filters = {
        ...(platform && { platform }),
        ...(includeInactive ? {} : { is_active: true })
      };

      const result = await fetchData(TABLES.ACCOUNTS, filters);

      if (!result.success) throw new Error(result.error?.message || 'Failed to fetch accounts');

      const data = result.data;

      // Sort by platform and then by name
      const sortedAccounts = (data || []).sort((a, b) => {
        if (a.platform !== b.platform) {
          return a.platform.localeCompare(b.platform);
        }
        return (a.page_name || a.username || '').localeCompare(b.page_name || b.username || '');
      });

      setAccounts(sortedAccounts);
      setLastFetch(new Date());
    } catch (err) {
      console.error('❌ Error fetching social media accounts:', err);
      setError(err.message || ERROR_MESSAGES.NETWORK_ERROR);
    } finally {
      setLoading(false);
    }
  }, [platform, includeInactive]);

  // Auto refresh
  useEffect(() => {
    if (autoRefresh && refreshInterval > 0 && !error) {
      const interval = setInterval(() => {
        fetchAccounts();
      }, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval, fetchAccounts, error]);

  // Initial fetch
  useEffect(() => {
    fetchAccounts();
  }, [fetchAccounts]);

  // Computed values
  const accountsByPlatform = useMemo(() => accounts.reduce((acc, account) => {
      if (!acc[account.platform]) {
        acc[account.platform] = [];
      }
      acc[account.platform].push(account);
      return acc;
    }, {}), [accounts]);

  const activeAccountsCount = useMemo(() => accounts.filter(account => account.is_active).length, [accounts]);

  const platformCounts = useMemo(() => Object.keys(accountsByPlatform).reduce((acc, p) => {
      acc[p] = accountsByPlatform[p].length;
      return acc;
    }, {}), [accountsByPlatform]);

  // Helper functions
  const getAccountsByPlatform = useCallback((targetPlatform) => 
    accountsByPlatform[targetPlatform] || [], [accountsByPlatform]);
  
  const getAccountById = useCallback((accountId) => 
    accounts.find(acc => acc.id === accountId), [accounts]);
  
  const getAccountByPlatformId = useCallback((targetPlatform, platformAccountId) => 
    accounts.find(acc => acc.platform === targetPlatform && acc.page_id === platformAccountId), [accounts]);

  return {
    accounts,
    accountsByPlatform,
    activeAccountsCount,
    platformCounts,
    loading,
    error,
    lastFetch,
    refetch: fetchAccounts,
    // Helper functions
    getAccountsByPlatform,
    getAccountById,
    getAccountByPlatformId
  };
}

/**
 * Hook để lấy thông tin một tài khoản social media cụ thể
 * @param {string} accountId - Account ID
 * @param {Object} options - Additional options
 * @returns {Object} Hook state và functions
 */
export function useSocialMediaAccount(accountId, options = {}) {
  const [account, setAccount] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const { autoRefresh = false, refreshInterval = 60000 } = options;

  const fetchAccount = useCallback(async () => {
    if (!accountId) {
      setAccount(null);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const result = await fetchData(TABLES.ACCOUNTS, { id: accountId });

      if (!result.success) throw new Error(result.error?.message || 'Failed to fetch account');

      setAccount(result.data?.[0] || null);
    } catch (err) {
      console.error('❌ Error fetching social media account:', err);
      setError(err.message || ERROR_MESSAGES.NETWORK_ERROR);
    } finally {
      setLoading(false);
    }
  }, [accountId]);

  // Auto refresh
  useEffect(() => {
    if (autoRefresh && refreshInterval > 0 && accountId) {
      const interval = setInterval(fetchAccount, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval, accountId, fetchAccount]);

  // Initial fetch
  useEffect(() => {
    fetchAccount();
  }, [fetchAccount]);

  return {
    account,
    loading,
    error,
    refetch: fetchAccount,
    // Helper properties
    isActive: account?.is_active || false,
    platform: account?.platform,
    accountName: account?.page_name || account?.username,
    avatarUrl: account?.avatar_url,
    followerCount: account?.follower_count || 0,
    isVerified: account?.is_verified || false
  };
}

/**
 * Hook để lấy cấu hình auto-reply
 * @param {string} accountId - Account ID hoặc page_id
 * @param {string} platform - Platform name
 * @returns {Object} Hook state và functions
 */
export function useSocialMediaConfig(accountId, platform) {
  const [config, setConfig] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchConfig = useCallback(async () => {
    if (!accountId) {
      setConfig(null);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      console.log('🔍 Fetching social media config for accountId:', accountId);

      // Sử dụng supabase-utils pattern với camelCase filters
      const result = await fetchData(TABLES.CONFIG, {
        filters: { pageId: accountId }
      });

      console.log('📄 Config fetch result:', result);

      if (!result.success) throw new Error(result.error?.message || 'Không thể tải cấu hình');

      setConfig(result.data?.[0] || null);
    } catch (err) {
      console.error('❌ Error fetching social media config:', err);
      setError(err.message || ERROR_MESSAGES.NETWORK_ERROR);
    } finally {
      setLoading(false);
    }
  }, [accountId]);

  useEffect(() => {
    fetchConfig();
  }, [fetchConfig]);

  return {
    config,
    loading,
    error,
    refetch: fetchConfig,
    // Helper properties - sử dụng camelCase từ supabase-utils transform
    isCommentReplyEnabled: config?.enableCommentReply || false,
    isMessageReplyEnabled: config?.enableMessageReply || false,
    isInstagramCommentsEnabled: config?.enableInstagramComments || false,
    isInstagramMessagesEnabled: config?.enableInstagramMessages || false,
    replyTone: config?.replyTone || 'friendly',
    replyLanguage: config?.replyLanguage || 'vi'
  };
}

/**
 * Hook để lấy activity logs
 * @param {string} accountId - Account ID
 * @param {Object} options - Filter options
 * @returns {Object} Hook state và functions
 */
export function useSocialMediaActivityLogs(accountId, options = {}) {
  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const { 
    limit = 50, 
    activityType = null,
    autoRefresh = false,
    refreshInterval = 30000 
  } = options;

  const fetchLogs = useCallback(async () => {
    if (!accountId) {
      setLogs([]);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const filters = {
        pageId: accountId,
        ...(activityType && { activity: activityType })
      };

      const result = await fetchData(TABLES.ACTIVITY_LOGS, {
        filters,
        orderBy: 'createdAt',
        ascending: false,
        limit
      });

      if (!result.success) throw new Error(result.error?.message || 'Failed to fetch logs');

      setLogs(result.data || []);
    } catch (err) {
      console.error('❌ Error fetching activity logs:', err);
      setError(err.message || ERROR_MESSAGES.NETWORK_ERROR);
    } finally {
      setLoading(false);
    }
  }, [accountId, activityType, limit]);

  // Auto refresh
  useEffect(() => {
    if (autoRefresh && refreshInterval > 0) {
      const interval = setInterval(fetchLogs, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval, fetchLogs]);

  useEffect(() => {
    fetchLogs();
  }, [fetchLogs]);

  return {
    logs,
    loading,
    error,
    refetch: fetchLogs,
    // Helper functions
    getLogsByActivity: (activity) => logs.filter(log => log.activity === activity),
    getRecentLogs: (hours = 24) => {
      const cutoff = new Date(Date.now() - hours * 60 * 60 * 1000);
      return logs.filter(log => new Date(log.created_at) > cutoff);
    }
  };
}

/**
 * Hook tổng hợp cho social media integration setup
 * @param {Object} options - Setup options
 * @returns {Object} Comprehensive state và functions
 */
export function useSocialMediaIntegrationSetup(options = {}) {
  const { 
    includePlatforms = Object.values(PLATFORMS),
    autoRefresh = true 
  } = options;

  // Get accounts for all platforms
  const { 
    accounts, 
    accountsByPlatform, 
    loading: accountsLoading, 
    refetch: refetchAccounts 
  } = useSocialMediaAccounts(null, { autoRefresh });

  // Filter by included platforms
  const filteredAccounts = useMemo(() => accounts.filter(account => includePlatforms.includes(account.platform)), [accounts, includePlatforms]);

  const filteredAccountsByPlatform = useMemo(() => Object.keys(accountsByPlatform)
      .filter(platform => includePlatforms.includes(platform))
      .reduce((acc, platform) => {
        acc[platform] = accountsByPlatform[platform];
        return acc;
      }, {}), [accountsByPlatform, includePlatforms]);

  // Statistics
  const stats = useMemo(() => {
    const totalAccounts = filteredAccounts.length;
    const activeAccounts = filteredAccounts.filter(acc => acc.is_active).length;
    const platformStats = includePlatforms.reduce((acc, platform) => {
      const platformAccounts = filteredAccountsByPlatform[platform] || [];
      acc[platform] = {
        total: platformAccounts.length,
        active: platformAccounts.filter(account => account.is_active).length
      };
      return acc;
    }, {});

    return {
      totalAccounts,
      activeAccounts,
      inactiveAccounts: totalAccounts - activeAccounts,
      platformStats
    };
  }, [filteredAccounts, filteredAccountsByPlatform, includePlatforms]);

  return {
    accounts: filteredAccounts,
    accountsByPlatform: filteredAccountsByPlatform,
    stats,
    loading: accountsLoading,
    refetch: refetchAccounts,
    // Helper functions
    hasAccountsForPlatform: (platform) => (filteredAccountsByPlatform[platform]?.length || 0) > 0,
    getActiveAccountsForPlatform: (platform) => 
      (filteredAccountsByPlatform[platform] || []).filter(acc => acc.is_active),
    isAnyPlatformConnected: () => Object.keys(filteredAccountsByPlatform).length > 0
  };
}
