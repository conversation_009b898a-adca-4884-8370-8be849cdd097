/**
 * Service để quản lý việc lọc sản phẩm theo loại chatbot
 * Tối ưu hóa và đồng bộ hệ thống cho việc chọn sản phẩm phù hợp với từng loại bot
 */

import { PRODUCT_TYPES } from './product-constants';

/**
 * Lấy loại sản phẩm phù hợp theo bot type
 * @param {string} botType - Loại bot ('sale_bot' hoặc 'rag_bot')
 * @returns {Object} - Filter object cho product type
 */
export function getProductTypeFilterByBotType(botType) {
  switch (botType) {
    case 'rag_bot':
      // RAG bot sử dụng sản phẩm simple để tối ưu cho AI chatbot
      return { type: PRODUCT_TYPES.SIMPLE };
    case 'sale_bot':
    default:
      // Sale bot sử dụng sản phẩm variable để hỗ trợ bán hàng với nhiều lựa chọn
      return { type: PRODUCT_TYPES.VARIABLE };
  }
}

/**
 * L<PERSON>y mô tả về loại sản phẩm được lọc
 * @param {string} botType - Loại bot
 * @returns {string} - Mô tả loại sản phẩm
 */
export function getProductTypeDescription(botType) {
  switch (botType) {
    case 'rag_bot':
      return 'sản phẩm đơn giản (Simple) phù hợp với RAG Bot';
    case 'sale_bot':
    default:
      return 'sản phẩm có biến thể (Variable) phù hợp với Sale Bot';
  }
}

/**
 * Lấy thông báo cho người dùng về việc lọc sản phẩm
 * @param {string} botType - Loại bot
 * @returns {string} - Thông báo cho người dùng
 */
export function getProductFilterMessage(botType) {
  switch (botType) {
    case 'rag_bot':
      return 'Chỉ hiển thị sản phẩm đơn giản (Simple) phù hợp với RAG Bot';
    case 'sale_bot':
    default:
      return 'Chỉ hiển thị sản phẩm có biến thể (Variable) phù hợp với Sale Bot';
  }
}

/**
 * Lấy empty message khi không có sản phẩm
 * @param {string} botType - Loại bot
 * @returns {string} - Empty message
 */
export function getEmptyProductMessage(botType) {
  switch (botType) {
    case 'rag_bot':
      return 'Không tìm thấy sản phẩm đơn giản (Simple) nào phù hợp';
    case 'sale_bot':
    default:
      return 'Không tìm thấy sản phẩm có biến thể (Variable) nào phù hợp';
  }
}

/**
 * Kiểm tra xem sản phẩm có phù hợp với bot type không
 * @param {Object} product - Sản phẩm cần kiểm tra
 * @param {string} botType - Loại bot
 * @returns {boolean} - true nếu sản phẩm phù hợp
 */
export function isProductCompatibleWithBotType(product, botType) {
  if (!product || !product.type) return false;
  
  const requiredType = getProductTypeFilterByBotType(botType).type;
  return product.type === requiredType;
}

/**
 * Lọc danh sách sản phẩm theo bot type
 * @param {Array} products - Danh sách sản phẩm
 * @param {string} botType - Loại bot
 * @returns {Array} - Danh sách sản phẩm đã lọc
 */
export function filterProductsByBotType(products, botType) {
  if (!products || !Array.isArray(products)) return [];
  
  return products.filter(product => isProductCompatibleWithBotType(product, botType));
}

/**
 * Lấy cấu hình hoàn chỉnh cho việc lọc sản phẩm theo bot type
 * @param {string} botType - Loại bot
 * @returns {Object} - Cấu hình hoàn chỉnh
 */
export function getBotTypeProductConfig(botType) {
  return {
    filter: getProductTypeFilterByBotType(botType),
    description: getProductTypeDescription(botType),
    message: getProductFilterMessage(botType),
    emptyMessage: getEmptyProductMessage(botType),
    productType: getProductTypeFilterByBotType(botType).type,
  };
}

/**
 * Hook helper để sử dụng trong các component
 * @param {string} botType - Loại bot
 * @returns {Object} - Cấu hình và các hàm helper
 */
export function useBotTypeProductFilter(botType) {
  const config = getBotTypeProductConfig(botType);
  
  return {
    ...config,
    isCompatible: (product) => isProductCompatibleWithBotType(product, botType),
    filterProducts: (products) => filterProductsByBotType(products, botType),
  };
}
