'use client';

import { useEffect, useState } from 'react';

import { Box, Alert, Typography, CircularProgress } from '@mui/material';

import { useAuthContext } from 'src/auth/hooks';
import { createClient } from 'src/utils/supabase/client';

// ----------------------------------------------------------------------

export function OAuthUserSetup({ children }) {
  const { user, loading: authLoading } = useAuthContext();
  const [setupStatus, setSetupStatus] = useState('checking'); // checking, success, error
  const [error, setError] = useState(null);
  const [retryCount, setRetryCount] = useState(0);

  useEffect(() => {
    if (authLoading || !user) return;

    const checkUserSetup = async () => {
      try {
        const supabase = createClient();
        
        // Kiểm tra metadata trước
        const tenantId = user.app_metadata?.tenant_id;
        
        if (tenantId) {
          setSetupStatus('success');
          return;
        }

        // Nếu chưa có metadata, kiểm tra database
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('tenant_id, is_active')
          .eq('id', user.id)
          .single();

        if (userError) {
          throw userError;
        }

        if (userData?.tenant_id) {
          setSetupStatus('success');
          
          // Refresh session để cập nhật metadata
          await supabase.auth.refreshSession();
          return;
        }

        // Nếu vẫn chưa có tenant_id, retry
        if (retryCount < 10) {
          setRetryCount(prev => prev + 1);
          setTimeout(checkUserSetup, 2000);
          return;
        }

        // Sau 10 lần retry vẫn không có tenant_id
        throw new Error('Không thể thiết lập tài khoản. Database triggers có thể chưa hoàn thành.');

      } catch (err) {
        setError(err.message);
        setSetupStatus('error');
      }
    };

    checkUserSetup();
  }, [user, authLoading, retryCount]);

  // Hiển thị loading khi đang check setup
  if (authLoading || setupStatus === 'checking') {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '50vh',
          gap: 2,
        }}
      >
        <CircularProgress size={40} />
        <Typography variant="body1" color="text.secondary">
          Đang thiết lập tài khoản...
        </Typography>
        {retryCount > 0 && (
          <Typography variant="body2" color="text.secondary">
            Đang thử lại... ({retryCount}/10)
          </Typography>
        )}
      </Box>
    );
  }

  // Hiển thị lỗi nếu setup thất bại
  if (setupStatus === 'error') {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          <Typography variant="h6" gutterBottom>
            Lỗi thiết lập tài khoản
          </Typography>
          <Typography variant="body2">
            {error}
          </Typography>
        </Alert>
        <Typography variant="body2" color="text.secondary">
          Vui lòng thử đăng nhập lại hoặc liên hệ hỗ trợ.
        </Typography>
      </Box>
    );
  }

  // Setup thành công, hiển thị children
  return children;
}
