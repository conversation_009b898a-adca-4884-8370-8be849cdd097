# Hướng dẫn c<PERSON><PERSON> hình Google OAuth với Supabase

## 1. Tạo Google OAuth Credentials

### Bước 1: <PERSON><PERSON><PERSON> c<PERSON>p Google Cloud Console
1. <PERSON><PERSON> đ<PERSON> [Google Cloud Console](https://console.cloud.google.com/)
2. Tạo project mới hoặc chọn project hiện có
3. Bật Google+ API trong Library

### Bước 2: Tạo OAuth 2.0 Credentials
1. <PERSON><PERSON> đến **APIs & Services** > **Credentials**
2. Click **Create Credentials** > **OAuth 2.0 Client IDs**
3. Chọn **Web application**
4. Đặt tên cho OAuth client
5. Thêm **Authorized redirect URIs**:
   - Development: `https://vhduizefoibsipsiraqf.supabase.co/auth/v1/callback`
   - Production: `https://your-project.supabase.co/auth/v1/callback`

### Bước 3: Lấy Client ID và Client Secret
- Copy **Client ID** và **Client Secret** để cấu hình trong Supabase

## 2. Cấu hình Supabase

### Bước 1: <PERSON><PERSON><PERSON> <PERSON>nh Google Provider
1. Đi đến Supabase Dashboard > **Authentication** > **Providers**
2. Tìm **Google** và bật nó
3. Nhập **Client ID** và **Client Secret** từ Google Cloud Console
4. Thêm redirect URL: `https://vhduizefoibsipsiraqf.supabase.co/auth/v1/callback`

### Bước 2: Cấu hình Site URL
1. Đi đến **Authentication** > **URL Configuration**
2. Thêm Site URL:
   - Development: `http://localhost:3032`
   - Production: `https://your-domain.com`
3. Thêm Redirect URLs:
   - `http://localhost:3032/auth/callback`
   - `https://your-domain.com/auth/callback`

## 3. Cấu hình Environment Variables

Thêm vào file `.env.local`:

```env
# Google OAuth
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your-google-client-id
```

## 4. Kiểm tra Database Triggers

Hệ thống đã có sẵn các triggers tự động:

### Trigger `on_auth_user_created`
- Tự động tạo tenant mới khi user đăng ký
- Tạo bản ghi trong bảng `users`
- Cập nhật metadata cho user

### Trigger `create_tenant_credits_trigger`
- Tự động cấp 200 credits cho tenant mới
- Tạo transaction trong `credit_transactions`
- Ghi lại trong `user_welcome_credits`

## 5. Test Google OAuth

### Bước 1: Kiểm tra cấu hình
1. Đảm bảo Google Client ID đã được thêm vào `.env.local`
2. Restart development server
3. Truy cập trang đăng nhập

### Bước 2: Test đăng nhập
1. Click nút "Đăng nhập với Google"
2. Chọn tài khoản Google
3. Cho phép quyền truy cập
4. Kiểm tra redirect về dashboard

### Bước 3: Kiểm tra database
1. Kiểm tra bảng `auth.users` có user mới
2. Kiểm tra bảng `tenants` có tenant mới
3. Kiểm tra bảng `users` có user record
4. Kiểm tra bảng `tenant_credits` có 200 credits
5. Kiểm tra bảng `credit_transactions` có transaction welcome bonus

## 6. Troubleshooting

### Lỗi "redirect_uri_mismatch"
- Kiểm tra redirect URI trong Google Cloud Console
- Đảm bảo URL chính xác: `https://vhduizefoibsipsiraqf.supabase.co/auth/v1/callback`

### Lỗi "invalid_client"
- Kiểm tra Client ID và Client Secret
- Đảm bảo Google+ API đã được bật

### User không được tạo tenant
- Kiểm tra trigger `on_auth_user_created` có hoạt động
- Kiểm tra logs trong Supabase Dashboard

### User không nhận được credits
- Kiểm tra trigger `create_tenant_credits_trigger`
- Kiểm tra function `create_tenant_credits()`

## 7. Security Notes

1. **Client Secret**: Chỉ lưu trữ trong Supabase Dashboard, không expose ra client
2. **Redirect URLs**: Chỉ thêm các domain tin cậy
3. **Scopes**: Chỉ yêu cầu các quyền cần thiết (email, profile)
4. **HTTPS**: Luôn sử dụng HTTPS trong production

## 8. Production Deployment

### Cập nhật Google Cloud Console
1. Thêm production domain vào Authorized redirect URIs
2. Cập nhật Authorized JavaScript origins

### Cập nhật Supabase
1. Cập nhật Site URL với production domain
2. Thêm production redirect URLs
3. Test OAuth flow trên production

### Environment Variables
```env
# Production
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your-production-google-client-id
NEXT_PUBLIC_PUBLIC_SITE_URL=https://your-production-domain.com
```
