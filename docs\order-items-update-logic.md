# 📋 Logic Cập Nhật Order Items - Hướng Dẫn Chi Tiết

## 🎯 Tổng Quan

Hệ thống cập nhật order items được thiết kế để xử lý 3 trường hợp chính:
1. **Cập nhật** item đã tồn tại
2. **Thêm mới** item vào order
3. **Xóa** item khỏi order

Tất cả các thao tác đều tự động cập nhật inventory và tạo transaction history.

## 🏗️ Kiến Trúc Hệ Thống

### Frontend (React)
```
OrderEditItemsDialog.jsx
├── Validation (Zod Schema)
├── Data Processing (Utils)
└── API Call (Mutations)
```

### Backend (Supabase)
```
update_order_items_with_inventory()
├── Phân tích items cũ vs mới
├── Cập nhật/Thêm/Xóa items
└── update_product_inventory_for_order_change()
    ├── Cập nhật stock_quantity
    └── Tạo inventory_transactions
```

## 🔄 Flow Xử Lý Chi Tiết

### 1. Frontend Validation & Processing

#### Zod Schema Validation
```javascript
// Validation rules
- id: UUID optional (null cho item mới)
- productId: UUID required
- variantId: UUID optional hoặc empty string
- quantity: số nguyên > 0
- unitPrice, totalPrice: số >= 0
- Không duplicate productId + variantId
```

#### Data Processing
```javascript
// Chuẩn hóa dữ liệu
prepareOrderItemsForSubmit(rawItems)
├── normalizeOrderItem() - Loại bỏ UI fields
├── validateOrderItem() - Validate từng item
└── checkDuplicateItems() - Kiểm tra trùng lặp
```

### 2. Database Function Logic

#### `update_order_items_with_inventory(p_order_id, p_order_items)`

**Bước 1: Lấy items hiện tại**
```sql
SELECT * FROM order_items WHERE order_id = p_order_id
-- Tạo map: current_items_map[item_id] = item_data
```

**Bước 2: Xử lý từng item mới**
```sql
FOR new_item IN p_order_items LOOP
  IF item_id EXISTS IN current_items_map THEN
    -- CẬP NHẬT item đã tồn tại
    UPDATE order_items SET ...
    -- Cập nhật inventory nếu quantity thay đổi
    IF quantity_diff != 0 THEN
      CALL update_product_inventory_for_order_change(...)
    END IF
  ELSE
    -- THÊM MỚI item
    INSERT INTO order_items ...
    -- Trừ inventory cho item mới
    CALL update_product_inventory_for_order_change(...)
  END IF
END LOOP
```

**Bước 3: Xóa items không còn trong danh sách**
```sql
FOR old_item NOT IN processed_items LOOP
  -- Trả lại inventory
  CALL update_product_inventory_for_order_change(+quantity)
  -- Xóa item
  DELETE FROM order_items WHERE id = old_item.id
END LOOP
```

### 3. Inventory Update Logic

#### `update_product_inventory_for_order_change()`

**Xác định loại sản phẩm**
```sql
-- Bỏ qua digital/service products
IF product_type IN ('digital', 'service') THEN RETURN; END IF
```

**Cập nhật inventory**
```sql
IF variant_id IS NOT NULL THEN
  -- Cập nhật variant stock
  UPDATE product_variants SET stock_quantity = new_quantity
ELSE
  -- Cập nhật product stock
  UPDATE products SET stock_quantity = new_quantity
END IF
```

**Tạo transaction history**
```sql
INSERT INTO inventory_transactions (
  type: 'sale' (quantity âm) hoặc 'return' (quantity dương),
  quantity: ABS(quantity_change),
  reference_id: order_id,
  reference_type: operation_type
)
```

## 📊 Các Trường Hợp Xử Lý

### Case 1: Cập Nhật Quantity
```
Item cũ: quantity = 5
Item mới: quantity = 8
→ quantity_diff = +3
→ Trừ thêm 3 từ inventory
→ Transaction: type='sale', quantity=3
```

### Case 2: Giảm Quantity
```
Item cũ: quantity = 10
Item mới: quantity = 7
→ quantity_diff = -3
→ Trả lại 3 vào inventory
→ Transaction: type='return', quantity=3
```

### Case 3: Thêm Item Mới
```
Item mới: quantity = 5
→ Trừ 5 từ inventory
→ Transaction: type='sale', quantity=5
```

### Case 4: Xóa Item
```
Item cũ: quantity = 8
→ Trả lại 8 vào inventory
→ Transaction: type='return', quantity=8
```

## 🛡️ Error Handling

### Frontend Validation Errors
- Missing required fields
- Invalid data types
- Duplicate items
- Business rule violations

### Database Function Errors
- Product not found
- Insufficient inventory
- Constraint violations
- Transaction failures

### Rollback Strategy
- Database function sử dụng transaction
- Lỗi ở bất kỳ bước nào sẽ rollback toàn bộ
- Frontend hiển thị error message chi tiết

## 🔍 Debug & Monitoring

### Console Logs
```javascript
// Frontend
console.log('Updating order items:', {
  orderId, itemsCount, items
});

// Backend function logs
console.log('✅ Đã cập nhật order items:', result.data);
```

### Database Queries để Debug
```sql
-- Kiểm tra order items
SELECT * FROM order_items WHERE order_id = 'xxx';

-- Kiểm tra inventory transactions
SELECT * FROM inventory_transactions 
WHERE reference_id = 'xxx' 
ORDER BY created_at DESC;

-- Kiểm tra stock hiện tại
SELECT stock_quantity FROM products WHERE id = 'xxx';
SELECT stock_quantity FROM product_variants WHERE id = 'xxx';
```

## ✅ Testing Checklist

### Functional Tests
- [ ] Cập nhật quantity item đã tồn tại
- [ ] Thêm item mới vào order
- [ ] Xóa item khỏi order
- [ ] Mix operations (update + add + delete)
- [ ] Inventory tracking chính xác
- [ ] Transaction history đầy đủ

### Edge Cases
- [ ] Order chỉ có 1 item → xóa → thêm item mới
- [ ] Cập nhật quantity = 0 (should delete)
- [ ] Digital/Service products (no inventory update)
- [ ] Variant vs simple products
- [ ] Insufficient inventory scenarios

### Error Scenarios
- [ ] Invalid product ID
- [ ] Missing required fields
- [ ] Duplicate items
- [ ] Network errors
- [ ] Database constraint violations

## 🚀 Performance Considerations

### Optimizations
- Single database function call thay vì multiple API calls
- Batch inventory updates
- Efficient JSON processing
- Minimal data transfer

### Monitoring Points
- Function execution time
- Inventory transaction volume
- Error rates
- User experience metrics

---

**Kết luận**: Hệ thống đã được thiết kế để xử lý tất cả các trường hợp update order items một cách an toàn và chính xác, với inventory tracking tự động và error handling toàn diện.
