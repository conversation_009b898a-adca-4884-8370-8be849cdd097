'use client';

import { useRef, useState, useCallback } from 'react';

import { snakeToCamelObject } from 'src/utils/format-data/case-converter';

import { callRPC } from './supabase-utils';

/**
 * Mời người dùng vào cửa hàng
 * @param {string} storeId - ID của cửa hàng
 * @param {string} email - Email của người dùng được mời
 * @param {string} role - <PERSON>ai trò của người dùng trong cửa hàng (viewer, editor, admin)
 * @param {boolean} isManager - <PERSON><PERSON> phải là quản lý cửa hàng không
 * @returns {Promise<Object>} - Kế<PERSON> quả từ API
 */
export async function inviteUserToStore(storeId, email, role = 'viewer', isManager = false) {
  console.log('Inviting user to store:', { storeId, email, role, isManager });

  if (!storeId) {
    console.error('Store ID is required');
    return { success: false, error: 'Store ID is required', data: null };
  }

  if (!email) {
    console.error('Email is required');
    return { success: false, error: 'Email is required', data: null };
  }

  try {
    // Sử dụng RPC thay vì Edge Function để tránh lỗi
    const result = await callRPC('invite_user_to_store', {
      store_id_param: storeId,
      email_param: email,
      role_param: role,
      is_manager_param: isManager,
    });

    console.log('Invitation result:', result);

    if (!result.success) {
      console.error('Error inviting user to store:', result.error);
      return result;
    }

    return snakeToCamelObject(result);
  } catch (error) {
    console.error('Error inviting user to store:', error);
    return { success: false, error, data: null };
  }
}

/**
 * Lấy thông tin lời mời dựa trên token
 * @param {string} token - Token của lời mời
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getStoreInvitation(token) {
  if (!token) {
    return { success: false, error: 'Token is required', data: null };
  }

  try {
    // Sử dụng RPC thay vì Edge Function để tránh lỗi
    const result = await callRPC('get_store_invitation', {
      token_param: token,
    });

    console.log('Get invitation result:', result);

    if (!result.success) {
      console.error('Error getting invitation:', result.error);
      return result;
    }

    return snakeToCamelObject(result);
  } catch (error) {
    console.error('Error getting invitation:', error);
    return { success: false, error, data: null };
  }
}

/**
 * Chấp nhận lời mời vào cửa hàng
 * @param {string} token - Token của lời mời
 * @param {string} password - Mật khẩu mới (nếu là người dùng mới)
 * @param {string} fullName - Họ tên của người dùng (nếu là người dùng mới)
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function acceptStoreInvitation(token, password = null, fullName = null) {
  if (!token) {
    return { success: false, error: 'Token is required', data: null };
  }

  try {
    // Sử dụng RPC thay vì Edge Function để tránh lỗi
    const result = await callRPC('accept_store_invitation', {
      token_param: token,
      password_param: password,
      full_name_param: fullName,
    });

    console.log('Invitation acceptance result:', result);

    if (!result.success) {
      console.error('Error accepting invitation:', result.error);
      return result;
    }

    return snakeToCamelObject(result);
  } catch (error) {
    console.error('Error accepting invitation:', error);
    return { success: false, error, data: null };
  }
}

/**
 * Lấy danh sách lời mời của cửa hàng
 * @param {string} storeId - ID của cửa hàng
 * @param {string} status - Trạng thái lời mời (pending, accepted, expired, cancelled)
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getStoreInvitations(storeId, status = 'pending') {
  if (!storeId) {
    return { success: false, error: 'Store ID is required', data: null };
  }

  try {
    // Sử dụng RPC thay vì Edge Function để tránh lỗi
    const result = await callRPC('get_store_invitations', {
      store_id_param: storeId,
      status_param: status,
    });

    console.log('Get invitations result:', result);

    if (!result.success) {
      console.error('Error getting invitations:', result.error);
      return result;
    }

    return snakeToCamelObject(result);
  } catch (error) {
    console.error('Error getting invitations:', error);
    return { success: false, error, data: null };
  }
}

/**
 * Hủy lời mời vào cửa hàng
 * @param {string} invitationId - ID của lời mời
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function cancelStoreInvitation(invitationId) {
  if (!invitationId) {
    return { success: false, error: 'Invitation ID is required', data: null };
  }

  try {
    // Sử dụng RPC thay vì Edge Function để tránh lỗi
    const result = await callRPC('cancel_store_invitation', {
      invitation_id_param: invitationId,
    });

    console.log('Cancel invitation result:', result);

    if (!result.success) {
      console.error('Error cancelling invitation:', result.error);
      return result;
    }

    return snakeToCamelObject(result);
  } catch (error) {
    console.error('Error cancelling invitation:', error);
    return { success: false, error, data: null };
  }
}

/**
 * Hook để quản lý lời mời cửa hàng
 * @returns {Object} - Các hàm và trạng thái liên quan đến lời mời
 */
export function useStoreInvitations() {
  const [loadingStates, setLoadingStates] = useState({
    inviting: false,
    fetching: false,
    accepting: false,
    cancelling: false,
  });

  // Sử dụng useRef để lưu trữ các hàm API mà không gây re-render
  const apiRef = useRef({
    inviteUserToStore,
    getStoreInvitation,
    acceptStoreInvitation,
    getStoreInvitations,
    cancelStoreInvitation,
  });

  // Helper function to handle loading state and error handling - sử dụng useCallback để tránh tạo lại hàm
  const withLoadingState = useCallback(async (stateKey, asyncFn) => {
    setLoadingStates((prev) => ({ ...prev, [stateKey]: true }));
    try {
      return await asyncFn();
    } finally {
      setLoadingStates((prev) => ({ ...prev, [stateKey]: false }));
    }
  }, []);

  // Mutation functions using the helper - sử dụng useCallback để tránh tạo lại hàm
  const inviteUserToStoreMutation = useCallback(
    (storeId, email, role, isManager) =>
      withLoadingState('inviting', () =>
        apiRef.current.inviteUserToStore(storeId, email, role, isManager)
      ),
    [withLoadingState]
  );

  const getStoreInvitationMutation = useCallback(
    (token) => withLoadingState('fetching', () => apiRef.current.getStoreInvitation(token)),
    [withLoadingState]
  );

  const acceptStoreInvitationMutation = useCallback(
    (token, password, fullName) =>
      withLoadingState('accepting', () =>
        apiRef.current.acceptStoreInvitation(token, password, fullName)
      ),
    [withLoadingState]
  );

  const getStoreInvitationsMutation = useCallback(
    (storeId, status) =>
      withLoadingState('fetching', () => apiRef.current.getStoreInvitations(storeId, status)),
    [withLoadingState]
  );

  const cancelStoreInvitationMutation = useCallback(
    (invitationId) =>
      withLoadingState('cancelling', () => apiRef.current.cancelStoreInvitation(invitationId)),
    [withLoadingState]
  );

  // Calculate if any mutation is in progress
  const isMutating = Object.values(loadingStates).some(Boolean);

  return {
    inviteUserToStore: inviteUserToStoreMutation,
    getStoreInvitation: getStoreInvitationMutation,
    acceptStoreInvitation: acceptStoreInvitationMutation,
    getStoreInvitations: getStoreInvitationsMutation,
    cancelStoreInvitation: cancelStoreInvitationMutation,
    isInviting: loadingStates.inviting,
    isFetching: loadingStates.fetching,
    isAccepting: loadingStates.accepting,
    isCancelling: loadingStates.cancelling,
    isMutating,
  };
}
